import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { G9DemoDataService, overlapVariables } from '../ui-schooladmin/g9-demo-data.service';
import { IUserInfo, AuthService, getFrontendDomain } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { Router } from '@angular/router';
import { LangService } from './lang.service';
import { IStudentAccount } from '../ui-schooladmin/data/types';
import { ActivatedRoute } from '@angular/router';
import { LoginGuardService } from '../api/login-guard.service';


@Injectable({
    providedIn: 'root'
  })
export class ClassroomsService {
  // updateSubSessionTime(subSessionId: any, refinedPayload: any, params: { query: { school_class_group_id: any; }; }) {
  //   throw new Error('Method not implemented.');
  // }

  constructor(
    private g9DemoData: G9DemoDataService,
    private auth: AuthService,
    private route: ActivatedRoute,
    private routes: RoutesService,
    private router: Router,
    private lang: LangService,
    public loginGuard: LoginGuardService,
  ) {
    this.classrooms = []; //this.demoData.teacherClassrooms.list;
    this.schl_group_id = this.route.snapshot.queryParams['school']
    this.auth.user().subscribe(v => {
      this._userInfo = v;
      this.router
      if (this._userInfo) {
        this.loadSchoolData();
      }
    });
  }

  // classroom = this.dataSource.asObservable();
  classrooms = []
  private _userInfo:IUserInfo;
  private info: BehaviorSubject<any> = new BehaviorSubject(null);
  private dataSource: BehaviorSubject<any> = new BehaviorSubject('');
  private schl_group_id;
  private schoolData
  private activeSchoolInfo: { // this is likely a duplicate intention of the above, but the above is always null and I am not sure what will happen if I suddenly feed it with a value
    lang: string,
    foreign_id: string,
    name: string,
    school_type: string,
    sd_foreign_id: string,
    sd_name: string,
    type_slug: string,
    is_active: number,
    is_free_start: number,
    is_kiosk_approved: number,
    is_private: number,
    is_sandbox: number,
    is_sasn_login: number,
    // is_insecure
    // is_insecure_g9
    // is_insecure_g10
    // is_insecure_pj
    // is_softlock_enabled
    // is_softlock_enabled_g9
    // is_softlock_enabled_g10
    // is_softlock_enabled_pj

  }
  isIntervalInitialized:boolean;
  // private classGroupIds = [];

  public sub(){
    return this.info;
  }

  isLoadingInited:boolean;
  private loadSchoolData(){
    if (this.isLoadingInited){
      return Promise.resolve();
    }
    else{
      this.isLoadingInited = true;
      return this.auth
        .apiFind(this.routes.EDUCATOR_SCHOOL, {query: {schl_group_id: this.schl_group_id}})
        .then (schoolsData => {
          const activeSchool = schoolsData[0];
          this.activeSchoolInfo = activeSchool.school[0]
          console.log('activeSchoolInfo', this.activeSchoolInfo)
          this.g9DemoData.init(activeSchool); // always just one school from this request
          this.classrooms = this.g9DemoData.teacherClassrooms.list;
          this.schoolData = this.g9DemoData.schoolData[0];
          this.info.next(schoolsData);
          this.initInterval();
        });
    }
  }

  getSchoolByClassroomId(classroomId:number){
    const schools = this.info.value;
    if (schools && schools.length ){
      const schoolRecords = schools[0].school;
      if (schoolRecords && schoolRecords.length){
        return schoolRecords[0];
      }
    }
  }

  isFreeStart(classroomId:number){
    const school = this.getSchoolByClassroomId(classroomId);
    if (school){
      // to do: make it match the classroom
      return (school.is_free_start == 1);
    }
  }

  isSASNLogin(classroomId:number){
    const school = this.getSchoolByClassroomId(classroomId);
    if (school){
      return (school.is_sasn_login == 1);
    }
  }

  public navigateToClassroom(classroomId:string, queryParams = {}){
    this.router.navigate([this.lang.c(),
      'educator',
      'classrooms',
      classroomId
    ], {
      queryParams
    });
  }
  public navigateToAssessmentScheduler(classroomId:string){
    this.router.navigate([this.lang.c(),
      'educator',
      'assessment-scheduler',
      classroomId
    ]);
  }
  public navigateToAssessmentSession(classroomId:string, sessionId:string, queryParams = {}){
    this.router.navigate([this.lang.c(),
      'educator',
      'assessment',
      classroomId,
      sessionId
    ], {
      queryParams
    });
  }

  public closeAssessmentSession(school_class_id:string, sessionId:string,params:{query:{school_class_group_id:number,is_session_completed:boolean}}){
    return this.auth
      .apiRemove(this.routes.EDUCATOR_SESSION, sessionId,params)
      .then(() => {
        if(params.query.is_session_completed){
          const classroom = this.getClassroomById(school_class_id);
          this.removeAssessment(classroom.openAssessments,sessionId);
        }
        else{
          const classroom = this.getClassroomById(school_class_id);
          this.removeAssessment(classroom.scheduledAssessments,sessionId);
        }
      })
  }
  removeAssessment(assessments,sessionId){
    for (let i=0; i<assessments.length; i++){
      const session = assessments[i];
      if (session.test_session_id == sessionId){
        assessments.splice(i ,1);
      }
    }
  }
  public createStudent(schoolClassId, student, schoolClassGroupId, isSasnLogin = false){
    return this.auth.apiCreate(this.routes.EDUCATOR_STUDENTS, {
      schoolClassId,
      student,
      schoolClassGroupId,
      isSasnLogin : isSasnLogin? 1:0 
    },
    {
      query: {
        school_class_group_id:schoolClassGroupId
      }
    })
    // this.auth.apiCreate(this.routes.LOG, {
    //   slug: 'TEACHER_CREATE_STUDENT_PENDING',
    //   data: {
    //     classroom_id,
    //     student,
    //   }
    // });
  }

  public moveStudent(student: IStudentAccount, school_class_group_id: number){
    return this.auth.apiPatch(this.routes.EDUCATOR_STUDENTS, student.id, {},
    {
      query: { school_class_group_id }
    }).then(res => {
      // Find the student's new class room (Moved to)
      const targetClass = this.classrooms.find(classroom => classroom.group_id === school_class_group_id);
      let currentStudent: IStudentAccount = null;
      if (targetClass !== undefined) {
        // Find the student's previous class room (Moved From)
        const studentPreviousClass = this.classrooms.find(clr => {
          const existingStudent = clr.currentStudents.list.find(st => {
            if (st.id === student.id) {
              return student;
            }
          });
          if (existingStudent) {
            currentStudent = existingStudent;
            return clr;
          }
        });

        // ========= Handle internal moves =========

        // Remove the student from the previous classroom
        if (studentPreviousClass) {
          // Find the index
          const index = studentPreviousClass.currentStudents.list.indexOf(currentStudent);
          if (index > -1) {
            // Remove from the previous classroom
            studentPreviousClass.currentStudents.list.splice(index, 1);
          }

          if (studentPreviousClass.group_type === targetClass.group_type) {
            targetClass.currentStudents.list.push(currentStudent);
          }
        } else { // We moved the student from other teacher's class to target class
          // This student is new for our scope we will creat tmp object and add it to the target class
          student.displayName = student.first_name + " " + student.last_name;
          student.eqao_g9_class_code = targetClass.id;
          student.eqao_g9_class_code_label = targetClass.name;
          // const movedStudent = {
          //   id: student.id,
          //   first_name: student.first_name,
          //   last_name: student.last_name,
          //   eqao_gender: student.meta.Gender,
          //   displayName: student.first_name + " " + student.last_name,
          //   eqao_g9_class_code: targetClass.id,
          //   eqao_g9_class_code_label: targetClass.name,
          //   eqao_student_gov_id: student.eqao_student_gov_id,
          //   uid: student.id,
          // };
          targetClass.currentStudents.list.push(student);
          targetClass.currentStudents.map[student.id] = student;
        }
        // Update the UI
        this.sub().next(this.sub().value);
      }      
    })
  }

  public async teacherReportIssue(school_class_id, test_session_id, msg, categorySelection, subjectsSelected:string, student?){
    await this.auth.apiCreate('public/educator/session-reported-issue', {
      school_class_id,
      test_session_id,
      msg,
      student,
      categorySelection,
      subjectsSelected
    },
    { 
      query: { 
        schl_class_group_id: this.getClassroomById(school_class_id).group_id
      }
    })
    .catch(e => {
      alert('Failed to save reported issue')
    })

    this.loginGuard.quickPopup(this.lang.tra('lbl_issues_reported') + ':\n\n' + msg)
  }


  public createAssessmentSession(config: {slug:string, school_class_id:number, isScheduled:boolean,scheduled_time:string, is_fi:boolean}, params:{query:{school_class_group_id:number}}){
    return this.auth
      .apiCreate(this.routes.EDUCATOR_SESSION, config,params )
      .then( (session:any) => {
        const classroom = this.getClassroomById(config.school_class_id);
        if (classroom && !config.isScheduled){
          // classroom.openAssessments.unshift(this.g9DemoData.processSessionEntry(session))
          classroom.openAssessments.push(this.g9DemoData.processSessionEntry(session))
        }
        return session;
      })
      .catch(error =>{
        if (error.message === 'ONGOING_SAMPLE_ASSESSMENT') {
          if (config.slug === 'PRIMARY_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_primary'))  
          } else if (config.slug === 'JUNIOR_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_junior'))  
          } else if (config.slug === 'G9_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))  
          } else if (config.slug === 'OSSLT_SAMPLE') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'));
          }
        } else if (error.message === 'ONGOING_LIVE_ASSESSMENT') {
          if (config.slug === 'PRIMARY_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_primary'))  
          } else if (config.slug === 'JUNIOR_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_junior'))  
          } else if (config.slug === 'G9_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))  
          } else if (config.slug === 'OSSLT_OPERATIONAL') {
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'));   
          }
        } else if (error.message === 'MSG_INVALID_SCHEDULE_TIME'){
          this.loginGuard.disabledPopup(this.lang.tra('msg_invalid_schedule_time'));
        } else if (error.message === 'SANDBOX_SCHL_NOT_ALLOW_OPERATIONAL_TEST'){
          this.loginGuard.disabledPopup(this.lang.tra('msg_sandbox_schl_not_allow_op_test'));
        } 
        else{
          throw error
        }
      })
  }
  getActiveAssessments(params:{query:{school_class_group_id,school_class_id,slug}}){
    return   this.auth
    .apiFind(this.routes.EDUCATOR_SESSION, params)
  }
  getClassroomById(classroomId){
    let match;
    this.classrooms.forEach(classroom => {
      if (classroom.id == classroomId){
        match = classroom;
      }
    })
    return match;
  }
  constructClassGroupIdQuery(classroomId){
    const classroom = this.getClassroomById(classroomId);
    if (classroom){
      return {
        query: {
          school_class_group_id: classroom.group_id,
          school_class_id: classroomId
        }
      }
    }
    return null;
  }
  private initInterval(){
    if (!this.isIntervalInitialized){
      this.isIntervalInitialized = true;
      setInterval(this.refreshStudentData, 5*1000);
      // this.refreshStudentData();
    }
  }
  refreshStudentData = () => {
    if (this.classrooms){
      // this.classGroupIds = this.classrooms.map(classroom => classroom.group_id);
        const group_ids = this.classrooms.map(classroom => classroom.group_id)
        // return this.auth
        //   .apiCreate(this.routes.EDUCATOR_STUDENTS, {type: 'TEACHER_INTERVAL', group_ids})
        //   .then (res => {

        //     // console.log(studentSummaries, this.classrooms)
        //     this.classrooms.forEach(classroom => {
        //       classroom.currentStudents.list.forEach(student => {
        //         if (res.studentSummaries[student.id]){
        //           student.status = res.studentSummaries[student.id];
        //         }
        //       })
        //     });

        //     // hack
        //     const classroom = this.classrooms[0];
        //     if (res.activeAssessment && classroom.openAssessments.length === 0){
        //       classroom.openAssessments = [
        //         {
        //           name: 'Sample Assessment ',
        //           timeDirectStart: 'Nov 2',
        //           session_id: res.activeAssessment
        //         },
        //       ]
        //     }
        //   })
      }
  }


  invigOpenCloseSubSessionForStudents(testSessionId:number, subSessionId:number, twtdarOrder:number, studentUids:number[],classId:number, isClosing:boolean, params:{query:{school_class_group_id:number}}, isPJ=false, isSubmitting?: boolean){
    return this.auth.apiPatch(this.routes.EDUCATOR_SESSION_SUB, testSessionId, {
      subSessionId,
      twtdarOrder,
      isClosing,
      studentUids,
      classId,
      isPJ,
      isSubmitting
    },params)
  }

  invigOpenCloseTestAttemptForStudents(testAttemptId:number, isPausing:boolean, params:{query:{school_class_group_id:number}}){
    return this.auth.apiPatch(this.routes.EDUCATOR_SOFT_LOCK, testAttemptId, {
      isPausing,
    },params)
  }
  
  updateSubSessionTime(subSessionId:number,payload,params:{query:{school_class_group_id:number}}){
    return this.auth.apiUpdate(this.routes.EDUCATOR_SESSION_SUB, subSessionId, payload,params)
             .catch(error =>{
                if (error.message === 'MSG_INVALID_SCHEDULE_TIME'){
                  this.loginGuard.disabledPopup(this.lang.tra('msg_invalid_schedule_time'));
                }
                throw error;
             })
  }

  setActiveClassroom(classroom){
    this.dataSource.next(classroom);
  }

  saveClassroom(classroom){
    this.classrooms.push(classroom)
  }

  getSchlGpId(){
    return this.schl_group_id;
  }

  reloadSchool(){
    this.schl_group_id = this.route.snapshot.queryParams['school']
    this.isLoadingInited = false;
    this.loadSchoolData();
  }

  getSchoolName(){
    if (this.activeSchoolInfo){
      return `${this.activeSchoolInfo.name} (${this.activeSchoolInfo.foreign_id})`
    }
    return '...'
  }

  generateClassG9Reports(schl_class_group_id, bypassAdminSignOff = false){
    const queryparams = {
      schl_class_group_id,
      bypassAdminSignOff,
      clientDomain:getFrontendDomain(),
    }
    return this.auth
      .apiCreate(this.routes.EDUCATOR_G9_REPORTS, {},{
        query: queryparams
      }).then(res =>{
        return res;
      })
  }
  
  loadClassG9Reports(schl_class_group_id, bypassAdminSignOff = false){
    const queryparams = {
      schl_class_group_id,
      bypassAdminSignOff,
      clientDomain:getFrontendDomain(),
    }
    return this.auth
      .apiGet(this.routes.EDUCATOR_G9_REPORTS, schl_class_group_id, {
        query: queryparams
      }).then(res =>{
        return res;
      })
  }

  downloadClassG9IndividualReports(schl_class_group_id: string, attemptId: number, student_uid: number){
    const queryparams = {
      schl_class_group_id,
      attemptId,
      clientDomain:getFrontendDomain(),
      uid: student_uid
    }
    return this.auth
      .apiFind(this.routes.EDUCATOR_G9_REPORTS, {
        query: queryparams
      }).then(res =>{
        return res;
      })
  }

  /**
   * gets the generic accommodation label from curriculum specific label and 
   * @returns accommodation slug 
   */
  getAccLabel(acc, getPrefixFunction) {
    if(overlapVariables.includes(acc)) {
      return this.teacherAccommodationsLabels[acc];
    }
    const propNoPrefix = acc.substring(getPrefixFunction().length);
    return this.teacherAccommodationsLabels[propNoPrefix];
  }

    /**
   * gets the generic assistive tech label from curriculum specific label and 
   * @returns assistive tech slug 
   */
  getTechLabel(acc, getPrefixFunction) {
    if(overlapVariables.includes(acc)) {
      return this.teacherAssissTechsLabels[acc];
    }
    const propNoPrefix = acc.substring(getPrefixFunction().length);
    return this.teacherAssissTechsLabels[propNoPrefix];
  }

    /**
   * gets the generic exemption label from curriculum specific label and 
   * @returns exemption slug 
   */
  getExemptionLabel(acc, getPrefixFunction) {
    if(overlapVariables.includes(acc)) {
      return this.teacherExemptionLabels[acc];
    }
    const propNoPrefix = acc.substring(getPrefixFunction().length);
    return this.teacherExemptionLabels[propNoPrefix];
  }

  genTeacherAccommProps(curricShort) {
    return Object.keys(this.teacherAccommodationsLabels).map( prop => this.g9DemoData.getPropName(prop, curricShort));
  }

  genTeacherExemptionProps(curricShort){
    return Object.keys(this.teacherExemptionLabels).map( prop => this.g9DemoData.getPropName(prop, curricShort));
  }

  genTeacherAssisTechProps(curricShort){
    return Object.keys(this.teacherAssissTechsLabels).map( prop => this.g9DemoData.getPropName(prop, curricShort));
  }

  teacherAccommodationsLabels = {
    'eqao_acc_braille': 'sdc_1_ueb',
    'AccAudioVersion': 'sdc_1_aud_audio_version',
    'AccOther': 'lbl_sdc_acc_other', 
  
    'eqao_pres_format': 'sdc_1_sign_lang_oral',
    '_extended_time': 'sdc_1_ext_per_sup_br',
    '_audio_recording_of_resp': 'sdc_1_aud_rec_st_resp',
    'eqao_acc_scribing': 'sdc_1_verb_scrb',
    'eqao_acc_alt_version': 'sdc_1_alt_version',
    'AccVideotapeResponse': 'lbl_sdc_acc_videotape_response',
    '_periodic_breaks': 'sdc_1_spec_prov', 
  
    'eqao_acc_braille_pj_mathematics': 'sdc_1_acc_view_mathematics',
    'eqao_acc_braille_pj_reading': 'sdc_1_acc_view_reading',
    'eqao_acc_braille_pj_writing': 'sdc_1_acc_view_writing',
  
    'AccAudioVersion_pj_mathematics': 'sdc_1_acc_view_mathematics',
    'AccAudioVersion_pj_reading': 'sdc_1_acc_view_reading',
    'AccAudioVersion_pj_writing': 'sdc_1_acc_view_writing',
  
    'eqao_pres_format_pj_mathematics': 'sdc_1_acc_view_mathematics',
    'eqao_pres_format_pj_reading': 'sdc_1_acc_view_reading',
    'eqao_pres_format_pj_writing': 'sdc_1_acc_view_writing',
  
    'eqao_acc_scribing_pj_mathematics': 'sdc_1_acc_view_mathematics',
    'eqao_acc_scribing_pj_reading': 'sdc_1_acc_view_reading',
    'eqao_acc_scribing_pj_writing': 'sdc_1_acc_view_writing',
  };

  teacherExemptionLabels = {
    "NonParticipationStatus_exempted": "sdc_nonparticipation_1",
    "NonParticipationStatus_deferred": "sdc_nonparticipation_2",
    "NonParticipationStatus_osslc": "sdc_nonparticipation_3",
    "NonParticipationStatus_osslc_spring": "sdc_nonparticipation_4",
    
    "eqao_exemption_pj": "sdc_pj_exemption",
    "eqao_exemption_pj_reading": "sdc_1_acc_view_reading", 
    "eqao_exemption_pj_writing": "sdc_1_acc_view_writing",
    "eqao_exemption_pj_mathematics": "sdc_1_acc_view_mathematics"
  }

  teacherAssissTechsLabels = {
    'eqao_acc_assistive_tech_1_chrome': 'sdc_assist_tech_chrome',
    'eqao_acc_assistive_tech_2_kurz_dl': 'sdc_assist_tech_kurz_dl',
    'eqao_acc_assistive_tech_2_kurz_ext': 'sdc_assist_tech_kurz_ext',
    'eqao_acc_assistive_tech_2_nvda': 'sdc_assist_nvda',
    'eqao_acc_assistive_tech_2_voiceover': 'sdc_assist_voiceover',
    'eqao_acc_assistive_tech_2_readaloud': 'sdc_assist_readaloud',
    'eqao_acc_assistive_tech_2_jaws': 'sdc_jaws',
    'eqao_acc_assistive_tech_2_chromevox': 'sdc_chromevox',
    'eqao_acc_assistive_tech_2_read': 'sdc_natural_reader',
    'eqao_acc_assistive_tech_2_other': 'sdc_1_assist_tech_other', 
    'eqao_acc_assistive_tech_3_chrome_2': 'sdc_assist_tech_chrome_2',

    'eqao_acc_assistive_tech_1_pj_mathematics_chrome': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_1_pj_reading_chrome': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_1_pj_writing_chrome': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_kurz_dl': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_kurz_dl': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_kurz_dl': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_kurz_ext': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_kurz_ext': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_kurz_ext': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_nvda': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_nvda': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_nvda': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_voiceover': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_voiceover': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_voiceover': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_readaloud': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_readaloud': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_readaloud': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_jaws': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_jaws': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_jaws': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_chromevox': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_chromevox': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_chromevox': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_read': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_read': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_read': 'sdc_1_acc_view_writing',

    'eqao_acc_assistive_tech_2_pj_mathematics_other': 'sdc_1_acc_view_mathematics',
    'eqao_acc_assistive_tech_2_pj_reading_other': 'sdc_1_acc_view_reading',
    'eqao_acc_assistive_tech_2_pj_writing_other': 'sdc_1_acc_view_writing',
  }
}
