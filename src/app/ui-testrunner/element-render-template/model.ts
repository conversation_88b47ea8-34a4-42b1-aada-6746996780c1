import { IContentElement, IScoredResponse } from "src/app/ui-testrunner/models";
import { IElementTypeEditInfo } from './../models/index'

export const templateEditInfo: IElementTypeEditInfo = {
  nonHiddenChangeLogFields: ['content'],
  editExcludeFields: ['content']
}

enum IScientificNotationHiddenConfigs {
}
enum IInteractiveDiagramHiddenConfigs {
    
}

export interface IClozeMathHiddenConfigs {
  isHideFontSize?: boolean,
}

export interface ISelectionData {
  type: 'dropdown' | 'button'
  options: {id: any, caption?: string, icon?: string}[]
}

export interface IAdvancedInlineHiddenConfigs {
  isConfigureContentHiddenConfigs?: boolean,
  isShowOnlyStaticElements?: boolean,
  contentHiddenConfigs:{
    // 'scientific-notation': IScientificNotationHiddenConfigs,
  }
}
export interface IClozeMathHiddenConfigs {
  isHideFontSize?: boolean,
}

export interface ISelectionData {
  type: 'dropdown' | 'button'
  options: {id: any, caption?: string, icon?: string}[]
}

export interface IContentElementTemplate extends IContentElement, IScoredResponse {
    templateName?: string,
    templateId?: number,
    templateVersion?: number,
    templateDescription?: string,
     // `content` stores the current inner representation of the elements as shaped by `contentConfig`, user-modified `templateConfig`, and `metaAdjusters`
    content: IContentElement[],
    templateConfigs: TemplateConfigDefRow[],
    migrationOptions: TemplateMigrationOption[],
    configs: {
        [id:string]: any,
    }
}
export interface IBlockTemplateConfig {
    templateId: number, // from db
    templateVersion: number, // from db
    // `contentConfig` determines the elements that will exist at base in the template (all elements that need to exist and be controlled by the template should be defined here, and turned off as needed). In all cases, you should be able to render this in the `content` prop of an item and you would see some of the basic elements. It is encouraged to set some of the sizing constants here (if it is not more appropriate to make them visible using the meta adjusters)
    contentConfig: IContentElement[],
    // `templateConfig`
    templateConfig: TemplateConfigRow[],
    // `metaAdjusters`
    metaAdjusters: TemplateMetaAdjust[],
    // `migrationOptions`
    migrationOptions: TemplateMigrationOption[],
}

export interface IStimTemplateConfig {
    isVerticalContainerAlwaysFullWidth?: boolean,
    isAllowBorderOnlyOnOuterVerticalContainer?: boolean,
}

export interface IMCQConfig {
    isOnlyOneCorrectOption?: boolean,
}

// Meta Adjusters
export interface TemplateMigrationOption {
    parent_id: string,
    elementRef?: string, // redacted but keeping it compatible
    elementRefs: string[],
    isExcluded: boolean,
}
export interface TemplateMigrationTransform {
    transformation: 'move' | 'split',
    pathSource: string,
    pathTarget: string,
}

export interface TemplateMeta {
    is_math_compatible: boolean
}


// Meta Adjusters
export interface TemplateMetaAdjust {
    metaAdjusterType: 'const' | 'var' | 'listener',
}
export interface TemplateMetaAdjustConst extends TemplateMetaAdjust {

}
export interface TemplateMetaAdjustListener extends TemplateMetaAdjust {

}

export const TemplateConfigTypes = [
    'el',
    'text',
    'note',
    'number',
    'math',
    'checkbox',
    'selection',
    'advanced-inline',
    'mcq',
    'mcq-table',
    'mcq-freeform',
    'image',
    'table',
    'select-table',
    'validator',
    'translation',
    'interactive-diagram',
    'canvas',
    'keyboard-input-freeform',
    'math-cloze',
    'select-paragraph',
    'select-text',
    'scientific-notation',
    'stim',
    'solution',
]

export const TemplateOptionTypes = [
    'text',
    'math',
    'image',
    'advanced-inline',
    'table'
]

// Template Config Rows are used to dynamically specificy the aspects of a template that an author can edit.
export interface TemplateConfigDefRow {
    configType: 'el' | 'label' | 'text' | 'text-list' | 'number' | 'math' | 'mcq' | 'mcq-table' | 'mcq-freeform' | 'image' | 'advanced-inline' | 'select-table' | 'table' | 'checkbox' | 'validator' | 'translation' | 'interactive-diagram' | 'canvas' | 'keyboard-input-freeform' | 'math-cloze' | 'select-paragraph' | 'select-text' | 'note' | 'scientific-notation' | 'selection' | 'stim' | 'solution'
    id?: string, // not needed for 'label'
    label: string,
    label_fr?: string,
    elementRef: string,
    optionElementType?: 'text' | 'math' | 'image' | 'advanced-inline',
    secondaryRef?: string,
    defaultWeight?: number,
    translationEN?: string,
    translationFR?: string,
    hiddenConfigs?: IAdvancedInlineHiddenConfigs | IClozeMathHiddenConfigs | IStimTemplateConfig | IMCQConfig,
    selectionData?: ISelectionData,
}

// easier to carry over some aspects that are in the def
export interface TemplateConfigRow {
    id: string, // not needed for 'label'
    elementRef: string,
}

export interface TemplateConfigRowEl extends TemplateConfigRow {
    config: IContentElement,
}

export interface TemplateConfigRowText  extends TemplateConfigRow {
    text: string,
    isTextArea: boolean,
}

export interface TemplateConfigRowTextList  extends TemplateConfigRow {
    text_els: {text: string}[],
}

export interface TemplateConfigRowMcqOptions  extends TemplateConfigRow {
    text_els: {text: string}[],
}

export interface TemplateConfigRowTargets  extends TemplateConfigRow {
    targets: GroupingTarget[],
    defaultOptions: GroupingOption[],
    displayOrder: string[],
}
export interface GroupingTarget {
    isDefaultGroup: boolean,
    label: string,
}
export interface GroupingOption {
    label: string,
}
