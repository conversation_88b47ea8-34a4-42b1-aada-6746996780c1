@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_modal.scss';
@import "../../../styles.scss";

$testNavWidth: 15em;
$toolbarBotLeftRad: 0.7em;
$toolbarWidth: 4em;
$pjToolbarWidth: 3.5em;
$nextQuestionBtnHeight: 4em;
$pageContentWidth: 42.1em;

.custom-modal {
    @extend %custom-modal;
}

body {
    -webkit-overflow-scrolling: touch;
}

.screen-warning {
    display: none;
}

.expander,
.collapser {
    display: none;
}

.info-button {
    width: $toolbarWidth;
    border-bottom-left-radius: $toolbarBotLeftRad;
}

.info-button:hover {
    cursor: pointer;
}

.question-content {
    position: relative;
    height: 100vh;
    // padding-top: 2em;
    width: 100%;
    // padding-left: $testNavWidth;
    // padding-right: 3em;
    // padding-bottom: $nextQuestionBtnHeight + 2em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    -webkit-overflow-scrolling: touch;
    // @media only screen and (max-width: 770px) {
    //     width: 100;
    //     margin: 0;
    // }
    &__overlay {
        position: absolute;
        height: 100%;
        width: 100%;
        // opacity: 0.8;
        z-index: 1;
        top: 0;
        //background: red;
        left: 50%;
        transform: translateX(-50%);
        @media only screen and (max-width: 1200px) {
            // width: 100%;
        }
    }
    &__faux-overlay {
        position: absolute;
        height: 0;
        width: 100%;
        background: grey;
        opacity: 0.1;
        z-index: 1;
        pointer-events: none;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        @media only screen and (max-width: 1200px) {
            //width: 100%;
        }
    }
    &-help {
        //position: absolute;
        //background-color: goldenrod;
        //height: 100%;
        //width: 100%;
        //opacity: 0.7;
        //top: 0;
        //z-index: 11;
    }
    .animateDown {
        -webkit-animation: topDown 1.0s forwards;
        /* for less modern browsers */
        animation: topDown 1.0s forwards;
    }
    .animateUp {
        -webkit-animation: bottomUp 0.01s forwards;
        animation: bottomUp 0.01s forwards;
    }
    @keyframes topDown {
        0% {
            height: 0%;
        }
        // 50% {
        //     height: 50%;
        // }
        100% {
            height: 100%;
            ;
        }
    }
    @keyframes bottomUp {
        0% {
            height: 100%;
        }
        // 50% {
        //     height: 50%;
        // }
        100% {
            height: 0%;
            ;
        }
    }
    .question-content-constrainer {
        // max-width: 32em;
        position: relative;
        overflow: visible;
        padding-left:1em;
        overflow-x:auto;
        width: 100%;
        // max-width: $pageContentWidth;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        height: 100vh;
        -webkit-overflow-scrolling: touch;
        @include viewport-md {
            // width: 22em;
            padding-left: 0em;
            padding-right: 0em;
        }
        .as-split-area {
            overflow-x:auto;
            -webkit-overflow-scrolling: touch;
        }
        &.no-padding {
            padding-left: 0em;
        }
        .needs-padding {
            padding-left: 13em;
            padding-right: 9em;
        }


        // .test-question {
        //     display: flex;
        //     justify-content: center;
        //     .whole-width {
        //         width: 100%;
        //     }
        //     .right-justify {
        //         display: flex;
        //         justify-content: flex-end;
        //         align-items: flex-start;
        //         position: relative;
        //         padding-right: 0.5em;
        //         padding-left: 0.5em;
        //     }
        // }
        // .split-segments {
        //     display: flex;
        //     flex-direction: column;
        //     align-items: flex-start;
        //     position: relative;
        //     padding-left: 0.5em;
        // }
    }
    .helper-tools {
        display: flex;
        flex-direction: column;
        // flex-wrap: wrap;
        align-items: flex-start;
        justify-content: flex-start;
        // margin-bottom: 2em;
        width: auto;
        overflow: auto;
        // max-width: calc(100vw - #{$testNavWidth});
        
        &>div {
            // margin-right: 2em;
        }
        iframe.calculator {
            width: 350px;
            height: 506px;
        }

        .top-bar-row {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            flex-wrap: wrap;
            width: 100%;
        }
        .top-bar {
            margin-bottom: 1em;
            max-width: $pageContentWidth;
            width: 100%;
            position: relative; 
            padding-top: 1em;
            padding-bottom: 1em;
            padding-left: 0.5em;
            padding-right: 0.5em;
            border-bottom: 1px solid #ccc;
            font-size: 1.3em;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
    .next-button-container {
        margin-top: 4em;
        padding: 1em;
        // padding-bottom: 8em;
        @include viewport-md {
            margin-top: 0.5em;
            button {
                width: 100%;
                margin-bottom: 0.5em;
            }
        }

        &.no-top-margin {
            margin-top: 0em;
        }

        .flag-button-container {
            margin-top:1.0em;
            a {
                font-weight: 400;
                img.flag { 
                    height:1em;
                    object-fit: contain;
                    margin-right: 0.5em;
                    max-width:1em;
                    position: relative;
                    top: 0.1em;
                }
            }
        }
    }
    // #questionWithReadingPassage {
    //     width: 100%;
    // }
    height: 100vh;
    width: $pageContentWidth; // this gets over-written by the grow
    padding-bottom:0em;
    display:flex;
    flex-direction: row;
    justify-content: stretch;
    flex-grow: 1;

    .helper-tools { 
        margin-bottom:0em;
        display:flex;
        flex-grow: 1;
        max-width: 100%;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .question-content-constrainer {
        overflow: hidden;
        max-width:unset;
        display:flex;
        flex-grow: 1;
        max-width: 100%;
        justify-content: flex-start;
        align-items: flex-start;
    }
    .split-view-container{
        display: flex;
        flex-grow: 1;
        width: 100%;
        overflow-y: hidden;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        flex-direction: row;
        align-items: stretch;
        .split-view-right {
            border-left:1px solid #ccc;
            // margin-left: 2em;
        }
        .split-view-left,
        .split-view-right
        {
            width:50%;
            height: 100vh;
            -webkit-overflow-scrolling: touch;
            overflow: auto;
            max-width: $pageContentWidth;
            position:relative;
            padding-bottom: 5em;

            .question-runner-padder {
                padding-left:1em;
                padding-right:1em;
            }
            &.is-hidden {
                display:none;
            }
            &.is-saving {
                opacity: 0.01;
            }
            &.is-solo {
                width:auto;
                min-width: $pageContentWidth;
                flex-shrink: 1;
                margin: auto;
                box-shadow: 0em 0em 1em rgba(0, 0, 0, 0.1);
                
                &.is-full-screen {
                    max-width: unset;
                    padding-bottom: 0;
                    width: 100%;
                }
            }
        }
    }
    
}

.banner {
    width: 100%; 
    height: 6.5em; 
    display: flex; 
    flex-direction: column; 
    justify-content: space-evenly;

    .banner-title {
        margin-left: 3em; 
        margin-top: 0em; 
        margin-right: auto; 
        width: fit-content; 
        height: fit-content
    }
    .banner-subtitle {
        margin-right: 3em; 
        margin-left: auto; 
        width: fit-content; 
        height: fit-content;
    }
}

.notepad-container {
    position:fixed;
    top:30px;
    left:100px;
    padding:1.5em;
    padding-top:0.5em;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.5);
    width:32em;
    max-width:40vw;
    max-height:80vh;
    font-weight:600;
    background-color: #2A304B;
    color:#fff;
    textarea.textarea {
        font-size: 1em;
    }
    z-index:11; 
}
.formula-container{
    position: fixed;
    height:100vh;
    width:100vw;
    background:rgba(22, 22, 22, 0.685);
    z-index:11; 
    top:0;
    left:0;
    //pointer-events: none;
    &__sheet{
        height:55em;
        width:60em;
        left:50%;
        top:10%;
        transform: translateX(-50%);
        background-color: rgb(253, 253, 253);
        position:absolute;
        z-index:12;
        &-header{
            background-color: rgb(245, 243, 243);
            width:100%;
            height:10%;
            align-items: center;
            display:flex;
            flex-direction: row;
            justify-content: space-between;
            padding:0 1em;
            font-size:2em;
            button{
                cursor:pointer;
                background:none;
                border:none;
                font-size:1em;
                &:hover{
                    color:rgb(73, 73, 73);
                }
            }
        }
    }
}
.deactivated {
    background: transparent;
    pointer-events: none;
}

.question-navbar-block {
    width: auto;
    cursor: pointer;
    &:hover {
        background-color: #f1f1f1;
    }
    &.is-active {
        background-color: #d9eaff;
    }
}

.question-navbar {
    flex: 1 1 auto; 
    min-height: 0px;
}

.question-navbar-container {
    display: flex; 
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: auto;
}

.question-content-frame {
    height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: #fff;
    overflow: hidden;
    .reading-selections-container {
        $reading-selection-width: 16em;
        position:relative;
        // min-width:$reading-selection-width;
        max-width:$reading-selection-width;
        font-size: 0.8em;
        // height:2em;
        // white-space:pre;
        .reading-selections {
            max-width:$reading-selection-width;
            // white-space:pre;
            // position:absolute;
            // right:0em;
            // top:0em;
            // min-width:$reading-selection-width;
        }
    }
    .reading-selections {
        // border: 2px solid $light-blue;
        // border-radius:1em;
        //color: $light-blue;
        // background-color: white;
        // padding: 0.5em;
        line-height: 1.1em;
        // z-index: 20;
        display: flex;
        flex-direction: column;
        user-select: none;
        i.icon {
            margin-right:0.3em;
        }
        .reading-selection-options {
            // padding:0.5em;
            padding-right:0em;
        }
        .reading-selection-option {
            margin-top: 0.5em;
            user-select: text;
        }
    }
}

.flag-image {
    &.is-hi-contrast {
        filter: invert(1);
    }
    width:1em;
    max-width:1em; 
    position:relative; 
    top:0.2em; 
    margin-right:0.5em;
}

.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5em;
}

.is-hi-contrast {
    //Rest is defined in styles.scss
    background-color: #000;
    .test-nav {
        background-color: orange;
        color: purple;
        .test-questions button.question {
            color: #000;
        }
    }
    .documents-container {
        background-color: #000 !important;
    }
    .section-progress-marker {
        background-color: blue;
    }
    .results-page{
        color: white !important;
    }
}

.img-export-button {
    margin-right: 0.2em;
    // height: 150%;
    width: 0.8em;
    object-fit: contain;
}

.toolbar-surrogate {
    width: $toolbarWidth;
    flex-grow:0;

    &.is-pj {
        width: $pjToolbarWidth;
    }
}
.test-nav {
    flex-grow:0;
    width: $testNavWidth;
    border-right: 1px solid #f1f1f1;
    display: none;
    &.is-expanded {
        display: flex;
    }
    &.is-wide {
        width: $testNavWidth*3;
    }
    &.is-onTop{
        z-index: 1;        
    }
    flex-direction: column;
    background-color: #fff;
    .support,
    .identity,
    .test-title,
    .test-section,
    .test-questions,
    .test-timing {
        padding: 0.5em;
        border-bottom: 1px solid #f1f1f1;
        flex-grow: 0;
    }
    .identity {
        .identity-name {
            font-weight: 600;
        }
        .identity-key {
            font-weight: 700;
            color: #6085B0;
            line-height: 110%;
        }
        border-bottom: 1px solid #ccc;
    }
    .test-title {
        font-size: 0.8em;
        font-weight: 400;
        line-height: 110%;
        // color: #7470cb;
        padding: 1em 0.5em;
        border-bottom: 1px dashed rgb(223, 223, 223);
    }
    .test-section {
        .section-navigation-dropdown {
            position: relative;
            .dropdown-trigger {
                position: relative;
                z-index: 1;
                background: white;
                width: 100%;
                border: 1px solid rgb(223, 223, 223);
                border-radius: 5px;
                padding: 1px 0px 1px 6px;
                cursor: pointer;
                > .icon {
                    display: inline-flex;
                }
            }
            .dropdown-content {
                position: absolute;
                width: 100%;
                padding: 0;
                border: 1px solid #dfdfdf;
                border-top: none;
                &.is-collapsed {
                    display:none;
                }
                > button {
                    cursor: pointer;
                    border-color: transparent;
                    background: transparent;
                    padding: 1px 6px;
                    &:hover {
                        color: #2996f4;
                    }
                }
            }
        }
    }
    .test-section {
        font-weight: 600;
        border-bottom: 1px solid #ccc;

        .button-text {
            // white-space: pre-wrap;
            display: flex;
            align-items: center;
            line-height: 1em;
            flex: 1 1 0;
            // font-size: 0.8em;
        }
        .button-back {
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 13em;
            background-color: rgb(167, 200, 230);
            border-radius: 0.6em;
            color: #293b82;
            font-weight: 500;
            font-size: 1.3em;
            transition: 300ms;
            padding: 1em 0.5em;
            &.is-active {
                color: #fff;
                background-color: #1740e4;;
            }
        }

        .img-container {
            width: 4em;
            min-width: 4em;
            max-width: 4em;
        }

        .img {
            object-fit: scale-down;
        }
    }
    .test-questions {
        flex-grow: 3;
        overflow: auto;
        padding: 0em;
        display: flex;
        flex-direction: column;
        
        button.question {
            @extend %clean-button;
            min-height: 2em;
            color: #0E4D81;
            padding: 0.5em 1em;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            // &:hover {
            //     background-color: #f1f1f1;
            // }
            // &.is-active {
            //     background-color: #d9eaff;
            // }
            &:hover {
                color: #1570bc;
                background-color: #f1f1f1;
            }
            &.is-active {
                color: #146bb3;
                background-color: #d9eaff;
            }
            .icon {
                margin-right: 0.5em;
                flex-shrink:0;
            }
            .coloured-flag {
                object-fit: contain;
                width: 1.5em;
                flex-shrink:0;
                &.invisible-flag {
                    opacity: 0;
                }
            }
        }
        &.hide-flags button.question {
            .coloured-flag {
                display:none;
            }
        }
    }
    .support {
        button {
            @extend %clean-button;
            img {
                width: 1em;
                margin-right: 0.6em;
            }
            font-weight: 400;
            font-size: 0.8em;
            color: #ff6767;
            &:hover {
                color: #f00;
            }
        }
    }
    .test-timing {
        button {
            @extend %clean-button;
            img {
                width: 1em;
            }
        }

        &.back-to-map {
            color: #0E4D81;
            text-align: center;
            display: flex;
            justify-content: space-around;
            align-items: center;
            font-weight: normal;
            &:hover {
                cursor: pointer;
                color: #1570bc;
                background-color: #f1f1f1;
            }
        }
    }
}

button.toolbar-icon {
    @extend %clean-button;
    padding: 0.5em;
    // padding: 0em;
    // margin-bottom: 0.5em;
    user-select: none;
    transition: opacity 300ms;
    color: white;
    &:hover {
        opacity: 0.5;
    }
    &.is-active {
        background-color: #9a740c;
        &.is-pj {
            background-color: lightgreen;
            &.is-g6 {
                background-color: lightskyblue;
            }
        }
        // background-color: #000;
    }
    &.is-inactive {
        background-color: rgb(92, 90, 90);
        opacity: 0.3;
        cursor: not-allowed;
    }
    &.tool-help {
        font-size: 1em;
        color: #fff;
        &.is-activated {
            color: rgb(255, 192, 19);
        }
    }

    .toolbar-icon-icon {
        color: white;
        font-size: 2em;

        @media only screen and (max-width: 770px), screen and (max-height: 650px){
            font-size: 1.5em;
        }

    }
    @media only screen and (max-width: 770px), screen and (max-height: 650px){
        padding-top: 0;
        padding-bottom: 0;
        margin: 0.3em;
    }

    @media only screen and (max-width: 770px), screen and (max-height: 750px) and (orientation:landscape){
        padding-top: 0;
        padding-bottom: 0;
        margin: 0.3em;
    }
}

.points-indicator {
    margin-left: 0.5em;
    font-size: 0.8em;
}

.is-green {
    color: green
}

.is-red {
    color: red;
}

.tts-button-div {
    position: fixed;
    top: 1em;
    left: 0em;
    width: 3em;
    height: 3em;
    background-color: #2A304B;
    border-top-right-radius: $toolbarBotLeftRad;
    border-bottom-right-radius: $toolbarBotLeftRad;
    z-index: 100;
    box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;

    &.pj-tts-button-div {
        background-color: white;
        right: 0em;
        left: auto;
        border-top-left-radius: $toolbarBotLeftRad;
        border-bottom-left-radius: $toolbarBotLeftRad;
        border-top-right-radius: 0em;
        border-bottom-right-radius: 0em;
        width: 4em;
        height: 4em;
    }
}


.banner-right-overlay {
    position: absolute;
    bottom: 0em;
    right: 0em;
    left: auto;
    top: 0em;
    height: 6.5em; //same as banner height 
    object-fit: cover;
}


.toolbar {
    position: fixed;
    top: 1em;
    bottom: 7em;
    right: 0em;
    width: $toolbarWidth;
    background-color: #2A304B;
    border-top-left-radius: 0.7em;
    border-bottom-left-radius: $toolbarBotLeftRad;
    box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    overflow: hidden;
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    //overflow-y: scroll; 
    .tools-top,
    .tools-bottom {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0em;
        // padding:0.5em;
    }
    .tools-top {
        padding-top: 1em;
    }
    .toolbar-icon-img-doc {
        width: 100%; 
        min-width: $toolbarWidth - 1.5em;
    }
    .toolbar-icon-img-cal {
        height:50%; 
        min-width: $toolbarWidth - 1.5em;
    }
    @media only screen and (max-width: 770px), screen and(max-height :650px){
        bottom: unset;

    }
}

.pj-toolbar{
    width: $pjToolbarWidth;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 50%, #7dd5f5 50%);
    box-shadow: unset;
    min-height: 100vh;
    top: 0em;
    bottom: 0em;
}

.g6-toolbar{
    background: linear-gradient(90deg, rgba(0,0,0,0) 50%, #12365A 50%);
}

.toolbar::-webkit-scrollbar {
    display: none;
}

.is-hidden {
    display: none;
}

.section-progress-marker {
    position: absolute;
    top: 0px;
    bottom: 0px;
    background-color: #333;
    left: 45%;
    width: 3px;
}

.save-overlay {
    position: fixed;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    pointer-events: none;
    transition: 600ms;
    opacity: 0;
    &.is-activated {
        opacity: 1;
        pointer-events: unset;
    }
    .overlay-bg {
        background-color: rgba(255, 255, 255, 0.7);
        ;
        position: absolute;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
    }
    .overlay-text {
        position: absolute;
        top: 50%;
        bottom: 1em;
        left: 0em;
        right: 0em;
        text-align: center;
    }
}

.info-overlay {
    position: fixed;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    ;
    .info-box {
        padding: 3em;
        box-shadow: 0.2em 0em 1em rgba(0, 0, 0, 0.2);
        background-color: #fff;
        max-width: 60vw;
        &.is-maximized {
            padding: 1.5em;
            width: 60vw;
            height: 90vh;
            overflow: auto;
        }
    }
}

.chat-overlay {
    position: fixed;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.4);
    ;
    .info-box {
        padding: 3em;
        box-shadow: 0.2em 0em 1em rgba(0, 0, 0, 0.2);
        background-color: #fff;
        max-width: 80vw;
        width: 80vw;
        max-height: 80vh;
        height: 80vh;
    }
}

.printed-test-form {
    .section-block {
        padding-top: 2em;
        page-break-before: always;
        page-break-after: always;
        .section-marker {
            border-top: 2px solid #666;
            margin-bottom: 1px;
        }
    }
    .question-block {
        page-break-inside: avoid;
        font-size: 1em;
        .question-block-label {
            border-top: 0.3em solid #666; 
            padding:0.2em 0em;  
            font-weight:600; 
            margin-top:1em;
        }
    }
}

.line-reader-container {
    position: absolute;
    left: 0em;
    top: 2em;
    z-index: 10;
    pointer-events: none;
}

.line-reader {
    $top-height: 8em;
    $win-height: 2em;
    $win-width: 2em;
    $bott-height: 16em;
    // width: 40em;
    height: $top-height + $win-height + $bott-height;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.5);
    position: relative;
    pointer-events: none;
    transform: translate3d(100px, 0, 0);
    &:active {
        box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.6);
    }
    >div {
        background-color: rgba(220, 220, 220, 0.98);
        position: absolute;
    }
    .line-reader-top {
        top: 0em;
        left: 0em;
        right: 0em;
        height: $top-height;
        pointer-events: all;
    }
    .line-reader-left {
        top: $top-height;
        height: $win-height;
        width: $win-width;
        left: 0em;
        pointer-events: all;
    }
    .line-reader-right {
        top: $top-height;
        height: $win-height;
        width: $win-width;
        right: 0em;
        pointer-events: all;
        ;
    }
    .line-reader-bottom {
        top: $top-height + $win-height;
        left: 0em;
        right: 0em;
        bottom: 0em;
        pointer-events: all;
    }
    .resize-line-reader{
        position: absolute;
        right: 0em;
        top:0em;
        bottom: 0em;
        pointer-events: all !important;
        border-style: dashed;
        border-width: 3px;
        border-color: transparent #A9A9A9 transparent transparent;
        box-shadow: none;
        background-color: transparent !important;
        &:hover, &:active {
            background-color: transparent !important;
            box-shadow: none;
            cursor: ew-resize;
        }

        .resizer-tool {
            position: absolute;
            bottom: 7px;
            right: 12px;
            > .line {
                position: absolute;
                transform: rotate(-45deg);
                width: 10px;
                background: #A9A9A9;
                border: 1px solid #7d7d7d;
                &:first-child {
                    width: 20px;
                    bottom: 3px;
                    right: -10px;
                }
                &:last-child {
                    bottom: 0px;
                    width: 15px;
                    right: -11px;
                }
            }
        }
    }
}

@include viewport-md {
    .collapser {
        display: flex;
        padding-left: 0.75em;
        padding-right: 0.75em;
        margin-right: 0;
    }
    .expander {
        display: flex;
        position: fixed;
        padding: 1em;

        .button {
            padding-left: 0.75em;
            padding-right: 0.75em;
            margin-right: 0;
        }
    }
    // .test-nav,
    .toolbar {
        display: none;
        &.is-expanded {
            display: flex;
        }
    }
    .question-content {
        padding-left: 0em;
        padding-right: 0em;
    }
}

.navigation-panel-container {
    position: fixed;
    bottom: 0em;
    right: 0em;
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    z-index: 100;

    .custom-nav-button-container {
        margin-bottom: 1.5em;
        margin-right: 4em;
        display: flex;
    }
}
.reading-text-controls {
    padding: 0.5em;
    display: flex;
    flex-direction: row;
    margin-right: 1em;
    background-color: #2A304B;
    border-top-left-radius: 0.7em;
    border-top-right-radius: 0.7em;

    .reading-text-btn {
        @media only screen and (max-width: 770px), screen and(max-height :650px){
            font-size: 0.8rem;
        }
    }

    @media only screen and (max-width: 770px), screen and(max-height :650px){
        font-size: 0.8em;
    }
    &.pj-reading-controls{
        background-color: #7dd5f5;
    }
    &.g6-reading-controls{
        background-color:#12365A;
    }
}

.drawing-panel {
    display: flex;
    gap: 0.5em;
}
.navigation-panel {
    // position: absolute;
    // right: -4em;
    // bottom: -1.5em;
    width: 10em;
    height: 5em;
    background-color:  #2A304B;
    border-top-left-radius: 0.7em;
    box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    &.is-wider {
        width: 12em;
    }
    .navigation-icon {
        width: 45%;
        height: 86%;
        background-color: #1e2235;
        color: white;
        font-size: 1.2em;
        padding: 0em;

        &.left-nav-button {
            border-top-left-radius: 0.5em;
        }

        .nav-button-div {
            display: flex;
            width: 100%;
            height: 100%;
            flex-direction: column;
            align-content: center;
            align-items: center;
            justify-content: center;
        }

        @media only screen and (max-width: 770px), screen and(max-height :650px){
            font-size: 1em;
        }
    }

    @media only screen and (max-width: 770px), screen and(max-height :650px){
        font-size: 0.8em;
    }
}

.annotation-tool-close {
    cursor: pointer;
    font-weight:bold; 
    height: 3.25em; 
    width: 10em;
    margin-right:1em; 
    background:#2A304B; 
    border-top-left-radius: 1.3em; 
    border-top-right-radius: 1.3em; 
    padding:0 1em; 
    color:white; 
    font-size:1.2em;

    @media only screen and (max-width: 770px), screen and(max-height :650px){
        font-size: 1em;
    }
}
.pj-annotation-tool-close{
    background-color: #7dd5f5;
    color: black;
    border-color: transparent;
}
.g6-annotation-tool-close{
    background-color:#12365A;
    border-color: transparent;
    color: white;
}

.results-page {
    align-items: center;
    color: black;
    overflow-y: auto;
    padding: 2em;
    box-shadow: 0em 0em 1em rgba(0, 0, 0, 0.1);
}

.results-intro {
    background: linear-gradient(180deg, #003366 3.52%, rgba(0, 51, 102, 0.99664) 10.94%, rgba(0, 51, 102, 0.992944) 15.23%, rgba(0, 51, 102, 0.987904) 24.8%, rgba(0, 51, 102, 0.976506) 31.05%, rgba(0, 51, 102, 0.907786) 45.7%, rgba(0, 51, 102, 0.881592) 53.12%, rgba(0, 51, 102, 0.86021) 60.74%, rgba(0, 51, 102, 0.837891) 69.53%, rgba(0, 51, 102, 0.793945) 84.37%, rgba(0, 51, 102, 0.756658) 94.14%, rgba(0, 51, 102, 0.683416) 98.24%, rgba(0, 51, 102, 0.603516) 98.83%, rgba(0, 51, 102, 0.646129) 99.02%, rgba(0, 51, 102, 0.269807) 100%, rgba(0, 51, 102, 0.712713) 100%);
    .results-page {
        color: white;
        font-size: 1.6em;
        text-align: center;
        line-height: 1.5em;
        box-shadow: 0em 0em 1em rgba(0, 0, 0, 0.3);
    }
}


.results-summary {
    line-height: 1.1em;
    font-size: 1.3em;
}

.results-instructions {
    line-height: 1.1em;
    font-size: 1.3em;
}

.centered-div {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    width: 30em;
    height: 40em;
    direction: ltr;
}
.results-intro-div {
    height: 80vh;
}


.custom-result-page {
    .page-title{
        font-size: 2em ;
        font-weight: bold; 
    }    
}

.results-export-button {
    cursor: pointer;
    border: 2px solid #003366;
    border-radius: 15px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    background: white;
    padding-left: 0.3em;
    padding-right: 0.4em;
    height: 1.6em;
    color: #003366;
    font-size: 1.5em;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

// For safari 13.1 and 14 (Descrepancy between safari 13.1 & 14)
// @media not all and (min-resolution:.001dpcm) { 
//     @supports (-webkit-appearance:none) {
//          .results-export-button {
//             padding-top: 0 !important;
//             padding-bottom: 0 !important;
//          }
//     }
// }

.score-legend-box {
    position: fixed;
    top: 0.85em;
    right: 5.0em;
    width: 18.0em;
    color: black;
    z-index: 2;

    .score-box {
        font-size: 1.5em;
        border-radius: 10px;
        height: 2.0em;
        width: 100%;
        display: flex;
        background: white;
        border: 1px solid black;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-left: 0.5em;
        padding-right: 0.5em;
        font-weight: 600;
        margin-bottom: 0.5em;
    }
}

.results-table {
    font-weight: 800;
    color: black;
    font-size: 1.25em;
    th {
        color: black;
        text-align: center !important;
        border: 1px solid #E5E5E5;
        background: rgba(0, 51, 102, 0.42)
    }
    td {
        border: 1px solid #E5E5E5;
        border-right: 1px solid rgba(0,0,0,0);
        background: rgba(196, 196, 196, 0.3);
        &:nth-child(2) {
            text-align: right;
        }

        &:nth-child(3) {
            text-align: right;
            font-weight: 600;
            border-right: 1px solid #E5E5E5;
        }
    }
}

.is-right-align-thumbnail {
    display: flex;
    justify-content: flex-end;
}

@media print {
    .pagebreak { page-break-before: always; }
}
.test-time {
    font-size:1.2em;
    margin-top: 0.5em
}
#left-split-container {
    position:relative; 
    margin-bottom: 8em;
}

.info-button-pj{
    height: 3em;
    width: 3em;
    border-radius: 5em;
    border: solid 1px black;
    background-color: #0091ff;
    display: flex;
    justify-content: center;
    align-items: center;
    @media only screen and (max-width: 770px), screen and(max-height :800px){
        width: 3em;
    }
}

.pj-toolbar-icon{
    min-width: $pjToolbarWidth - 0.25em;
    @media only screen and (max-width: 770px), screen and(max-height :800px){
        min-width: $pjToolbarWidth - 1em;
        // border-radius: 5em;
        // border: solid .5px black;
    }
}

.filled-icon{
    width: 2em;
}

.question-header{
    width: 100%;
    margin-top: .5em;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .pj-eqao-logo{
        width: 7em;
        margin-left: .4em;
    }
    .title{
        font-size: 2em;
        font-weight: bold;
    }
    .right-side-text{
        display: inline-block;
        color: #3f72c0;
        font-size: 2em;
        margin-right: .4em;
        text-decoration: underline;
    }
    .hide-text{
        visibility: hidden;
    }
    .show-text{
        visibility: visible;
    }
}
.amble-icon{
    min-width: 3em;
    min-height: 3em;
}

.debug-button {
    position: fixed;
    &.is-embedded {
        position: absolute;
    }
    top: 0px;
    right: 0px;
    width: 1em;
    height: 1em;
    cursor: not-allowed;
    // z-index: 1000;
    &:hover {
        background-color: rgba(255, 255, 0, 0.507);
    }
}
.debug-box {
    z-index: 1000;
    position: absolute;
    top:0%;
    left:50%;
    height: 1em;
    .debug-content {
        font-family:'Courier New', Courier, monospace;
        background-color: rgba(0,0,0,0.8);
        max-width: 60vw;
        
        display: flex;    
        flex-direction: row;
        justify-content: space-between;
        .panel-name { color: #fff; }
        .debug-current {color: cyan; padding:0.5em;}
        .debug-last {color: yellow;  padding:0.5em;}
        .debug-drag-bar {
            height:1em; 
            background-color:  grey;
            cursor: move;
        }
    }
}

.fps-frame {
    margin-left: 0.5em;
    margin-top: 0.5em;
    margin-bottom:  0.5em;
    font-size: 15px;
    border-radius: 2px;
    width: 9.5em;
    padding: 0.5em;
    border-bottom: 1px solid #f1f1f1;
}

.fps-good {
    background-color: #98DE75;
}

.fps-adequate {
    background-color: #DBDE75;
}

.fps-poor {
    background-color: #DE7575;
}

.color-square {
    width: 1.4em;
    height: 1.4em;
    border: 1px solid rgb(46, 46, 46);
    border-radius: 100%;
    box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    &:hover{
        opacity:0.8;
    }
}

.color-palette {
    display: flex;
    gap: 1em;
    align-items: center;
    justify-content: center;
}