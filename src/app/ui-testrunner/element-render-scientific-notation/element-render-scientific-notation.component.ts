import { AfterViewInit, Component, ElementRef, Input, OnChanges, OnInit, QueryList, SimpleChanges, ViewChild, ViewChildren } from '@angular/core';
import { OverlayModule } from '@angular/cdk/overlay';
import { FormControl, Validators } from '@angular/forms';
import { getElementWeight, QuestionState, ScoringTypes } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { IContentElementScientificNotation, IEntryStateInputScientificNotation, IScientificNotationAnswer } from './model';
import * as _ from 'lodash';
import { PubSubTypes } from '../element-render-frame/pubsub/types';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { isSupportedVersion } from 'src/app/ui-item-maker/services/util';

const checkBlankValue = (val:any) => (val === null || val === undefined || val === '');
@Component({
  selector: 'element-render-scientific-notation',
  templateUrl: './element-render-scientific-notation.component.html',
  styleUrls: ['./element-render-scientific-notation.component.scss']
})
export class ElementRenderScientificNotationComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() element: IContentElementScientificNotation;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState;
  @Input() changeCounter:number;
  @Input() questionPubSub?: QuestionPubSub;
  
  @ViewChild('exponentDropdownTrigger', { read: ElementRef }) exponentDropdownTrigger: ElementRef;
  @ViewChild('exponentDropdownFirstButton', { static: false }) exponentDropdownFirstButton: ElementRef;
  @ViewChild('coefficientDropdownTrigger', { read: ElementRef }) coefficientDropdownTrigger: ElementRef;
  @ViewChild('coefficientDropdownFirstButton', { static: false }) coefficientDropdownFirstButton: ElementRef;
  
  wholeDigitInput: FormControl[] = [];
  fractionalDigitInput: FormControl[] = [];
  exponentDigitInput: FormControl[] = [];
  coefficientSignSelected: '+'|'-'|undefined = undefined;
  exponentSignSelected: '+'|'-'|undefined = undefined;
  isCoefficientSignDropdownOpen: boolean = false;
  isExponentSignDropdownOpen: boolean = false;
  
  isStarted: boolean = false;
  isResponded: boolean = false;
  
  @ViewChildren('digitInput') digitInputElements: QueryList<ElementRef>;

  constructor(
    private profile: StyleprofileService,
  ) { }

  ngOnInit(): void {
    this.initFormControls();
    this.tryLoadState();
    this.updateState();
  }
  
  ngAfterViewInit(): void {
    const firstInput = this.digitInputElements.toArray()[0];
    firstInput?.nativeElement?.focus?.();
  }
  
  ngOnChanges(changes: SimpleChanges) {
    if (changes.changeCounter) {
      this.initFormControls();
      this.tryLoadState();
    }
  }
  
  initFormControls(){
    const wholeLength = this.element.whole?.length || 1;
    const fractionalLength = this.element.fractional?.length || 1;
    const exponentLength = this.element.exponent?.length || 1;
    this.wholeDigitInput = [];
    
    for (let i = 0; i < wholeLength; i++) {
      const fc = new FormControl('');
      fc.valueChanges.subscribe(val => { this.updateState(); })
      this.wholeDigitInput.push(fc);
    }
    this.fractionalDigitInput = [];
    for (let i = 0; i < fractionalLength; i++) {
      const fc = new FormControl('');
      fc.valueChanges.subscribe(val => { this.updateState(); })
      this.fractionalDigitInput.push(fc);
    }
    this.exponentDigitInput = [];
    for (let i = 0; i < exponentLength; i++) {
      const fc = new FormControl('');
      fc.valueChanges.subscribe(val => { this.updateState(); })
      this.exponentDigitInput.push(fc);
    }
  }
  onNumericInputFocusIn(a:any){
  }
  onNumericInputFocusOut(){
  }
  onDigitInputKeyDown(event: KeyboardEvent, listIndex: number, type: 'whole' | 'fractional' | 'exponent'){
    const index = this.resolveIndex(listIndex, type);
    const isNumeric = /^[0-9]$/.test(event.key);
    const input = event.target as HTMLInputElement;
    
    if (event.key  == 'Tab'){
      return;
    }
    
    if (this.isLocked) return;
    event.preventDefault();
    if (isNumeric) {
      // replace the value of the input with the key pressed
      input.value = event.key;
      input.dispatchEvent(new Event('input'));
      this.gotoNextInput(index)
    } else if (event.key == 'ArrowLeft') {
      this.gotoPrevInput(index);
    } else if (event.key == 'ArrowRight') {
      this.gotoNextInput(index);
    } else if (event.key == 'Backspace') {
      // if there is no input, delete the previous input
      if (input.value === ''){
        if (index === 0) return;
        const prevInput = this.digitInputElements.toArray()[index - 1].nativeElement;
        prevInput.value = '';
        prevInput.dispatchEvent(new Event('input'));
        this.gotoPrevInput(index);
      } else {
        input.value = '';
        input.dispatchEvent(new Event('input'));
      }
    } else if (event.key == 'Delete') {
      // delete the value
      input.value = '';
      input.dispatchEvent(new Event('input'));
    } else {
      return;
    }
  }
  resolveIndex(listIndex: number, type: 'whole' | 'fractional' | 'exponent'){
    const wholeLength = this.element.isFixedWhole ? 0 : (this.wholeDigitInput.length || 0);
    const fractionalLength = this.element.isFixedFractional ? 0 : (this.fractionalDigitInput.length || 0);
    switch (type) {
      case 'whole':
        return listIndex;
      case 'fractional':
        return listIndex + wholeLength;
      case 'exponent':
        return listIndex + wholeLength + fractionalLength;
      default:
        return listIndex;
    }
  }
  
  gotoNextInput(index: number){
    this.gotoInput(index + 1);
  }
  gotoPrevInput(index: number){
    this.gotoInput(index - 1);
  }
  gotoInput(index: number){
    if (index < 0 || index >= this.digitInputElements.length) return;
    const input = this.digitInputElements.toArray()[index];
    input.nativeElement.focus();
  }
  
  getValueFromInputList(list: FormControl[]): string {
    const values = list.map(fc => Boolean(fc.value) ? fc.value : '(EMPTY)');
    return values.join('');
  }
 
  isCorrect(correctAnswer: IScientificNotationAnswer, userInput: IScientificNotationAnswer){
    if (correctAnswer.whole != userInput.whole) return false;
    if (correctAnswer.fractional != userInput.fractional) return false;
    if (correctAnswer.exponent != userInput.exponent) return false;
    if (Boolean(correctAnswer.isNegativeCoefficient) != userInput.isNegativeCoefficient) return false;
    if (Boolean(correctAnswer.isNegativeExponent) != userInput.isNegativeExponent) return false;
    return true;
  }
  
  // bool (isNegative)
  signToBool(sign: '+'|'-'): boolean|undefined{
    if (sign == '-') return true;
    else if (sign == '+') return false;
    return undefined;
  }
  boolToSign(bool: boolean|undefined): '+'|'-'|undefined{
    if (bool === undefined) return undefined;
    return bool ? '-' : '+';
  }
  
  getEntryState() {
    const isFixedWhole = this.element.isFixedWhole;
    const isFixedFractional = this.element.isFixedFractional;
    const isFixedExponent = this.element.isFixedExponent;
    const isFixedCoefficientSign = this.element.isFixedCoefficientSign;
    const isFixedExponentSign = this.element.isFixedExponentSign || this.element.isFixedExponent;
    
    let isFilled = true;
    {
      if (!isFixedWhole){
        for (let digitInput of this.wholeDigitInput){
          if (checkBlankValue(digitInput.value)){ isFilled = false; }
        }
      }
      if (!isFixedFractional){
        for (let digitInput of this.fractionalDigitInput){
          if (checkBlankValue(digitInput.value)){ isFilled = false; }
        }
      }
      if (!isFixedExponent){
        for (let digitInput of this.exponentDigitInput){
          if (checkBlankValue(digitInput.value)){ isFilled = false; }
        }
      }
      if (!isFixedCoefficientSign && !this.coefficientSignSelected) isFilled = false;
      if (!isFixedExponentSign && !this.exponentSignSelected) isFilled = false;
    }
    
    if (isFilled) this.isStarted = true
    if (this.isStarted) this.isResponded = true
    
    const wholeStr = isFixedWhole ? this.element.whole : this.getValueFromInputList(this.wholeDigitInput);
    const fractionalStr = isFixedFractional ? this.element.fractional : this.getValueFromInputList(this.fractionalDigitInput);
    const exponentStr = isFixedExponent ? this.element.exponent : this.getValueFromInputList(this.exponentDigitInput);
    
    const isNegativeCoefficientInput = isFixedCoefficientSign ? Boolean(this.element.isNegativeCoefficient) : this.signToBool(this.coefficientSignSelected);
    const isNegativeExponentInput = isFixedExponentSign ? Boolean(this.element.isNegativeExponent) : this.signToBool(this.exponentSignSelected);
    
    let isCorrect: boolean;
    {
      const userInput: IScientificNotationAnswer = {
        whole: wholeStr, 
        fractional: fractionalStr, 
        exponent: exponentStr,
        isNegativeCoefficient: isNegativeCoefficientInput,
        isNegativeExponent: isNegativeExponentInput,
      }
      if (this.element.isAllowMultipleAnswers){
        isCorrect = false;
        const answerSets = [this.element, ...this.element.answerSets];
        for (let answerSet of answerSets){
          if (this.isCorrect(answerSet, userInput)){
            isCorrect = true;
            break;
          }
        }
      } else {
        isCorrect = this.isCorrect(this.element, userInput)
      }
    }
    
    const coefficientSignStr = this.boolToSign(isNegativeCoefficientInput) ?? '(EMPTY)';
    const exponentSignStr = this.boolToSign(isNegativeExponentInput) ?? '(EMPTY)';
    // if sign is fixed and is '+', replace it with ''
    const coefficientSignNormalized = isFixedCoefficientSign && coefficientSignStr == '+' ? '' : coefficientSignStr;
    const exponentSignNormalized = isFixedExponentSign && exponentSignStr == '+' ? '' : exponentSignStr;
    
    const expression = `${coefficientSignNormalized}${wholeStr}.${fractionalStr}x10^${exponentSignNormalized}${exponentStr}`;
    const weight = getElementWeight(this.element);
  
    return <IEntryStateInputScientificNotation>{
      type: 'input-scientific-notation',
      isCorrect,
      isStarted: this.isStarted,
      isFilled,
      isResponded: this.isResponded,
      whole: wholeStr,
      fractional: fractionalStr,
      exponent: exponentStr,
      coefficientSign: coefficientSignStr,
      exponentSign: exponentSignStr,
      expression,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.REVIEW, 
    }
  }
  
  updateState = _.throttle(() => {
    if (this.isLocked){ return; }
    // if (!this.questionState || !this.questionState[this.element.entryId]){ return; }
    this.questionState[this.element.entryId] = this.getEntryState();
    if(this.questionPubSub){
      this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
    }
  }, 500);
  
  tryLoadState(){
    if (this.questionState?.[this.element.entryId]) {
      this.loadState(this.questionState[this.element.entryId])
    }
  }
  loadState(state: IEntryStateInputScientificNotation){
    try {
      // replace '(EMPTY)' with '#'
      const wholeStr = state.whole.replace(/(\(EMPTY\))/g, '#');
      const fractionalStr = state.fractional.replace(/(\(EMPTY\))/g, '#');
      const exponentStr = state.exponent.replace(/(\(EMPTY\))/g, '#');
      if (!this.element.isFixedWhole){
        for (let i = 0; i < this.wholeDigitInput.length; i++) {
          if (wholeStr[i] == '#') continue;
          this.wholeDigitInput[i].setValue(wholeStr[i], {emitEvent: false});
        }
      }
      if (!this.element.isFixedFractional){
        for (let i = 0; i < this.fractionalDigitInput.length; i++) {
          if (fractionalStr[i] == '#') continue;
          this.fractionalDigitInput[i].setValue(fractionalStr[i], {emitEvent: false});
        }
      }
      if (!this.element.isFixedExponent){
        for (let i = 0; i < this.exponentDigitInput.length; i++) {
          if (exponentStr[i] == '#') continue;
          this.exponentDigitInput[i].setValue(exponentStr[i], {emitEvent: false});
        }
      }
      
      if (!this.element.isFixedCoefficientSign) {
        if (state.coefficientSign == '+') this.coefficientSignSelected = '+';
        else if (state.coefficientSign == '-') this.coefficientSignSelected = '-';
      }
      if (!this.element.isFixedExponentSign) {
        if (state.exponentSign == '+') this.exponentSignSelected = '+';
        else if (state.exponentSign == '-') this.exponentSignSelected = '-';
      }
      
      this.isStarted = state.isStarted;
      this.isResponded = state.isResponded;
    } catch(err) {
      console.warn("WARNING: Error loading scientific notation state", err);
    }
    
  }
  
  toggleCoefficientSignDropdown(){
    this.isCoefficientSignDropdownOpen = !this.isCoefficientSignDropdownOpen;
    if (this.isCoefficientSignDropdownOpen){
      requestAnimationFrame(() => {
        this.coefficientDropdownFirstButton.nativeElement.focus();
      });
    }
  }
  toggleExponentSignDropdown(value?: boolean){
    this.isExponentSignDropdownOpen = value ?? !this.isExponentSignDropdownOpen;
    if (this.isExponentSignDropdownOpen){
      requestAnimationFrame(() => {
        this.exponentDropdownFirstButton.nativeElement.focus();
      });
    }
  }
  selectCoefficientSign(sign: '+'|'-'){
    this.coefficientSignSelected = sign;
    this.toggleCoefficientSignDropdown();
    requestAnimationFrame(() => {
        this.coefficientDropdownTrigger.nativeElement.focus();
    });
    this.updateState();
  }
  selectExponentSign(sign: '+'|'-'){
    this.exponentSignSelected = sign;
    this.toggleExponentSignDropdown(false);
    requestAnimationFrame(() => {
      this.exponentDropdownTrigger.nativeElement.focus();
    });
    this.updateState();
  }
  getFontSizeForInput() {
    return this.profile.getInputFontSize();
  }
  
  isSupportCompactInputStyle() {
    const isBigFontSize = parseFloat(this.getFontSizeForInput()) >= 1.0;
    return isSupportedVersion(1, this.element) && isBigFontSize;
  }
  
  getExponentFontSizeFactor() {
    return this.isSupportCompactInputStyle() ? 0.8 : 1;
  }
  
}
