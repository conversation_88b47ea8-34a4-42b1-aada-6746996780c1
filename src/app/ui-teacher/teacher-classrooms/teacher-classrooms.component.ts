import * as _ from 'lodash';
import { Component, OnInit, OnDestroy, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { SidepanelService } from '../../core/sidepanel.service';
import { UserSiteContextService } from '../../core/usersitecontext.service';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatpanelService } from '../../core/chatpanel.service';
import { Subscription } from 'rxjs';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { AssessmentService } from '../../core/assessment.service';
import { randInt } from '../../ui-testadmin/demo-data.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { IClassroomArchivedTeachers, IUserInfo,IClassroomSlot,IClassroom,ISessionBegin, ASSESSMENT } from '../data/types';
import { ProfResponsibilityTeacherAgreement, TestSessionStatus, TestSessionType } from './data/types';
import { IAgreementConfirmConfig, IAgreementConfirmData } from 'src/app/ui-schooladmin/view-schooladmin-dashboard/data/types';
import { generateAccessCode, G9DemoDataService, hyphenateAccessCode } from '../../ui-schooladmin/g9-demo-data.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import * as moment from 'moment-timezone';
import { AuthService } from '../../api/auth.service';
import { ITypeSlug, SchoolTeacherAdminQuestionnaireService } from '../../ui-questionnaire/school-teacher-admin-questionnaire.service';
import { timeStamp } from 'console';
import { ClassroomModal } from 'src/app/ui-schooladmin/sa-classrooms/sa-classrooms.component';
import { NtfMessageComponent } from 'src/app/ui-notifications/ntf-message/ntf-message.component';
import { mtz, etz, tzLangAbbrs } from '../../core/util/moment';
import { sortBy } from './../../time/sort.pipe';

enum Overlays {
  NEW_CLASSROOM = 'NEW_CLASSROOM',
  NEW_ASSESSMENT = 'NEW_ASSESSMENT',
  ACTIVE_ASSESSMENT = 'ACTIVE_ASSESSMENT',
  ASSESSMENT_SCHEDULER = 'ASSESSMENT_SCHEDULER',
  ADD_REMOVE_INVIGILATORS = 'ADD_REMOVE_INVIGILATORS'
}

@Component({
  selector: 'teacher-classrooms',
  templateUrl: './teacher-classrooms.component.html',
  styleUrls: ['./teacher-classrooms.component.scss']
})
export class TeacherClassroomsComponent implements OnInit {

  @ViewChild('selectedClassroomPanel') selectedClassroomPanel:ElementRef;
  // isAssessmentScheduled: boolean = false;
  config: any;
  isActiveLoaded: boolean = false;
  isSessionsCompleted: string[];
  sessionDetails: any;
  private classroomServiceSub: Subscription = null;

  constructor(
    public whitelabelService: WhitelabelService,
    private route: ActivatedRoute,
    private router: Router,
    private sidePanel: SidepanelService,
    public lang: LangService,
    private classroomService: ClassroomsService,
    private userSiteContext: UserSiteContextService,
    public  loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private assessmentService: AssessmentService,
    private g9demoService: G9DemoDataService,
    private auth: AuthService,
    private routes: RoutesService,
    private taQuestionnaire: SchoolTeacherAdminQuestionnaireService,
    private changeDetector: ChangeDetectorRef,
  ) { }

  isSecreteUser
  activeClassroom:IClassroom;
  classroomId:string;
  classroomSlots:IClassroomSlot[]=[];
  classrooms:IClassroom[] = [];
  testWindowSlots: {tw_id: number, name: string, sortOrder: string, classes: IClassroomSlot[]}[]=[]
  archivedClassroomSlots:IClassroomSlot[];
  classroomNameLoaders:Map<string, any> = new Map();
  newClassName = new FormControl(null);
  classroomNameForm = new FormGroup({name: new FormControl('')});
  isEditingClassroomName: boolean;
  isSavingClassroomName: boolean;
  isAddingNewClassroom;
  isOnlineStatusShow:boolean = false;
  isManagingClassrooms:boolean;
  isClickBufferActive:boolean;
  activeTeachers:IUserInfo[] = [];
  OVERLAYS = Overlays;
  MAX_STUDENT_DISPLAY = 7;
  MAX_TEACHER_DISPLAY = 5;
  inputValue = this.lang.tra('g9_name_to_show')
  dummyTeacherlist = [ ]
  pendingClassroomSelection;
  activeSubSessions;
  completedSubSessions;
  subSessions;
  breadcrumb = [];

  isPrivateSchool:boolean = false;
  routeSub:Subscription;
  currentQueryParams;
  // Questionnaire 
  questionnaireSessionId: number
  questionnaireDueOn: string
  haveQuestionnaireSession = false

  private classroomClassCodeCache:Map<string, string> = new Map();
  private signinSub:Subscription;
  schoolName:string;

  classInvigilators = [];
  loadingSchoolTeachers = false;
  schoolTeachers = [] ;
  schl_group_id;

  public readonly HELPFUL_LINKS = [
    {slug: 'teacher_live_webinar', href: 'invig_live_webinar_link', icon: 'fa-video'},
    {slug: 'teacher_user_guides', href: 'invig_user_guides_link', icon: 'fa-book'},
    {slug: 'teacher_results', href: 'invig_results_link', icon: 'fa-pencil-alt'},
    {slug: 'title_feedback', icon: 'fa-headset',  onClick:() => this.loginGuard.supportReqActivate()},
  ]
  isPastWindowsShown:boolean = false;
  TestSessionType = TestSessionType;
  TestSessionStatus = TestSessionStatus;
  sampleAssessment:any
  operationalAssessment:any
  assessmentSlug:string

  ngOnInit() {
    this.sidePanel.activate();
    this.loginGuard.activate();
    this.loginGuard.runHealthCheck();
    this.activeClassroom = null;
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
    });
    this.schl_group_id = this.route.snapshot.queryParams['school']

    if(this.schl_group_id){
      this.classroomService.reloadSchool()
    } 
    if(this.classroomServiceSub) {
      this.classroomServiceSub.unsubscribe();
    }

    this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
      if (data){
        this.classrooms = this.classroomService.classrooms;
        // console.log('isFreeStart', this.classroomService.isFreeStart(0))
        this.schoolName = this.classroomService.getSchoolName();
        this.checkPrivateSchool();
        this.loadClassrooms();
        this.loadRoute();
        //this.loadInvigilators()
        this.initQuestionnaire(data[0].school[0].group_id);
        this.checkAgreement(ProfResponsibilityTeacherAgreement);
      }
    })
  }

  ngOnDestroy(): void {
    if(this.classroomServiceSub) {
      this.classroomServiceSub.unsubscribe();
    }
  }

  isPJ() {
    return this.activeClassroom.curricShort == "EQAO_G3" || this.activeClassroom.curricShort == "EQAO_G6";
  }

  isPJClass(classroom) {
    return classroom.curricShort == "EQAO_G3" || classroom.curricShort == "EQAO_G6";
  }

  isG9OrOPssltClass(classroom){
    return classroom.curricShort =="EQAO_G9" || classroom.curricShort =="EQAO_G10"
  }

  activeStudents(){
    return this.activeClassroom.currentStudents.list
  }

  getActiveClassroomDepth(){
    if (this.classroomId){
      return 'SETTINGS';
    }
    else{
      return 'ADD_REMOVE';
    }
  }

  isFreeStart(classroomId:number | string){
    if (this.activeClassroom){
      return this.classroomService.isFreeStart(+classroomId)
    }
  }

  checkPrivateSchool(){
    const schoolInfo = this.classroomService.getSchoolByClassroomId(0);
    this.isPrivateSchool = !!schoolInfo.is_private;
  }
  createNewAssessmentSession($event){
    this.sessionDetails = $event;
      // this.getActiveSessions(config);
    // console.log('active classroom', this.activeClassroom, config);
    if (this.config.isScheduled){
      this.openAssessmentSchedulerModal();
    }
    else{
      if (!this.sessionDetails.isStudentsPresent) {
        this.loginGuard.quickPopup('msg_scheduling_no_students');
      } else {
        this.finalizeAssessmentCreation(this.config)
      }
    }
    console.log(this.sessionDetails)
  }

  isLanguageVisibleForFIClass(): boolean {
    const isFI = this.activeClassroom?.is_fi == 1 ? true : false;
    if(this.g9demoService.schoolDist[0].fi_option === 'C' && isFI) {
      return false;
    }
    return true;
  }

  checkAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void) {
    const nonAcceptedWindows = this.g9demoService.totalNonAcceptedRecords;
    if(this.isPrivateSchool || (nonAcceptedWindows.length<=0)) {
      return;
    }
    
    this.displayAgreement(agreement, () => this.setAgreementConfirmed());
  }

  setAgreementConfirmed() {
    this.auth.apiCreate(this.routes.EDUCATOR_RESPONSIBILITY_AGREEMENTS, this.g9demoService.totalNonAcceptedRecords, { query: {schl_group_id: this.g9demoService.schoolData.group_id}})
    this.g9demoService.totalNonAcceptedRecords = [];
  }

  displayAgreement(agreement: IAgreementConfirmConfig, callback?: (agreement: IAgreementConfirmConfig) => void): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      const caption = this.lang.tra(agreement.captionSlug);
      const checkBoxText = this.lang.tra(agreement.checkboxSlug);
      const btnSlug = this.lang.tra(agreement.btnSlug);
      this.loginGuard.confirmationReqActivate({
        caption,
        requireCheckboxInput: {checkboxCaption: checkBoxText},
        btnProceedConfig: {
          caption: btnSlug
        },
        btnCancelConfig: {
          hide: true
        },
        width: '43em',
        confirm: () => {
          callback(agreement);
          resolve();
        }
      });
    })
  }

  finalizeAssessmentCreation($event){
    const config =  $event;
    const school_class_id = +this.activeClassroom.id
    const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
    const isFI = !this.isLanguageVisibleForFIClass();
    this.closeActiveOverlay()
      this.classroomService
      .createAssessmentSession({
        school_class_id,
        slug: config.slug,
        isScheduled:config.isScheduled,
        scheduled_time:config.scheduled_time,
        is_fi: isFI
      },params)
      .then(session => {
        let currDate = new Date();
        let sessionDate = new Date(session.date_time_start)  
        if (config.isScheduled){
          this.activeClassroom.scheduledAssessments.splice(0,0, {
            test_session_id:session.test_session_id,
            date_time_start:session.date_time_start,
            school_class_id:session.school_class_id,
            slug:session.slug,
            name:this.renderTitle(session.slug),
            timeDirectStart:this.renderDay(session.date_time_start),
            hourDirectStart:this.renderTime(session.date_time_start),
            access_code:session.access_code
          })
          this.closeActiveOverlay()
        }
       else{
          this.classroomService.navigateToAssessmentSession(''+school_class_id, session.test_session_id, this.currentQueryParams)
       }
      })
      .catch(e => {
        if (e.message === 'TECH_READI_PENDING'){
          this.loginGuard.disabledPopup(this.lang.tra('technical_readiness_warning'));
        } else if (e.message === 'MSG_ADMIN_WINDOW_WARNING') {
          const windowStart = mtz(config.windowStart).format(this.lang.tra('datefmt_dashboard_long'));
          const windowEnd = mtz(config.windowEnd).format(this.lang.tra('datefmt_dashboard_long'));
          if(this.isPJ()) {
            this.loginGuard.disabledPopup(this.lang.tra('msg_pj_administration_window_warning', undefined, {start: windowStart, end: windowEnd})); 
          } else if (this.activeClassroom.curricShort == "EQAO_G9") {
            this.loginGuard.disabledPopup(this.lang.tra('msg_g9_administration_window_warning', undefined, {start: windowStart, end: windowEnd}));
          } else if (this.activeClassroom.curricShort == "EQAO_G10") {
            this.loginGuard.disabledPopup(this.lang.tra('msg_osslt_administration_window_warning', undefined, {start: windowStart, end: windowEnd}));   
          }
        } else if (e.message === 'MSG_ASSES_TIME_INVALID_WARNING') {
          alert(this.lang.tra('sa_asses_time_invalid'));
        }
      })
  }
  getActiveSessions(config){
    const classroom = this.classroomService.getClassroomById(this.classroomId)
    const params = {
      query: {
        school_class_group_id: classroom.group_id,
        school_class_id:this.classroomId,
        slug:config.slug
      }
    }
    this.classroomService
    .getActiveAssessments(params)
    .then(res => {
      this.completedSubSessions = res.completedSubSessions;
      this.subSessions = res.subSessions;
      this.activeSubSessions = res.activeSubSessions;
      // if(this.completedSubSessions.length > 0 || this.activeSubSessions.length > 0){
      //   this.config = config;
      //   this.openActiveAssessmentModal();
      // }
      // else{
      //   this.finalizeAssessmentCreation(config)
      // }
      this.isActiveLoaded = true;
      this.config = config;
      this.openActiveAssessmentModal();
    })
    }
  
  selectClassroom(classroom:IClassroomSlot){
    this.selectClassroomById(classroom.id);
  }
 
  getSelectClassroomLabel(classroom: IClassroom): string {
    switch(classroom.curricShort) {
      case 'EQAO_G10':
        return 'g9_select_group'
      case 'EQAO_G3':
      case 'EQAO_G6':
      case 'EQAO_G9':
      default:
        return 'g9_select_class'
    }
  }

  getSchoolByClassroomId
  
  getInvigilateRouteLink(entry){
    return `/${this.lang.c()}/educator/assessment/${this.activeClassroom.id}/${entry[0]?.test_session_id}/`;
  }
  getScheduledInvigilateRouteLink(entry){

    this.classroomService.navigateToAssessmentSession(''+this.activeClassroom.id, entry[0]?.test_session_id, this.currentQueryParams)
  }
  getStudentRouteLink(){
    return `/${this.lang.c()}/educator/students/${this.activeClassroom.id}`;
  }

  renderTitle(title:string){
    switch(title){
      case ASSESSMENT.PRIMARY_SAMPLE: return this.lang.tra("primary_sample_test");
      case ASSESSMENT.PRIMARY_OPERATIONAL: return this.lang.tra("primary_operational_test");
      case ASSESSMENT.JUNIOR_SAMPLE: return this.lang.tra("junior_sample_test");
      case ASSESSMENT.JUNIOR_OPERATIONAL: return this.lang.tra("junior_operational_test");
      case ASSESSMENT.G9_SAMPLE: return "Sample Assessment (Grade 9)";
      case ASSESSMENT.G9_OPERATIONAL: return this.lang.tra('g9_assess_title');
      case ASSESSMENT.OSSLT_SAMPLE: return "Sample Test (OSSLT)";
      case ASSESSMENT.OSSLT_OPERATIONAL: return "Ontario Secondary School Literacy Test";
      default:
        return title;
    }
  }
  
  renderDay(dateTime){
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format(this.lang.tra('datefmt_day_month'));
  }

  renderTime(dateTime){
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format('h:mm A')
  }



  selectClassroomById(classroomId:string){
    this.classroomService.navigateToClassroom(classroomId, this.currentQueryParams)
    setTimeout(()=>{
      //time delay is for aesthetic reasons, but if we decide to remove it, we should take it out
      const el = this.selectedClassroomPanel.nativeElement;
      if (el){
        el.scrollIntoView({behavior: 'smooth'});
      }
    }, 700);
  }

  loadRoute(){
    if (this.routeSub){ this.routeSub.unsubscribe(); }
    this.routeSub = this.route.params.subscribe(params => {
      this.classroomId = this.userSiteContext.handleClassroomRouteParam(params['classroomId']);
      if (!this.classroomId){ 
        this.activeClassroom = null;
        this.classroomService.setActiveClassroom(this.activeClassroom)
        this.sidePanel.classroomId = null;
        this.updateBreadcrumb();
        return; 
      }
      this.sidePanel.classroomId = this.classroomId;
      this.loadClassroom(this.classroomId);
      this.updateBreadcrumb();
    });
  }

  updateBreadcrumb() {
    let teacherClass = this.lang.tra('lbl_classes');
    const basePath = `/${this.lang.c()}/educator/classrooms`;
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(teacherClass, basePath, this.currentQueryParams)
    ];
    if (this.activeClassroom){
      this.breadcrumb.push(
        this.breadcrumbsService._CURRENT( this.activeClassroom.name, basePath+'/'+this.classroomId, this.currentQueryParams)
      );
    }    
  }

  loadClassrooms(){
    const twRef = new Map();
    this.testWindowSlots = [];
    this.classroomSlots = [];
    this.classrooms.forEach(classroom =>{
      const classroomSearch = this.g9demoService.classrooms.find( c => c.id === +classroom.id);
      const tw = this.g9demoService.getTestWindowFromClassroom(classroomSearch)
      let twSlot = twRef.get(tw.id);
      if (!twSlot){
        twSlot = {
          tw_id: tw.id, 
          next_tw_id: tw.next_tw_id, 
          name: this.parseTwTitle(tw.title), 
          sortOrder: tw.date_start,
          twStartEndDate: this.renderTestWindowStartEndStrInEtz(tw.date_start, tw.date_end),
          classes: [],
        }
        this.testWindowSlots.push(twSlot)
        twRef.set(tw.id, twSlot)
      }
      if(this.filterClassesInActiveTestWindow(classroom)){
        twSlot.classes.push({
          id:classroom.id,
          name:classroom.name,
          isAssigned:classroom.isAssigned,
          curricShort:classroom.curricShort,
          classCode:classroom.classCode,
          numStudents:classroom.currentStudents.list.length,
          numTeachers:classroom.currentTeachers.length,
          is_fi:classroom.is_fi,
        })
      }
    })
    this.testWindowSlots = sortBy(this.testWindowSlots, 'sortOrder', true)
  }

  /**
   * Parse and return test window title in en or fr
   * @param twTitle 
   * @returns {string} titleStr
   */
  parseTwTitle(twTitle:string){
    try {
      const title = JSON.parse(twTitle) || {};
      const titleStr = title[this.lang.c()] || title['en'] || '';
      if (titleStr){
        return titleStr;
      }
    }
    catch (e) { }
    return '--'
  }

  filterClassesInActiveTestWindow(classroom: any){
    const classroomSearch = this.g9demoService.classrooms.find( c => c.id === +classroom.id);
    const classSemester = this.g9demoService.semesters.map[classroomSearch.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    if(!this.isPastWindowsShown){
      return (new Date(classTestWindow.date_end) > new Date ())
    } 
    // return classTestWindow.is_archived != 1
    return true;
  }

  /**
   * Returns start and end date of administration window of assessment session
   * @param { Date } startDate - start date of the assessment session
   * @param { Date } endDate - start date of the assessment session
   * 
   */
  renderTestWindowStartEndStrInEtz(startDate:Date, endDate:Date){
    let formattedStartDate = etz(startDate).format(this.lang.tra('datefmt_month_year_time'));
    let formattedEndDate = etz(endDate).format(this.lang.tra('datefmt_month_year_time'));
    if(this.lang.c() === 'fr'){
      formattedStartDate = tzLangAbbrs(formattedStartDate, 'fr')
      formattedEndDate = tzLangAbbrs(formattedEndDate, 'fr')
    }

    return `${formattedStartDate} ${this.lang.tra('lbl_date_to')} ${formattedEndDate}`;
  }

  loadInvigilators(){
    if(!this.activeClassroom || !this.g9demoService.invigilators){
      return
    }
    this.classInvigilators = [];
    //const invigilators = this.g9demoService.invigilators.filter(invig => invig.group_id == null || +invig.group_id == +this.activeClassroom.group_id)
    let invigilators = [];
    this.g9demoService.invigilators.forEach(g9invig => {
      const in_invigilator = invigilators.find(invig => invig.contact_email === g9invig.contact_email)
      if(!in_invigilator){ // if g9invig's email is not in invigilators list
        let theInvig = this.g9demoService.invigilators.find( invig => invig.contact_email === g9invig.contact_email && +invig.group_id == +this.activeClassroom.group_id)
        if(theInvig){
          invigilators.push(theInvig)
        }else{
          invigilators.push(g9invig)
        }
      }
    })
    invigilators.forEach(invig => {
      const avaiableInvig ={
        uid:invig.uid,
        name: invig.first_name+" "+invig.last_name,
        email: invig.contact_email,
        selected: +invig.group_id == +this.activeClassroom.group_id
      }
      this.classInvigilators.push(avaiableInvig)
    })
  }

  loadClassroom(classroomId:string){
    this.activeClassroom = this.classrooms.find(classroom => (''+classroom.id) === classroomId);
    this.classroomService.setActiveClassroom(this.activeClassroom) 
    this.loadInvigilators();
  }

  getReportRouteForClass(){
    if(!this.isPrivateSchool  || this.isPrivateSchool && this.activeClassroom.allow_ISR){
      this.router.navigate([this.lang.c(),
        'educator',
        'assessment-report',
        this.classroomId,
        this.activeClassroom.group_id
      ], {
        queryParams: this.currentQueryParams
      });
      //return `/${this.lang.c()}/${AccountType.EDUCATOR}/assessment-report/${this.classroomId}/${this.activeClassroom.group_id}`
    }else{
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra("ta_msg_g9_report_unavailable")), 0);
    }  
  }
  
  openAssessmentReport(assignmentId:string){
    // this.router.navigate(['teacher','classroom',this.classroomId,'assignment', assignmentId, 'report']);
  }

  openClassroomNameEditor(){
    if (!this.isEditingClassroomName){
      this.isEditingClassroomName = true;
      this.classroomNameForm.setValue({name: this.activeClassroom.name});
    }
  }
  closeClassroomNameEditor(){
    this.isEditingClassroomName = false;
  }
  changeCurrentClassroomName(){
    this.activeClassroom.name = this.classroomNameForm.controls.name.value;
    this.closeClassroomNameEditor();
  }

  activeOverlay:string;
  openNewClassroomModal(){
    this.activeOverlay = Overlays.NEW_CLASSROOM;
  }

  PREVENT_SIMUL_SCHED = true
  openNewAssessmentModal(){
    // try {
    //   if (this.PREVENT_SIMUL_SCHED){
    //     const sc = this.activeClassroom;
    //     if ( sc.openAssessments.length || sc.scheduledAssessments.length){
    //       this.loginGuard.quickPopup('txt_t_active_asmt');
    //       return;
    //     }
    //   }
    // }
    // catch(e){}
    this.activeOverlay = Overlays.NEW_ASSESSMENT;
  }
  closeActiveOverlay(){
    this.activeOverlay = null;
    this.schoolTeachers = []; 
  }
  openActiveAssessmentModal(){
    this.activeOverlay = Overlays.ACTIVE_ASSESSMENT;
  }
  openAssessmentSchedulerModal(){
    this.activeOverlay = Overlays.ASSESSMENT_SCHEDULER;
  }
  renderOverlayName(overlayId:string){
    switch(overlayId){
      case Overlays.NEW_CLASSROOM: return this.lang.tra('g9_create_new_group');
      case Overlays.NEW_ASSESSMENT:return this.lang.tra('g9_create_new_assess');
      case Overlays.ACTIVE_ASSESSMENT:return this.lang.tra('title_active_completed');
      default: return overlayId;
    }

  }

  getNumStudents(){

    return this.activeStudents().length;
  }
  
  getEntryProps(entry){
    let momentTime = moment.tz(entry.date_time_start, moment.tz.guess());
    let time = momentTime.format('h:mm');
    let timezone = momentTime.zoneAbbr();
    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    return{
      date:entry.timeDirectStart,
      time:`${time} ${timezone}`
    }
  }
  activateClickBuffer(){
    this.isClickBufferActive = true;
    setTimeout(()=>{
      this.isClickBufferActive = false;
    }, 800)
  }
  archiveClassroom(classroomSlot:IClassroomSlot){

  }
  deactivateClassroomManagement(){
    this.isManagingClassrooms = false;
  }


  createNewSession(){
    // this.activeClassroom.openAssessments.push()
  }

  studentsDynamicTranslation(num: number){
    const singular = this.lang.tra('teacher_students_singular');
    const plural = this.lang.tra('teacher_students_plural');
    return `${num} ${(num === 1) ? singular : plural}`;
  }
  // goToAssessmentScheduler($event){
  //   this.openAssessmentSchedulerModal()
  // }
  createNewClassroom(){
    return this.loginGuard.disabledPopup();
    this.isAddingNewClassroom = true;
    // this.classroomService
    //   .createNewClassroom('cambridge-stage6', this.newClassName.value)
    //   .then(res => {
    let id = ''+randInt(1,100000000)
     let name = this.newClassName.value;
     let cCode = generateAccessCode(8);
      let classroom = {
        id:id,
        name:name,
        classCode:cCode,
        owner: 'Test',
        enableClassListingByCC: true,
        currentStudents: {list:[], map:{}},
        currentTeachers: this.dummyTeacherlist,
        timeCreated: 'Oct 19,2020.1:15pm',
        timeLastTouched: 'Oct 19,2020.1:15pm'    
    }
    let classSlot = {
      id:id,
      name:name,
      curricShort:'',
      classCode:cCode,
      numStudents: classroom.currentStudents.list.length,
      numTeachers: classroom.currentTeachers.length
    }
    this.isAddingNewClassroom = false;
    this.newClassName.reset(); 
    this.closeActiveOverlay();
    //this.classroomClassCodeCache.set('classroom1', 'ABC');
    //this.selectClassroomById('classroom1');
  // })
   
    this.classroomService.saveClassroom(classroom)
    this.classroomSlots.push(classSlot)
  }

  inValidTestWindow(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    const classSemester = this.g9demoService.semesters.map[classroom.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    return (new Date(classTestWindow.date_end) < new Date ())
  }

  isPlaceHolder(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    return classroom.is_placeholder
  }

  isCreateSessionOverlays(){
    return this.activeOverlay == Overlays.NEW_CLASSROOM || this.activeOverlay == Overlays.NEW_ASSESSMENT || this.activeOverlay == Overlays.ACTIVE_ASSESSMENT || this.activeOverlay == Overlays.ASSESSMENT_SCHEDULER;
  }

  isAddRemoveInvigilatorOverlays(){
    return this.activeOverlay == Overlays.ADD_REMOVE_INVIGILATORS
  }

  /**
   * open ADD_REMOVE_INVIGILATORS modal when "[Add/Remove] Additional Teacher(s)/Invigilator(s) is click"
   * Also load the potential and current invigilator list from the API for the selected class and stored in schoolTeachers
   */
  openAddRemoveInvigilatorsModal(){
    this.schoolTeachers = []; // reset school teachers 
    this.loadingSchoolTeachers = true;
    const params = {
      query: {
        school_class_group_id: this.activeClassroom.group_id
      }
    }
    this.auth
      .apiFind(this.routes.EDUCATOR_INVIGILATORS, params )
      .then( res => {
        this.schoolTeachers = res
        this.loadingSchoolTeachers = false;
      })
    this.activeOverlay = Overlays.ADD_REMOVE_INVIGILATORS;
  }

  showAddRemoveSchoolTeachersBtn(){
    if(this.classInvigilators.length < 1){
      this.loadInvigilators();
    }
    return !this.activeClassroom.is_invigilating;
  }
 
  toggleAddRemoveInvigilator(teacher){
    teacher.selected = !teacher.selected
    if(teacher.selected){ //add teacher as invigilator
      this.auth.apiCreate( this.routes.EDUCATOR_INVIGILATORS,
        {
          invig_uid: teacher.uid,
        },
        this.configureQueryParams()
      ).then( res =>{
        this.g9demoService.invigilators.push({
          uid:teacher.uid, 
          contact_email:teacher.email, 
          first_name:teacher.first_name,
          last_name:teacher.last_name,
          group_id:this.activeClassroom.group_id
        })
        this.loadInvigilators()
      })
    }else{//remove teacher from invigilator
      const theTeacher = this.g9demoService.invigilators.find(invig => invig.contact_email == teacher.email && invig.group_id == this.activeClassroom.group_id)
      this.auth.apiRemove( this.routes.EDUCATOR_INVIGILATORS,-1,this.configureQueryParams({ data: { invig_uid:teacher.uid }}))
        .then( res =>{
          const theTeacherIndex = this.g9demoService.invigilators.indexOf(theTeacher)
          this.g9demoService.invigilators.splice(theTeacherIndex, 1)
          this.loadInvigilators()
        })
    }
  }

  configureQueryParams(query?:any){
    if(query){
      return {
        query:{
          school_class_group_id: this.activeClassroom.group_id,
          ...query
        }  
      }  
    }
    return {
      query:{
        school_class_group_id: this.activeClassroom.group_id
      }  
    }
  }

  getClassInvigilators(){
    let classInvigilatorsName=""
    const classInvigilators = this.classInvigilators.filter(teacher => teacher.selected)
    classInvigilators.forEach( invig => classInvigilatorsName = classInvigilatorsName.concat(" "+invig.name+","))
    return classInvigilatorsName;
  }

  reset_flag:boolean = true
  detectChanges(){
    this.reset_flag = false
    this.changeDetector.detectChanges();
    this.reset_flag = true;
  }
  
  // Questionnaire session

  getQuestionnaireRouteLink(test_session_id) {
    return `${this.lang.c()}/educator/questionnaire/${test_session_id}`
  }

  initQuestionnaire = (schl_group_id) => {
    const school_group_id = schl_group_id || this.g9demoService.schoolData.group_id
    this.taQuestionnaire
    .findQuestionnaireSession(AccountType.EDUCATOR, ITypeSlug.TEACHER_QUESTIONNAIRE, school_group_id)
    .then((session) => {
      if(session){
        // there will be only one session for all the educators
        this.questionnaireSessionId = session[0].id
        this.questionnaireDueOn = moment(session[0].due_on).format('LL')
        this.detectChanges(); //tra-md not detecting changes
        this.haveQuestionnaireSession = true;
      }
    })
  }

  onClickQuestionnaire = () => {
    
    if(!this.questionnaireSessionId){      
      alert("No Questionnaire session found");
      return;
    }

    this.router.navigate([this.getQuestionnaireRouteLink(this.questionnaireSessionId)], { queryParams: {school: this.schl_group_id}});        

  }

  getQuestionnaireDueDate = () => this.questionnaireDueOn ? this.questionnaireDueOn : ''

  getAssessmentPlaceholderTitle(classroomType){
    if(classroomType === "SAMPLE"){
      switch(this.activeClassroom.curricShort) {
        case 'EQAO_G10': return this.lang.tra("osslt_sample_test");
        case 'EQAO_G3': return this.lang.tra("primary_sample_test");
        case 'EQAO_G6': return this.lang.tra("junior_sample_test");
        case 'EQAO_G9': return this.lang.tra("invig_g9_sample_test");
      }
    }else{
      switch(this.activeClassroom.curricShort) {
        case 'EQAO_G10': return this.lang.tra("osslt_operational_test");
        case 'EQAO_G3': return this.lang.tra("primary_operational_test");
        case 'EQAO_G6': return this.lang.tra("junior_operational_test");
        case 'EQAO_G9': return this.lang.tra('g9_assess_title');
      }
    }
  }

  /**
   * state data passed down to child component
   */
  getTestSessionState(testSessionType){
    const state = {
      title: this.getAssessmentPlaceholderTitle(testSessionType),
      status: this.getTestSessionStatus(testSessionType),
      sessionType: testSessionType,
      sessionRouteLink: this.getTestSessionRouteLink(testSessionType),
      lastSessionClosedOn: this.getClosedOnDate((this.getLastSessionClosedOn(testSessionType)))
    }
    return state
  }

  /**
   * Returns test session status based on sample or operational assessments
   */
  getTestSessionStatus(testSessionType) {
    if (testSessionType === TestSessionType.SAMPLE) {
      const openSampleAssessment = this.activeClassroom.openAssessments.filter(assessment => assessment.slug === ASSESSMENT.PRIMARY_SAMPLE || assessment.slug === ASSESSMENT.JUNIOR_SAMPLE || assessment.slug === ASSESSMENT.G9_SAMPLE || assessment.slug === ASSESSMENT.OSSLT_SAMPLE);
      const scheduledSampleAssessment = this.activeClassroom.scheduledAssessments.filter(assessment => assessment.slug === ASSESSMENT.PRIMARY_SAMPLE || assessment.slug === ASSESSMENT.JUNIOR_SAMPLE || assessment.slug === ASSESSMENT.G9_SAMPLE || assessment.slug === ASSESSMENT.OSSLT_SAMPLE);
      this.sampleAssessment = openSampleAssessment.length ? openSampleAssessment : scheduledSampleAssessment;

      if(openSampleAssessment.length){
        return openSampleAssessment[0].is_paused ? TestSessionStatus.PAUSED : openSampleAssessment[0].in_progress ? TestSessionStatus.IN_PROGRESS : TestSessionStatus.READY_TO_START
      }else if (scheduledSampleAssessment.length){
        return TestSessionStatus.SCHEDULED
      } else{
        return TestSessionStatus.NONE
      }
    }

    if (testSessionType === TestSessionType.OPERATIONAL) {
      const openOperationalAssessment = this.activeClassroom.openAssessments.filter(assessment => assessment.slug === ASSESSMENT.PRIMARY_OPERATIONAL || assessment.slug === ASSESSMENT.JUNIOR_OPERATIONAL  || assessment.slug === ASSESSMENT.G9_OPERATIONAL || assessment.slug === ASSESSMENT.OSSLT_OPERATIONAL);
      const scheduledOperationalAssessment = this.activeClassroom.scheduledAssessments.filter(assessment => assessment.slug === ASSESSMENT.PRIMARY_OPERATIONAL || assessment.slug === ASSESSMENT.JUNIOR_OPERATIONAL  || assessment.slug === ASSESSMENT.G9_OPERATIONAL || assessment.slug === ASSESSMENT.OSSLT_OPERATIONAL);
      this.operationalAssessment = openOperationalAssessment.length ? openOperationalAssessment : scheduledOperationalAssessment;

      if(openOperationalAssessment.length){
        return openOperationalAssessment[0].is_paused ? TestSessionStatus.PAUSED : openOperationalAssessment[0].in_progress ? TestSessionStatus.IN_PROGRESS : TestSessionStatus.READY_TO_START
      }else if (scheduledOperationalAssessment.length){
        return TestSessionStatus.SCHEDULED
      } else{
        return TestSessionStatus.NONE
      }
    }
  }

  getTestSessionRouteLink(testSessionType){
    if(testSessionType === TestSessionType.SAMPLE){
      return  this.getInvigilateRouteLink(this.sampleAssessment) 
    }
    if(testSessionType === TestSessionType.OPERATIONAL){
      return  this.getInvigilateRouteLink(this.operationalAssessment) 
    }
  }

/**
 * Date of last completed assessment session
 */
  getLastSessionClosedOn(testsessionType){
    if(testsessionType === TestSessionType.SAMPLE){
      const lastClosedAssessment = this.activeClassroom.recentAssessments
      .filter(assessment => 
        assessment.slug === ASSESSMENT.PRIMARY_SAMPLE ||
        assessment.slug === ASSESSMENT.JUNIOR_SAMPLE ||
        assessment.slug === ASSESSMENT.G9_SAMPLE ||
        assessment.slug === ASSESSMENT.OSSLT_SAMPLE
      )
      .sort((a, b) => new Date(b.closed_on).getTime() - new Date(a.closed_on).getTime());
    return lastClosedAssessment[0]?.closed_on;
    }

    if(testsessionType === TestSessionType.OPERATIONAL){
      const lastClosedAssessment = this.activeClassroom.recentAssessments
      .filter(assessment =>
        assessment.slug === ASSESSMENT.PRIMARY_OPERATIONAL ||
        assessment.slug === ASSESSMENT.JUNIOR_OPERATIONAL ||
        assessment.slug === ASSESSMENT.G9_OPERATIONAL ||
        assessment.slug === ASSESSMENT.OSSLT_OPERATIONAL
      )
      .sort((a, b) => new Date(b.closed_on).getTime() - new Date(a.closed_on).getTime());
  
    return lastClosedAssessment[0]?.closed_on;
    }
  }

  /**
   * returns closed on date in translation slug format MMMM D, YYYY
   */
  getClosedOnDate(date){
    if (!date) {
      return
    }
    const mStart = moment.tz(date, moment.tz.guess());
    return `${mStart.format(this.lang.tra('datefmt_long_date_line2'))}`
  }

  getAssessmentSlug(sessionType) {
    if(sessionType === TestSessionType.SAMPLE){
      switch(this.activeClassroom.curricShort) {
        case 'EQAO_G3':
          this.assessmentSlug = ASSESSMENT.PRIMARY_SAMPLE;
          break;
        case 'EQAO_G6':
          this.assessmentSlug = ASSESSMENT.JUNIOR_SAMPLE;
          break;
        case 'EQAO_G9':
          this.assessmentSlug = ASSESSMENT.G9_SAMPLE;
          break;
        case 'EQAO_G10':
          this.assessmentSlug = ASSESSMENT.OSSLT_SAMPLE;
          break;
      }
    }
    if(sessionType === TestSessionType.OPERATIONAL){
      switch(this.activeClassroom.curricShort) {
        case 'EQAO_G3':
          this.assessmentSlug = ASSESSMENT.PRIMARY_OPERATIONAL;
          break;
        case 'EQAO_G6':
          this.assessmentSlug = ASSESSMENT.JUNIOR_OPERATIONAL;
          break;
        case 'EQAO_G9':
          this.assessmentSlug = ASSESSMENT.G9_OPERATIONAL;
          break;
        case 'EQAO_G10':
          this.assessmentSlug = ASSESSMENT.OSSLT_OPERATIONAL;
          break;
      }
    }
  }

  handleLinkClick(link: any) {
    if (link.onClick) {
      link.onClick();
    }
   if (link.href) {
    window.open(this.lang.tra(link.href), '_blank');
  }
  }

  /**
   * Update class access code
   * @param activeClassroom 
   */
  regenerateAccessCode(activeClassroom){
    const data = {
      secreteUserCode: this.isSecreteUser, 
      school_class_id: activeClassroom.id
    }
    this.auth.apiUpdate(this.routes.EDUCATOR_CLASS_ACCESS_CODE, activeClassroom.id, data, this.configureQueryParams())
    .then( res =>{
      //update class Access code
      this.activeClassroom.classCode = hyphenateAccessCode(res.access_code)
      this.loadClassrooms()
    })
    .catch(error =>{
      switch(error.message) {
        default:
          alert(error.message)
          break
      }
    })
  }
}