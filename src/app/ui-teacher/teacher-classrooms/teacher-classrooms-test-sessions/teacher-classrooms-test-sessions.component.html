<div class="active-card">
  <div class="assessment">{{title}}</div>
  <div [ngSwitch]="status">
    <a *ngSwitchCase="TestSessionStatus.IN_PROGRESS" [class.session-link-operational]="sessionType === TestSessionType.OPERATIONAL" [class.session-link-sample]="sessionType == TestSessionType.SAMPLE" [routerLink]="sessionRouteLink" [queryParams]="currentQueryParams" [ngClass]="{ 'disabled': isButtonDisabled() }">
      <img style="width: 1.8em; height: 1.8em;" src="https://eqao.vretta.com/authoring/user_uploads/3114703/authoring/in progress/1689271286968/in progress.png" alt=""/>
      <tra [slug]="getTitleSlug(status)"></tra>
    </a>
    <a *ngSwitchCase="TestSessionStatus.PAUSED" [class.session-link-operational]="sessionType === TestSessionType.OPERATIONAL" [class.session-link-sample]="sessionType == TestSessionType.SAMPLE"  [routerLink]="sessionRouteLink" [queryParams]="currentQueryParams" [ngClass]="{ 'disabled': isButtonDisabled() }">
      <img style="width: 1.8em; height: 1.8em;" src="https://eqao.vretta.com/authoring/user_uploads/3114703/authoring/pause/1689879672714/pause.png" alt=""/>
      <tra [slug]="getTitleSlug(status)"></tra>
    </a>
    <a *ngSwitchCase="TestSessionStatus.READY_TO_START" [class.session-link-operational]="sessionType === TestSessionType.OPERATIONAL" [class.session-link-sample]="sessionType == TestSessionType.SAMPLE" [routerLink]="sessionRouteLink" [queryParams]="currentQueryParams" [ngClass]="{ 'disabled': isButtonDisabled() }">
      <img style="width: 1.8em; height: 1.8em;" src="https://eqao.vretta.com/authoring/user_uploads/3114703/authoring/ready to start/1689274777523/ready to start.png" alt=""/>
      <tra [slug]="getTitleSlug(status)"></tra>
    </a>
    <a *ngSwitchCase="TestSessionStatus.SCHEDULED" [class.session-link-operational]="sessionType === TestSessionType.OPERATIONAL" [class.session-link-sample]="sessionType == TestSessionType.SAMPLE" [routerLink]="sessionRouteLink" [queryParams]="currentQueryParams" [ngClass]="{ 'disabled': isButtonDisabled() }">
      <img style="width: 1.8em; height: 1.8em;" src="https://eqao.vretta.com/authoring/user_uploads/3114703/authoring/schedule/1689270903171/schedule.png" alt=""/>
      <tra [slug]="getTitleSlug(status)"></tra>
    </a>
    <div *ngSwitchCase="TestSessionStatus.NONE">
      <a class="new-assessment-btn" [class.session-link-operational]="sessionType === TestSessionType.OPERATIONAL" [class.session-link-sample]="sessionType == TestSessionType.SAMPLE" href="#" (click)="testSessionClick(); $event.preventDefault(); " 
      [ngClass]="{ 'disabled': isButtonDisabled() }">
        <tra [slug]="getTitleSlug(status)"></tra>
      </a>
      <tra-md *ngIf="lastSessionClosedOn" class="closed-on" slug="lbl_closed_on" [props]="{date: lastSessionClosedOn }"></tra-md> 
    </div>
  </div>
</div>
