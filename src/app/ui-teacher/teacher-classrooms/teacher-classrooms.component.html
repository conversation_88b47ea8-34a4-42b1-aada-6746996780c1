<div class="page-body is-offwhite">
  <div>
    <header 
      [breadcrumbPath]="breadcrumb" 
      [hasSidebar]="true"
    ></header>
    <div style="padding:4em; padding-top:1em;">
      <div *ngIf="activeClassroom">
        <tra-md slug="sa_educators_classes2_header"></tra-md>
      </div>
      <div class="active-classroom-container" #selectedClassroomPanel [class.is-active]="activeClassroom">
        <div *ngIf="activeClassroom" class="active-classroom">
          <div class="classroom-header">
            <div class="classroom-identity">
              <div *ngIf="!isEditingClassroomName" style="font-size: 1.6em;">
                <span>
                  {{activeClassroom.name}} 
                </span>
                <span *ngIf="activeClassroom.isAssigned" style="margin: 0 1em; color:green;">
                  <i class="fas fa-star" ></i> 
                  <span style="font-size:.6em"><tra slug="teacher_class_this_is_your"></tra></span>  <!--This is your class section.-->
                </span>
              </div>
              <div *ngIf="showAddRemoveSchoolTeachersBtn()" style="margin-right: 0.5em; margin-top: 1em;">
                <a (click)="openAddRemoveInvigilatorsModal()">
                  <span><tra slug="pj_add_remove_class_invigilators"></tra></span>
                </a>
                <span style = "font-weight: bold; margin-left: 0.5em;"><tra slug="ta_class_invigilators_label"></tra></span>&nbsp;{{getClassInvigilators()}}
              </div>  
              <div *ngIf="isEditingClassroomName">
                <form [formGroup]="classroomNameForm" (ngSubmit)="changeCurrentClassroomName()">
                  <div>
                    <strong><tra slug="class_section_name"></tra></strong>
                  </div>
                  <input type="text" class="input" formControlName="name">
                  <button type="button" class="button" (click)="closeClassroomNameEditor()"><tra slug="btn_cancel"></tra></button>
                  <input type="submit" class="button is-success" value="Save">
                  <div class="notification" *ngIf="isSavingClassroomName">
                    <tra slug="ie_saving"></tra>
                  </div>
                </form>
              </div>
              <div class="teacher-list" *ngIf="false">
                <tra slug='lead_by'></tra>
                <span *ngFor="let teacher of activeTeachers; first as isFirst; last as isLast">
                  <span *ngIf="isLast && !isFirst"><tra slug="lbl_and"></tra></span>
                  <span>{{teacher.displayName}}</span>
                  <span *ngIf="!isLast">, </span>
                </span>
                <a routerLink="/teacher/classroom/{{classroomId}}/share"> 
                  [Share]
                </a>
              </div>
            </div>
            <div>
              <class-code-indicator 
              [isPJ]="isPJ()"
              [isG9OrOsslt]="!isPJ()"
              [classCode]="activeClassroom.classCode"
              [accessScope]="activeClassroom.isAssigned ? 'for_class_section' : 'for_group_section'"
              ></class-code-indicator>
              <button 
                *ngIf="isSecreteUser" 
                class="button is-fullwidth" 
                style = "margin-top: 0.5em; font-size: 1em;" 
                (click)="regenerateAccessCode(activeClassroom)"
              >
                <i class="material-icons">&#xe5d5;</i>
                <tra slug="ta_regenerate_accesscode"></tra>
              </button>
            </div>
          </div>
          
          <hr/>

          <div class="classroom-columns">
            
            <div class="classroom-info-column assessments-column">
              <div class="info-title">
              </div>
              <div class="entries-container">
                <teacher-classrooms-test-sessions
                  [state]="getTestSessionState(TestSessionType.SAMPLE)"
                  [currentQueryParams]="currentQueryParams"
                  (newAssessmentModal)="openNewAssessmentModal()"
                  [session]="sampleAssessment"
                  (assessmentSlug)="getAssessmentSlug($event)"
                  [inValidTestWindow]="inValidTestWindow.bind(this)"
                  [isPlaceHolder]="isPlaceHolder.bind(this)"
                >
                </teacher-classrooms-test-sessions>
                <teacher-classrooms-test-sessions
                  [state]="getTestSessionState(TestSessionType.OPERATIONAL)"
                  [currentQueryParams]="currentQueryParams"
                  (newAssessmentModal)="openNewAssessmentModal()"
                  [session]="operationalAssessment"
                  (assessmentSlug)="getAssessmentSlug($event)"
                  [inValidTestWindow]="inValidTestWindow.bind(this)"
                  [isPlaceHolder]="isPlaceHolder.bind(this)"
                >
                </teacher-classrooms-test-sessions>
                
              </div>
              <div *ngIf="isPJ()" style="margin-top: 1em;">
                <tra slug="msg_another_active_session_note_pj"></tra>
              </div>
              <a  *ngIf="activeClassroom.curricShort == 'EQAO_G9'" (click)="getReportRouteForClass()" class="button is-info is-small is-outlined is-fullwidth">
                <tra slug='view_reports'></tra>
              </a>
              <br/>
              <div *ngIf="activeClassroom.curricShort == 'EQAO_G9' && activeClassroom.isAssigned">
                <tra slug="report_error_message"></tra>
                <br/><br/>
              </div>
            </div>
              
            <div class="classroom-info-column students-column">
              <div class="info-title">
                <span class="icon icon-student"></span>
                <span><span><tra slug="lbl_students"></tra></span> ({{getNumStudents()}})</span>
              </div>
              <div>
                <div *ngFor="let entry of activeStudents()" class="info-text"> {{entry.displayName}} </div>
                <div *ngIf=" false && getNumStudents() > MAX_STUDENT_DISPLAY" class="info-more">+ {{getNumStudents() - activeStudents().length}} <span><tra slug="lbl_others"></tra></span></div>
              </div>
              <a [routerLink]="getStudentRouteLink()" [queryParams]="currentQueryParams" class="button is-info is-small is-outlined is-fullwidth">
                <tra slug="mng_manage_students"></tra>
              </a>
            </div>        
          </div>   
        </div>
      </div>
        
      <div *ngIf="isManagingClassrooms" class="classroom-list-header"><tra slug="active_classrooms"></tra></div>

        <div *ngIf="!activeClassroom" class="columns" style="margin-bottom: 0">
          <div class="column is-8">
            <tra-md slug="teacher_dashboard_header"></tra-md>

            <div class="container ui-container">
              <div class="ui-container-header has-text-centered">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-2x fa-envelope"></i>
                  </span>
                  <span class="title"><tra slug="lbl_message_centre"></tra></span>
                </span>
                <ntf-new-messages></ntf-new-messages>
              </div>
              <ntf-top-messages></ntf-top-messages>
            </div>

            <ng-container *ngIf="haveQuestionnaireSession">
              <hr>
              <div class="container ui-container">
                <a class="ui-container-header" (click)="onClickQuestionnaire()">
                  <span class="icon-text">
                    <span class="icon">
                      <i class="fas fa-2x fa-user"></i>
                    </span>
                    <div class="title">
                      <span><tra slug="lbl_teacher_questionnaire_eqao"></tra></span>
                      <p class="subtitle" *ngIf="reset_flag">
                        <tra-md slug ="teacher_questionnaire_complete" [props]="{DUE_DATE: getQuestionnaireDueDate()}" style="margin: 0"></tra-md>
                      </p>
                    </div>
                  </span>
                  <span class="icon">
                    <i class="far fa-2x fa-arrow-alt-circle-right"></i>
                  </span>
                </a>
              </div>
            </ng-container>

            <hr>

            <div class="container ui-container">
              <div class="ui-container-header">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-2x fa-link"></i>
                  </span>
                  <div class="title">
                    <span><tra slug="teacher_helpful_links"></tra></span>
                  </div>
                </span>    
              </div>
              <div class="columns is-multiline">
                <div class="column is-6" *ngFor="let link of HELPFUL_LINKS">
                  <div class="message is-info">
                    <a class="message-body" href="#" (click)="handleLinkClick(link); $event.preventDefault(); ">
                      <span class="icon">
                        <i class="fa" [class]="link.icon"></i>
                      </span>
                      <h2 class="title">
                        <tra [slug]="link.slug"></tra>
                      </h2>
                    </a>
                  </div>
                </div>
              </div>
            </div>


            <hr>

            <div class="container ui-container">
              <div class="ui-container-header">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-2x fa-users"></i>
                  </span>
                  <div class="title">
                    <span>Classes</span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div *ngFor="let twSlot of testWindowSlots">
          <h3 *ngIf="twSlot.classes.length > 0">
            <strong>{{twSlot.name}}</strong> {{twSlot.twStartEndDate}}
          </h3>
          <div class="class-card-container">
            <div *ngFor="let classroom of twSlot.classes" class="class-card" [class.is-selected]="classroomId == classroom.id">
              <!-- <ng-container *ngIf="isPrivateSchool && classroom.curricShort === 'EQAO_G10'"> -->
              <ng-container *ngIf="isPrivateSchool">  
                <div class="class-card-header">
                  <div>
                    <span class="star-container" *ngIf="classroom.isAssigned" style="color:green;"><i class="fas fa-star" ></i></span>
                    <span class="empty-star-container"  *ngIf="!classroom.isAssigned" style="color:transparent;"><i class="fas fa-star" ></i></span>
                  </div>
                  <div *ngIf="classroom.is_fi == 1" class="tag fr-imm"><tra slug="sa_lbl_french_immi"></tra></div> 
                  <div *ngIf="classroom.classCode" class="tag class-code" [class.pj-bkg-color]="isPJClass(classroom)" [class.g9-bkg-color]="isG9OrOPssltClass(classroom)">
                    {{classroom.classCode}}
                  </div>
                </div>
                <div class="class-card-title">
                  {{classroom.name}} 
                  <div class="class-card-subtitle">
                    {{ studentsDynamicTranslation(classroom.numStudents) }} <!-- 1 student vs 2 students -->
                    <!-- <div>{{classroom.curricShort}}</div> -->
                  </div>
                </div>
                <div class="button-container">
                  <button class="button is-fullwidth btn-select-class" [disabled]="isClickBufferActive" (click)="selectClassroom(classroom)">
                    <span *ngIf="classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                    <span *ngIf="!classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                  </button>
  
                  <button *ngIf="isManagingClassrooms" class="button is-danger" [disabled]="isClickBufferActive" (click)="archiveClassroom(classroom)"><tra slug="lbl_archive"></tra></button>
                </div>
              </ng-container>
  
              <ng-container *ngIf="!isPrivateSchool">
                <div class="class-card-header">
                  <div>
                    <span class="star-container" *ngIf="classroom.isAssigned" style="color:green;"><i class="fas fa-star" ></i></span>
                    <span class="empty-star-container"  *ngIf="!classroom.isAssigned" style="color:transparent;"><i class="fas fa-star" ></i></span>
                  </div>
                  <div *ngIf="classroom.is_fi == 1" class="tag fr-imm"><tra slug="sa_lbl_french_immi"></tra></div> 
                  <div *ngIf="classroom.classCode" class="tag class-code" [class.pj-bkg-color]="isPJClass(classroom)" [class.g9-bkg-color]="isG9OrOPssltClass(classroom)">
                    {{classroom.classCode}}
                  </div>
                </div>
                <div class="class-card-title">
                  {{classroom.name}}
                  <div class="class-card-subtitle">
                    {{ studentsDynamicTranslation(classroom.numStudents) }} <!-- 1 student vs 2 students -->
                    <!-- <div>{{classroom.curricShort}}</div> -->
                  </div>
                </div>
                <div class="button-container">
                  <button class="button is-fullwidth btn-select-class" [disabled]="isClickBufferActive" (click)="selectClassroom(classroom)">
                    <span *ngIf="classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                    <span *ngIf="!classroom.isAssigned"><tra [slug]="getSelectClassroomLabel(classroom)"></tra></span>
                  </button>
                  <button *ngIf="isManagingClassrooms" class="button is-danger" [disabled]="isClickBufferActive" (click)="archiveClassroom(classroom)"><tra slug="lbl_archive"></tra></button>
                </div>
              </ng-container>
          </div>
          <div *ngIf="false" class="btn-new-classroom" (click)="openNewClassroomModal()">
            <i class="icon fa fa-plus" aria-hidden="true"></i>
            <div class="caption"><tra slug="new_group"></tra> </div>
          </div>
        </div>


      </div>
      <label class="checkbox">
        <input 
          type="checkbox" 
          [(ngModel)]="isPastWindowsShown"
          (change)="loadClassrooms()"
        >
        <tra slug="lbl_previous_windows"></tra> 
      </label>
        
    </div>
    <div class="dashboard-overlays" *ngIf="activeOverlay">
      <div class="dashboard-overlay-screen" (click)="closeActiveOverlay()"></div>
      <div class="dashboard-overlay-window">
        <div class="dashboard-overlay-header">
          <div class="overlay-title" *ngIf ='isCreateSessionOverlays()'><tra slug="g9_create_new_assess"></tra></div>
          <div class="overlay-title" *ngIf ='isAddRemoveInvigilatorOverlays()'><tra slug="ta_add_remove_invigilators"></tra></div>
          <div class="overlay-close" (click)="closeActiveOverlay()">
            <i class="fa fa-times" aria-hidden="true"></i>
          </div>
        </div>
        <div class="dashboard-overlay-content">
          <div [ngSwitch]="activeOverlay">
            <div *ngSwitchCase="OVERLAYS.NEW_CLASSROOM" class="dashboard-overlay-account">
              <div class="field is-horizontal" *ngIf="false">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_curriculum"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field select">
                    <select>
                      <option selected>{{whitelabelService.getSiteText('DEFAULT_CURRICULUM', '(Default Curriculum)')}}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_name"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input class="input" type="text" [formControl]="newClassName" placeholder={{inputValue}}>
                  </div>
                </div>
              </div>
              <div *ngIf="false">
                <button [disabled]="!newClassName.value || isAddingNewClassroom" (click)="createNewClassroom()" class="button is-success is-fullwidth "><tra slug="create_group"></tra></button>
              </div>
            </div>
            <div *ngSwitchCase="OVERLAYS.NEW_ASSESSMENT" class="dashboard-overlay-account">
              <view-assessment-new 
                [activeClassroom]="activeClassroom" 
                [isFreeStart]="isFreeStart(classroomId)"
                [classId]="classroomId"
                (getActive)="getActiveSessions($event)"
                [assessmentSlug]="assessmentSlug"
              ></view-assessment-new>
            </div>
            <div *ngSwitchCase="OVERLAYS.ACTIVE_ASSESSMENT" class="dashboard-overlay-account">
              <view-assessment-active 
                [activeSubSessions]="activeSubSessions"
                [completedSubSessions]="completedSubSessions" 
                [subSessions]="subSessions" 
                [studentList]="activeStudents()"
                (confirmEvent)="createNewAssessmentSession($event)"
                [selectedSession]="config"
                (back)="openNewAssessmentModal()"
              ></view-assessment-active>
            </div>
            <div *ngSwitchCase="OVERLAYS.ASSESSMENT_SCHEDULER" class="dashboard-overlay-account">
              <view-assessment-scheduler 
                [state] = "{config: config, sessionDetails: sessionDetails, schoolClassId: activeClassroom.id, isFIClass: activeClassroom.is_fi}"
                (back)="openActiveAssessmentModal()"
                (scheduleEvent)="finalizeAssessmentCreation($event)"
              >
              </view-assessment-scheduler>
            </div>
            <div *ngSwitchCase="OVERLAYS.ADD_REMOVE_INVIGILATORS" class="dashboard-overlay-account">
              <div *ngIf = "loadingSchoolTeachers">
                <tra slug = "loading_caption"></tra>
              </div>
              <div *ngIf = "!loadingSchoolTeachers">
                <tra-md slug="ta_add_remove_invig_desc"></tra-md>
                <div style = "height:30em; overflow:auto;">
                  <table>
                    <tr>
                        <th style="width:2em;"></th>
                        <th><tra slug="ta_teacher_list_name_col"></tra></th>
                        <th><tra slug="ta_teacher_list_email_col"></tra></th>
                    </tr>
                    <tr *ngFor="let teacher of schoolTeachers">
                        <td><input type ="checkbox" [checked] = "teacher.selected" (change) = "toggleAddRemoveInvigilator(teacher)"></td>
                        <td>{{teacher.name}}</td>
                        <td>{{teacher.email}}</td>
                    </tr>
                  </table>
                </div>   
                <button class="button is-info invig-ok-btn" (click)="closeActiveOverlay()"><tra [slug]="'btn_ok'"></tra></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
 
  <footer [hasLinks]="true"></footer>
</div>

