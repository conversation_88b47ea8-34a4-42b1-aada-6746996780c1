<div *ngIf="!isManagingStudents && isPJOperationalTest()" class="student-scanning-summary-container">
  <div class="live-view-container bordered-container">
    <div class="row-container">
      <div>
        <b><tra slug="pj_title_missing_scans"></tra>&nbsp;=&nbsp;<span>{{ getMissingUnconfirmedScansNum()['missingScans'] }}</span></b>
      </div>
      <div>
        <b><tra slug="pj_title_unconfirmed_scans"></tra>&nbsp;=&nbsp;<span>{{ getMissingUnconfirmedScansNum()['unconfirmedScans'] }}</span></b>
      </div>
    </div>
  </div>
  <div class="scan-icon-legend-container bordered-container">
    <div class="left-container">
      <div class="text-icon-container">
        <div>
          <i class="fas fa-qrcode"></i>
          <tra slug="lbl_scan_required"></tra>
        </div>
      </div>
      <div class="text-icon-container">
        <div>
          <i class="far fa-file-pdf"></i>
          <tra slug="lbl_scan_uploaded"></tra>
        </div>
      </div>
      <div class="text-icon-container">
        <div>
          <i class="far fa-file-pdf needs-reupload"></i>
          <tra slug="lbl_scan_needs_reupload"></tra>
        </div>
      </div>
    </div>
    <div class="right-container">
      <div class="text-icon-container">
        <div>
          <i class="fas fa-check-square"></i>
        <tra slug="lbl_scan_confirmed"></tra>
        </div>
      </div>
      <div class="text-icon-container">
        <div>
          <i class="fas fa-check" style="color: #087000;"></i>
          <tra slug="lbl_online_session_submitted"></tra>
        </div>
      </div>
      <div class="text-icon-container"></div>
    </div>
  </div>
</div>
<table>
  <tr>
    <!-- <th style="width: 2em;" *ngIf="!isManagingStudents  && false"> <table-row-selector [entry]="studentSelections" prop="isAllSelected" (toggle)="toggleSelectAll()"></table-row-selector> </th> -->
    <!-- OEN COLUMN -->
    <th *ngIf="!isSasnLogin" style="width: 2em;"><tra slug="lbl_oen"></tra></th>
    <th *ngIf="isSasnLogin" style="width: 2em;"><tra slug="lbl_sasn"></tra></th>
    
    <!-- STUDENT COLUMN -->
    <!-- <th ><tra slug="lbl_student_1"></tra> </th> -->
    <th>
      <a (click)="updateSorting('first_name')">
        <tra slug="lbl_first_name"></tra> 
        <span  class="sort-caret">
          <i 
            class="fas" 
            [ngClass]="{
              'fa-sort-up': sorting.direction === 'DESC' && sorting.fieldName === 'first_name',
              'fa-sort-down': sorting.direction === 'ASC' && sorting.fieldName === 'first_name', 
              'fa-sort': sorting.fieldName !== 'first_name'
            }"
          ></i>
        </span>
      </a>
      / 
      <a (click)="updateSorting('last_name')">
        <tra slug="lbl_last_name"></tra> 
        <span class="sort-caret">
          <i 
            class="fas" 
            [ngClass]="{
              'fa-sort-up': sorting.direction === 'DESC' && sorting.fieldName === 'last_name',
              'fa-sort-down': sorting.direction === 'ASC' && sorting.fieldName === 'last_name', 
              'fa-sort': sorting.fieldName !== 'last_name'
            }"
          ></i>
        </span>
      </a>
    </th>
    <th *ngIf="isOnlineStatusShow"><tra slug="lbl_status" ></tra></th>
    <th *ngIf="isManagingStudents"><tra slug="lbl_tooltip_accommodations" ></tra></th>
    <th *ngIf="isManagingStudents"><tra slug="lbl_tooltip_exempt" ></tra></th>
    <th
      *ngFor="let subSession of subSessions; let subSessionIndex = index;"
      class="session-button-container"
      [class.is-locked]="getOverallSubSessionLockState(subSessionIndex)"
    >
      <!--  G9/OSSLT - (Click the button below to open/close the session for all students) text   -->
      <div class="lock-instr-para" *ngIf="isG9OrOsslt()">
        <div class="case-locked"><tra-md slug="lbl_click_unlock"></tra-md></div>
        <div class="case-unlocked"><tra-md slug="lbl_click_lock"></tra-md></div>
      </div>
      <div>
        <div class="tooltip-column" style="font-weight: 600; text-align: center;" *ngIf="isPrimaryOrJunior()">  
          {{getSessionSlugOne(subSession.slug)}}          <!-- PJ: L-A, L-B, etc column label -->
            <span *ngIf="isPJOperationalTest()" class="tooltiptext-column">{{lang.tra(getSessionSlugTwo(subSession.slug))}}</span>    <!-- PJ: Language Session A, B, C, D, Mathematics Stage 1, 2, 3, 4 etc column label -->
            <span  *ngIf="isPJSampleTest()" class="tooltiptext-column-pj-sample">{{lang.tra(getSessionSlugTwo(subSession.slug))}}</span>  <!-- PJ: Language Session A, B, C, D, Mathematics Stage 1, 2, 3, 4 etc column label -->
        </div>
        <div class="header-btns" [ngClass] = "{'session-button-center' : isPrimaryOrJunior()}">
          <!-- G9/OSSLT: Header Lock/Unlock Button  -->
          <button *ngIf = "isG9OrOsslt()"
                  (click)="checkOverallSubSessionLockForAll(subSessionIndex)"
                  class="button session-button is-info session-width"
                  [class.is-outlined]="getOverallSubSessionLockState(subSessionIndex)"
                  [disabled]="disableOverallButton(subSessionIndex) || isUmsubmitting()"
                  [attr.aria-label]="lang.tra('lbl_session_lock_toggle')"
          >
              <span>
                <span class="case-locked"> <i class="fas fa-lock "></i> </span>
                <span class="case-unlocked"> <i class="fas fa-lock-open"></i> </span>
              </span>
              <span class="session-name">
                <div *ngIf = "isG9Sample()">{{lang.tra("g9_sample_test")}}</div>
                <div *ngIf = "!isG9Sample()">{{lang.tra(subSession.slug)}}</div>
              </span>
              <span>&nbsp;</span>
          </button>
          <!-- PJ: Header Lock/Unlock Button with tooltips -->
          <button *ngIf = "isPrimaryOrJunior()"
                  (click)="checkOverallSubSessionLockForAll(subSessionIndex)"
                  class="button session-button is-info"
                  [class.is-outlined]="getOverallSubSessionLockState(subSessionIndex)"
                  [disabled]="disableOverallButton(subSessionIndex) || isUmsubmitting()"
                  [attr.aria-label]="lang.tra('lbl_session_lock_toggle')"
          >
            <span>
              <span class="case-locked" 
                [tooltip]="lang.tra('pj_lbl_click_unlock')" 
                [options]="tooltipOptions" 
                [attr.aria-label]="lang.tra('pj_lbl_click_unlock')"
              >
                <i class="fas fa-lock"></i>
              </span>
              <span 
                class="case-unlocked" 
                [tooltip]="lang.tra('pj_lbl_click_lock')" 
                [options]="tooltipOptions" [attr.aria-label]="lang.tra('pj_lbl_click_lock')"
              > 
                <i class="fas fa-lock-open"></i> 
              </span>
            </span>
          </button>
        </div>
      </div>
    </th>
  </tr>
  <!-- Use Pipe for sorting -->
  <tr *ngFor="let student of studentList | sort:sorting.fieldName :sorting.direction ==='DESC' let i = index">
    <!-- <td *ngIf="!isManagingStudents && false">
      <table-row-selector [entry]="student" prop="__isSelected" (toggle)="onToggleSelectStudent()"></table-row-selector>
    </td> -->
    <td>
      <div style="position:relative">
        <div style="display: flex; flex-direction: row; align-items: center;">
          <button *ngIf="!student.isShowingSecretId && !student.from_sasn_schl" (click)="student.isShowingSecretId = true" class="button is-small is-white show-oen-button">
            <tra slug="show_oen"></tra>
          </button>
          <button *ngIf="student.isShowingSecretId && !student.from_sasn_schl" (click)="student.isShowingSecretId = false" class="button is-small is-info show-oen-button">
            {{student.eqao_student_gov_id}}
          </button>
          <button *ngIf="!student.isShowingSecretId && ( student.from_sasn_schl || student.eqao_student_gov_id === '000000000' )" (click)="student.isShowingSecretId = true" class="button is-small is-white show-oen-button">
            <tra slug="show_sasn"></tra>
          </button>
          <button *ngIf="student.isShowingSecretId && ( student.from_sasn_schl || student.eqao_student_gov_id === '000000000' )" (click)="student.isShowingSecretId = false" class="button is-small is-info show-oen-button">
            {{student.SASN}}
          </button>
          <button *ngIf="haveLoginIssue(student.id)" (click)= "showStudentLoginIssueModal(student)" class="button"><i class="fa fa-exclamation is-danger "></i></button>
        </div>
      </div>
    </td>
    <td style="position:relative">
      <div style="display: flex; flex-direction: row; align-items: center;">
        <span *ngIf="student.is_guest == 1" class="tag guest-student"><tra slug="ta_lbl_guest_student"></tra></span>
        &nbsp; &nbsp;
        <div class="name-container">
          <a style="margin-right: 0.5em" href="#" (click)="showStudentDetails(i); $event.preventDefault(); ">{{student.first_name}} {{student.last_name}}</a>
          <img  *ngIf="getStudentAccommodations(i).length > 0 || getStudentAssistiveTechs(i).length > 0" class="accommodation-icon" [src]="lang.tra('accommodation_icon')" (click)="showStudentDetails(i)" [tooltip]="lang.tra('lbl_tooltip_accommodations')" [options]="tooltipOptions"/>
          <img *ngIf="getStudentExemptions(i).length > 0" class="exempt-icon" [src]="lang.tra('exemption_icon')" (click)="showStudentDetails(i)" [tooltip]="lang.tra('lbl_tooltip_exempt')" [options]="tooltipOptions"/>
        </div>
        <i *ngIf="!isManagingStudents && hasOldTestForm(student)" (click)="updateStudentTestForm(student)" class="fas fa-exclamation-triangle" style="color: #f5bb59; margin-left: 0.25em; cursor: pointer"></i>
        <ng-container *ngIf="showResponseType()">
          <i *ngIf="isWritingPaper(student)" (click)="showStudentDetails(i)" class="fas fa-pencil-alt response-type-icon" [tooltip]="lang.tra('scan_paper_response')" [options]="tooltipOptions"></i>
          <i *ngIf="!isWritingPaper(student)" (click)="showStudentDetails(i)" class="fas fa-keyboard response-type-icon" [tooltip]="lang.tra('scan_online_response')" [options]="tooltipOptions"></i>
        </ng-container>
        <ng-container *ngIf="isStudentPaid(student)">
          <i class="fas fa-check response-type-icon" [tooltip]="lang.tra('sa_payment_status_paid')" [options]="tooltipOptions"></i>
        </ng-container>
      </div>
      <div *ngIf="!isManagingStudents" class="student-online-status">
        <span *ngIf="isStudentOnline(student)"> <i class="fas fa-circle online"></i><tra slug="lbl_online"></tra></span>
        <span *ngIf="!isStudentOnline(student) && IS_OFFLINE_INDICATOR_ENABLED"> <i class="fas fa-circle offline"></i><tra slug="lbl_offline"></tra></span>
      </div>
      <div *ngIf="!isManagingStudents && isStudentWarning(student) && !isStudentPaused(student)" (click)="openSoftLockNotification(student)"  class="lock-notification">
        <i class="fas fa-exclamation-circle"></i>
      </div>
      <div *ngIf="!isManagingStudents && isStudentPaused(student)" (click)="openAndDismissUnpauseNotification(student)" class="pause-notification">
        <i class="far fa-pause-circle"></i>
      </div>
    </td>
    <td *ngIf="isOnlineStatusShow"> </td>
    <td *ngIf="isManagingStudents">
      <div *ngFor="let accommodation of getStudentAccommodations(i)">
        <li><tra [slug]="getAccGroupSlug(accommodation)"></tra>(<tra [slug]="getAccommodationSlug(accommodation)"></tra>)</li>
      </div>
      <div *ngFor="let assistiveTech of getStudentAssistiveTechs(i)">
        <li><tra [slug]="getAccGroupSlug(assistiveTech)"></tra>(<tra [slug]="getAssistiveTechSlug(assistiveTech)" ></tra>)</li>
      </div>
    </td>
    <td *ngIf="isManagingStudents">
      <div *ngFor="let exemption of getStudentExemptions(i)" >
        <tra [slug]="getExemptionSlug(exemption)" ></tra>
      </div>
    </td>
    <td
      *ngFor="let subSession of subSessions; let subSessionIndex = index;"
      class="session"
      [class.is-active]="isStudentSubSessionActive(student, subSessionIndex)"
      [class.is-notstarted]="isStudentSubSessionNotStarted(student, subSessionIndex)"
      [class.is-submitted]="isStudentSubSessionSubmitted(student, subSessionIndex)"
      [class.is-session-locked]="getStudentSessionLockState(student, subSessionIndex)"
    >
      <div
        class="session-info"
        [ngClass] = "{'session-button-center' : isPrimaryOrJunior()}"
        *ngIf="!isStudentSubSessionUpcoming(student, subSessionIndex) && canSubSessionStart(subSessionIndex)"
      >
        <!-- <span class='tooltiptext-column' *ngIf = "isPrimaryOrJunior() && (!isStudentSubSessionSurpassed(student, subSessionIndex) || isStudentSubSessionSubmitted(student, subSessionIndex) || getStudentQuestionResponseInfo(student, subSessionIndex) !== studentQuestionResponseInfoStatusType.None)">
          <tra [slug]="getStudentSubSessionStatus(student, subSessionIndex)"></tra>
        </span> -->
        <div *ngIf = "!showUnsubmit(student,subSessionIndex) || !isUnsubmitBtnVisiable!">         
          <button
            *ngIf="!isOtherSessionActive(student,subSessionIndex) && isStudentScanInfoMapInitialized()"
            class="button"
            (click)="checkStudentSubSessionLock(student, subSessionIndex)"
            [ngSwitch]="isStudentSubSessionSurpassed(student, subSessionIndex)"
            [attr.aria-label]="lang.tra('lbl_session_lock_toggle')"
            [tooltip]="lang.tra(getStudentSubSessionStatus(student, subSessionIndex))"
            [options]="isPrimaryOrJunior() && (!isStudentSubSessionSurpassed(student, subSessionIndex) || isStudentSubSessionSubmitted(student, subSessionIndex) || getStudentQuestionResponseInfo(student, subSessionIndex) !== studentQuestionResponseInfoStatusType.None) ? tooltipOptions : {display: false}"
            [disabled]="isUmsubmitting()"
          >  
            <span *ngSwitchCase="false">
              <i class="fas fa-lock-open" ></i>
              <i class="fas fa-lock"></i>  
            </span>
            <ng-container *ngSwitchCase="true">
              <ng-container [ngSwitch]="getStudentQuestionResponseInfo(student, subSessionIndex)">
                <span *ngSwitchCase="studentQuestionResponseInfoStatusType.None">
                  <span [ngSwitch]="!!isStudentSubSessionSubmitted(student, subSessionIndex)">
                    <span *ngSwitchCase="true">
                      <ng-container>
                        <i  *ngIf="!isPJMathSession(subSession.slug) && subSession.slug !== 'session_q' && isPJOperationalTest()" 
                            class="fas fa-qrcode" style="color:rgb(1, 1, 49); font-size:1.2em;"
                        ></i>
                        <i  *ngIf="isPJMathSession(subSession.slug) || subSession.slug == 'session_q' || !isPJOperationalTest()" 
                            class="fas fa-check"
                        ></i>
                      </ng-container>
                    </span>
                    <span *ngSwitchCase="false">
                      <i class="fas fa-lock"></i>
                    </span>  
                  </span>
                </span>
                <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanConfirmed">
                  <i class="fas fa-check-square"></i>
                </span>
                <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanUploaded">
                  <i class="far fa-file-pdf"></i>
                </span>
                <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanNeedsReupload">
                  <i class="far fa-file-pdf needs-reupload"></i>
                </span>
                <span *ngSwitchCase="studentQuestionResponseInfoStatusType.Online">
                  <span [ngSwitch]="!!isStudentSubSessionSubmitted(student, subSessionIndex)">
                    <span *ngSwitchCase="true">
                      <i class="fas fa-check"></i>
                    </span>
                    <span *ngSwitchCase="false">
                      <i class="fas fa-lock"></i>
                    </span>  
                  </span>
                </span>
              </ng-container>
            </ng-container>
          </button>
        </div>
          <!-- <span *ngSwitchCase="true">
              <span [ngSwitch]="!!isStudentSubSessionSubmitted(student, subSessionIndex)">
                <i *ngSwitchCase="true" class="fas fa-check"></i>  
              </span>
          </span>
        </button> -->
        <div *ngIf="!isOtherSessionActive(student,subSessionIndex) && isG9OrOsslt()" class="session-status" style="line-height:1.02em; text-align: center;">
          <tra [slug]="getStudentSubSessionStatus(student, subSessionIndex)"></tra>
          <div style="font-size:0.8em; text-align: center;">
            {{getStudentSessionTime(student, subSessionIndex)}}
          </div>
        </div>
        <button 
          type="button" 
          *ngIf="showUnsubmit(student,subSessionIndex) && isUnsubmitBtnVisiable" 
          [tooltip]="lang.tra('lbl_unsubmit_test_attempt_sub_session')" 
          [options]="tooltipOptions"
          [attr.aria-label]="lang.tra('lbl_unsubmit_test_attempt_sub_session')" 
          class = "button id--btn-unsubmit" 
          (click)="verifyUnsubmit(student,subSessionIndex)"
        >
          <img src="../../../assets/icons/unsubmit.svg" class="unsubmit-img" alt="lang.tra('lbl_unsubmit_test_attempt_sub_session')">
        </button>
        <!-- <div *ngIf="isOtherSessionActive(student,subSessionIndex)" style="display:flex; flex-direction:column; justify-items: center; align-items: center;">
          <tra [slug]="getStudentSubSessionStatus(student, subSessionIndex)"></tra>
          <button class="cancel-btn" (click)="cancelOtherSubSession(student,subSessionIndex)">Cancel Other Sub-Session</button>
        </div> -->
        <div *ngIf="isG9OrOsslt()">&nbsp;</div>
      </div>

      <!-- Session has schedulled -->
      <div
        class="session-info-scheduled"
        *ngIf="!canSubSessionStart(subSessionIndex) && !isOtherSessionActive(student,subSessionIndex) && !isStudentSubSessionSubmitted(student,subSessionIndex)"
      >
        <button *ngIf = "isPrimaryOrJunior() && getPJSubSessionScheduledDate(subSessionIndex)" 
          class="button"
          [tooltip]="lang.tra('lbl_pj_starts_on', null, getPJSubSessionScheduledDate(subSessionIndex))" 
          [options]="tooltipOptions" 
          [attr.aria-label]="lang.tra('lbl_pj_starts_on', null, getPJSubSessionScheduledDate(subSessionIndex))"
        >
          <i class="fa fa-clock" style="color: rgb(58, 58, 58);"></i>
        </button>
        
        <div *ngIf="isG9OrOsslt()">
          <tra-md slug="lbl_starts_on" [props]="getSubSessionScheduledTime(subSessionIndex)"></tra-md>
        </div>
        <div *ngIf="isG9OrOsslt()">&nbsp;</div>
      </div>

      <div
        class="session-info"
        [ngClass] = "{'session-button-center' : isPrimaryOrJunior()}"
        *ngIf="!canSubSessionStart(subSessionIndex) && (isOtherSessionActive(student,subSessionIndex) || isStudentSubSessionSubmitted(student,subSessionIndex))"
      >
        <button
          *ngIf="!isOtherSessionActive(student,subSessionIndex) && isStudentScanInfoMapInitialized()"
          class="button"
          (click)="checkStudentSubSessionLock(student, subSessionIndex)"
          [disabled]="isStudentSubSessionSurpassed(student, subSessionIndex) || isUmsubmitting()"
          [ngSwitch]="isStudentSubSessionSurpassed(student, subSessionIndex)"
          [tooltip]="lang.tra(getStudentSubSessionStatus(student, subSessionIndex))"
          [options]="tooltipOptions"
          [attr.aria-label]="lang.tra('lbl_session_lock_toggle')"
        >
          <span *ngSwitchCase="false">
            <i class="fas fa-lock-open" ></i>
            <i class="fas fa-lock"></i>  
          </span>
          <!-- <span *ngSwitchCase="true">
            <span [ngSwitch]="!!isStudentSubSessionSubmitted(student, subSessionIndex)">
              <i *ngSwitchCase="true" class="fas fa-check"></i>  
               <span class="tooltip-exclamation">
                <i *ngSwitchCase="false" class="fas fa-exclamation-triangle"></i>
                <span class="tooltiptext-exclamation"><tra slug="lbl_student_not_submitted"></tra></span>
              </span> 
            </span>
          </span> -->
          <ng-container *ngSwitchCase="true">
            <ng-container [ngSwitch]="getStudentQuestionResponseInfo(student, subSessionIndex)">
              <span *ngSwitchCase="studentQuestionResponseInfoStatusType.None">
                  <span [ngSwitch]="!!isStudentSubSessionSubmitted(student, subSessionIndex)">
                    <span *ngSwitchCase="true">
                        <ng-container>
                          <i  *ngIf="!isPJMathSession(subSession.slug) && subSession.slug !== 'session_q' && subSession.slug !== 'lang_session_d' && isPJOperationalTest()" 
                              class="fas fa-qrcode" style="color:rgb(1, 1, 49); font-size:1.2em;"
                          ></i>
                          <i  *ngIf="isPJMathSession(subSession.slug) || subSession.slug == 'session_q' || subSession.slug == 'lang_session_d' || !isPJOperationalTest()" 
                              class="fas fa-check"
                          ></i>
                        </ng-container>
                    </span>
                    <span *ngIf="isG9OrOsslt()" class="tooltip-exclamation">
                        <i *ngSwitchCase="false" class="fas fa-exclamation-triangle"></i>
                        <span class="tooltiptext-exclamation"><tra slug="lbl_student_not_submitted"></tra></span>
                    </span>
                  </span>
              </span>
              <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanConfirmed">
                  <i class="fas fa-check-square"></i>
              </span>
              <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanUploaded">
                  <i class="far fa-file-pdf"></i>
              </span>
              <span *ngSwitchCase="studentQuestionResponseInfoStatusType.ScanNeedsReupload">
                <i class="far fa-file-pdf needs-reupload"></i>
              </span>
              <span *ngSwitchCase="studentQuestionResponseInfoStatusType.Online">
                <i class="fas fa-check"></i>
              </span>
            </ng-container>
          </ng-container>
        </button>
        <div *ngIf="isG9OrOsslt() && !isOtherSessionActive(student,subSessionIndex)" class="session-status"  style="line-height:1.02em; text-align: center;">
          <tra [slug]="getStudentSubSessionStatus(student, subSessionIndex)"></tra>
          <div style="font-size:0.8em; text-align: center;">
            {{getStudentSessionTime(student, subSessionIndex)}}
          </div>
        </div>
        <button 
          type="button" 
          *ngIf="showUnsubmit(student,subSessionIndex) && isUnsubmitBtnVisiable" 
          [tooltip]="lang.tra('lbl_unsubmit_test_attempt_sub_session')" 
          [options]="tooltipOptions"
          [attr.aria-label]="lang.tra('lbl_unsubmit_test_attempt_sub_session')" 
          class = "button" 
          (click)="verifyUnsubmit(student, subSessionIndex)"
        >
          <img src="../../../assets/icons/unsubmit.svg" class="unsubmit-img" alt="lang.tra('lbl_unsubmit_test_attempt_sub_session')">
        </button>
        <!-- <div *ngIf="isOtherSessionActive(student,subSessionIndex)" style="display:flex; flex-direction:column; justify-items: center; align-items: center;">
          <tra [slug]="getStudentSubSessionStatus(student, subSessionIndex)"></tra>
          <button class="cancel-btn" (click)="cancelOtherSubSession(student,subSessionIndex)">Cancel Other Sub-Session</button>
        </div> -->
        <div>&nbsp;</div>
      </div>
    </td>
  </tr>

</table>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="TeacherModal.REVIEW_CURR_SS_STATUS_MODAL" style="width: 40em;">
        <t-modal-review-current-subsession-status (studentMapEmitter)="initStudentSSSubmissionMap($event)" [forAll]="ssReviewForAll" [ssIndex]="subsessionIndToReview" [studentStates]="studentStates" [studentList]="studentsToReviewSS"></t-modal-review-current-subsession-status>
      </div>
      <div *ngSwitchCase="TeacherModal.UNSUBMIT_STUDENT_MODAL_PREPAGE" style="width: 35em;">
        <tra-md slug = "lbl_unsubmit_tass_confirm" [props] = "cmc().props"></tra-md>
        <br>
        <br>
        <br>
        <div style="display: flex; justify-content: flex-end;">
          <button class="button btn-width is-info" (click)="unsubmitStudentModalPreContinue()"><tra slug="btn_continue"></tra></button>
          <button class="button btn-width" (click)="unsubmitStudentModalPrePageFinish()"><tra slug="btn_cancel"></tra></button>
        </div>
      </div>
      <div *ngSwitchCase="TeacherModal.UNSUBMIT_STUDENT_MODAL" style="width: 35em;">
        <t-modal-unsubmit-student 
          [config]="cmc()"
          (unsubmitConfirmed)="unsubmitStudentModalFinish($event)"
          (unsubmitCanceled)="pageModal.closeModal();"
        ></t-modal-unsubmit-student>
      </div>
    </div>
    <modal-footer *ngIf="cModal().type !== TeacherModal.UNSUBMIT_STUDENT_MODAL && cModal().type !== TeacherModal.UNSUBMIT_STUDENT_MODAL_PREPAGE" class="dont-print" [pageModal]="pageModal" ></modal-footer>
  </div>
</div>
