import { Component, OnInit, Input, Output, EventE<PERSON>ter, On<PERSON>estroy, SimpleChanges, OnChanges } from "@angular/core";
import { ListSelectService, ListSelector } from "../../ui-partial/list-select.service";
import { IStudentAccount } from "../../ui-schooladmin/data/types";
import { etz, mtz } from "../../core/util/moment";
import { ClassroomsService } from "../../core/classrooms.service";
import { LoginGuardService } from "../../api/login-guard.service";
import { LangService } from "../../core/lang.service";
import { IStudentLoginIssue, StudentG9ConnectionService } from "src/app/ui-student/student-g9-connection.service";
import * as moment from "moment-timezone";
import { PageModalController, PageModalService } from "src/app/ui-partial/page-modal.service";
import { AuthService } from "src/app/api/auth.service";
import { RoutesService } from "src/app/api/routes.service";
import { ASSESSION_SLUG_MAPPING, ASSESSMENT, IStudent, TeacherModal} from '../data/types';
import { G9DemoDataService, overlapVariables } from "src/app/ui-schooladmin/g9-demo-data.service";
import { OnlineOrPaperService } from "src/app/ui-testrunner/online-or-paper.service";
import { ScanInfoService } from "../scan-info.service";
import { CURRICSHORT } from "../view-invigilate/view-invigilate.component";
import { reverse } from "cypress/types/lodash";
import { Direction } from "src/app/ui-testrunner/models";
import { sortBy } from "cypress/types/lodash";
import { getActivatedAccommodations, getActivatedAssistiveTechs, getActivatedExemptions } from "src/app/ui-schooladmin/data/mappings/accommodations";
import { WhitelabelService } from "src/app/domain/whitelabel.service";
import { Accom_Group_Slug_Mapping } from "../data/mappings";

interface ISubSessionRecord {
  duration_hours: any;
  duration: any;
  date_time_start: any;
  caption: string;
  id: number;
  order: number;
  sections_allowed: number[];
  slug: string;
  twtdar_order: number;
  is_last: number;
}

interface IReviewSSModalConfig {
  studentStates: any,
  studentList: any,
  subSessionIndex: number,
  isClosing: boolean,
  isAll: boolean
}

export interface IStudentState {
  is_paused: number;
  uid;
  attempt_id;
  active_sub_session_id;
  last_touch_on;
  section_index;
  question_index;
  question_caption;
  is_submitted;
  hasOldTestForm;
  canUpdateTestForm;
  // isOnline?:boolean, // appended
  subSessions: Array<{
    is_submitted: number;
    last_locked_on: string;
    started_on: string;
    last_touch_on: string;
    subtracted_time: number;
    num_responses: number;
    sections_allowed: number[];
    subsession_slug?: string;
    _timeSpent?: string; // mutate
    tass_id?: number;
  }>;
  is_ta_unsubmit_pending;
}
@Component({
  selector: "student-list",
  templateUrl: "./student-list.component.html",
  styleUrls: ["./student-list.component.scss"],
})
export class StudentListComponent implements OnInit, OnChanges {
  @Input() studentList: IStudentAccount[];
  @Input() closeAllSubSessions: boolean;
  @Input() isSessionOpened: boolean;
  @Output() selectionUpdate = new EventEmitter();
  @Output() updateStudentSoftLock = new EventEmitter();
  isDismissedSessionANotfifcation: boolean = false;
  isDismissedSessionBNotification: boolean = false;
  isDismissedSessionBNotificationWarning: boolean = false;
  isDismissedSessionANotfifcationWarning: boolean = false;
  @Output() isAllSubSessionsClosed = new EventEmitter();
  @Output() resetSoftLock = new EventEmitter();
  @Output() openStudentInfo = new EventEmitter();
  @Output() openEditModal = new EventEmitter();
  @Output() openSoftlockModal = new EventEmitter();
  @Output() openUnpauseModal = new EventEmitter();
  @Output() openStudentLoginIssueModal = new EventEmitter();

  /////////////////
  @Input() isManagingStudents: boolean;
  /////////////////
  @Input() testSessionId: number;
  @Input() classId: number;
  @Input() activeSession;
  @Input() subSessions: ISubSessionRecord[] = [];
  @Input() activeSubSessions;
  @Input() activeClassroom;
  @Input() completedSubSessions;
  @Input() studentStates: { [key: number]: IStudentState };
  @Input() studentSocketState: { [key: number]: any };
  @Input() studentLoginIssueList: IStudentLoginIssue[] = [];
  @Input() isNoTdOrder: boolean;
  @Output() locksUpdate = new EventEmitter();
  @Output() reloadSessionInfo: EventEmitter<any> = new EventEmitter();
  @Output() studentLockActionEmitter: EventEmitter<any> = new EventEmitter();
  @Input() isForceUnsubmitAbility: boolean;
  @Input() isUnsubmitBtnVisiable: boolean;
  @Output() isUnsubmitBtnVisiableChange = new EventEmitter<boolean>();
  @Input() asmtSlug;
  @Input() isSecreteUser;
  @Input() isSasnLogin;
  @Input() studentScanInfo;

  // Ann Sorting ticket - this is prop from the Parent component
  @Input() sorting: {fieldName: string, direction: string};
  @Input() updateSorting: (fieldUserClicked: string) => void; //void means it does not return anything



  studentsToReviewSS: IStudentAccount[];
  studentSSSubmissionMap: any;
  subsessionIndToReview: number;
  ssReviewForAll: boolean = false;
  isSoftLockReset: boolean = false;
  isSessionAlocked = true;
  // isSessionBlocked = false;
  isOnlineStatusShow = false;
  showSessionAEdit = false;
  showSessionBEdit = false;
  studentLockState = new Map();
  studentLockNotification = new Map(); // for warning icon
  studentPauseNotification = new Map(); // for paused icon
  studentSubSessionToRestore = new Map();
  studentIsLockingUnlockingStatus = new Map(); // for enabling/disabling lock button 
  overallSubSessionLockState = new Map();
  // studentComputedStates:{[key:number]:IStudentState};
  studentSelections: ListSelector;
  ticker: number;
  pageModal: PageModalController;
  currentStudent: IStudentAccount;
  assessment: ASSESSMENT;
  unsubmitedStudent: IStudentAccount = null;
  unsubmitedSubSessionIndex: number = null;

  IS_OFFLINE_INDICATOR_ENABLED = true
  CURRICSHORT = CURRICSHORT;

  tooltipOptions = {
    placement:"right",
    theme:"light", 
    animationDuration:200,
    offset: 12
  }
  TeacherModal = TeacherModal;
  constructor(
    private listSelectService: ListSelectService,
    private loginGuard: LoginGuardService, 
    private pageModalService: PageModalService, 
    public lang: LangService, 
    private classroomsService: ClassroomsService, 
    private studentG9Connection: StudentG9ConnectionService, 
    private auth: AuthService, 
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
    private onlineOrPaper: OnlineOrPaperService,
    private scanInfo: ScanInfoService,
    private whitelabelService: WhitelabelService,
    ) {}

  ngOnInit(): void {
    // this.studentSelections = this.listSelectService.defineNewSelectableList(this.studentList);
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.selectionUpdate.emit(this.studentSelections);
    this.checkAllStudentSessionState();
    this.updateOverallLockStates();
    this.refreshComputedState();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.studentStates) {
      this.refreshComputedState();
    }
    if (changes.closeAllSubSessions && changes.closeAllSubSessions.currentValue) {
      this.lockAllSubSessions();
    }
  }

  refreshComputedState() {
    if (!this.subSessions || !this.studentStates) {
      return;
    }
    // console.log('refreshComputedState')
    const nowTime = mtz().valueOf();
    let isAnyUnlocked: { [subSessionIndex: number]: boolean } = {};
    for (const [uid, studentState] of Object.entries(this.studentStates)) {
      // studentState.isOnline = false;
      // if (studentState.last_touch_on){
      //   const lastTouchTime = mtz(studentState.last_touch_on).valueOf();
      //   console.log(nowTime - lastTouchTime)
      //   if ((nowTime - lastTouchTime) < 60*1000){
      //     studentState.isOnline = true;
      //   }
      // }

      studentState.subSessions.forEach((studentSubSessionState, subSessionIndex: number) => {
        const isLocked = this.isStudentSessionLocked(studentState, subSessionIndex);
        if (!isLocked) {
          isAnyUnlocked[subSessionIndex] = true;
        }
        if (studentSubSessionState && studentSubSessionState.started_on) {
          let startTime = mtz(studentSubSessionState.started_on).valueOf();
          let endTime;
          if (studentSubSessionState.last_locked_on && isLocked) {
            const lockTime = mtz(studentSubSessionState.last_locked_on).valueOf();
            if (studentSubSessionState.last_touch_on && !studentSubSessionState.is_submitted) {
              const touchTime = mtz(studentSubSessionState.last_touch_on).valueOf();
              endTime = Math.min(lockTime, touchTime);
            } else {
              endTime = lockTime;
            }
          } else {
            endTime = nowTime;
          }
          const ms = endTime - startTime;
          const minutes = Math.max(Math.floor(ms / (1000 * 60)), 0);
          studentSubSessionState._timeSpent = minutes + " minute(s)";
        }
      });
    }
    this.subSessions.forEach((subSession, subSessionIndex) => {
      this.overallSubSessionLockState.set(subSessionIndex, !isAnyUnlocked[subSessionIndex]);
    });
  }
  
  showStudentDetails(studentIdx: number) {
    this.openStudentInfo.emit(studentIdx);
    // alert('Student info (including accessiblity info) will be available here, but is currently not enabled.')
  }
  
  showStudentLoginIssueModal(student: IStudentAccount){
    this.openStudentLoginIssueModal.emit(student);
  }

  updateOverallLockStates() {
    // this.subSessions.forEach((subSession, subSessionIndex) => {
    //   this.overallSubSessionLockState.set(subSessionIndex, true); // to do: should be based on summary of students
    // })
  }

  getOverallSubSessionLockState(subSessionIndex: number) {
    return this.overallSubSessionLockState.get(subSessionIndex);
  }

  getStudentUids() {
    return this.studentList.map(student => student.id);
  }

  disableOverallButton(subSessionIndex: number) {
    let subSessDateTimeStart
    if(this.subSessions[subSessionIndex].slug === 'session_q' && this.isPJOperationalTest()){
      subSessDateTimeStart = this.getEarliestPJOpSubSessStartDateForQ()
    }else{
      subSessDateTimeStart = this.subSessions[subSessionIndex].date_time_start
    }

    if (this.subSessions && subSessDateTimeStart) {
      if (this.verifyPastDate(subSessDateTimeStart)) {
        return false;
      }
    }
    return true;
  }

  isAllStudentOnline() {
    return this.studentList.every(student => {
      if (!this.studentSocketState[student.id]) {
        return false;
      }
      return true;
    });
  }

  anyStudentInDiffSession(subSessionIndex: number) {
    const sub_session_id = this.subSessions[subSessionIndex].id;
    for(const student of this.studentList) {
      const studentState = this.getStudentState(student);
      if(studentState.active_sub_session_id != null && studentState.active_sub_session_id != sub_session_id)  {
        return true;
      }      
    }
    return false;
  }

  /**
   * checks conditions that prevent overall sub session lock/unlock toggle 
   * also handles pausing other open PJ sessions to unlock overall sub session in current session
   */
  checkOverallSubSessionLockForAll(subSessionIndex: number) {
    if(this.g9DemoData.getIsUnsubmitting()) return //disable the button when unsubmitting
    const isClosing = !this.getOverallSubSessionLockState(subSessionIndex);

    // popup modal to confirm pausing of another on-going session to unlock this student
    //if another PJ session is active and unpaused, and toggle is opening lock, 
    if(this.isOtherSessionAlreadyActive() && this.isPrimaryOrJunior() && !isClosing) {
      const otherActiveSession = this.isOtherSessionAlreadyActive()
      const descriptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_operational") : this.lang.tra("invig_unlock_sample");
      const description = this.lang.tra("invig_unlock_caption", "", {ASSESSMENT: descriptionProps})
      const subCaptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_sample") : this.lang.tra("invig_unlock_operational");
      const subCaption = this.lang.tra("invig_unlock_subcaption", "", {ASSESSMENT: subCaptionProps.toLowerCase()})
      this.loginGuard.confirmationReqActivate({
        description,
        subCaption,
        width: '34em',
        btnProceedConfig: {caption: "invig_unlock_pause"},
        btnCancelConfig: {caption: "invig_unlock_go_back"},
        confirm: async () => {
          await this.auth.apiUpdate(this.routes.EDUCATOR_SESSION, otherActiveSession.test_session_id, {openAssessments: this.activeClassroom.openAssessments}, {query: { school_class_group_id: this.classroomsService.getClassroomById(this.classId).group_id}})
          const otherSession = this.activeClassroom.openAssessments.find(assessment => assessment.test_session_id == otherActiveSession.test_session_id)
          otherSession.is_paused = 1
          const isPjPausingOtherSession = true
          this.toggleOverallSubSessionLockForAll(subSessionIndex, isPjPausingOtherSession)
        },
        close: () => {
        }
      })
      // Prevent locking/unlocking of overall sub session if is not PJ session and another session is open 
    } else if(this.activeClassroom.openAssessments.length > 1 && !this.isPrimaryOrJunior()) {
      return this.loginGuard.confirmationReqActivate({
        caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_another_active_session_pj") : this.lang.tra("msg_another_active_session")// remove isPrimary check here
      });
    } else {
      this.toggleOverallSubSessionLockForAll(subSessionIndex,false)
    }
    //this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
  }

  /**
   * Toggles overall subsession lock state also handles pausing/unpausing other PJ open assessments
   */
  toggleOverallSubSessionLockForAll(subSessionIndex: number, isPjPausingOtherSession: boolean){
    const isClosing = !this.getOverallSubSessionLockState(subSessionIndex);
    let warnings = [];
    if (!isClosing) {
      const retrieveStudentSubsessionsToUnlock = () => {
        const studentsToUnlock = [];
        let requireSSReview = false;
        this.studentList.forEach(stu => {
          // const studentSubSessionState = this.getStudentSubSessionState(stu, subSessionIndex);
          const prevStudentSubSessionState = subSessionIndex > 0 ? this.getStudentSubSessionState(stu, subSessionIndex - 1) : this.getStudentSubSessionState(stu, subSessionIndex);
          const currStudentSubSessionState = this.getStudentSubSessionState(stu, subSessionIndex);
          const isUpcoming = this.isStudentSubSessionUpcoming(stu, subSessionIndex);
          // const isSurpassed = this.isStudentSubSessionSurpassed(stu, subSessionIndex);
          if ( prevStudentSubSessionState && !isUpcoming && !prevStudentSubSessionState.is_submitted) {
            requireSSReview = true;
            studentsToUnlock.push(stu);
          } else if (!isUpcoming && !currStudentSubSessionState.is_submitted) {
            studentsToUnlock.push(stu);
          }
        })
        return {studentsToUnlock, requireSSReview};
      }
      const {studentsToUnlock, requireSSReview} = retrieveStudentSubsessionsToUnlock();
      let pjUnlockConditions;
      if (this.isPJOperationalTest()) {
        pjUnlockConditions = subSessionIndex != 4
      } else if (this.isPJSampleTest()) {
        pjUnlockConditions = subSessionIndex != 2
      }
      if (studentsToUnlock.length > 0 && subSessionIndex != 0  && requireSSReview && ((this.isPrimaryOrJunior() && subSessionIndex != 8 && pjUnlockConditions) || (this.isG9OrOsslt() && subSessionIndex != 3))) {
        this.ssReviewForAll = true;
        this.reviewSubsessionProgressModalStart(studentsToUnlock, subSessionIndex, isClosing, true);
      }else{
        if (!isPjPausingOtherSession){
          this.loginGuard.confirmationReqActivate({
            caption: 'msg_unlock_all',
            confirm: () => {
              this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
            }
          });
        } else{
          this.setStudentsLocks(isClosing, this.studentList, subSessionIndex, false, isPjPausingOtherSession);
        }
      }
    } else if (isClosing && !isPjPausingOtherSession){
      this.loginGuard.confirmationReqActivate({
        caption: this.isG9OrOsslt() ? 'msg_lock_warning' : 'msg_lock_warning_pj',
        confirm: () => {
          this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
        }
      });
    } else{
      this.setStudentsLocks(isClosing, this.studentList, subSessionIndex, false, isPjPausingOtherSession);
    }
  }

  enforceOverallSubSessionLockForAll(subSessionIndex: number) {
    const isClosing = true;
    this.setStudentsLocks(isClosing, this.studentList, subSessionIndex);
  }

  lockAllSubSessions() {
    Promise.all(
      this.subSessions.map(async (subSession, idx) => {
        this.enforceOverallSubSessionLockForAll(idx);
      })
    ).then(res => this.isAllSubSessionsClosed.emit(true));
  }

  setStudentsLocks(isClosing: boolean, studentList: IStudentAccount[], subSessionIndex: number, isSubmitting = false, isPjPausingOtherSession = false) {
    const students = [];
    const effectiveClosers = [];
    if (isClosing) {
      studentList.forEach(student => {
        if (!this.getStudentSessionLockState(student, subSessionIndex)) {
          students.push(student);
        }
      });
    } else {
      studentList.forEach(student => {
        const isLocked = this.getStudentSessionLockState(student, subSessionIndex);
        const isSurpassed = this.isStudentSubSessionSurpassed(student, subSessionIndex);
        const isUpcoming = this.isStudentSubSessionUpcoming(student, subSessionIndex);
        const isSubmitted = this.isSubSessionSubmitted(student, subSessionIndex)
        if (isLocked && !isUpcoming && !isSubmitted) {
        // if (isLocked && !isSurpassed && !isUpcoming) {
          students.push(student);
          if (this.isStudentSubSessionEffectiveClosing(student, subSessionIndex)) {
            effectiveClosers.push(student);
          }
        }
      });
    }
    if (students.length === 0) {
      return;
    }
    // else if (effectiveClosers.length > 0){
    //   const subSession = this.subSessions[subSessionIndex];
    //   this.loginGuard.confirmationReqActivate({
    //     caption: this.lang.tra('msg_cnf_effective_close', null, {NUM_STUDENTS: effectiveClosers.length}),
    //     btnCancelCaption: this.lang.tra('lbl_cnf_effective_close_cancel'),
    //     btnProceedCaption: this.lang.tra('lbl_cnf_effective_close_proceed', null, {SESSION_CAPTION: subSession.caption}),
    //     confirm: () => this.patchStudentLocks(subSessionIndex, isClosing, students)
    //   })
    // }
    // else{
    //   this.patchStudentLocks(subSessionIndex, isClosing, students);
    // }
    this.patchStudentLocks(subSessionIndex, isClosing, students, isSubmitting, isPjPausingOtherSession);
  }
  private patchStudentLocks(subSessionIndex: number, isClosing: boolean, students: IStudentAccount[], isSubmitting: boolean, isPjPausingOtherSession:boolean) {
    const studentUids = students.map(s => s.uid);
    const subSession = this.subSessions[subSessionIndex];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return this.classroomsService
      .invigOpenCloseSubSessionForStudents(this.testSessionId, subSession.id, subSession.twtdar_order, studentUids, this.classId, isClosing, params, this.isPrimaryOrJunior(),isSubmitting)
      .then(res => {
        if (isClosing) {
          students.forEach(student => {
            const studentState = this.getStudentState(student);
            studentState.active_sub_session_id = null;
          });
        } else {
          const targetSubSessionId = this.subSessions[subSessionIndex].id;
          students.forEach(student => {
            const studentState = this.getStudentState(student);
            if (studentState) {
              studentState.active_sub_session_id = targetSubSessionId;
              const studentSocketState = this.getStudentSocketState(student)
              if(isPjPausingOtherSession && studentSocketState){
                this.studentSocketState[student.uid] = {
                  stageIndex: student.stageIndex,
                  questionCaption: student.questionCaption,
                  questionIndex: student.questionIndex,
                  submitted: student.submitted,
                  softLock: student.softLock,
                  numTimesOffScreen: 0,
                }
              }
            }
            this.studentLockState.set(student.uid, false);
          });
        }
        this.checkAllStudentSessionState();
        this.refreshComputedState();
        this.reloadSessionInfo.emit(null);
        this.studentLockActionEmitter.emit({studentLockActionMap: this.studentIsLockingUnlockingStatus, studentUids: studentUids});

        this.studentG9Connection.notifyStudentsOfSubsession(studentUids, !isClosing, subSession.caption, this.activeSession.slug, subSession.slug, isSubmitting, isPjPausingOtherSession);
      })
      .catch(err => {
        if (err.message === "OTHER_SESSION_ACTIVE") {
          this.loginGuard.disabledPopup(this.lang.tra("msg_another_active_session"));
        }
        if (err.message === "COMPLETED_SUB_SESSION") {
          this.loginGuard.disabledPopup("Student has already made a submission for this sub-session");
        }
      });
  }

  getStudentLockingUnlockingStatus(student) {
    if (!this.studentIsLockingUnlockingStatus.has(student.uid)) return false;
    return this.studentIsLockingUnlockingStatus.get(student.uid);
  }

  lockSession(subSession) {
    this.isSessionAlocked = !this.isSessionAlocked;
    this.studentList.forEach(student => {
      student.session_a.is_session_active = !this.isSessionAlocked;
    });
    this.checkAllStudentSessionState();
  }

  showUnlockWarning(){
    this.loginGuard.confirmationReqActivate({
      caption: "message",
      confirm: () => {}
    });
    
  }

  /**
   * checks conditions that prevent student sub session lock/unlock toggle 
   * also handles pausing other open PJ sessions to unlock student in current session
   */
  checkStudentSubSessionLock(student: IStudentAccount, subSessionIndex: number) {
    const targetSubSessionId = this.subSessions[subSessionIndex].id;
    const studentState = this.getStudentState(student);
    let isClosing = studentState.active_sub_session_id === targetSubSessionId;
    if(this.g9DemoData.getIsUnsubmitting()) return //disable the button when unsubmitting
    this.studentIsLockingUnlockingStatus.set(student.uid, true);

    // popup modal to confirm pausing of another on-going session to unlock this student
    //if another PJ session is active and unpaused, and toggle is opening lock, 
    if(this.isOtherSessionAlreadyActive() && this.isPrimaryOrJunior() && !isClosing) {
      const otherActiveSession = this.isOtherSessionAlreadyActive()
      const descriptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_operational") : this.lang.tra("invig_unlock_sample");
      const description = this.lang.tra("invig_unlock_caption", "", {ASSESSMENT: descriptionProps})
      const subCaptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_sample") : this.lang.tra("invig_unlock_operational");
      const subCaption = this.lang.tra("invig_unlock_subcaption", "", {ASSESSMENT: subCaptionProps.toLowerCase()})
      this.loginGuard.confirmationReqActivate({
        description,
        subCaption,
        width: '34em',
        btnProceedConfig: {caption: "invig_unlock_pause"},
        btnCancelConfig: {caption: "invig_unlock_go_back"},
        confirm: async() => {
          await this.auth.apiUpdate(this.routes.EDUCATOR_SESSION, otherActiveSession.test_session_id, {openAssessments: this.activeClassroom.openAssessments}, {query: { school_class_group_id: this.classroomsService.getClassroomById(this.classId).group_id}})
          const otherSession = this.activeClassroom.openAssessments.find(assessment => assessment.test_session_id == otherActiveSession.test_session_id)
          otherSession.is_paused = 1
          const isPjPausingOtherSession = true
          this.toggleStudentSubSessionLock(student,subSessionIndex, isPjPausingOtherSession)
        },
        close: () => {
        }
      })
      // Prevent locking/unlocking if is not PJ session and another session is open 
    } else if (this.activeClassroom.openAssessments.length > 1 && !this.isPrimaryOrJunior()) {
      this.loginGuard.confirmationReqActivate({
        caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_another_active_session_pj") : this.lang.tra("msg_another_active_session")
      });
    } else {
      this.toggleStudentSubSessionLock(student, subSessionIndex)
    }
  }

  /**
   * Toggles student subsession lock state 
   */
  toggleStudentSubSessionLock(student, subSessionIndex, isPjPausingOtherSession = false){
    const isLocked = this.getStudentSessionLockState(student, subSessionIndex);
    if (!this.isStudentSubSessionSubmitted(student, subSessionIndex) && !this.isStudentSubSessionSurpassed(student, subSessionIndex) && !isLocked) {
      this.loginGuard.confirmationReqActivate({
        caption: this.isPrimaryOrJunior() ? this.lang.tra("msg_close_sub_session_pj") : this.lang.tra("msg_close_sub_session"),
        confirm: () => {
          const targetSubSessionId = this.subSessions[subSessionIndex].id;
          const studentState = this.getStudentState(student);
          let isClosing = studentState.active_sub_session_id === targetSubSessionId;
          this.setStudentsLocks(isClosing, [student], subSessionIndex);
        },
        close: () => {
          this.studentIsLockingUnlockingStatus.set(student.uid, false);
        }
      });
    } 
    else if (this.isStudentSubSessionSubmitted(student, subSessionIndex))  {
      this.studentIsLockingUnlockingStatus.set(student.uid, false);
      return;
    } 
    else if (!this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      const targetSubSessionId = this.subSessions[subSessionIndex].id;
      const studentState = this.getStudentState(student);
      const subSession = studentState.subSessions[subSessionIndex];
      let isClosing = studentState.active_sub_session_id === targetSubSessionId;

      const prevSubSession = subSessionIndex > 0 ? studentState.subSessions[subSessionIndex - 1] : null;

      const setLocks = () => {
        this.setStudentsLocks(isClosing, [student], subSessionIndex, false, isPjPausingOtherSession );
      }
      if(this.isG9OrOsslt() && !isClosing && studentState.active_sub_session_id != null) {
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra("invig_mid_session"),
          confirm: setLocks
        });
      } else if (prevSubSession) {
        if (!isClosing && subSession.started_on == null && prevSubSession.started_on && !prevSubSession.is_submitted && subSession.subsession_slug != 'session_q') {
          this.ssReviewForAll = false;
          this.reviewSubsessionProgressModalStart([student], subSessionIndex, isClosing, false);
        } else { 
          // opening new subsession for first time
          setLocks();
        }
      } else {
        setLocks();
      }
      if (!isClosing){
        //this.showUnlockWarning()
      }
    } 
    else {
      if(this.isPrimaryOrJunior()) {
        this.loginGuard.disabledPopup(this.lang.tra("pj_previously_submitted"));
      }
      if(this.isG9OrOsslt()) {
        this.loginGuard.disabledPopup("Student has already submitted this sub-session in a another test session");
      }
    }
    // temp //////////
    // studentState.active_sub_session_id = newSubSessionId;
    ///////////
    // session.is_session_active = !session.is_session_active;
    // this.checkAllStudentSessionState();
    // this.refreshComputedState()
    
  }

  initStudentSSSubmissionMap(ssMap: any) {
    this.studentSSSubmissionMap = ssMap;
  }

  reviewSubsessionProgressModalStart(students: IStudentAccount[], subSessionIndex: number, isClosing: boolean, isAll: boolean) {
    this.studentsToReviewSS = [];
    students.forEach(student => {
      this.studentsToReviewSS.push(student);
    })
    this.subsessionIndToReview = subSessionIndex - 1;
    const config: IReviewSSModalConfig = { isClosing: !isClosing, studentStates: this.studentStates, studentList: this.studentsToReviewSS, subSessionIndex: this.subsessionIndToReview, isAll };
    this.pageModal.newModal({
      type: TeacherModal.REVIEW_CURR_SS_STATUS_MODAL,
      config,
      finish: this.proceedSSSubmissions.bind(this),
      cancel: this.cancelReviewProgressModal.bind(this)
    })
  }

  cancelReviewProgressModal(config: IReviewSSModalConfig) {
    const {studentList} = config;
    studentList.forEach(student => {
      this.studentIsLockingUnlockingStatus.set(student.uid, false);
    })
  }

  proceedSSSubmissions(config:IReviewSSModalConfig) {
    const {isClosing, subSessionIndex, studentList} = config;
    const studentsUidsToUpdate = [];
    const studentsToUpdate = [];

    for (const [uid, stuData] of this.studentSSSubmissionMap.entries()) {
      if (stuData) {
        const student = studentList.find(stu => stu.uid == uid);
        studentsUidsToUpdate.push(student.uid);
        studentsToUpdate.push(student);
      }
    }

    studentsToUpdate.forEach(stu => {
      this.studentIsLockingUnlockingStatus.set(stu.uid, true);
    })
    const subSession = this.subSessions[subSessionIndex];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);

    if (studentsUidsToUpdate.length < 1) {
      this.pageModal.closeModal();
      return;
    }
    this.classroomsService
    .invigOpenCloseSubSessionForStudents(this.testSessionId, subSession.id, subSession.twtdar_order, studentsUidsToUpdate, this.classId, isClosing, params,this.isPrimaryOrJunior(), true)
      .then(res => {
        studentsToUpdate.forEach(student => {
          const studentState = this.getStudentState(student);
          studentState.active_sub_session_id = null;
          const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
          subSessionState.is_submitted = 1;
        });
        this.checkAllStudentSessionState();
        this.refreshComputedState();
        this.reloadSessionInfo.emit(null);
        this.studentLockActionEmitter.emit({studentLockActionMap: this.studentIsLockingUnlockingStatus, studentUids: studentsUidsToUpdate});

        this.studentG9Connection.notifyStudentsOfSubsession(studentsUidsToUpdate, !isClosing, subSession.caption, this.activeSession.slug, subSession.slug, true);

        this.setStudentsLocks(!isClosing, studentsToUpdate, subSessionIndex + 1, false); 
      })

    this.pageModal.closeModal();
  }

  checkAllStudentSessionState() {
    let num = 0;
    this.studentList.forEach(student => {
      for (let i = 0; i < this.subSessions.length; i++) {
        if (!this.getStudentSessionLockState(student, i)) {
          num++;
          break;
        }
      }
    });
    // this.isSessionAlocked = (num === 0)
    this.locksUpdate.emit({ num });
  }

  getStudentState(student: IStudentAccount) {
    if (this.studentStates) {
      return this.studentStates[student.uid];
    }
  }

  getStudentSocketState(student: IStudentAccount) {
    return this.studentSocketState[student.uid];
  }

  getStudentSubSessionState(student: IStudentAccount, subSessionIndex: number) {
    const state = this.getStudentState(student);
    if (state) {
      return state.subSessions[subSessionIndex];
    }
  }

  isStudentOnline(student: IStudentAccount) {
    //console.log(this.studentSocketState)
    return !!this.studentSocketState[student.uid];
  }
  isStudentClosing = false;
  pauseStudentAttempt(testAttemptIds, isPausing, student) {
    //console.log(subSessionIndex)
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return Promise.all(testAttemptIds.map((testAttemptId) => {
      this.classroomsService.invigOpenCloseTestAttemptForStudents(testAttemptId, isPausing, params).then(res => {
        if (!isPausing) {
          this.studentStates[student.uid].is_paused = 0;
        }
        console.log("attempt paused");
        // this.studentG9Connection.notifyStudentsOfSubsession([student.uid], !isPausing, subSession.caption);
      });
    }))
  }

  hasOldTestForm(student: IStudentAccount) {
    if(!this.studentStates) {
      return false;
    }
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    return !!studentState?.hasOldTestForm;
  }

  isStudentWarning(student: IStudentAccount) {
    // check if they require softlock or not
    const studentSocketState = this.getStudentSocketState(student);
    // case where softlock and they're offline
    if (studentSocketState && !this.isStudentOnline(student)) {
      return false;
    }
    // give them lock notification if online and has softlock requirements
    if (studentSocketState && studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
      if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
        this.studentLockNotification.set(student.uid, true);
        return true;
      }
    }
    // case where not softlock and they're offline or they're dismissing warning notif
    return false;
  }

  isStudentPaused(student: IStudentAccount) {
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    let isStudentPaused = false;
    if (this.studentPauseNotification.has(student.uid) && this.studentPauseNotification.get(student.uid)) {
      isStudentPaused = true;
    } else if (studentState) {
      isStudentPaused = !!studentState.is_paused;
    }
    return isStudentPaused;
  }

  isStudentWarningOrPaused(student: IStudentAccount) {
    const studentState = this.studentStates[student.id] || this.studentStates[student.uid];
    var isStudentPaused = false;
    if(studentState){
      isStudentPaused = !!studentState.is_paused;
    }
    if (isStudentPaused) {
      return true;
    }
    if (!this.isStudentOnline(student)) {
      return false;
    }
    const studentSocketState = this.getStudentSocketState(student);
    // console.log(studentSocketState)
    if (studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
      //console.log('inside first')
      //console.log(this.studentLockState.has(student.uid),this.studentLockState.get(student.uid))
      if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
        this.studentLockNotification.set(student.uid, true);
        // //console.log('inside second')
        // const subSessions = this.studentStates[student.uid].subSessions;
        // const attemptId = this.studentStates[student.uid].attempt_id;
        // const data = [""];
        // Promise.all(
        //   subSessions.map(async (subSession, idx) => {
        //     //console.log(this.getStudentSessionLockState(student,idx),'session lock state',idx)
        //     if (!this.getStudentSessionLockState(student, idx)) {
        //       this.studentLockNotification.set(student.uid, true);
        //     }
        //   })
        // ).then(res => {
        //   this.studentLockState.set(student.uid, true);
        // });
      }
    }
    //console.log('got here',this.studentLockNotification.get(student.uid))
    return this.studentLockNotification.get(student.uid);
  }
  isStudentSoftLockWarning(student) {
    const studentSocketState = this.getStudentSocketState(student);
    if (this.isStudentWarningOrPaused(student) && studentSocketState.softLock <= 2) {
      return true;
    }
    return false;
  }
  
  isStudentSoftLockStrongWarning(student) {
    const studentSocketState = this.getStudentSocketState(student);
    if (this.isStudentWarningOrPaused(student) && studentSocketState.softLock > 2) {
      return true;
    }
    return false;
  }

  updateStudentTestForm(student: IStudentAccount) {
    const uid = student.id || student.uid;

    const alreadyBegunMessage = 'msg_old_test_form_begun';
    if(!this.studentStates[uid].canUpdateTestForm) {
      this.loginGuard.quickPopup(alreadyBegunMessage)
      return;
    }

    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('msg_old_test_form'),
      confirm: () => {
        this.auth.apiPatch(this.routes.EDUCATOR_UPDATE_TEST_FORM, student.id || student.uid, {}, {query: {test_session_id: this.testSessionId, schl_class_group_id: this.classroomsService.getClassroomById(this.classId).group_id}})
        .then(() => {
          this.studentStates[uid].hasOldTestForm = false;
        }).catch((e) => {

          let errMessage;
          switch(e.message) {
            case 'ALREADY_BEGUN':
              errMessage = alreadyBegunMessage;
            default:
              errMessage = 'msg_old_test_form_err';
          }
          this.loginGuard.quickPopup(errMessage)
        })
      }
    });
  }

  // this.studentSubSessionToRestore.set(student.uid, idx);
  // this.pauseStudentAttempt(attemptId, true, student, idx);

  openSoftLockNotification(student: IStudentAccount) {
    this.currentStudent = student;
    this.openSoftlockModal.emit({openModal: true, uid: student.uid});
  }

  openDismissModal() {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_dismiss"),
      confirm: () => {
        this.studentLockNotification.set(this.currentStudent.uid, false);
        this.resetStudentSoftLock(this.currentStudent);
        this.currentStudent = null;
      }
    });
  }

  openAndDismissUnpauseNotification(student: IStudentAccount) {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_unpause"),
      confirm: () => {
        this.resetStudentSoftLock(student);
        const subSessions = this.studentStates[student.uid].subSessions;
        const testAttemptIds = [];
        subSessions.forEach((ss: any) => {
          if (!testAttemptIds.includes(ss.test_attempt_id)) {
            testAttemptIds.push(ss.test_attempt_id);
          }
        });
        //console.log('checking index to restore',idxToRestore, typeof(idxToRestore))
        this.pauseStudentAttempt(testAttemptIds, false, student);
        this.studentLockState.set(student.uid, false);
        this.studentPauseNotification.set(student.uid, false);
        this.currentStudent = null;
      }
    });
  }

  async pauseStudentAndDismissSoftLockNotification() {
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra("msg_navigate_pause"),
      confirm: () => {
        const student = this.currentStudent;
        const studentSocketState = this.getStudentSocketState(student);
        // console.log(studentSocketState)
        if (studentSocketState.softLock && studentSocketState.softLock.get(student.uid) > 0) {
          //console.log('inside first')
          //console.log(this.studentLockState.has(student.uid),this.studentLockState.get(student.uid))
          if (!this.studentLockState.has(student.uid) || !this.studentLockState.get(student.uid)) {
            //console.log('inside second')
            const subSessions = this.studentStates[student.uid].subSessions;
            const testAttemptIds = [];
            subSessions.forEach((ss: any) => {
              if (!testAttemptIds.includes(ss.test_attempt_id)) {
                testAttemptIds.push(ss.test_attempt_id);
              }
            });
            Promise.all(
              subSessions.map(async (subSession, idx) => {
                //console.log(this.getStudentSessionLockState(student,idx),'session lock state',idx)
                if (!this.getStudentSessionLockState(student, idx) || this.isStudentBetweenSubsessions(student, idx)) {
                  this.studentSubSessionToRestore.set(student.uid, idx);
                  await this.pauseStudentAttempt(testAttemptIds, true, student);
                }
              })
            ).then(res => {
              this.studentLockNotification.set(student.uid, false);
              this.studentPauseNotification.set(student.uid, true);
              this.studentLockState.set(student.uid, true);
              this.currentStudent = null;
            });
          }
        }
      }
    });
  }

  // deprecated
  // dismissSoftLockNotification(student: IStudentAccount) {
  //   this.loginGuard.confirmationReqActivate({
  //     caption: this.lang.tra("msg_student_navigation_warning"),
  //     confirm: () => {
  //       this.resetStudentSoftLock(student);
  //       const subSessions = this.studentStates[student.uid].subSessions;
  //       const attemptId = this.studentStates[student.uid].attempt_id;
  //       const idxToRestore = this.studentSubSessionToRestore.get(student.uid);
  //       //console.log('checking index to restore',idxToRestore, typeof(idxToRestore))
  //       this.pauseStudentAttempt(attemptId, false, student, idxToRestore);
  //       this.studentLockState.set(student.uid, false);
  //       this.studentLockNotification.set(student.uid, false);
  //       // if(idxToRestore != null && idxToRestore != undefined){
  //       //   console.log('in here')
  //       //   this.setStudentsLocks(false, [student], parseInt(idxToRestore))
  //       //   this.studentLockState.set(student.uid,false)
  //       // }
  //       // else{
  //       //   subSessions.forEach(async(subSession,idx) =>{
  //       //     if (subSession.started_on != null && subSession.is_submitted != 1){
  //       //       this.setStudentsLocks(false, [student], idx)
  //       //       this.studentLockState.set(student.uid,false)
  //       //     }
  //       //   })
  //       // }

  //       //this.isSoftLockReset = true;
  //     }
  //   });
  // }
  resetStudentSoftLock(student: IStudentAccount) {
    this.resetSoftLock.emit(student.uid);
  }
  isStudentSubSessionActive(student: IStudentAccount, subSessionIndex: number) {
    // this should be using the online status
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }

    return !this.getStudentSessionLockState(student, subSessionIndex);
  }
  isStudentSubSessionNotStarted(student: IStudentAccount, subSessionIndex: number) {
    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);

    const studentSocketState = this.getStudentSocketState(student);
    if (studentSocketState && studentSocketState.questionCaption !== undefined && studentSocketState.stageIndex !== undefined) {
      return false;
    }

    return !(subSessionstate && subSessionstate.started_on);
  }

  isSubSessionSubmitted(student, subSessionIndex) {
    const studentSocketState = this.getStudentSocketState(student);
    if (studentSocketState && studentSocketState[`submitted${subSessionIndex}`]) {
      // for osslt/g9 submitted sub sessions that do not send test session id in ws student sub session data
      if (studentSocketState.testSessionId === undefined) {
          return true;
      // for pj submitted sub sessions that include test session id to differentiate sub session submission state between
      // operation & sample open at the same time
      } else if (studentSocketState.testSessionId === this.activeSession.test_session_id) {
          return true;
      }
    }

    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);
    return subSessionstate && subSessionstate.is_submitted;
  }
  isStudentSubSessionSubmitted(student: IStudentAccount, subSessionIndex: number) {
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }

    return this.isSubSessionSubmitted(student, subSessionIndex);
  }

  showSoftLockWarning(student) {
    this.loginGuard.quickPopup("This student has left the test. You can lock the test session or allow them to continue with the test.");
    this.updateSoftLockRules(student);
  }

  updateSoftLockRules(student) {
    this.updateStudentSoftLock.emit(student);
  }

  getStudentSubSessionStatus(student: IStudentAccount, subSessionIndex: number) {
    const subSessionstate = this.getStudentSubSessionState(student, subSessionIndex);
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return "Ongoing in Another Session";
    }
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return "lbl_submitted_other_session";
    } else {
      const lockState = this.getStudentSessionLockState(student, subSessionIndex);
      const studentSocketState = this.getStudentSocketState(student);

      const renderPosition = (stageIndex, questionCaption, questionIndex?) => {
        let eqao_title: string = "eqao_title_stage";
        let pj_title_stageIndex: string = '';
        if (student.eqao_is_g3 === '1' || student.eqao_is_g6 === '1') {
          if(this.isPJSampleTest()) {
            if(subSessionIndex == 0 || subSessionIndex == 1) {
              pj_title_stageIndex = PJSampleLangTitle[stageIndex];
            }
            if(subSessionIndex == 2 || subSessionIndex == 3) {
              pj_title_stageIndex = PJSampleMathTitle[stageIndex];
            }   
          }
          if(this.isPJOperationalTest()) {
            if(subSessionIndex == 0 || subSessionIndex == 1 || subSessionIndex == 2 || subSessionIndex == 3) {
              pj_title_stageIndex = PJOperationalLangTitle[stageIndex];
            }
            if(subSessionIndex == 4 || subSessionIndex == 5 || subSessionIndex == 6 || subSessionIndex == 7) {
              pj_title_stageIndex = PJOperationalMathTitle[stageIndex];
            }   
            if(subSessionIndex == 8) {
              pj_title_stageIndex = 'pj_session_q';
            }
          }
        }
        if (student.eqao_is_g9 === "1") {
          eqao_title = "eqao_title_stage";
        }
        if (student.eqao_is_g10 === "1") {
          eqao_title = "eqao_title_section";
        }

        if(!questionCaption) {
          questionCaption = `Question ${questionIndex + 1}`; //For the transition period of the switchover to questionCaption, where questionCaption may not be initialized
        }
        if(this.isPrimaryOrJunior()) {
          return `${this.lang.tra(pj_title_stageIndex)}, ${questionCaption}`;
        } 
        return `${this.lang.tra(eqao_title)} ${stageIndex + 1}, ${questionCaption}`;
      };
      if (subSessionstate) {
        if (this.isSubSessionSubmitted(student, subSessionIndex)) {
          const subSessionSlug = this.subSessions[subSessionIndex].slug;
          if(subSessionSlug !== "session_q" && this.isPJOperationalTest() && !this.isPJMathSession(this.subSessions[subSessionIndex].slug)) {
            switch(this.getStudentQuestionResponseInfo(student, subSessionIndex)) {
              case StudentQuestionResponseInfoStatusType.ScanConfirmed:
                return 'lbl_scan_confirmed';
              case StudentQuestionResponseInfoStatusType.ScanUploaded:
                return 'lbl_scan_uploaded';
              case StudentQuestionResponseInfoStatusType.None:
                return 'lbl_submitted_scan_required';
              case StudentQuestionResponseInfoStatusType.Online:
                return 'lbl_online_session_submitted';
              case StudentQuestionResponseInfoStatusType.ScanNeedsReupload:
                return 'lbl_scan_needs_reupload';
            }
          }
          return this.subSessions[subSessionIndex].slug === "session_q" ? "lbl_status_submitted" : "lbl_submitted";
        } else if (studentSocketState?.stageIndex != null && studentSocketState?.questionCaption != null) {
          return renderPosition(studentSocketState.stageIndex, studentSocketState.questionCaption, studentSocketState.questionIndex );
        } else if (!subSessionstate.started_on) {
          if (lockState) {
            // console.log(this.subSessions)
            // console.log(this.subSessions[subSessionIndex].date_time_start)
            return this.subSessions[subSessionIndex].slug === "session_q" ? "lbl_not_started_2" : "lbl_not_started";
            // let subSessionDate = this.subSessions[subSessionIndex].date_time_start;
            // if(subSessionDate){
            //   const startDate = new Date(subSessionDate);
            //   const timeConvert = moment.tz(startDate, moment.tz.guess())
            //   const startDay = timeConvert.format(this.lang.tra('datefmt_day_month'));
            //   const startTime = timeConvert.format(this.lang.tra('h:mm A'));
            //   return `${startDay},${startTime}`;
            // }
          } else {
            return "lbl_ready_to_start";
          }
        } else {
          const studentState = this.getStudentState(student);
          if(subSessionstate?.sections_allowed?.includes(studentState?.section_index)) {
            return renderPosition(studentState.section_index, studentState.question_caption, studentState.question_index,);
          } else {
            return 'lbl_ready_to_start';
          }
          // if (lockState){
          //   return 'lbl_locked';
          // }
          // return 'lbl_inprogress';
        }
      }
    }

    // return 'lbl_not_started';
    return "...";
  }

  isPJMathSession(sessionSlug: string){
    if(sessionSlug == 'math_stage_1' || sessionSlug == 'math_stage_2' || sessionSlug == 'math_stage_3' || sessionSlug == 'math_stage_4') {
      return true;
    }
    return false;
  }

  showEditModal(subSessionIndex) {
    const subSession = this.subSessions[subSessionIndex];
    this.openEditModal.emit(subSession);
  }
  canSubSessionStart(subSessionIndex) {
    if (this.subSessions[subSessionIndex].slug === 'session_q') {
      if (this.isPJOperationalTest()) {
        return this.verifyPastDate(this.getEarliestPJOpSubSessStartDateForQ())
      } else {
        return true;
      }
    }
    const startDate = this.subSessions[subSessionIndex].date_time_start;
    const duration = this.subSessions[subSessionIndex].duration_hours;

    if (this.startSession(startDate, duration, subSessionIndex)) {
      return true;
    }
    return false;
  }

  startSession(dateStr, duration, subSessionIndex) {
    if (dateStr) {
      if (this.isSessionInProgress(dateStr, duration)) {
        const currDate = new Date();
        const selectedDate = new Date(dateStr);
        const endTime = selectedDate.getTime() + duration * 60 * 60 * 1000;
        const thirtyMinsIn = selectedDate.getTime() + 30 * 60000;
        const oneHourLeft = endTime - 1 * 60 * 60 * 1000;
        if (currDate.getTime() >= thirtyMinsIn && !this.isDismissedSessionNotification(subSessionIndex)) {
          // if (subSessionIndex === 0) {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_30_mins_warning_session_a"));
          // } else {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_30_mins_warning_session_b"));
          // }
          this.setDismissedSessionNotification(subSessionIndex);
        }

        if (currDate.getTime() >= oneHourLeft && !this.isDismissedSessionNotificationWarning(subSessionIndex)) {
          // if (subSessionIndex === 0) {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_one_hour_left_session_a"));
          // } else {
          //   this.loginGuard.disabledPopup(this.lang.tra("msg_one_hour_left_session_b"));
          // }
          this.setDismissedSessionNotificationWarning(subSessionIndex);
        }
        return true;
      } else {
        if (this.isSessionStarted(dateStr, duration)) {
          return true;
        }
        return false;
      }
    }
    return false;
  }
  isSessionStarted(dateStr, duration) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    let sessionEnd = selectedDate.getTime() + duration * 60 * 60 * 1000;
    if (currDate.getTime() >= selectedDate.getTime()) {
      return true;
    }
    return false;
  }
  isSessionInProgress(dateStr, duration) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    let sessionEnd = selectedDate.getTime() + duration * 60 * 60 * 1000;
    if (currDate.getTime() >= selectedDate.getTime() && currDate.getTime() < sessionEnd) {
      return true;
    }
    return false;
  }

  isDismissedSessionNotification(subSessionIndex) {
    if (subSessionIndex == 0) {
      return this.isDismissedSessionANotfifcation;
    }
    return this.isDismissedSessionBNotification;
  }
  isDismissedSessionNotificationWarning(subSessionIndex) {
    if (subSessionIndex == 0) {
      return this.isDismissedSessionANotfifcationWarning;
    }
    return this.isDismissedSessionBNotificationWarning;
  }
  setDismissedSessionNotificationWarning(subSessionIndex) {
    if (subSessionIndex == 0) {
      return (this.isDismissedSessionANotfifcationWarning = true);
    }
    return (this.isDismissedSessionBNotificationWarning = true);
  }

  setDismissedSessionNotification(subSessionIndex) {
    if (subSessionIndex == 0) {
      return (this.isDismissedSessionANotfifcation = true);
    }
    return (this.isDismissedSessionBNotification = true);
  }
  getStudentSessionTime(student: IStudentAccount, subSessionIndex: number) {
    const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
    if (subSessionState && subSessionState.started_on) {
      return subSessionState._timeSpent;
    }
  }
  getSubSessionScheduledTime(subSessionIndex) {
    let subSessionDate = this.subSessions[subSessionIndex].date_time_start;
    let duration = this.subSessions[subSessionIndex].duration_hours;
    if (subSessionDate) {
      const startDate = new Date(subSessionDate);
      const endDate = moment(startDate).add(duration, 'h');
      const timeConvert = etz(startDate);
      const endTimeConvert = etz(endDate);
      const startDay = timeConvert.format(this.lang.tra("datefmt_day_month"));
      let timezone = timeConvert.zoneAbbr();
      timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label');
      const startTime = `${timeConvert.format(this.lang.tra("timefmt_hour_time"))} ${timezone}`;
      const endTime = `${endTimeConvert.format(this.lang.tra("timefmt_hour_time"))} ${timezone}`;
      return {
        date: startDay,
        first_time: startTime,
        second_time: endTime
      };
    }
  }

  getPJSubSessionScheduledDate(subSessionIndex) {
    let subSessionDate:Date|string
    if (this.subSessions[subSessionIndex].slug === 'session_q') {
      subSessionDate = this.getEarliestPJOpSubSessStartDateForQ()
    } else {
      subSessionDate = this.subSessions[subSessionIndex].date_time_start;
    }

    if (subSessionDate) {
      const startDate = new Date(subSessionDate);
      const timeConvert = moment.tz(startDate, moment.tz.guess());
      const startDay = timeConvert.format(this.lang.tra("datefmt_day_month"));
      return {
        date: startDay,
      };
    }
    return null;
  }

  isStudentSubSessionEffectiveClosing(student: IStudentAccount, subSessionIndex: number) {
    const subSessionState = this.getStudentSubSessionState(student, subSessionIndex);
    const subSessionIndexPre = subSessionIndex - 1;
    const ssTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
    if (subSessionIndexPre >= 0) {
      const preSsTwtdarOrder = this.subSessions[subSessionIndexPre].twtdar_order;
      if(!this.isNoTdOrder || (ssTwtdarOrder === preSsTwtdarOrder)) {
        const subSessionStatePre = this.getStudentSubSessionState(student, subSessionIndexPre);
        const isCompleted = subSessionStatePre && subSessionStatePre.is_submitted;
        const isStarted = subSessionState && subSessionState.started_on;
        if (!isCompleted && !isStarted) {
          return true;
        }
      }
    }
    return false;
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() {
    return this.cModal().config;
  }

  isStudentSubSessionUpcoming(student: IStudentAccount, subSessionIndex: number, verbose = false) {
    const ssTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
    const prevSubSessionIndex = subSessionIndex - 1;

    if (prevSubSessionIndex >= 0) {
      const prevSsTwtdarOrder = this.subSessions[prevSubSessionIndex].twtdar_order;
      if(!this.isNoTdOrder || (prevSsTwtdarOrder === ssTwtdarOrder)) {
        if (this.checkOtherCompletedSessions(student.uid, this.subSessions[prevSubSessionIndex].slug)) {
          return false;
        }
        const subSessionState = this.getStudentSubSessionState(student, prevSubSessionIndex);
        const isCompleted = subSessionState && subSessionState.is_submitted;
        const isStarted = subSessionState && subSessionState.started_on;
        const isLocked = this.getStudentSessionLockState(student, prevSubSessionIndex);
        if (verbose) {
          console.log({
            targetSubSessionIndex: prevSubSessionIndex,
            isCompleted,
            isStarted,
            isLocked
          });
        }
        // if (isCompleted) { // CLOSER_LOOK_20210807 conflicted with the one below
        if (isCompleted || (isLocked && isStarted)) {
          return false;
        } else {
          return true;
        }
      }
    }
    return false;
  }

  isStudentSubSessionSurpassed(student: IStudentAccount, subSessionIndex: number) {
    if (this.checkOtherCompletedSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return false;
    }
    if (this.isSubSessionSubmitted(student, subSessionIndex)) {
      return true;
    }
    if (this.getStudentSessionLockState(student, subSessionIndex)) {
      const currTwtdarOrder = this.subSessions[subSessionIndex].twtdar_order;
      for (let i = subSessionIndex + 1; i < this.subSessions.length; i++) {
        const nextTwtdarOrder = this.subSessions[i].twtdar_order; //Assumes all subsessions with the same twtdar order are adjacent
        if(nextTwtdarOrder !== currTwtdarOrder) {
          break;
        }
        const subSessionState = this.getStudentSubSessionState(student, i);
        if ((subSessionState && subSessionState.started_on) || !this.getStudentSessionLockState(student, i)) {
          return true;
        }
      }
    }

    const isLastSubSession = subSessionIndex+1< this.subSessions.length && (!this.isNoTdOrder || (this.subSessions[subSessionIndex + 1].twtdar_order === this.subSessions[subSessionIndex].twtdar_order));
    if(isLastSubSession && !this.isStudentSubSessionUpcoming(student, subSessionIndex+1) && this.canSubSessionStart(subSessionIndex+1)){
      return true
    }

    return false;
  }
  isOtherSessionActive(student, subSessionIndex) {
    if (this.checkOtherActiveSessions(student.uid, this.subSessions[subSessionIndex].slug)) {
      return true;
    }
  }
  getStudentSessionLockState(student: IStudentAccount, subSessionIndex: number) {
    const studentState = this.getStudentState(student);
    return this.isStudentSessionLocked(studentState, subSessionIndex);
  }
  isStudentSessionLocked(studentState: IStudentState, subSessionIndex: number) {
    const subSession = this.subSessions[subSessionIndex];
    if (studentState && subSession) {
      return !(studentState.active_sub_session_id === subSession.id);
    }
    return true;
  }
  isStudentBetweenSubsessions(student: IStudentAccount, subSessionIndex: number) {
    const studentState = this.getStudentState(student);
    const subSessions = studentState.subSessions;
    const prevSs = subSessionIndex === 0 ? null : subSessions[subSessionIndex - 1];
    const currSs = subSessions[subSessionIndex];

    if (!prevSs) {
      // check if first ss has been started
      if (!currSs.started_on) {
        return studentState.active_sub_session_id === null;
      }
    } else {
      // check if prev one is at least started and current one hasn't been started
      if (prevSs.started_on && !currSs.started_on) {
        return studentState.active_sub_session_id === null;
      } 
    }
    return false;
  }
  checkOtherCompletedSessions(uid, slug) {
    const completed = this.completedSubSessions.filter(session => {
      return session.student_uid === uid && session.session_type === this.activeSession.slug && session.sub_session_slug === slug && session.test_session_id != this.testSessionId;
    });
    return completed.length > 0;
  }
  checkOtherActiveSessions(uid, slug) {
    const active = this.activeSubSessions.filter(session => {
      return session.student_uid === uid && session.session_type === this.activeSession.slug && session.sub_session_slug === slug && session.test_session_id != this.testSessionId;
    });
    return active.length > 0;
  }
  getActiveSubSession(uid, slug) {
    const active = this.activeSubSessions.filter(session => {
      return session.student_uid === uid && session.sub_session_slug === slug;
    });
    return active[0];
  }
  cancelOtherSubSession(student, subSessionIndex) {
    const subSessionToCancel = this.getActiveSubSession(student.uid, this.subSessions[subSessionIndex].slug);
    const students = [student];
    const params = this.classroomsService.constructClassGroupIdQuery(this.classId);
    return this.classroomsService.invigOpenCloseSubSessionForStudents(subSessionToCancel.test_session_id, subSessionToCancel.active_sub_session_id, subSessionToCancel.twtdar_order, [student.uid], this.classId, true, params,this.isPrimaryOrJunior(),false).then(res => {
      const i = this.activeSubSessions.indexOf(subSessionToCancel);
      this.activeSubSessions.splice(i, 1);
      students.forEach(student => {
        const studentState = this.getStudentState(student);
        studentState.active_sub_session_id = null;
      });
      this.checkAllStudentSessionState();
      this.refreshComputedState();

      this.studentG9Connection.notifyStudentsOfSubsession([student.uid], false, subSessionToCancel.current_session, this.activeSession.slug, subSessionToCancel.slug);
    });
  }
  verifyPastDate(dateStr) {
    let currDate = new Date();
    let selectedDate = new Date(dateStr);
    if (currDate.getTime() > selectedDate.getTime()) {
      return true;
    }
    return false;
  }

  verifyUnsubmit(student: IStudentAccount, subSessionIndex: number){  
    this.unsubmitedStudent = null;
    this.unsubmitedSubSessionIndex = null;
    this.unsubmitedStudent = student;
    this.unsubmitedSubSessionIndex = subSessionIndex;
    const studentUID = student.uid;
    const studentRecord = this.studentStates[studentUID];
    if(studentRecord && studentRecord.is_ta_unsubmit_pending) {
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_existing_pending_unsubmission_request')), 0); 
      return;
    }
    if(this.isStudentOnline(student)){
      alert(this.lang.tra("lbl_verify_student_offline"))
    }else{
      this.verifyUnsubmitStep2(student);
    }
  }

  /**
   * Get unsubmit reasons from API and show the unsubmit modal first page
   * @param student 
   */
  verifyUnsubmitStep2(student: IStudentAccount){
    const groupIdQuery = this.classroomsService.constructClassGroupIdQuery(this.classId);
    let params = {
      query: {
        school_class_group_id: groupIdQuery.query.school_class_group_id,
      }
    }
    this.auth
    .apiFind(this.routes.EDUCATOR_TEST_ATTEMPT_UNSUBMISSION_REASONS, params)
    .then(result => {
      if(result[0].testAttemptUnsubmissionReasons) {
        const G9DemoClass = this.g9DemoData.classrooms.find(classroom => +classroom.id === +this.activeClassroom.id)
        const auto_approve_unsubmit_time_limit_h = this.g9DemoData.getTestWindowFromClassroom(G9DemoClass)?.auto_approve_unsubmit_time_limit_h
        const props = {
          studentFirstName: student.first_name, 
          studentLastName: student.last_name, 
          studentOEN: student.eqao_student_gov_id,
          testAttemptUnsubmissionReasons: result[0].testAttemptUnsubmissionReasons,
          auto_approve_unsubmit_time_limit_h: auto_approve_unsubmit_time_limit_h || '0'
        }
        const config = {
          props
        }
        this.unsubmitStudentModalPrePageStart(config)
      }
    }) 
  }

  /**
   * Show unsubmit modal first page
   * @param config 
   */
  unsubmitStudentModalPrePageStart(config = {}){
    this.pageModal.newModal({
      type: TeacherModal.UNSUBMIT_STUDENT_MODAL_PREPAGE,  
      config,
      finish: () => this.unsubmitStudentModalPrePageFinish
    });
  }

  /**
   * Show unsubmit modal second page where user choose unsubmit reason
   * @param config 
   */
  unsubmitStudentModalPreContinue = () =>{
    const props = this.cmc().props
    this.pageModal.closeModal();
    this.unsubmitStudentModalStart(props);
  }

  /**
   * Close unsubmit modal first page
   */
  unsubmitStudentModalPrePageFinish = () => {
    this.pageModal.closeModal();
  }

  /**
   * Show unsubmit modal second page where user choose unsubmit reason
   * @param config 
   */
  unsubmitStudentModalStart(studentInfo, preSelectOptionId = 0){
    let config = {};  
    if(studentInfo) {
      config = studentInfo;
    }
    this.pageModal.newModal({
      type: TeacherModal.UNSUBMIT_STUDENT_MODAL,  
      config: {...config, preSelectOptionId},
      finish: () => this.unsubmitStudentModalFinish
    });
  }
  /**
   * Close unsubmit modal second page
   */
  unsubmitStudentModalFinish = (unsubmissionDetail:any) => {
    const testAttemptUnsubmissionReasons = this.cmc().testAttemptUnsubmissionReasons
    this.unsubmit(this.unsubmitedStudent, this.unsubmitedSubSessionIndex, unsubmissionDetail, testAttemptUnsubmissionReasons);
    this.pageModal.closeModal();
  }

  showUnsubmit(student: IStudentAccount, subSessionIndex: number){

    if (this.isForceUnsubmitAbility){
      return true
    }
    else if (!this.isUnsubmitBtnVisiable) {
      return false
    }

    // check if its OSSLT and SubSessionSurpassed
    const isOSSLTOperational = this.activeSession.slug === ASSESSMENT.OSSLT_OPERATIONAL;
    const isG9Operational = this.activeSession.slug === ASSESSMENT.G9_OPERATIONAL;
    const isPrimaryOperational = this.activeSession.slug === ASSESSMENT.PRIMARY_OPERATIONAL;
    const isJunioroperational = this.activeSession.slug === ASSESSMENT.JUNIOR_OPERATIONAL;
    const isStudentSubSessionSurpassed = this.isStudentSubSessionSurpassed(student, subSessionIndex)

    //temporary comment out for teacher to unsubmit*******************************************************
    //const maxSubSession = (isOSSLTOperational || isG9Operational) ? 2:isPrimaryOperational||isJunioroperational?8:0
    const maxSubSession = 8

    //temporary comments out for teacher to unsubmit everything
    // if((isPrimaryOperational || isJunioroperational) && subSessionIndex <  maxSubSession){ // if its PJ and subsession 0-7 (questioneer doesnt count)
    //    let nextSubsessionIndex = subSessionIndex+1
    //    if(+nextSubsessionIndex === 4){ //for lang session d the next stage is questionair not math stage a
    //     nextSubsessionIndex = 8
    //    }
    //    if(!this.isSecreteUser && this.studentStates[student.uid].subSessions[nextSubsessionIndex].last_touch_on){ //if the next subsession have touched
    //      return false;
    //    }
    // }
    
    // if((isOSSLTOperational || isG9Operational||isPrimaryOperational||isJunioroperational) && isStudentSubSessionSurpassed && subSessionIndex < maxSubSession){
    //temporary comment out for teacher to unsubmit*******************************************************
    if(isStudentSubSessionSurpassed && subSessionIndex < maxSubSession){
      return true;
    }

    return false;
  }

  /**
   * Call API and sent unsubmit request
   */
  unsubmit(student: IStudentAccount, subSessionIndex: number, config:any, testAttemptUnsubmissionReasons:any){
    const test_session_id = this.testSessionId
    const studentStates = this.studentStates[student.uid];
    if(studentStates === undefined || studentStates.attempt_id === undefined ||  studentStates.attempt_id === null){
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_error')), 0);
      return
    }
    const attemptId = studentStates.attempt_id
    const data = {
      testSessionId:test_session_id, 
      studentId: student.uid, 
      subSessionIndex:subSessionIndex, 
      reasonId: config.unsubmitReason,
      reasonText: config.reasonText,
      //isAllowAutoUnsubmit: config.isAllowAutoUnsubmit, //Checking on API end now
      isSecreteUser:this.isSecreteUser
    }
    const school_class_group_id = this.classroomsService.getClassroomById(this.classId).group_id
    const params = { query : {school_class_group_id}}
    this.g9DemoData.setIsUnsubmitting(true);
    this.auth.apiPatch(this.routes.EDUCATOR_STUDENT_TEST_ATTEMPT, attemptId, data, params)
      .then((res) => {
        this.reloadSessionInfo.emit(res);
        this.isUnsubmitBtnVisiableChange.emit(false);
        //Redirect to unsubmit option 3 if option 1 or 2 request is deny
        if(res){
          const log_entry_data = JSON.parse(res.data)
          if(+log_entry_data.reasonId === 1 || +log_entry_data.reasonId === 2 ){
            if(!log_entry_data.isAllowAutoUnsubmit){
              const preSelectOptionId = 3
              this.unsubmitStudentModalStart({
                studentFirstName: student.first_name, 
                studentLastName: student.last_name, 
                studentOEN: student.eqao_student_gov_id,
                testAttemptUnsubmissionReasons,
              }, preSelectOptionId)
            }
          }    
        }

      }).catch((err:Error)=>{
        this.g9DemoData.setIsUnsubmitting(false);
        if(err.message.includes("REQ_PARAMS_MISS")||err.message.includes("Error_FECTCHING_DATA")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_error')), 0);
        }
        if(err.message.includes("SELECTED_CATEGORY_MISS")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('alert_submit_student_assessment2_empty2')), 0);
        }
        if(err.message.includes("UNSUBMIT_TIME_EXPIRE")){
          //setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_time_expire')), 0);
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_time_expire_168')), 0);
        }
        if(err.message.includes("UNSUBMIT_STAGE_PASSED")){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_stage_passed')), 0);
        }
        if(err.message.includes("INVALID_SECRETE_USER")){ 
          setTimeout(() => this.loginGuard.quickPopup("Invalid Secrete User"), 0);  //this message is for support when they try to unsubmit and have wrong secrete code 
        }
        if(err.message.includes("ACTIVE_SUB_SESSION_NOT_NULL")){ 
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_active_sub_session_not_null')), 0); 
        }
        if(err.message.includes("EXISTING_PENDING_UNSUBMISSION_REQUEST")){ 
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_existing_pending_unsubmission_request')), 0); 
        }
      })
  }

  showResponseType() {
    return ['EQAO_G3', 'EQAO_G6'].includes(this.activeClassroom.curricShort) && (this.isManagingStudents || this.activeSession?.slug?.includes('OPERATIONAL'))
  }

  isWritingPaper(student: IStudentAccount) {
    const isCrScanDefault = this.g9DemoData.getPropVal(student, 'IsCrScanDefault', this.activeClassroom.curricShort);
    return this.onlineOrPaper.getPaperVal(isCrScanDefault, this.activeClassroom.curricShort);
  }
  // isSessionCompleted
  // if (this.checkOtherCompletedSessions(student.uid,this.subSessions[subSessionIndex].slug)){

  // showStudentInfo(student){

  // // Name: ${}
  // Course: Academic

  // Do you wish to update their record?`)
  //   }

  getSessionSlugOne(slug: string) {
    let targetSlug;
    let mappedSlug = ASSESSION_SLUG_MAPPING().find(record => record.source === slug);
    if(mappedSlug) {
      targetSlug = mappedSlug.target_1;
    }
    return targetSlug;
  }

  getSessionSlugTwo(slug: string) {
    let targetSlug;
    let mappedSlug = ASSESSION_SLUG_MAPPING().find(record => record.source === slug);
    if(mappedSlug) {
      targetSlug = mappedSlug.target_2;
    }
    return targetSlug;
  }

  isPJOperationalTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }

  isPJSampleTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE);
  }

  isPrimaryOrJunior() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }

  isG9OrOsslt() {
    return (this.asmtSlug === ASSESSMENT.G9_SAMPLE || this.asmtSlug === ASSESSMENT.G9_OPERATIONAL || this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE || this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL);
  }

  isG9Sample(){
    return this.asmtSlug === ASSESSMENT.G9_SAMPLE
  }

  getStudentQuestionResponseInfo(student: IStudentAccount, subSessionIndex: number): StudentQuestionResponseInfoStatusType {
    const studentUid:number = student.uid
    if(!this.isPJOperationalTest()) {
      return this.studentQuestionResponseInfoStatusType.None;
    }
    if (!studentUid) {
      throw 'Missing Student UId';
    }
    if (!this.scanInfo.studentScanInfoMap) {
      return this.studentQuestionResponseInfoStatusType.Loading;
    }
    const crQuestionsBySec = this.scanInfo.getCrQuestions(studentUid);
    const subSession = this.subSessions[subSessionIndex];
    if (crQuestionsBySec && subSession) {
      const sessionsKeys = Object.keys(crQuestionsBySec).sort();
      const sessionKey = sessionsKeys[subSession.order];
      if (sessionKey) {
        const session = crQuestionsBySec[sessionKey];
        const quesIds = Object.keys(session);
        const hasTaqr = !!session.hasTaqr;
        const isPaperFormat = session.isPaperFormat;
        // if no taqr
        if(!hasTaqr) {
          if(this.isWritingPaper(student)) {
            return this.studentQuestionResponseInfoStatusType.None; // show scan require icon for no taqr but the drop drop selection is paper
          }else {
            return this.studentQuestionResponseInfoStatusType.Online; // show online submit icon for no taqr but the drop drop selection is online
          }
        }
        if (quesIds && !isPaperFormat && hasTaqr ) {
          return this.studentQuestionResponseInfoStatusType.Online;
        }
        if (quesIds && isPaperFormat  && hasTaqr) {
          const question = session[quesIds[0]];
          if (question) {
            if (question.isConfirmed) {
              return this.studentQuestionResponseInfoStatusType.ScanConfirmed;
            } 
            if (question.isNeedsReupload) {
              return this.studentQuestionResponseInfoStatusType.ScanNeedsReupload;
            } 
            if (question.responses && question.responses.length > 0) {
              return this.studentQuestionResponseInfoStatusType.ScanUploaded;
            } else {
              return this.studentQuestionResponseInfoStatusType.None;
            }
          }
        }
        if(quesIds && !hasTaqr) {
          if(this.activeClassroom.curricShort === CURRICSHORT.EQAO_G3) {
            return this.studentQuestionResponseInfoStatusType.None;
          } 
          if(this.activeClassroom.curricShort === CURRICSHORT.EQAO_G6){
            return this.studentQuestionResponseInfoStatusType.Online;
          }
        }
      }
    }
    return this.studentQuestionResponseInfoStatusType.None;
  }

  get studentQuestionResponseInfoStatusType() {
    return StudentQuestionResponseInfoStatusType;
  }

  isStudentScanInfoMapInitialized(): boolean {
    if (this.scanInfo.studentScanInfoMap) {
      return true
    }
    return false
  }

  // isSasnLogin() {
  //   return this.classroomsService.isSASNLogin(this.classId);
  // }
  isUmsubmitting(){
    return this.g9DemoData.getIsUnsubmitting();
  }

  getMissingUnconfirmedScansNum() {
    if(!this.studentScanInfo) {
      return  { missingScans:null, unconfirmedScans: null };;
    }
    let missingScans = 0;
    let unconfirmedScans = 0;
    const paperStudents = this.onlineOrPaper.getPaperStudents();
    if(this.studentList.length > 0) {
      const students = this.studentList.map(student => {
        const studentSubSessionsState = this.getStudentState(student)['subSessions'];
        return {
          uid: student.id,
          studentSubSessionsState,
          isStudentWritingPaper: (paperStudents.includes(student.uid))
        };
      });
      if(students.length > 0) {
        students.forEach( student => {
          this.scanInfo.getMissingUnconfirmedScans(this.studentScanInfo, student, _ => {missingScans++;} , _ => {unconfirmedScans++;});
        })
      }
      return { missingScans, unconfirmedScans };
    }
  }




  getStudentAccommodations(index){
    return getActivatedAccommodations(this.classroomsService.genTeacherAccommProps(this.activeClassroom.curricShort), 
    this.studentList[index], 
    this.g9DemoData.g10PropCheckMarkMapping,
    this.whitelabelService,
    this.activeClassroom.curricShort,
    this.g9DemoData)
  }

  getStudentAssistiveTechs(index){
    return getActivatedAssistiveTechs(
      this.classroomsService.genTeacherAssisTechProps(this.activeClassroom.curricShort), 
      this.studentList[index], 
      this.g9DemoData.g10PropCheckMarkMapping,
      this.whitelabelService,
      this.activeClassroom.curricShort,
      this.g9DemoData);
  }


  getStudentExemptions(index){
    return this.g9DemoData.getStudentExemptions(
      this.studentList[index],
      this.activeClassroom.curricShort,
      this.classroomsService
    )
  }

  /**
   * This function is used to get accommodation group translation slug ( accommodation slug without specific read, write, math)
   * The specific slug can be get from function "getAccommodationSlug"
   * @param accommodation
   * @returns Slug
   */
  getAccGroupSlug (accommodation: string, ): string  {
    const accomGroupSlugMapping = Accom_Group_Slug_Mapping()
    
    //take out the prefix (something like "_g3_" "_g10_",  "_g6_") in accommodation
    let NoPrefixAccomodation = '';
    if(overlapVariables.includes(accommodation)) {
      NoPrefixAccomodation =  this.classroomsService.teacherAccommodationsLabels[accommodation];
    } else {
      NoPrefixAccomodation = accommodation.substring(this.getPrefix().length);
    }

    //find the accommodation translation slug (without specific read, write, math) for this accommodation
    const accomGroupSlug = accomGroupSlugMapping.find((e) => {
      if (e.value.indexOf(NoPrefixAccomodation) > -1) {
        return e.key;
      }
      return null
    });
    return accomGroupSlug ? accomGroupSlug.key : null;
  };

  getAccommodationSlug(title) {
    return this.classroomsService.getAccLabel(title, this.getPrefix.bind(this));
  }

  getAssistiveTechSlug(title){
    return this.classroomsService.getTechLabel(title, this.getPrefix.bind(this));
  }

  getExemptionSlug(title){
    return this.classroomsService.getExemptionLabel(title, this.getPrefix.bind(this));
  }

  getPrefix() {
    return this.g9DemoData.getNamespacePropPrefix(this.g9DemoData.getKeyNamespace(this.activeClassroom.curricShort));
  }

  /**
   * Check if student_uid have Login Issue
   * @param student_uid 
   * @returns 
   */
  haveLoginIssue(student_uid){
    return !!this.studentLoginIssueList.find(studentLoginIssue => +studentLoginIssue.uid === +student_uid)
  }
 
  /**
   * Checks if another PJ assessment session is active and unpaused (is_paused = 0)
   */
  isOtherSessionAlreadyActive() {
    const activeSessions = this.activeClassroom.openAssessments;

    const otherActiveSession = activeSessions.find(session => {
        return session.test_session_id !== this.activeSession.test_session_id && !session.is_paused;
    });

    return otherActiveSession;
  }

  /**
   * @returns true if the student is paid for the class
   */
  isStudentPaid(student: IStudentAccount) {
    return !!student.paidForClass[this.classId]?.isPaid;
  }

  /**
   * Get the earliest PJ operational sub-session start date (exclude null value).
   * This is used to determine lock status of student questionnaire,
   * to keep questionnaire lock until one of the sub-session is ready to start.
   * @returns {Date|string} The earliest start date of Math or Lang subSession
   */
  getEarliestPJOpSubSessStartDateForQ():Date|string {
    return this.subSessions
      .slice(0, -1) // Remove session_q
      .map(subSession => subSession.date_time_start)
      .filter(date => date) // Remove null date
      .reduce((earliest, current) => {
        return Date.parse(current) < Date.parse(earliest) ? current : earliest;
      });
  }
}

enum StudentQuestionResponseInfoStatusType {
  None = 0,
  ScanConfirmed = 1,
  ScanUploaded = 2,
  Loading = 3,
  Online = 4,
  ScanNeedsReupload = 5,
}

enum PJSampleLangTitle {
  pj_lang_session_A = 0,
  pj_lang_session_B = 1,
}

enum PJSampleMathTitle {
  pj_math_stage_1 = 0,
  pj_math_stage_2 = 1,
}

enum PJOperationalLangTitle {
  pj_lang_session_A = 0,
  pj_lang_session_B = 1,
  pj_lang_session_C = 2,
  pj_lang_session_D = 3,
}

enum PJOperationalMathTitle {
  pj_math_stage_1 = 0,
  pj_math_stage_2 = 1,
  pj_math_stage_3 = 2,
  pj_math_stage_4 = 3,
}