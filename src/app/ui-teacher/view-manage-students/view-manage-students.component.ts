import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { IStudentAccount, IStudentTestSession, } from '../../ui-schooladmin/data/types';
import { IStudent, IStudentList, ISession, PrintScanModal} from '../data/types';
import { ActivatedRoute } from '@angular/router';
import { SidepanelService } from '../../core/sidepanel.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { FormGroup, FormControl, AbstractControl } from '@angular/forms';
import { STUDENT_G9_COURSES_SIMPLE, STUDENT_GENDERS, STUDENT_G9_COURSES } from '../../ui-schooladmin/data/constants';
import {PageModalController, PageModalService} from "../../ui-partial/page-modal.service";
import { TeacherModal } from '../data/types';
import { LoginGuardService } from '../../api/login-guard.service';
import { ClassroomsService } from '../../core/classrooms.service'
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { IStudentInfoConfig, showScanModal } from '../t-modal-student-info-chooser/t-modal-student-info-chooser.component';
import { ScanInfoService } from '../scan-info.service';
import { I } from '@angular/cdk/keycodes';
import { TeacherViews, TEACHER_VIEWS } from './data/view';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
import { Subscription } from 'rxjs';

const trim = (str:any) => {
  if(!str){
    return null;
  } else return (str.toString() || '').trim();
}

const TEMP_META_MAPPING = { // to be replaced with the central
  eqao_student_gov_id: 'StudentOEN',
  eqao_g9_course: 'Program',
  eqao_g9_gender: 'Gender',
  teacher_notes: 'TeacherNotes',
  eqao_g9_sasn: 'SASN',
}
@Component({
  selector: 'view-manage-students',
  templateUrl: './view-manage-students.component.html',
  styleUrls: ['./view-manage-students.component.scss']
})
export class ViewManageStudentsComponent implements OnInit, OnDestroy {

  constructor(
    private route: ActivatedRoute,
    public lang: LangService,
    private auth: AuthService,
    private sidePanel: SidepanelService,
    public  loginGuard: LoginGuardService,
    private g9demoService: G9DemoDataService,
    private breadcrumbsService: BreadcrumbsService,
    private pageModalService: PageModalService,
    private classroomService: ClassroomsService,
    private routes: RoutesService,
    private scanInfo: ScanInfoService,
    private onlineOrPaper: OnlineOrPaperService,
  ) { }

  breadcrumb = [];
  studentList:IStudentAccount[];
  // students:IStudent[] = [];
  classroomId:string;
  activeClassroom;
  additionalInvigilators;
  guestClassInviligates;
  isCreatingStudents:boolean;
  formGroup = new FormGroup({
    first_name: new FormControl(),
    last_name: new FormControl(),
    eqao_student_gov_id: new FormControl(),
    eqao_g9_course: new FormControl('1'),
    eqao_g9_gender: new FormControl(),
    teacher_accomm: new FormControl(),
    teacherNotes: new FormControl(),
    lang: new FormControl(),
    class_access_code: new FormControl(),
  })
  pageModal: PageModalController;
  TeacherModal = TeacherModal;
  isLookedupStudentGovId:boolean;
  lookupButtonValue:string = this.lang.tra('student_account_look');
  studentExistsInAnotherClass:boolean;

  isScanInfoLoaded = false;
  isAddingVirtualClass = false;
  addButtonValue:string = this.lang.tra('ta_add_btn_value_look');

  TeacherViews = TeacherViews;
  views = [];
  selectedView: TeacherViews;
  guestClasses= [];
  currentQueryParams
  PrintScanModal = PrintScanModal;
  private classroomServiceSub: Subscription = null;

  

  ngOnInit(): void {
    this.sidePanel.activate();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
    });
    this.route.params.subscribe(params => {
      this.classroomId = params['classroomId'];
      this.sidePanel.classroomId = this.classroomId;
      this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
        if (data){
          this.loadStudentList();
          this.loadGuestClassList();
          this.loadAdditionalInviligatorList();
          this.loadGuestClassInviligateList();
          this.initRoute();
        }
      })
    });
    //console.log(this.classroomService.classrooms)
    this.pageModal = this.pageModalService.defineNewPageModal();

    this.views = [];
    this.selectedView = this.TeacherViews.Students;
    TEACHER_VIEWS.forEach(view => {
      this.views.push( Object({
        ...view,
        caption: this.lang.tra(view.caption),
        description: this.lang.tra(view.description),
      }))
    })
  }

  ngOnDestroy(): void {
    if(this.classroomServiceSub) {
      this.classroomServiceSub.unsubscribe();
    }
  }
  initRoute(){
    this.activeClassroom = this.g9demoService.getClassroomById(this.classroomId);
    if(this.activeClassroom.curricShort === 'EQAO_G10'){
      this.formGroup =  new FormGroup({
        first_name: new FormControl(),
        last_name: new FormControl(),
        eqao_student_gov_id: new FormControl(),
        eqao_g9_course: new FormControl(),
        eqao_g9_gender: new FormControl(),
        teacher_accomm: new FormControl(),
        teacherNotes: new FormControl(),
        lang: new FormControl(),
        class_access_code: new FormControl(),
      });
    }
    
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator/classrooms`
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_classes'), basePath, this.currentQueryParams),
      this.breadcrumbsService._CURRENT( classroomName, basePath+'/'+this.classroomId, this.currentQueryParams),
      this.breadcrumbsService._CURRENT( this.lang.tra('mng_manage_students'), basePath+'/students/'+this.classroomId, this.currentQueryParams),
    ];
  }

  getCaptionForExistingStudent(user: any, inSameClass: boolean) {
    let mainSlug;
    
    if(inSameClass) {
      mainSlug = this.isSasnLogin ? 'txt_student_exists_class_sasn':'txt_student_exists_class'
    } else if (user.isInSameTW){
      mainSlug = this.isSasnLogin ? 'txt_student_exists_tw_same_sasn':'txt_student_exists_tw_same'
    }
    else{
      mainSlug = this.isSasnLogin ? 'txt_student_exists_tw_diff_sasn':'txt_student_exists_tw_diff'
    } 
    let caption = `${this.lang.tra(mainSlug)}
          <br><br> ${this.lang.tra('lbl_student_info')} <br> ${this.lang.tra('lbl_first_name')}: ${user.first_name}
          <br> ${this.lang.tra('lbl_last_name')}: ${user.last_name}`;
    // If user is comes from lookupStudentGovId() try to show the student classes from the user object     
    if(!inSameClass) {
      if (user.classrooms) {
        user.classrooms.forEach(classroom => {
          caption += `<br> ${this.lang.tra('lbl_class')}: ${classroom.name} `;
        });
      } else { // This student is already exists in the current scope (Teachers students)
        this.g9demoService.classrooms.forEach(classroom => {
          let students = this.g9demoService.getStudentsByClassroomId(String(classroom.id)).list;
          const student = students.find(student => student.id == user.id);
          if(student) {
            caption += `<br> ${this.lang.tra('lbl_class')}: ${classroom.class_code} `;
          }
        });
      }   
    } 
    
    return caption;
  }

  creatNewStudentEnableOEN(){
    this.isCreatingStudents = true;
    this.formGroup.controls.eqao_student_gov_id.enable();
  }

  lookupStudentGovId(){
    let eqao_student_gov_id = this.formGroup.controls.eqao_student_gov_id.value;
    eqao_student_gov_id = trim(eqao_student_gov_id);
    const invalidMsg = this.isSasnLogin?'student_account_inv_sasn':'student_account_inv'
    if(!eqao_student_gov_id){
      return this.loginGuard.quickPopup(this.lang.tra(invalidMsg))
    }else if (!this.isSasnLogin && eqao_student_gov_id.length !== 9){
      return this.loginGuard.quickPopup(this.lang.tra(invalidMsg))
    }else if (this.isSasnLogin && eqao_student_gov_id.length > 9){
      return this.loginGuard.quickPopup(this.lang.tra(invalidMsg))
    }else if(!this.isSasnLogin && !/^\d+$/.test(eqao_student_gov_id)){
      return this.loginGuard.quickPopup(this.lang.tra(invalidMsg))
    }else if(this.isSasnLogin && !/^[A-Za-z0-9]+$/.test(eqao_student_gov_id)){
      return this.loginGuard.quickPopup(this.lang.tra(invalidMsg))
    }


    this.auth.apiFind(this.routes.EDUCATOR_STUDENTS, {
      query: {
        value: eqao_student_gov_id,
        schoolClassId: this.classroomId,
        school_class_group_id:this.getClassGroupId(),
        sasn_login: this.isSasnLogin ? 1:0
      }
    }).then( users => {
      let caption;
      let user = users?.length ? users.find(user => user.isInSameTW) : undefined;
      if (!user){
        caption = this.isSasnLogin ? this.lang.tra('lbl_no_student_sasn') : this.lang.tra('lbl_no_student_oen');
        this.studentExistsInAnotherClass = false;
      }
      else{ 
        this.studentExistsInAnotherClass = !user.classrooms.find( c => c.id == this.classroomId);
        caption = this.getCaptionForExistingStudent(user, !this.studentExistsInAnotherClass);
      }
      this.loginGuard.confirmationReqActivate({
        caption,
        confirm: () => {
          this.formGroup.controls.eqao_student_gov_id.disable();
          if (user){
            const controls = this.formGroup.controls;
            controls.first_name.setValue(user.first_name);
            controls.last_name.setValue(user.last_name);
            controls.eqao_g9_course.setValue('1'); // temp
            // if(user.meta[TEMP_META_MAPPING.eqao_g9_course]){controls.eqao_g9_course.setValue(user.meta[TEMP_META_MAPPING.eqao_g9_course]);}
            controls.eqao_g9_gender.setValue(user.meta[TEMP_META_MAPPING.eqao_g9_gender]);
            
            if(!this.studentExistsInAnotherClass) { //user in same class
              this.cancelStudentEntry();
              return;
            }
          }
          if (this.studentExistsInAnotherClass) {
            // Make a temperary object for student
            const tmpStudent: any = {
              id: user.id,
              first_name: user.first_name,
              last_name: user.last_name
            };
            // Add the necessary metas
            user.meta.forEach(m => {
              tmpStudent[m.key_namespace] = tmpStudent[m.key_namespace] || {}
              tmpStudent[m.key_namespace][m.key] = m.value;
            });
            tmpStudent.class_code = [...new Set(user.classrooms.map(c => c.name))];
            tmpStudent.classroomIds = [...new Set(user.classrooms.map(c => c.id))];
            // Make a proper IStudentAccount object
            const student = this.g9demoService.apiToClientPayloadStudent(tmpStudent);
            this.classroomService
              .moveStudent(student, this.getClassGroupId())
              .then(() => {
                this.cancelStudentEntry();
                // window.location.reload(); // to do, insert the student record             
                this.loadStudentList();
              })
          }
          else{
            this.isLookedupStudentGovId = true;
          }
        }
      })
    }).catch(error =>{
      if(error.message == "STUDENT_NOT_EXIST_IN_COURSE" ){
        window.alert(this.lang.tra("lbl_student_not_in_course"));
      }
    })
  }

  moveStudentIntoCurrentClass(uid:number){

  }

  validateFormEntries(controls:{label:string, fc:AbstractControl}[]){
    //console.log("CONTROLS-----------",controls)
    const invalidEntries = [];
    controls.forEach(control => {
      if (!trim(control.fc.value)){
        invalidEntries.push(this.lang.tra(control.label))
      }
    });
    if (invalidEntries.length){
      this.loginGuard.quickPopup(this.lang.tra("lbl_fill_missing_field") + `${invalidEntries.join(', ')}.`)
      return false;
    }
    return true;
  }
  getValidatableFormentries(){
    const controls = this.formGroup.controls;
    if(this.activeClassroom.curricShort === 'EQAO_G10'){
      return  [
          {label: 'OEN', fc: controls.eqao_student_gov_id},
          {label: 'lbl_first_name', fc: controls.first_name},
          {label: 'lbl_last_name', fc: controls.last_name},
          {label: 'sdc_1_gender', fc: controls.eqao_g9_gender},
        ];
      } else{
        return  [
          {label: 'OEN', fc: controls.eqao_student_gov_id},
          {label: 'lbl_first_name', fc: controls.first_name},
          {label: 'lbl_last_name', fc: controls.last_name},
          // {label: 'sdc_1_course', fc: controls.eqao_g9_course},
          {label: 'sdc_1_gender', fc: controls.eqao_g9_gender},
        ];
      }
  }
  addStudentEntry(){
    if (!this.isLookedupStudentGovId){
      return this.lookupStudentGovId()
    }
    const controls = this.formGroup.controls;
    let isValid = this.validateFormEntries(this.getValidatableFormentries());
    if (!isValid){
      return;
    }

    const isG3 = this.activeClassroom.curricShort === 'EQAO_G3';
    const isG6 = this.activeClassroom.curricShort === 'EQAO_G6';
    const isG9 = this.activeClassroom.curricShort === 'EQAO_G9';
    const isG10 = this.activeClassroom.curricShort === 'EQAO_G10';
    const localStudentRecord = {
      id: -1,
      first_name:          trim(controls.first_name.value),
      last_name:           trim(controls.last_name.value),
      eqao_student_gov_id: this.isSasnLogin?'*********':trim(controls.eqao_student_gov_id.value.toString()),
      eqao_g9_gender:      controls.eqao_g9_gender.value,
      eqao_gender:         controls.eqao_g9_gender.value,
      eqao_is_g3:          isG3 ? "1":"#",
      eqao_is_g6:          isG6 ? "1":"#",
      eqao_is_g9:          isG9 ? "1":"#",
      eqao_is_g10:         isG10 ? "1":"#",
      lang:                trim(controls.lang.value),
      teacher_notes:       controls.teacherNotes.value,
      uid:                 null,
      displayName:         trim(controls.first_name.value) + " " + trim(controls.last_name.value),
      SASN:                this.isSasnLogin?trim(controls.eqao_student_gov_id.value.toString()): null,
      paidForClass:        {}
    }
    
    let student:Partial<IStudentAccount> = {
      account: {
        first_name: trim(controls.first_name.value),
        last_name:  trim(controls.last_name.value),
      },
      meta: {
        [TEMP_META_MAPPING.eqao_student_gov_id]: this.isSasnLogin?'*********':trim(controls.eqao_student_gov_id.value.toString()),
        [TEMP_META_MAPPING.eqao_g9_gender]:      controls.eqao_g9_gender.value,
      },
      meta_clean: [
        { key_namespace:'eqao_sdc', key: 'TeacherNotes', value: controls.teacherNotes.value, }
      ]
    }

    if(isG3){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G3', value: 1} )
      student.meta_clean.push({ key_namespace:'eqao_sdc_g3', key: 'Grade', value: 3} )
    }
    if(isG6){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G6', value: 1 } )
      student.meta_clean.push({ key_namespace:'eqao_sdc_g6', key: 'Grade', value: 6} )
    }
    if(isG9){
      localStudentRecord['eqao_g9_course'] = controls.eqao_g9_course.value;
      student.meta[TEMP_META_MAPPING.eqao_g9_course] = controls.eqao_g9_course.value;
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G9', value: 1 })
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G10', value: '#'})

    }
    if(isG10){
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G9', value: '#'} )
      student.meta_clean.push({ key_namespace:'eqao_sdc', key: 'IS_G10', value: 1 } )
    }
    if(this.isSasnLogin){
      student.meta[TEMP_META_MAPPING.eqao_g9_sasn] = trim(controls.eqao_student_gov_id.value.toString())
    }
    

    this.classroomService
      .createStudent(this.classroomId, student, this.getClassGroupId(), this.isSasnLogin)
      .then( (studentUid) => {
        localStudentRecord.uid = studentUid
        localStudentRecord.id = studentUid;
        this.clearStudentEntry();
        const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
        students.list.push(localStudentRecord);
        const defaultWritingType = this.onlineOrPaper.getDefaultIsPaperValue(this.activeClassroom.curricShort);
        this.onlineOrPaper.addStudentWritingType(studentUid, defaultWritingType);
        // this.studentList
        // .push(
        //   this.g9demoService.generateStudentInvigilationRecord(localStudentRecord)
        // )
        // this.loginGuard.quickPopup(`A new student account has been created for ${localStudentRecord.first_name+' '+localStudentRecord.last_name}.`)
        this.loginGuard.quickPopup(this.lang.tra('lbl_new_student_added', undefined, {student_name: localStudentRecord.first_name+' '+localStudentRecord.last_name}))
        this.clearStudentEntry()
        this.isLookedupStudentGovId = false;
        this.isCreatingStudents = false;
      })
      .catch((e)=>{
        let caption:string = 'Could not create student record.';
        if (e.message === 'GOV_ID_IN_CLASS'){
          caption = this.lang.tra('lbl_oen_already_added')
        }
        this.loginGuard.quickPopup(caption)
      })
  }
  getClassGroupId(){
    let school_class_group_id = null;
    const currentClassRecord = this.classroomService.classrooms.find(classroom => classroom.id == this.classroomId)
    if (currentClassRecord){
      school_class_group_id = currentClassRecord.group_id;
    }
    return school_class_group_id;
  }
  cancelStudentEntry(){
    this.formGroup.reset();
    this.formGroup.controls.eqao_student_gov_id.enable();
    this.isLookedupStudentGovId = false;
    this.isCreatingStudents = false;
  }
  clearStudentEntry(){
    this.formGroup.reset();
  }

  loadStudentList(){
    const classRoom = this.g9demoService.getStudentsByClassroomId(this.classroomId);
    if(classRoom!== undefined){
      this.studentList = this.g9demoService.getStudentsByClassroomId(this.classroomId).list
    }  
    // .map((student, index) => {
    //   return this.g9demoService.generateStudentInvigilationRecord(student)
    // });
  }

  loadGuestClassList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.guestClasses = [];
    if(classRoom!== undefined){
      this.guestClasses = this.g9demoService.getguestClasssByHostClassroomGroupId(classRoom.group_id)
    }
  }

  loadGuestClassInviligateList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.guestClassInviligates = [];
    if(classRoom!== undefined){
      this.guestClassInviligates = this.g9demoService.getGuestClassInvigilateByHostClassroomGroupId(classRoom.group_id)
    }
  }

  loadAdditionalInviligatorList(){
    const classRoom = this.g9demoService.getClassroomById(this.classroomId);
    this.additionalInvigilators = [];
    if(classRoom!== undefined){
      this.additionalInvigilators = this.g9demoService.getAdditionalInvigilatorByGroupId(classRoom.group_id);
    }
  }

  renderCourse(id:number|string){
    return STUDENT_G9_COURSES_SIMPLE.map[id].label
  }
  renderGender(id:number|string){
    return STUDENT_GENDERS.map[id].label
  }
  getCourseList(){
    return STUDENT_G9_COURSES.list
  }
  getGenderList(){
    return STUDENT_GENDERS.list
  }

  timeConvert(n) {
    var num = n;
    var hours = (num / 60);
    var rhours = Math.floor(hours);
    var minutes = (hours - rhours) * 60;
    var rminutes = Math.round(minutes);
    if (rhours > 0){
      return rhours + "hr " + rminutes + "min";
    }
    return  rminutes + "min";
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() {  return this.cModal().config; }

  sampleModalStart(studentIdx: number){
    const config: IStudentInfoConfig = {studentList: this.studentList, curricShort: this.activeClassroom.curricShort, studentIdx};
    const isProceedOnly = this.showScanModal();
    this.pageModal.newModal({
      type: TeacherModal.SAMPLE_MODAL_OTHER,
      config,
      isProceedOnly,
      finish: this.sampleModalFinish
    });
  }

  showScanModal() {
    return showScanModal(this.activeClassroom.curricShort);
  }
  sampleModalFinish = () => {
    this.pageModal.closeModal();
  }

  existModalStart(account){
    const config = {account, curricShort: this.activeClassroom.curricShort};
    this.pageModal.newModal({
      type: TeacherModal.ALREADY_EXIST,
      config,
      finish: this.existModalFinish
    });
  }
  existModalFinish = () => {
    this.pageModal.closeModal();
  }

  inValidTestWindow(){
    const classroom = this.g9demoService.classrooms.find( classroom => classroom.id === +this.classroomId)
    const classSemester = this.g9demoService.semesters.map[classroom.semester]
    const classTestWindow = this.g9demoService.testWindows.find(tw => tw.id === classSemester.testWindowId)
    return (new Date(classTestWindow.date_end) < new Date ()) || classTestWindow.reg_locked
  }

  selectView(id: TeacherViews) {
    this.selectedView = id;
  }

  addVirtualClassEnableAccessCode(){
    this.isAddingVirtualClass = true;
  }

  addVirtualClassEntry(){
    let class_access_code = this.formGroup.controls.class_access_code.value;
    this.auth.apiCreate(this.routes.EDUCATOR_GUEST_STUDENTS, {
      class_access_code 
    },this.configureQueryParams())
    .then( res =>{
      this.g9demoService.addGuessClass(res.class)
      this.g9demoService.addGuessClassInvigilate(res.invigilate);
      this.loadGuestClassList()
      this.loadGuestClassInviligateList()
      this.clearStudentEntry();
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
      res.students.forEach(student =>{
        students.list.push(student);
      })
    })
    .catch(err =>{
      switch(err.message){
        case 'CLASS_DOES_NOT_EXIST':
          alert(this.lang.tra('ta_guessclass_not_exist'));
          break;
        case 'GUEST_CLASS_ALREADY_EXIST':
          alert(this.lang.tra('ta_guessclass_already_exist'));
          break;
        case 'GUEST_CLASS_IS_HOST_CLASS':
          alert(this.lang.tra('ta_guessclass_is_host'));
          break;
        case 'GUEST_CLASS_IS_SAME_SCHOOL':
          alert(this.lang.tra('ta_guessclass_in_same_school'));
          break;     
        default:
          break;
      }
    })
    this.isAddingVirtualClass = false;
  }

  revokeGuestClass(gclass){
    this.auth.apiRemove(this.routes.EDUCATOR_GUEST_STUDENTS, gclass.scg_id, this.configureQueryParams())
    .then( res =>{
      this.g9demoService.removeGuessClass(gclass.scg_id)
      this.loadGuestClassList()
      this.loadGuestClassInviligateList()
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId);
      const removeStudents = students.list.filter( student => student.scg_id == gclass.scg_id)
      removeStudents.forEach(student =>{
        const studentID = students.list.indexOf(student)
        students.list.splice(studentID,1)
      }) 
    })
    .catch(error =>{
      switch(error.message) {
        case "GUEST_STUDENT_ATTEMPT_STARTED":
          this.loginGuard.quickPopup(this.lang.tra('ta_guessclass_remove_not_allow_ta_started'))
          break
        default:
          alert(error.message)
          break
      }
    })
  }

  configureQueryParams(){
    const classrooom = this.g9demoService.getClassroomById(this.classroomId)
    const param =  
    {
      query: {
        school_class_group_id:classrooom.group_id
      }
    }
    return param
  }

  // Print Student Information Modal
  studentInfoModalStart(){
    const config = { 
      studentList: this.studentList, 
      activeClassroom: this.activeClassroom, 
      additionalInvigilators: this.additionalInvigilators,
    };  
    const isProceedOnly = true;
    this.pageModal.newModal({
      type: PrintScanModal.STUDENT_INFO,  
      config,
      isProceedOnly,
      finish: this.studentInfoModalFinish
    });
  }

  studentInfoModalFinish = () => {
    this.pageModal.closeModal();
  }

  get isSasnLogin():boolean {
    return this.classroomService.isSASNLogin(+this.classroomId);
  }

  sorting = {
    fieldName: "last_name", //default
    direction: "ASC" //A-Z
  }

  updateSorting = (fieldUserClicked: string) => {
    if (fieldUserClicked === this.sorting.fieldName){
      this.reverseTheOrder();
    } else {
      // we change the sorting field to the one that got clicked 
      this.sortBy(fieldUserClicked);
    }
  }

  private reverseTheOrder = () => {
    this.sorting.direction = this.sorting.direction === "ASC"? "DESC":"ASC"    
  }

  private sortBy = (fieldUserClicked: string) =>{
    this.sorting.fieldName = fieldUserClicked; 
    this.sorting.direction ="ASC";
  }

  isPj(){
    return this.activeClassroom.curricShort === "EQAO_G3" || this.activeClassroom.curricShort === "EQAO_G6"
  }
  getEducatorsStudentsHeader(){
    const SASN = this.lang.tra('lbl_sasn')
    const OEN = this.lang.tra('lbl_oen')
    return this.lang.tra('sa_educators_stu_header_EQAO', undefined, { OEN_SLUG: this.isSasnLogin ? SASN : OEN })
  }
}
