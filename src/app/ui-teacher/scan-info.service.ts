import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { IQuestionConfig } from '../ui-item-maker/item-set-editor/models';
import { identifyQuestionResponseEntries } from '../ui-item-maker/item-set-editor/models/expected-answer';
import { ITestDesignPayload } from '../ui-testtaker/view-tt-test-runner/view-tt-test-runner.component';
import { IGenRespSheetConfig } from './scan-button-bar/scan-button-bar.component';
import { EResponseType } from './t-modal-student-scan/t-modal-student-scan.component';
import { LoginGuardService } from '../api/login-guard.service';
import { LangService } from '../core/lang.service';
import { WhitelabelService } from '../domain/whitelabel.service';
import { downloadFile } from '../core/download-string';

const UPLOAD_COMPLETION_CHECKER_SEC = 5;

interface IScanInfoPayload {
  studentScanInfoMap: IStudentScanInfo,
  testSlugs: string[],
  sessionSlugs: string[]
}

interface IBulkReqScanInfoContents {
  [uid: number]: {
    file_path: string,
    test_form_id: number
  }
}

interface ISingleScanRes {
  croppedFile: string,
  uncroppedFile: string
}

export interface IPaperResponseInfo {
  url: string
}

type IOnlineResponseInfo = any;
export interface IScanResponse {
  responseType: EResponseType,
  info: IPaperResponseInfo | IOnlineResponseInfo
}
export interface IScanQuestion {
  [test_question_id: number]: any
}

export interface IScanQuesConfig {
  SESSION_A?: IScanQuestion,
  SESSION_B?: IScanQuestion,
  SESSION_C?: IScanQuestion
}

export const sessionSlugLangSlug: {sessionSlug: string, langSessionSlug: string}[] = [
  {sessionSlug: "SESSION_A", langSessionSlug: "lang_session_a"},
  {sessionSlug: "SESSION_B", langSessionSlug: "lang_session_b"},
  {sessionSlug: "SESSION_C", langSessionSlug: "lang_session_c"},
  {sessionSlug: "SESSION_DW", langSessionSlug: "lang_session_d"},
  {sessionSlug: "SESSION_DR", langSessionSlug: "lang_session_d"}
]

export interface IScanInfo {
  file_path: string,
  scanningQues: IScanQuesConfig,
  scanningQuesIds: number[],
  test_attempt_id: number,
  test_form_id: number
}

export interface IStudentScanInfo {
  [uid:number]: IScanInfo
}
@Injectable({
  providedIn: 'root'
})
export class ScanInfoService {

  constructor(private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService,
    public lang: LangService, 
    private sanitizer: DomSanitizer,
    private whiteLabel: WhitelabelService
  ) { 
  }

  isScanInfoLoaded: boolean = false;
  isUploadingSingle: boolean = false;
  isUploadingBulk: boolean = false;
  bulkScanData = {};
  isPrintingBulk: boolean = false;
  // isOverwriting: boolean = false;
  isGeneratingStudentResSheet: boolean = false;
  bulkStudentScanPdf: string; // base 64 string 
  studentScanInfoMap: IStudentScanInfo;
  sessionSlugsSet:any = new Set(); 
  currStudentResponseTemplates: any;
  timePerUploadCheck: number = UPLOAD_COMPLETION_CHECKER_SEC;
  bulkUploadTimer;

  findCrQuestion(scanInfo: any, qid: number): any {
    const scanningQues = scanInfo.scanningQues;
    const quesConfigs = [];
    for (const sec in scanningQues) {
      quesConfigs.push(scanningQues[sec]);
    }

    const qConfig = quesConfigs.find(qConfig => {
      const key = (Object.keys(qConfig))[0];
      return +key === qid;
    });

    return qConfig[qid];
  }

  loadScanInfo(classId: number, schl_class_group_id: number) {
    return this.auth.apiGet(this.routes.EDUCATOR_CLASS_SCAN_INFO, classId, {
      query: {
        schl_class_group_id
      }
    }).then(async (res: any) => {
      const {studentScanInfoMap, testSlugs}: IScanInfoPayload = res;
      testSlugs.forEach(slug => this.sessionSlugsSet.add(slug));
      this.studentScanInfoMap = studentScanInfoMap;
      this.isScanInfoLoaded = true;
    }).catch((e) => {
      console.log(e);
    })
  }

  loadSingleScanInfo(schl_class_group_id: number, tass_id: number){
    return this.auth.apiFind(this.routes.EDUCATOR_CLASS_SCAN_INFO, {
      query: {
        schl_class_group_id,
        tass_id
      }
    }).then(res => {
      // update this.studentScanInfoMap
      if(Object.keys(res).length === 0){
        return
      }
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].hasTaqr = res.hasTaqr
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isPaperFormat = res.isPaperFormat
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isNoPaperResponse = res.isNoPaperResponse
      this.studentScanInfoMap[res.uid].scanningQues[res.subsession_slug].isNotLegible = res.isNotLegible
    }).catch((e) => {
      console.log(e);
    })
  }

  base64ToArrayBuffer(base64) {
    let binaryString = window.atob(base64);
    let binaryLen = binaryString.length;
    let bytes = new Uint8Array(binaryLen);
    for (let i = 0; i < binaryLen; i++) {
       let ascii = binaryString.charCodeAt(i);
       bytes[i] = ascii;
    }
    return bytes;
  }

  generateResponseSheetPreview(byte) {
    let blob = new Blob([byte], {type: "application/pdf"});
    let fileURL = URL.createObjectURL(blob);
    let a = document.createElement('a');
    a.href = fileURL;
    a.download = 'StudentResponseSheets.pdf';
    a.dispatchEvent(new MouseEvent('click'));
  };

  generateResponseSheetLink(byte) {
    const pdfArrayBuffer = this.base64ToArrayBuffer(byte);
    let blob = new Blob([pdfArrayBuffer], {type: "application/pdf"});
    let fileURL = URL.createObjectURL(blob);
    const safeFileURL: SafeResourceUrl = this.sanitizer.bypassSecurityTrustResourceUrl(fileURL);
    return safeFileURL;
  }

  async generateResponseSheetPdf(base64: string) {
    const pdfArrayBuffer = this.base64ToArrayBuffer(base64);
    this.generateResponseSheetPreview(pdfArrayBuffer);
  }

  /** Generate bulk PDF for all students, immediately download file when ready */
  async getClassResponseTemplate(respSheetConfig: IGenRespSheetConfig) { 
    this.isPrintingBulk = true;
    try {
      const res = await this.auth.apiCreate(this.routes.EDUCATOR_GEN_RESPONSE_SHEETS, respSheetConfig);
      if (res.length > 0) {
        const resObj = res[0];
        await this.generateResponseSheetPdf(resObj.responsePdf);
      }
    } catch (err) {
      this.generateErrorPopupByErrMsg(err.message)
    }
    this.isPrintingBulk = false;
  }

  async getStudentResponseTemplates(respSheetConfig: IGenRespSheetConfig) { 
    this.isGeneratingStudentResSheet = true;
    try {
      const res = await this.auth.apiCreate(this.routes.EDUCATOR_GEN_RESPONSE_SHEETS, respSheetConfig)

      if (res.length > 0) {
        const resObj = res[0];
        const studentPdfs = JSON.parse(resObj.studentPdfs);
        this.currStudentResponseTemplates = new Map();
        studentPdfs.forEach(pdf => {
          const sessionSlug = pdf.session_slug;
          if (!this.currStudentResponseTemplates.has(sessionSlug)) {
            this.currStudentResponseTemplates.set(sessionSlug, pdf.base_64_response_sheet);
          }
        })
      }
    } catch (err) {
      this.generateErrorPopupByErrMsg(err.message)
    }
    this.isGeneratingStudentResSheet = false;
  }

  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            const result = reader.result as string;
            const withoutPrefix = result.substr(result.indexOf(',') + 1);
            resolve(withoutPrefix);
        };
        reader.onerror = error => reject(error);
    });
}


   /** Starting a bulk scan from a single file for many students in the class */
  async initSingleFileBulkScan(bulk_file_path: string, test_session_id: number, isSasn: number, schl_class_group_id:number, schl_class_id:number, is_override_flagged_only: number){
    const data = {
      isBulk: true,
      isSasn,
      bulk_file_path, 
      test_session_id,
      schl_class_group_id, 
      schl_class_id,
      is_override_flagged_only
    }
    return this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, data)
    .catch(err => {
      this.loginGuard.quickPopup(this.lang.tra('lbl_error'))
      throw new Error(err.message)
    })
  }

  showNotSubmittedModal() {
    this.loginGuard.confirmationReqActivate({
      caption:('pj_err_session_not_submitted'),
      isModalChain: true,
      modalChainLimit: 1
    });
  }

  /** Uploading a single scan for one student */
  async initUploadSingleScan(test_attempt_id:number, test_question_id:number, uid: number, file: File, scanningSlug: string, tassId: number, sessionSlug: string, schl_class_group_id: number) {
    this.isUploadingSingle = true;
    return this.auth.uploadFile(file, `user_scans/uploads/${file.name}`).then(res => {
      if(res.success && res.url) {
        const scanFileUrl = res.url;
        const scanFilePath = res.filePath;
        // Pass the file through the QR code scanner
        const query = { isSingleScanCheck: 1}
        const data = { 
          uid,
          sessionSlug,
          scanFileUrl: res.url
        }
        this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_QR_CONTENTS, data, {query})
        .then(() => {
          // If no errors (scan matches intended student and session) - proceed to upload
          const data = {
            scanFileUrl,
            scanFilePath,
            test_attempt_id,
            test_question_id,
            uid,
            scanningSlug,
            tassId,
            sessionSlug,
            schl_class_group_id
          }
          this.finishUploadSingleScan(data)
        })
        .catch((err) => {
          this.isUploadingSingle = false;
          switch (err.message){
            case "ERR_TOO_MANY_SCANS":
              return this.loginGuard.quickPopup(this.lang.tra('err_too_many_pages_msg'));
            case "NO_QR_DETECTED":
              return this.loginGuard.quickPopup(this.lang.tra('err_scan_no_qr_detected'));
            case "STUDENT_MISMATCH":
              return this.loginGuard.quickPopup(this.lang.tra('err_scan_student_mismatch'));
            // If matches student but not session, prompt whether to upload to the correct session instead.
            case "STUDENT_MATCH_SESSION_MISMATCH":
              const qrSessionSlug = err.data.qrSessionSlug
              const ogLangSessionSlug = sessionSlugLangSlug.find(r => r.sessionSlug == sessionSlug).langSessionSlug
              const qrLangSessionSlug = sessionSlugLangSlug.find(r => r.sessionSlug == qrSessionSlug).langSessionSlug
              return this.loginGuard.confirmationReqActivate({
                caption: this.lang.tra("err_scan_student_match_session_mismatch", undefined, {qr_session: this.lang.tra(qrLangSessionSlug), og_session: this.lang.tra(ogLangSessionSlug)}),
                btnProceedConfig: {caption: this.lang.tra('lbl_yes')},
                btnCancelConfig: {caption: this.lang.tra('lbl_no')},
                confirm: () => {
                  // Find relevant data for the session in the QR code, not the currently selected one
                  const qrSessionIndex = this.getSessionSlugs().findIndex(slug => slug == qrLangSessionSlug)
                  const selectedSession = this.getSessionSlug(uid, qrSessionIndex);
                  const questionId_qr = this.getQuesIdBySection(uid, selectedSession);
                  const sessionInfo = this.getCrQuestions(uid)[selectedSession];
                  const tassId_qr = sessionInfo.tassId;
                  const fileConfirmedAndSubmitted = sessionInfo[questionId_qr].isConfirmed && sessionInfo[questionId_qr].is_test_session_submitted
                  if (fileConfirmedAndSubmitted && sessionInfo[questionId_qr].responses.length > 0) {
                    this.loginGuard.quickPopup('err_upload_scan_prev_confirmed_session');
                  } else {
                    // Proceed to complete the upload, but to the session that matches the QR code instead
                    const data = {
                      scanFileUrl,
                      scanFilePath,
                      test_attempt_id,
                      test_question_id: questionId_qr,
                      uid,
                      scanningSlug: qrSessionSlug,
                      tassId: tassId_qr,
                      sessionSlug: qrSessionSlug,
                      schl_class_group_id
                    }
                    this.finishUploadSingleScan(data)
                    .then(() => {
                      // Confirm when the upload is done
                      const caption = this.lang.tra('lbl_succ_upload_scan_corrected_session', undefined, {qr_session: this.lang.tra(qrLangSessionSlug)})
                      this.loginGuard.quickPopup(caption)
                    });
                  }
                }
              })
            default:
              return this.loginGuard.quickPopup('err_check_single_scan'); 
          }
        })


      } else {
        throw new Error('Upload Failed')
      } 
    })
  }

  async finishUploadSingleScan (scanData: {scanFileUrl:string, scanFilePath: string, test_attempt_id:number, test_question_id:number, uid:number, scanningSlug: string, tassId: number, sessionSlug: string, schl_class_group_id: number}){
    this.isUploadingSingle = true;
    const {scanFileUrl, scanFilePath, test_attempt_id, test_question_id, uid, scanningSlug, tassId, sessionSlug, schl_class_group_id} = scanData
    return this.auth.apiCreate(this.routes.EDUCATOR_RESP_SHEET_UPLOAD_RES_SHEETS, 
      {
        scanFileUrl,
        scanFilePath,
        test_attempt_id, 
        test_question_id,
        sessionSlug,
        tassId,
        schl_class_group_id
      }).then( (resList: ISingleScanRes) => {
        if(resList) {
          const sectionQues = this.studentScanInfoMap[uid].scanningQues[scanningSlug]
          const crQuestion = sectionQues[test_question_id];
          crQuestion.isConfirmed = false;
          crQuestion.isNeedsReupload = false;
          sectionQues.isNeedsReupload = false;
          crQuestion.isNeedsReuploadScorLeadFlagged = false;
          if (crQuestion.responses.length > 0) {
            crQuestion.responses = [];
          }
          crQuestion.responses.push({
            responseType: EResponseType.PAPER,
            info: {
              url: resList['uncroppedFile'],
              url_cropped: resList['croppedFile']
            }
          })
        }
      }).catch((e) => {
        if (e.message === 'ERR_TOO_MANY_SCANS') {
          this.loginGuard.quickPopup(this.lang.tra('err_too_many_pages_msg'));
        } else if (e.message === 'ERR_SESS_NOT_SUBMITTED') {
          this.loginGuard.quickPopup('pj_err_session_not_submitted');
        }
      })
      .finally(() => {this.isUploadingSingle = false})

  }

  getMissingUnconfirmedScans(studentScanInfo: any, student: any, onMissingScans: (sessionKey: string) => void, onUnconfirmedScans: (sessionKey: string) => void, onExpectedScans?: (sessionKey: string) => void) {
    const {uid, isStudentWritingPaper, studentSubSessionsState} = student;
    for (const sess in studentScanInfo[uid]?.scanningQues) {
      const quesId = studentScanInfo[uid].scanningQuesIds[sess];
      const scanInfo = studentScanInfo[uid].scanningQues[sess][quesId];
      const isConfirmed = scanInfo.isConfirmed;
      const isOnPaper = studentScanInfo[uid].scanningQues[sess]['isPaperFormat'];
      const hasTaqr = studentScanInfo[uid].scanningQues[sess]['hasTaqr'];
      const langSessionSlug = sessionSlugLangSlug.find(record => record.sessionSlug == sess)?.langSessionSlug
      const subsession = studentSubSessionsState.find(ss => ss.subsession_slug == langSessionSlug);
      const isSubmitted = subsession?.is_submitted;
      // Expected if submitted subsession (otherwise can't upload scan), and either accessed on paper or didn't access but the setting is paper
      const isExpected = isSubmitted && ((hasTaqr && isOnPaper) || (!hasTaqr && isStudentWritingPaper))
      if (onExpectedScans && isExpected){
        onExpectedScans(sess)
      }
      // If expected but not available, is a missing scan
      if (isExpected && scanInfo.responses.length < 1 && !isConfirmed) {
        onMissingScans(sess);  
      }
      if (scanInfo.responses.length > 0 && isOnPaper && !isConfirmed) {
        onUnconfirmedScans(sess);
      }
    }
  }

  getScanInfo(uid: number) {
    return this.studentScanInfoMap[uid];
  }

  getScanSlugs(uid: number) {
    return Object.keys(this.getScanInfo(uid).scanningQues).sort();
  }

  getCrQuestions(uid: number) {
    return this.getScanInfo(uid)?.scanningQues || {};
  }

  getSessionSlug(uid: number, sessionIndex: number) {
    const sessionSlug = this.getScanSlugs(uid)[sessionIndex];
    return sessionSlug;
  }

  getCrSessionQuestions(uid: number, sessionIndex: number) {
    const sessionSlug = this.getSessionSlug(uid, sessionIndex);
    return this.getCrQuestions(uid)[sessionSlug];
  }

  getTestAttemptId(uid: number) {
    return this.getScanInfo(uid).test_attempt_id;
  }

  getSessionSlugs() {
    return this.sessionSlugsSet.size > 0 ? [...this.sessionSlugsSet].sort() : [];
  }
  
  isLoaded() {
    return this.isScanInfoLoaded;
  }

  getQuesIdBySection(uid: number, sessionSlug: string) {
    return this.studentScanInfoMap[uid]['scanningQuesIds'][sessionSlug];
  }

  /**
   * Generate error popup message according to error message received
   * And display popup to user
   * @param {string} errMessage error.message received from API endpoint
   */
  generateErrorPopupByErrMsg(errMessage:string){
    switch(errMessage){
      case 'STUDENT_LIST_MISMATCH':
        const reloadBtn = this.lang.tra('btn_reload')
        this.loginGuard.confirmationReqActivate({
          caption: this.lang.tra('fail_gen_response_sheet_stu_list_change', null, { 'RELOAD': reloadBtn }),
          btnProceedConfig: {
            caption: reloadBtn
          },
          confirm: () => {
            window.location.reload()
          }
        })
        break;
      default:
        this.loginGuard.quickPopup(this.lang.tra('failure_gen_response_sheet'));
    }
  }
}
