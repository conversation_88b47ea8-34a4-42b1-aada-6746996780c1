
<div class="page-body is-offwhite">
  <div>
    <header
      [breadcrumbPath]="breadcrumb"
      [hasSidebar]="true"
    ></header>
    <div style="padding:4em; padding-top:1em;">
      <div *ngIf="isShowHeaderInstr">
        <tra-md slug="sa_educators_assesment_header"></tra-md>
      </div>
      <div  *ngIf="session_id" class="active-classroom">
        <div class="pre-table-strip">
          <div>
            <div style="margin-bottom:1em;">
              <div style="font-weight:600; font-size: 1.7rem;">
                <tra-md [slug]="activeSession.name"></tra-md>
                <!-- <tra-md slug="g9_assess_title"></tra-md> -->
              </div>
              <div [ngStyle]="{visibility: isLoaded ? 'visible' : 'hidden'}">
                <div *ngIf="isSessionOpened"> <tra slug="g9_started_on"></tra> {{activeSession.dateTimeStartLong}} </div>
                <div *ngIf="!isSessionOpened && isG9OrOsslt()"> Session Scheduled for {{activeSession.timeDirectStart}} at {{activeSession.hourDirectStart}}</div>
                <div *ngIf="!isSessionOpened && isPrimaryOrJunior()"> Session Scheduled for {{activeSession.timeDirectStart}}</div>
              </div>
            </div>
            <div 
              [ngStyle]="{visibility: isLoaded ? 'visible' : 'hidden'}" 
              [ngClass]="[isSessionOpened ? 'button-group' : '', 
              isPrimaryOrJunior() ? 'btn-border-radius' : '']">
              <div style="display: flex; flex-direction: column;">
                <button 
                  class="button is-small has-icon head-btn is-paused-btn-pj" 
                  [class.is-success]="!didSessionStart" 
                  [class.is-dark]="isSessionPaused && didSessionStart && isG9OrOsslt()" 
                  (click)="togglePauseSession()" 
                  [disabled]="!isSessionOpened">
                  <span *ngIf="didSessionStart && !isSessionPaused" class="icon">
                    <i class="far fa-pause-circle"></i>
                  </span>
                  <span *ngIf="!didSessionStart || isSessionPaused" class="icon">
                    <i class="far fa-play-circle"></i>
                  </span>
                  <span *ngIf="didSessionStart">
                    <span *ngIf="!isSessionPaused">
                      <b><tra slug="g9_pause_session"></tra></b>
                    </span>
                    <span *ngIf="isSessionPaused">
                      <b><tra slug="resume_session"></tra></b>
                    </span>
                  </span>
                  <span *ngIf="!didSessionStart">
                    <b><tra slug="start_session"></tra></b>
                  </span>
                </button>
                <b style="color: red; text-align: center;">
                  <tra-md *ngIf="pausedByUser" slug="invig_paused_by" [props]="{ INVIG_NAME: pausedByUser }"></tra-md>
                </b>               
              </div>
              <button *ngIf="false" class="button is-small has-icon" [disabled]="!isOpenSessionEnabled">
                <span class="icon"><i class="fas fa-lock-open"></i></span>
                <span><tra slug="open_session"></tra></span>
              </button>
              <button class="button is-small has-icon" [class.is-danger-pj]="isPrimaryOrJunior()" [class.is-danger]="isG9OrOsslt()" (click)="reportIssue()">
                <span class="icon"><i class="far fa-flag"></i></span>
                <span>
                  <b><tra slug="btn_report_issue"></tra></b>
                </span>
              </button>
              <button *ngIf="canEditDate()" (click)="editModalStart()" class="button is-small has-icon" [class.is-info-pj]="isPrimaryOrJunior()" [class.is-info]="isG9OrOsslt()">
                <span class="icon"><i class="fas fa-pen"></i></span>
                <span *ngIf="isG9OrOsslt()"><tra slug="btn_edit_session_date_time"></tra></span>
                <span *ngIf="isPrimaryOrJunior()"><tra slug="btn_edit_session_date_pj"></tra></span>
              </button>
              <button *ngIf="!isSessionOpened"  class="button is-small has-icon" (click)="cancelScheduledSession()">
                <span class="icon"><i class="fas fa-times"></i></span>
                <span>
                  <b><tra slug="g9_btn_cancel_session"></tra></b>
                </span>
              </button>
              <button *ngIf="showShowUnsubmitBtn()" (click)="isUnsubmitBtnVisiable = !isUnsubmitBtnVisiable" class="button is-small" [ngClass]="isG9OrOsslt() ? 'unsubmit-btn' : (isPrimaryOrJunior() ? 'unsubmit-btn-pj' : null)">
                <span class="icon" *ngIf="isPrimaryOrJunior()"><i class="fas fa-redo-alt"></i></span>
                  <b><span>{{ getUnsubmitBtnText() }}</span></b>
              </button>
              <div *ngIf="isSessionOpened && isPrimaryOrJunior()">
                <button *ngIf="isPJSampleTest()" (click)="submitAssessmentSessions()" class="button is-small has-icon is-success-pj" [disabled]="!didSessionStart">
                  <span class="icon"><i class="far fa-hand-paper"></i></span>
                  <span>
                    <b><tra [slug]="submitBtnSlug()"></tra></b>
                  </span>
                </button>
                <button *ngIf="isPJOperationalTest()" (click)="reviewSubmissionsModalStart()" class="button is-small has-icon is-success-pj" [disabled]="!didSessionStart">
                  <span class="icon"><i class="far fa-hand-paper"></i></span>
                  <span>
                   <b><tra [slug]="submitBtnSlug()"></tra></b>
                  </span>
                </button>
              </div>
              <div *ngIf="isSessionOpened && isG9OrOsslt()" style = "display: flex; flex-direction: column; max-width: 20em;">
                <button (click)="submitAssessmentSessions()" class="button is-small is-success has-icon" [disabled]="!didSessionStart">
                  <span class="icon"><i class="fas fa-upload"></i></span>
                  <span><tra [slug]="submitBtnSlug()"></tra></span>
                </button>
                <div style="color: #B30000; padding-top: 0.5em; padding-left: 0.5em;">	
                  <tra-md slug='txt_pj_submit_note'></tra-md>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="activeClassroom" [ngClass]="isSessionOpened ? 'class-code' : null">
            <class-code-indicator
              [isPJ]="isPrimaryOrJunior()"
              [isG9OrOsslt]="isG9OrOsslt()"
              [classCode]="activeClassroom.classCode"
              accessScope="lbl_for_sessions"
            ></class-code-indicator>
          </div>
        </div>
        <div *ngIf="!isScoreEntrySession">
          <div class='one-em-padding'>
            <span *ngIf="!numUnlockedStudents"><tra slug="lbl_student_no_acess"></tra></span>
            <span *ngIf="numUnlockedStudents"><tra-md slug="lbl_student_no_acess_param" [props]="{numUnlockedStudents: numUnlockedStudents}"></tra-md></span>
          </div>
          <div *ngIf="studentLoginIssueList.length" class='one-em-padding'>
            <h3 class="login-issue-alert"><i class="fa fa-exclamation is-danger"></i><tra slug="stu_login_issue"></tra></h3>
          </div>
          <div class='one-em-padding'>
            <tra-md [slug]='getStudentPostSlug()'></tra-md>
          </div>
          <div class='one-em-padding'>
            <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'txt_ldb_invigilation_instructions_beta' : 'txt_ldb_invigilation_instructions'"></tra>&nbsp;
            <a (click)="showLDBProctorPassword()"><tra [slug]="'lbl_ldb_show_password'"></tra></a>
          </div>
          <div class="one-em-padding">
            <tra [slug]="'txt_ldb_invigilation_config'"></tra>&nbsp;
            <a (click)="openLDBConfigModal()"><tra [slug]="IS_RESPONDUS_BETA_MODE ? 'ldl_ldb_show_config_beta' : 'ldl_ldb_show_config'"></tra></a>
          </div>
          <div *ngIf="!whitelabel.isNBED() && !whitelabel.isMBED() && !whitelabel.isABED()" class='one-em-padding'>
            <tra-md slug='txt_submit_results_warning'></tra-md>
            <tra-md slug='txt_clarify_password_warning'></tra-md>
          </div>
          <div *ngIf="isBulkPrint()" class="notification is-warning">
            Printing...
          </div>
          <div *ngIf="isBulkUpload()" class="notification is-warning">
            Uploading response sheets...<br>
            Time until next check for upload completion: {{ getBulkUploadTime() }}s
          </div>
          <div *ngIf="isSessionInitializing || isRefreshingSessionsInfo">
            <div class="notification" style="text-align: center;">
              <tra slug="init_assessment_session"></tra>
              <div *ngIf="isAllowLoadRetry" style="margin-top:2em;">
                <button class="button is-small" (click)="loadSessionInfo()">Retry</button>
              </div>
            </div>
          </div>
        </div>
        <div class='one-em-padding horizontal-container' *ngIf="!isRefreshingSessionsInfo">
          <scan-button-bar *ngIf="showScanInfo()" 
            (reviewClicked)="sampleModalStart(0)" 
            [testSessionId]="session_id" 
            [asmtSlug]="asmtSlug" 
            [classroomId]="classroomId"
            [studentList]="studentList"
            [currentQueryParams]="currentQueryParams"
            (showPrintScanInstruction)="instructionModalStart()" 
            (showPrintStudentInfo)="studentInfoModalStart()" 
            [invigilateView]="true"
            [isFIOptionC]="isFIOptionC"
            [isPrimary]="isPrimary()" 
            [isPrintingBulk]="isBulkPrint()"
          ></scan-button-bar>


          <div style="margin-top: 0.5rem;" *ngIf="isLoaded">
            <student-list class="dont-print"
              [sorting]="sorting"
              [updateSorting]="updateSorting"
              [studentList]="studentList"
              [studentSocketState]="studentSocketState"
              [studentLoginIssueList]="studentLoginIssueList"
              (openStudentLoginIssueModal)="openStudentLoginIssueModal($event)"
              [testSessionId]="session_id"
              [studentStates]="studentStates"
              [closeAllSubSessions]="closeAllSubSessions"
              (isAllSubSessionsClosed)="finalizeSessionSubmission($event)"
              [subSessions]="subSessions"
              [completedSubSessions]="completedSubSessions"
              [activeSubSessions]="activeSubSessions"
              (selectionUpdate)="onStudentSelectionUpdate($event)"
              (locksUpdate)="onStudentLockUpdate($event)"
              (studentLockActionEmitter)="onLockActionsUpdate($event)"
              (openStudentInfo)="sampleModalStart($event)"
              (resetSoftLock)="onResetStudentSoftLock($event)"
              [classId]="classroomId"
              [activeSession]="activeSession"
              [isSessionOpened]="isSessionOpened"
              (openSoftlockModal)="toggleSoftLockModal($event)"
              [activeClassroom]="activeClassroom"
              [isNoTdOrder]="isNoTdOrder"
              (reloadSessionInfo) = "reloadSessionInfo($event)"
              [(isUnsubmitBtnVisiable)] = "isUnsubmitBtnVisiable"
              [isForceUnsubmitAbility]="isForceUnsubmitAbility"
              [isSecreteUser] ="isSecreteUser"
              [asmtSlug] = "asmtSlug"
              [isSasnLogin] = "isSasnLogin"
              [studentScanInfo]="getScanInfoMap()"
            ></student-list>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="TeacherModal.SAMPLE_MODAL">
        <!-- <t-student-data [savePayload]="cmc()" saveProp="payload"></t-student-data> -->
        <t-modal-student-info-chooser               
          [testSessionId]="session_id"
          [classId]="classroomId"
          [asmtSlug] = "asmtSlug"
          [config]="cmc()"
          [pageModal]="pageModal"
          [showRADropdown]="false"
          [isSasnLogin] = "isSasnLogin"
          [isFIClass]="isFIOptionC"
          [isPrimary]="isPrimary()"
        ></t-modal-student-info-chooser>
      </div>
      <div *ngSwitchCase="TeacherModal.EDIT_MODAL" style="width: 40em;">
        <t-modal-edit-session 
          [savePayload]="cModal().config" 
          saveProp="payload"  
          [canEditSessionA]="showSessionAEdit" 
          [canEditSessionB]="showSessionBEdit" 
          [isShowSessionB]="isShowSessionB"
          [canEditSessionLang]="showSessionLangEdit"
          [canEditSessionMath]="showSessionMathEdit"
          [asmtSlug]="asmtSlug"
          [isFIClass]="isFIOptionC"
          [currTestWindow]="currTestWindow"
        ></t-modal-edit-session>
      </div>
      <div *ngSwitchCase="TeacherModal.REPORT_MODAL">
        <t-modal-report-issue 
          [studentList]="studentList" 
          [reportIssuesCategoryList]="reportIssuesCategories"
          [component_slugs]="component_slugs"
          [savePayload]="cModal().config"
          (reportClicked)="reportModalFinish(cModal().config)"
          (cancelClicked)="pageModal.closeModal()"
        ></t-modal-report-issue>
      </div>
      <div *ngSwitchCase="TeacherModal.REVIEW_SUBM_MODAL" style="width: 40em;">
        <!-- MODAL WHEN SUBMIT TO EQAO -->
        <t-modal-review-student-submissions 
          [sorting]="getSortingForPopUp()"
          [testSessionId]="session_id" 
          (studentMapEmitter)="initStudentReviewedMap($event)" 
          [studentList]="studentList" 
          [completedSubsessions]="completedSubSessions" 
          [subSessions]="subSessions" 
          [studentSubsessionStates]="studentStates" 
          [studentScanInfo]="getScanInfoMap()"
          [isFIClass]="isFIOptionC"
          [activeClassroom]="activeClassroom"
        ></t-modal-review-student-submissions>
      </div>
      <div *ngSwitchCase="TeacherModal.STUDENT_LOGIN_ISSUE_MODAL">
        <t-modal-resolve-login-issue 
          [config]="cmc()"
          (closeModal)="StudentLoginIssueModalFinish()"
          (resolveLoinIssueApprove)="resolveLoinIssueApprove($event)"
          (resolveLoinIssueDeny)="resolveLoinIssueDeny($event)"
        ></t-modal-resolve-login-issue>
      </div>
      <div *ngSwitchCase="PrintScanModal.INSTRUCTION_MODAL" style="width: 50em;">
        <t-modal-print-scan-instruction></t-modal-print-scan-instruction>
      </div>
      <div *ngSwitchCase="TeacherModal.AUTO_SUBMISSION_WARNING_CONFIRMATION" style="width: 45em;">
        <t-modal-session-auto-submission-warning></t-modal-session-auto-submission-warning>
      </div>
      <div *ngSwitchCase="PrintScanModal.STUDENT_INFO" style="width: 40em;">
        <t-modal-print-student-info [config]="cmc()" [isSasnLogin] = "isSasnLogin"></t-modal-print-student-info>
      </div>
      <div *ngSwitchCase="LDBModal.LDB_CONFIG_MODAL" style="width: 80em;">
        <modal-ldb-config 
          [config]="cmc()"
          [pageModal]="pageModal"
        ></modal-ldb-config>
      </div>
    </div>
    <modal-footer 
      *ngIf="!(cModal().type === TeacherModal.SAMPLE_MODAL || cModal().type === TeacherModal.REPORT_MODAL || cModal().type === TeacherModal.STUDENT_LOGIN_ISSUE_MODAL|| cModal().type === LDBModal.LDB_CONFIG_MODAL)"
      [isEditDisable]="checkIfOkDisabled()" 
      class="dont-print" 
      [pageModal]="pageModal"
    ></modal-footer>
  </div>
</div>

<div *ngIf="showSoftlockModal">   
  <div class="confirmation-overlay modal-screen-cover">
    <div class="modal-screen-cover-content  is-boxed">
      <div class="modal-scrollpane">
        <div><tra-md slug="msg_student_navigation_warning_choice" [props]="getNumTimesOffScreenProps()"></tra-md></div>
        <div class="btn-group-container">
          <button class="btn-container button is-warning" (click)="pauseStudentTest()"><tra slug="btn_pause"></tra></button>
          <button class="btn-container button is-info" (click)="openDismissPopup()"><tra slug="dismiss_btn"></tra></button>
        </div>
      </div>
    </div>
  </div>      
</div>


