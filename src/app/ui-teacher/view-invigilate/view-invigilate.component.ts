import { Component, OnInit, OnDestroy, Input, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationStart, Router, Event as NavigationEvent } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service'
import { IStudentAccount, IStudentTestSession } from '../../ui-schooladmin/data/types';
import { ASSESSMENT, IStudent, IStudentList, ISession, IClassroom, ISessionBegin, PrintScanModal } from '../data/types';
import * as moment from 'moment-timezone';
import { cloneDeep } from 'lodash';
import { ListSelector } from '../../ui-partial/list-select.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { TeacherModal } from '../data/types';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { IStudentPositionUpdate, IStudentLoginIssue,  StudentG9ConnectionService, IStudentLoginIssueResolved } from '../../ui-student/student-g9-connection.service';
import { IStudentState, StudentListComponent } from '../student-list/student-list.component';
import { AssessmentService } from '../../core/assessment.service';
import { etz, mtz, tzLangAbbrs } from '../../core/util/moment';
import { IStudentInfoConfig, showScanModal } from '../t-modal-student-info-chooser/t-modal-student-info-chooser.component';
import { ScanInfoService } from '../scan-info.service';
import { OnlineOrPaperService } from '../../../../src/app/ui-testrunner/online-or-paper.service';
import { filter, first, switchMap } from 'rxjs/operators';
import { APP_VERSION } from 'src/version';
import { AccountType } from '../../constants/account-types';
import { ITestWindow } from '../../api/models/db/test_windows.schema';
import { LDBConfigLevel, LDBModal } from 'src/app/ui-partial/modal-ldb-config/modal-ldb-config.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { IS_RESPONDUS_BETA_MODE } from './../../ui-student/lockdown.service';

export interface IReviewModalConfig {
  studentStates: any,
  studentScanInfo: any,
  subSessions: any,
  asmtSlug?: ASSESSMENT
}

export enum CURRICSHORT {
  EQAO_G3 = 'EQAO_G3', 
  EQAO_G6 = 'EQAO_G6'
}

export enum STUDENT_LOGIN_VERIFICATION_TEACHER_ACTION {
  approve = 'approve',
  deny = 'deny',
}

const INTERVAL_DURATION = 60 * 1000;

@Component({
  selector: 'view-invigilate',
  templateUrl: './view-invigilate.component.html',
  styleUrls: ['./view-invigilate.component.scss']
})
export class ViewInvigilateComponent implements OnInit, OnDestroy {

  //Ann Sorting ticket
  getSortingForPopUp = () => {
    return {
      fieldName: {first_name:"firstName", last_name:"lastName"}[this.sorting.fieldName],
      direction: this.sorting.direction 
    }
    //fieldName: lookupTable[this.sorting.fieldName] this is a lookup table
    // const lookupTable = {first_name:"firstName", last_name:"lastName"}
  }
  @ViewChild(StudentListComponent) studentListChildRef: StudentListComponent
  subSessionId: any;
  closeAllSubSessions: boolean;
  showSessionAEdit: boolean = false;
  showSessionBEdit: boolean = false;
  isShowSessionB:boolean = false;
  showSessionLangEdit: boolean = false;
  showSessionMathEdit: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
    private sidePanel: SidepanelService,
    public loginGuard: LoginGuardService,
    private g9demoService: G9DemoDataService,
    private breadcrumbsService: BreadcrumbsService,
    private classroomService: ClassroomsService,
    private pageModalService: PageModalService,
    private studentG9Connection: StudentG9ConnectionService,
    private assessmentService: AssessmentService,
    private scanInfo: ScanInfoService,
    private onlineOrPaper: OnlineOrPaperService,
    public whitelabel: WhitelabelService,
  ) {

  }

  // title=this.lang.tra('g9_assess_title');
  IS_RESPONDUS_BETA_MODE = IS_RESPONDUS_BETA_MODE;
  isSubmitted = true;
  isActive = true;
  routeSub: Subscription;
  initSessionInfoSub = new Subject();
  session_id: string;
  classroomId: string;
  activeSession: any = {};
  isScoreEntrySession: boolean = false;
  sampleStudents: any[];
  studentList: IStudentAccount[] = []
  studentEntry = {
    oen: '',
    firstname: '',
    lastname: '',
    accommodation: ''
  }
  studentReviewedStatusMap: any;
  hasReviewMapInit: boolean = false;
  softLockCount = new Map();
  isOpenSessionEnabled: boolean
  isCloseSessionEnabled: boolean;
  didSessionStart: boolean = true;
  isSessionPaused: boolean = false
  showSoftlockModal: boolean = false;
  breadcrumb = [];
  pageModal: PageModalController;
  TeacherModal = TeacherModal;
  LDBModal = LDBModal;
  isShowHeaderInstr: boolean = false; // because it needs to be customized based on the assessment type
  numUnlockedStudents = 0;
  activeClassroom: IClassroom;
  studentStates: { [key: number]: IStudentState };
  studentSocketState: { [key: number]: any } = {};
  subSessions;
  activeSubSessions;
  completedSubSessions:boolean = false;
  isSessionOpened: boolean = true;
  isLoaded: boolean;
  pauseStudentFunc: any;
  currentStudentOpenForSoftlock: number; // uid
  studentLockActionMap: any;
  studentsToTrackLockAction: any;
  studentLoginIssueList: IStudentLoginIssue[] = [];
  isUnsubmitToggleVisible: boolean = false;
  isUnsubmitBtnVisiable: boolean = false;
  isSecreteUser
  currentQueryParams
  connectedStudentsSub;
  disconnectedStudentsSub:Subscription;
  updateStudentPositionSub;
  updateStudentSoftLockSub;
  autoSubmissionTeacherPopupSub;
  studentLoginIssueSub;
  studentLoginIssueResolveSub
  userSub: Subscription;
  isNoTdOrder: boolean;
  asmtSlug: ASSESSMENT;
  PrintScanModal = PrintScanModal;
  isRefreshingSessionsInfo: boolean = false;
  reportIssuesCategories: any;
  component_slugs: string[] = [];
  private classroomServiceSub: Subscription = null;
  isSubmitValid:boolean = false;
  reloginFromFormSubscript:Subscription;
  currTestWindow: ITestWindow;
  completedInitilizedAttemptStudents: number = 0;
  isConfirmSessionPassed: boolean = false
  pausedByUser;

  ngOnInit(): void {
    const fromInit = true
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.sidePanel.activate();
    this.loginGuard.activate();
    this.route.queryParams.subscribe((queryParams) => {
      this.currentQueryParams = {
        school: queryParams['school']
      }
    });
    if(this.g9demoService.getIsFromSchoolAdmin()){
       this.classroomService.reloadSchool()
     }  
    this.classroomServiceSub = this.classroomService.sub().subscribe(data => {
      if (data && !this.isSessionInitializing) {
        this.loadRoute(fromInit);
      }
    })
    this.initSessionInfoSub.subscribe((data) => {
      if (data) {
        this.loadStudentList()
      }
    })

    this.autoSubmissionTeacherPopupSub = this.studentG9Connection.autoSubmissionTeacherPopupSub.subscribe((data) => {
      if(data){
        // This function will be called when websocket event type'autoCloseWarning' happens 
        this.handleAutoCloseWarning(data);
      }
    })

    //re-initilize redis test session, When user relogin from the login guard popup form
    this.initUserReloginSub(fromInit)
    
    window.onbeforeunload = () => this.ngOnDestroy();
  }

  /**
   * When user relogin from the login guard popup form, on first non-null UserInfo from this.auth.user(), call loadRouten.
   * Used to re-initilize redis test session.
   * @param fromInit 
   */
  initUserReloginSub (fromInit = false){
    this.reloginFromFormSubscript = this.loginGuard.reloginFromLoginForm.pipe(
      switchMap(() => this.auth.user().pipe(
        filter(user => user !== null && user !== undefined),
        first()
      ))
    ).subscribe(user => {
      this.loadRoute(fromInit);
    });
  }

  // Check if current class a Primary class
  // Other class type could be OSSLT, G9 or Junior
  isPrimary() {
    const primaryAssessmentSlugs = [
      ASSESSMENT.PRIMARY_SAMPLE,
      ASSESSMENT.PRIMARY_OPERATIONAL
    ]
    return primaryAssessmentSlugs.includes(this.asmtSlug as ASSESSMENT);
  }

  isPrimaryOrJunior() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }

  isPJSampleTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE);
  }

  isPJOperationalTest() {
    return (this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL);
  }

  isG9OrOsslt() {
    return (this.asmtSlug === ASSESSMENT.G9_SAMPLE || this.asmtSlug === ASSESSMENT.G9_OPERATIONAL || this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE || this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL);
  }  

  isOperational() {
    return this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL || this.asmtSlug === ASSESSMENT.G9_OPERATIONAL || this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL;
  }

  isOssltOperationalTest() {
    return this.asmtSlug === ASSESSMENT.OSSLT_OPERATIONAL;
  }

  getSessionStart(session) {
    this.setSessionOpenState(session)
  }

  checkIfUnsubmitToggleVisible() {
    return this.isUnsubmitToggleVisible;
  }

  toggleUnsubmitFeature() {
    this.isUnsubmitBtnVisiable = !this.isUnsubmitBtnVisiable
    return this.isUnsubmitBtnVisiable;
  }

  isWritingPaper(student: IStudentAccount) {
    const isCrScanDefault = this.g9demoService.getPropVal(student, 'IsCrScanDefault', this.activeClassroom.curricShort);
    return this.onlineOrPaper.getPaperVal(isCrScanDefault, this.activeClassroom.curricShort);
  }

  initPaperOnlineStudents() {
    const students: IStudentAccount[] = this.studentList;
    const studentsWritingConfig = {};
    students.forEach(stu => {
      const writingPaperBool = !!this.isWritingPaper(stu);
      studentsWritingConfig[stu.uid] = writingPaperBool;
    })
    this.onlineOrPaper.initStudentsWritingType(studentsWritingConfig);
  }

  setSessionOpenState(session) {
    let currDate = new Date();
    let sessionDate = new Date(session.date_time_start)
    if (currDate.getTime() > sessionDate.getTime()) {
      const isG9orOsslt = session.asmt_slug == ASSESSMENT.G9_SAMPLE || session.asmt_slug == ASSESSMENT.G9_OPERATIONAL || session.asmt_slug == ASSESSMENT.OSSLT_SAMPLE || session.asmt_slug == ASSESSMENT.OSSLT_OPERATIONAL; 
      const currentSession = this.getScheduledAssessmentById(session.id);
      if (currentSession) {
        const mStart = moment.tz(session.date_time_start, moment.tz.guess());
        let timezone = mStart.zoneAbbr();
        timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
        const currentOpenSession = {
          date_time_start:currentSession.date_time_start,
          slug:currentSession.slug,
          access_code:currentSession.access_code,
          school_class_id:currentSession.school_class_id,
          test_session_id: session.id,
          dateTimeStartLong: isG9orOsslt ? `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}` : `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`,
          name: currentSession.name,
          timeDirectStart: currentSession.timeDirectStart,
          hourDirectStart: currentSession.hourDirectStart,
        }
        this.activeSession = currentOpenSession;
        this.activeClassroom.openAssessments.push(currentOpenSession);
        this.removeAssessment(this.activeClassroom.scheduledAssessments, session.id);
      }

      this.isSessionOpened = true;

    }
    else {
      //scheduled for the future
      this.isSessionOpened = false;
    }
  }

  addRecentSession(sessionToClose){
    const currentSession = this.getAssessmentById(sessionToClose.test_session_id);
    if (currentSession) {
      const mStart = moment.tz(this.activeSession.date_time_start, moment.tz.guess());
      const sessionCloseTime = new Date()
      const mClosed = moment.tz(sessionCloseTime, moment.tz.guess());
      const timeDirectClose = mClosed.format(this.lang.tra('datefmt_day_month'));
      const session = {
        date_time_start:currentSession.date_time_start,
        slug:currentSession.slug,
        access_code:currentSession.access_code,
        school_class_id:currentSession.school_class_id,
        test_session_id: currentSession.id,
        timeDirectClose,
        dateTimeStartLong: mStart.format(this.lang.tra('datefmt_sentence')),
        name: currentSession.name,

        timeDirectStart: currentSession.timeDirectStart,
        hourDirectStart: currentSession.hourDirectStart,
      }
      return session;
    }
  }

  isBulkUpload() {
    return this.scanInfo.isUploadingBulk;
  }

  isBulkPrint() {
    return this.scanInfo.isPrintingBulk;
  }

  renderTitle(title: string) {
    switch (title) {
      case "G9_OPERATIONAL": return "Grade 9 Assessment of Mathematics";
      case "G9_SAMPLE": return "Sample Test";
    }
  }

  renderDay(dateTime) {
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format(this.lang.tra('datefmt_day_month'));
  }
  private updateStudentSocketStates(connectedStudents: any[]) {
    console.log('updateStudentSocketStates', connectedStudents)

    const prevStudentSocketState = cloneDeep(this.studentSocketState);
    this.studentSocketState = {};
    for (let student of connectedStudents) {
      if (prevStudentSocketState[student.uid]) {
        this.studentSocketState[student.uid] = prevStudentSocketState[student.uid];
      } else {
        this.studentSocketState[student.uid] = {
          stageIndex: student.stageIndex,
          questionCaption: student.questionCaption,
          questionIndex: student.questionIndex,
          submitted: student.submitted,
          softLock: student.softLock,
          numTimesOffScreen: 0,
        }
      }
    }

    // Only keep those online in the having login issue student list  when student online/off status changed
    this.removeOfflineStudentfromLoginIssue()
  }
  private updateDisconnectedSocketStates(disconnectedUids: any[]) {
    //this.studentSocketState = {};
    //console.log("disconnected sub",disconnectedUids)
    for (let studentUid of disconnectedUids) {
      //console.log(studentUid,"LOGGING OFFLINE UID")
      delete this.studentSocketState[studentUid];
    }
    // Only keep those online in the having login issue student list when student online/off status changed
    this.removeOfflineStudentfromLoginIssue()
  }

  /**
   * This function is called when student online status changed to keep Student Login Issue List align with student online/offline status
   * Those offline student should be removed from the Student Login Issue List
   */
  removeOfflineStudentfromLoginIssue(){
    this.studentLoginIssueList = this.studentLoginIssueList.filter(studentLoginIssue => {
      if(this.studentSocketState[+studentLoginIssue.uid]){
        return studentLoginIssue
      }
    })
  }

  private updateStudenSoftLock(studentSoftLockUpdate){

  }

  onResetStudentSoftLock(uid){
    // The line below would reset the softLockCount when the teacher dismissed the softlock notification 
    // this.softLockCount.set(uid, 0); 
    // this.studentSocketState[uid]['softLock'] = 1; // CLOSER_LOOK_20210807 this line was introduced without the line below on a March 18  (which was added on a separate branch on March 23). Without knowing too much about how this piece of the code operates, it seems like a safe bet to activate this chunk and have it set back to anotehr value based on the below condition but I may be wrong
    if(this.studentSocketState[uid]) {
      this.studentSocketState[uid]['softLock'] = 0
      // this.studentSocketState[uid]['softLock'] = this.softLockCount
    }
  }

  pauseStudentTest() {
    // this.pauseStudentFunc();
    this.studentListChildRef.pauseStudentAndDismissSoftLockNotification();
    this.toggleSoftLockModal({openModal: false});
  }

  openDismissPopup() {
    this.studentListChildRef.openDismissModal();
    this.toggleSoftLockModal({openModal: false});
  }

  private updateStudentPosition(studentPositionUpdate: IStudentPositionUpdate) {
    if (!studentPositionUpdate || !this.studentSocketState[studentPositionUpdate.uid]) {
      return;
    }
    // need to keep the previous numTimesOffScreen to prevent softlock notification saying 'undefined' numtimesOffScreen
    let prevNumTimesOffScreen = 0;
    if (this.studentSocketState[studentPositionUpdate.uid]) {
      prevNumTimesOffScreen = this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'];
    }
    // this.studentSocketState[studentPositionUpdate.uid] = {};
    this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] = prevNumTimesOffScreen;

    for(let [key, _val] of Object.entries(studentPositionUpdate)) {
      const val:{subSessionIndex:number, submitted:boolean|number} =  <any>_val;
      if(key === "uid"){
        continue;
      } 
      else if(key === "submitConfig" && val !== undefined && val !== null) {
        this.studentSocketState[studentPositionUpdate.uid][`submitted${val.subSessionIndex}`] = val.submitted; 
        //Update the student states, since we know it will now contain updated lock information for that student
        this.loadSessionInfo();
      } 
      else if(key === "softLock" && val != null && val !== undefined){
        // let softLockValue = this.studentSocketState[studentPositionUpdate.uid][key];// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
        if (this.softLockCount.has(studentPositionUpdate.uid)){
          let softLockCount = this.softLockCount.get(studentPositionUpdate.uid);

          // do not increase the softLockCount if the student refreshes their page or exits fullscreen
          if (this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] !== studentPositionUpdate['numTimesOffScreen']) {
            softLockCount++;
          }

          // update softLockCount in the teacher's browser, mainly used for continuing the count if the student refreshes their page
          const numTimesOffScreen = Math.max(softLockCount, studentPositionUpdate['numTimesOffScreen']);
          this.softLockCount.set(studentPositionUpdate.uid, numTimesOffScreen);
          this.studentSocketState[studentPositionUpdate.uid][key] = this.softLockCount;
          // this.studentSocketState[studentPositionUpdate.uid][key] = 1 // CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
        }
        else{
          const softLockCount = this.softLockCount.set(studentPositionUpdate.uid,1)
          this.studentSocketState[studentPositionUpdate.uid][key] = softLockCount;
          // this.studentSocketState[studentPositionUpdate.uid][key]++;// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
        }
        // this.studentSocketState[studentPositionUpdate.uid][key] = val;// CLOSER_LOOK_20210807 this block is slightly older (5 days) and less detailed than what is shown above... it looks like both are doing the same thing
      }
      else if(val !== undefined && val !== null) {
        // if(this.softLockCount.has(studentPositionUpdate.uid)){
        //   //let softLockCount = this.softLockCount.get(studentPositionUpdate.uid) 
        //   this.studentSocketState[studentPositionUpdate.uid]['softLock'] = this.softLockCount;
        // }
        this.studentSocketState[studentPositionUpdate.uid][key] = val;
        // update student's numTimesOffScreen, mainly used for continuing the count if the student refreshes their page
        if (key === "numTimesOffScreen") {
          const softLockCount = this.softLockCount.get(studentPositionUpdate.uid);
          const numTimesOffScreen = Math.max(softLockCount, studentPositionUpdate['numTimesOffScreen']);
          this.studentSocketState[studentPositionUpdate.uid]['numTimesOffScreen'] = numTimesOffScreen;
        }
      }
    }
  }

  startSession() {
    this.didSessionStart = !this.didSessionStart
  }

  toggleSoftLockModal(studentWarningConfig: any) {
    this.showSoftlockModal = studentWarningConfig.openModal;
    this.currentStudentOpenForSoftlock = studentWarningConfig.openModal ? studentWarningConfig.uid : null;
  }

  getNumTimesOffScreenProps() {
    return {NUM_TIMES: this.studentSocketState[this.currentStudentOpenForSoftlock]['numTimesOffScreen']};
  }

  async togglePauseSession() {
    if (!this.didSessionStart) {
      this.startSession();
    } else {
      const isPaused = !this.isSessionPaused;
      const otherActiveSession = this.isOtherSessionAlreadyActive();
      
      try {
        // Trigger "Pause Other Session" popup if unpausing current session and is pj
        if (!isPaused && this.isPrimaryOrJunior() && otherActiveSession) {
          const descriptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_operational") : this.lang.tra("invig_unlock_sample");
          const description = this.lang.tra("invig_unlock_caption", "", {ASSESSMENT: descriptionProps})
          const subCaptionProps = this.isPJOperationalTest() ? this.lang.tra("invig_unlock_sample") : this.lang.tra("invig_unlock_operational");
          const subCaption = this.lang.tra("invig_unlock_subcaption", "", {ASSESSMENT: subCaptionProps.toLowerCase()})
          this.loginGuard.confirmationReqActivate({
            description,
            subCaption,
            width: '34em',
            btnProceedConfig: {caption: "invig_unlock_pause"},
            btnCancelConfig: {caption: "invig_unlock_go_back"},
            confirm: async() => {
              await this.pauseSession(isPaused)

              await this.auth.apiUpdate(this.routes.EDUCATOR_SESSION, otherActiveSession.test_session_id, {openAssessments: this.activeClassroom.openAssessments}, {query: { school_class_group_id: this.classroomService.getClassroomById(this.classroomId).group_id}})
              const otherSession = this.activeClassroom.openAssessments.find(assessment => assessment.test_session_id == otherActiveSession.test_session_id)
              otherSession.is_paused = 1
            },
            close: () => {
            }
          })
        } else{
          await this.pauseSession(isPaused)
        }
      } catch (error) {
        console.error("Error toggling pause session:", error);
      }
    }
  }

  /**
   * Sends request to session pausing endpoint and sets 
   */
  async pauseSession(isPaused:boolean){
    const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);

    const res = await this.auth.apiPatch(this.routes.EDUCATOR_SESSION, this.session_id, { is_paused: isPaused }, params);
    this.pausedByUser = res.pausedByUser;
    this.isSessionPaused = isPaused;
  }
  

  loadRoute(fromInit:boolean = false) {
    if (this.routeSub) { this.routeSub.unsubscribe(); }
    this.routeSub = this.route.params.subscribe(async (params) => {
      this.session_id = params['asmtSessionId'];
      this.classroomId = params['classroomId'];
      await this.g9ConnectionInit();

      this.sidePanel.classroomId = this.classroomId;
      this.isSessionInitializing = true;
      this.initInterval();
      this.loadSessionInfo(fromInit);
      this.initSessionInfoSub.next(true);
      this.updateBreadCrumbs();
      this.simulateAttemptInitialization()
    });
    this.route.queryParams.subscribe(queryParams => {
      // if (queryParams.showUnsubmit) {
      //   queryParams.showUnsubmit === 'true' ? this.isUnsubmitToggleVisible = true : this.isUnsubmitToggleVisible = false;
      // }
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
        this.isUnsubmitToggleVisible = true // allow isSecreteUser to unsubmit G9 as well
        this.isForceUnsubmitAbility = true
      }
    })
  }
  isForceUnsubmitAbility

  g9ConnectionInit() {
    if(!this.userSub) {
      this.userSub = this.auth.user().subscribe( async (info) => { //ensure that the uid is set before connecting
        if (this.classroomId !== undefined) {
          const classroom = this.classroomService.getClassroomById(this.classroomId)
          if(classroom){
            this.studentG9Connection.setClassId(classroom.group_id);
          }
          if (this.connectedStudentsSub) {
            console.log('skipping student connection')
          }
          else{
            console.log('init student connection')
            this.connectedStudentsSub = this.studentG9Connection.connectedStudentsSub
              .subscribe(connectedStudents => {
                console.log('updating connnected students')
                this.updateStudentSocketStates(connectedStudents)
              });
    
            this.updateStudentPositionSub = this.studentG9Connection.updateStudentPositionSub
              .subscribe((studentPositionUpdate: IStudentPositionUpdate) => {
                this.updateStudentPosition(studentPositionUpdate);
                // If student was having login issue and started writing, auto resolve student login issue
                if (this.studentLoginIssueList.find(studentIssue => +studentIssue.uid === +studentPositionUpdate.uid)) {
                  this.removeStudentfromLoginIssueList(studentPositionUpdate)
                }
              })

            this.studentLoginIssueSub = this.studentG9Connection.studentLoginIssueSub
              .subscribe((studentLoginIssue: IStudentLoginIssue) => {
                this.addStudentToLoginIssueList(studentLoginIssue);
              })

            this.studentLoginIssueResolveSub = this.studentG9Connection.studentLoginIssueResolveSub
              .subscribe((studentLoginIssueResolve: IStudentLoginIssueResolved) => {
                this.removeStudentfromLoginIssueList(studentLoginIssueResolve);
              })  

            this.disconnectedStudentsSub = this.studentG9Connection.disconnectedStudentsSub
              .subscribe(uid => {
                if(uid && uid.length > 0){
                  this.updateDisconnectedSocketStates(uid)
                }
              });


            await this.studentG9Connection.connect();
          }
        }
      }) 
    }
  }

  updateBreadCrumbs() {
    const classroomName = this.g9demoService.getClassroomNameById(this.classroomId)
    const basePath = `/${this.lang.c()}/educator`;
    const asmtName = this.activeSession.name || this.lang.tra('lbl_assessment_session');
    if(this.g9demoService.getIsFromSchoolAdmin()){
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('sa_dashboard_school_admin'), `/${this.lang.c()}/school-admin/dashboard`, this.currentQueryParams),
        this.breadcrumbsService._CURRENT("Assessment Sessions", `/${this.lang.c()}/school-admin` + '/sessions', this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, basePath + '/assessment/' + this.classroomId + '/' + this.session_id, this.currentQueryParams),
      ];
    }else{
      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('lbl_classes'), basePath, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(classroomName, basePath + '/classrooms/' + this.classroomId, this.currentQueryParams),
        this.breadcrumbsService._CURRENT(asmtName, basePath + '/assessment/' + this.classroomId + '/' + this.session_id, this.currentQueryParams),
      ];
    }
  }

  onStudentLockUpdate(res: { num: number }) {
    this.numUnlockedStudents = res.num;
  }

  reportIssue() {
    this.loginGuard.confirmationReqActivate({
      caption: 'view_invigilate_report_issue_disc',
      width: '40em', 
      confirm: () => {
        this.reportModalStart()
      }
    })
    

  }
  
  isLanguageVisibleForFIClass(): boolean {
    const isFI = this.activeClassroom?.is_fi == 1 ? true : false;
    if(this.g9demoService.schoolDist[0].fi_option === 'C' && isFI) {
      return false;
    }
    return true;
  }

  get isFIOptionC():boolean {
    return this.isLanguageVisibleForFIClass() ? false : true;
  }

  isSessionInitializing:boolean;
  isAllowLoadRetry:boolean;
  isScanStateInited:boolean;
  loadSessionInfo(fromInit:boolean = false) {
    this.isAllowLoadRetry = false
    this.activeClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId];
    const activeSession = this.g9demoService.getSessionByClassroomId(this.classroomId, this.session_id);
    this.currTestWindow = this.getTestWindowInfo(+this.classroomId)

    const isG9orOsslt = activeSession.slug == ASSESSMENT.G9_SAMPLE || activeSession.slug == ASSESSMENT.G9_OPERATIONAL || activeSession.slug == ASSESSMENT.OSSLT_SAMPLE || activeSession.slug == ASSESSMENT.OSSLT_OPERATIONAL; 
    const mStart = etz(activeSession.date_time_start)
    const mStartTime = mStart.format('h:mm A');
    let timezone = mStart.zoneAbbr();
    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    activeSession.dateTimeStartLong = isG9orOsslt ? `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}` : `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`;
    if(this.lang.c() === 'fr'){
      activeSession.dateTimeStartLong = tzLangAbbrs(activeSession.dateTimeStartLong, 'fr')
    }
    activeSession.hourDirectStart = `${mStartTime} ${timezone}`;
    this.activeSession = activeSession;
    //const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
    const classroom = this.classroomService.getClassroomById(this.classroomId)
    if(!classroom){
      return;
    }
    const params = {
      query: {
        school_class_group_id: classroom.group_id,
        school_class_id:this.classroomId,
        fromInit: fromInit ? 1:0, // pass the info to API to tell when the teacher init this page, to load data into redis once.
        client_app_version: APP_VERSION,
      }
    }

    this.studentG9Connection.logWebsocketInfo('WS_EDUCATOR_HEARTBEAT', this.studentSocketState)

    this.auth
        .apiGet(this.routes.EDUCATOR_SESSION, this.session_id, params)
        .then(res => {
          this.handleSessionInfoRes(res, fromInit);
          // this.activeSession.dateTimeStartLong = this.isG9OrOsslt() ? `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}` : `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`;
        })
        .then(res => {
          if (this.showScanInfo && !this.isScanStateInited) {
            this.isScanStateInited = true;
            this.scanInfo.loadScanInfo(+this.activeClassroom.id, this.activeClassroom.group_id)
          }
          this.togglethisIsSessionInitializing(false);
        })
        .catch(e =>{
          if(e.message === 'CLIENT_VERSION_OUTDATED'){
            this.loginGuard.quickPopup("msg_client_version_outdated")
            return
          }
          if(e.message === 'SESSION_INITIALIZING'){
            this.isAllowLoadRetry = true
          }
          if(e.message === 'TEST_SESSION_IS_NOT_ACTIVE'){
            this.clearInterval()
            this.removeAssessment( this.activeClassroom.openAssessments, this.session_id )
            this.closedSessionDetectedPopup()
          }
          setInterval(()=>{
            this.isAllowLoadRetry = true
          }, 30*1000)
        })
  }
    ngOnDestroy() {
      this.clearInterval();
      this.studentG9Connection.disconnect();
      if(this.classroomServiceSub) {
        this.classroomServiceSub.unsubscribe();
      }
      if(this.connectedStudentsSub) {
        this.connectedStudentsSub.unsubscribe();
      }
      if(this.updateStudentPositionSub) {
        this.updateStudentPositionSub.unsubscribe();
      }
      if(this.userSub) {
        this.userSub.unsubscribe();
      }
      if (this.initSessionInfoSub) {
        this.initSessionInfoSub.unsubscribe();
      }
      if(this.reloginFromFormSubscript){
        this.reloginFromFormSubscript.unsubscribe();
      }
      if (this.autoSubmissionTeacherPopupSub) {
        this.autoSubmissionTeacherPopupSub.unsubscribe();
      }
      if(this.studentLoginIssueSub){
        this.studentLoginIssueSub.unsubscribe();
      }
      if(this.studentLoginIssueResolveSub){
        this.studentLoginIssueResolveSub.unsubscribe();
      }
    }

    ticker
    initInterval() {
      if(!this.ticker){
        this.ticker = window.setInterval(() => {
          this.loadSessionInfo();
          // this.refreshComputedState();
        }, INTERVAL_DURATION)
      }  
    }

    clearInterval() {
      window.clearInterval(this.ticker);
    }


    loadStudentList() {
      const students = this.g9demoService.getStudentsByClassroomId(this.classroomId)
      if(students){
        this.studentList = this.g9demoService.getStudentsByClassroomId(this.classroomId).list;
        this.initPaperOnlineStudents();
      }  
    }

    submitAssessmentSessions() {
      let slug = this.activeSession.slug;
      let caption = this.lang.tra('warn_submit_student_assessment2');
      let subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
      if(this.isFIOptionC) {
        if(slug === ASSESSMENT.PRIMARY_SAMPLE) {
          caption = this.lang.tra('pj_fi_sample_submit_results_caption');
          subCaption = this.lang.tra('pj_fi_submit_results_subcaption_1');
        }
        if(slug === ASSESSMENT.PRIMARY_OPERATIONAL) {
          caption = this.lang.tra('pj_fi_submit_results_caption');
          subCaption = this.lang.tra('pj_fi_submit_results_subcaption_1');
        }
      } else {
        if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.JUNIOR_SAMPLE) {
          caption =  this.lang.tra('pj_sample_submit_results_caption')
          subCaption = this.lang.tra('pj_sample_submit_results_subcaption_1');
        } else if( slug === ASSESSMENT.G9_OPERATIONAL) {
          caption =  this.lang.tra('warn_submit_student_assessment3')
          subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
        } else if(slug === ASSESSMENT.OSSLT_OPERATIONAL){
          caption =  this.lang.tra('warn_submit_student_assessment2')
          subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
        }else if( slug === ASSESSMENT.G9_SAMPLE || slug === ASSESSMENT.OSSLT_SAMPLE) {
          caption =  this.lang.tra('warn_submit_student_assessment_sample')
          subCaption = this.lang.tra('warn_submit_student_assessment2_proceed');
        }
      }

      // switch(this.activeSession.slug){
      //   case ASSESSMENT.PRIMARY_SAMPLE:
      //   case ASSESSMENT.JUNIOR_SAMPLE:
      //     caption =  this.lang.tra('pj_sample_submit_results_caption')
      //   break;
      //   case "G9_OPERATIONAL":
      //     caption =  this.lang.tra('warn_submit_student_assessment3')
      //   break;
      //   case "G9_G9_SAMPLE":
      //     caption =  this.lang.tra('warn_submit_student_assessment3')
      //   break;
      //   case "OSSLT_OPERATIONAL":
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;
      //   case "OSSLT_SAMPLE":
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;
      //   default:
      //     caption =  this.lang.tra('warn_submit_student_assessment2')
      //   break;   
      // }

      this.loginGuard.confirmationReqActivate({
        caption,
        subCaption,
        requireTextInput: ['yes', 'oui'],
        confirm: () => {
          this.closeAllSubSessions = true;
          const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
          let params = {
            query: {
              school_class_group_id: groupIdQuery.query.school_class_group_id,
              is_session_completed: false
            }
          }
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.backToPrevPage()
            });
          // this.classroomService
          //   .closeAssessmentSession(this.classroomId, this.session_id, params)
          //   .then(() => {
          //     this.activeClassroom.recentAssessments.push(session);
          //     this.classroomService.navigateToClassroom(this.classroomId);
          //   })
        }
      })
    }

    getUnsubmitBtnText(): string {
      if(this.isUnsubmitBtnVisiable) {
        if(this.isPrimaryOrJunior()) {
          return this.lang.tra('pj_lbl_hide_unsubmit_button');
        } 
        if (this.isG9OrOsslt()) {
          return this.lang.tra('lbl_hide_unsubmit_button');
        }
      } else {
        if(this.isPrimaryOrJunior()) {
          return this.lang.tra('pj_lbl_show_unsubmit_button');
        } 
        if (this.isG9OrOsslt()) {
          return this.lang.tra('lbl_show_unsubmit_button');
        }
      }
    }

    reviewSubmissionsModalStart() {
      const config: IReviewModalConfig = { subSessions: this.subSessions, studentStates: this.studentStates, studentScanInfo: this.scanInfo.studentScanInfoMap, asmtSlug: this.asmtSlug};
      this.pageModal.newModal({
        type: TeacherModal.REVIEW_SUBM_MODAL,
        config,
        finish: this.startPjSubmissionModal.bind(this)
      })
    }

    startPjSubmissionModal() {    // for PJ Operational Test
      this.pageModal.closeModal();
      if(this.isFIOptionC){
        this.submitAssessmentSessions();
      } else {
        this.submitPJAssessmentSessions();
      }
    }

    submitPJAssessmentSessions() {
      const caption = this.lang.tra('pj_submit_results_caption');
      const subCaption = this.lang.tra('pj_submit_results_subcaption_1');
      const checkboxCaption = this.lang.tra('pj_submit_results_subcaption_2');
      this.loginGuard.confirmationReqActivate({
        caption,
        subCaption: subCaption,
        requireCheckboxInput: {
          checkboxCaption: checkboxCaption
        },
        requireTextInput: ['yes', 'oui'],
        confirm: () => {
          this.closeAllSubSessions = true;
          const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
          let params = {
            query: {
              school_class_group_id: groupIdQuery.query.school_class_group_id,
              is_session_completed: false
            }
          }
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.backToPrevPage();
            });
        }
      })
    }

    finalizeSessionSubmission($event){
      const sessionToClose = this.activeSession;
      const session = this.addRecentSession(sessionToClose);
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      // CloseAssessmentSession is done in function "submitAssessmentSessions" (OSSLT/G9/PJ_Sample) or submitPJAssessmentSessions(PJ_OPEATIONAL)
      // No need to close AssessmentSession again
      // let params = {
      //   query: {
      //     school_class_group_id: groupIdQuery.query.school_class_group_id,
      //     is_session_completed: true
      //   }
      // }
      // this.classroomService
      // .closeAssessmentSession(this.classroomId, this.session_id, params)
      // .then(() => {
      //   this.activeClassroom.recentAssessments.unshift(session);
      //   this.backToPrevPage()
      // })
      this.activeClassroom.recentAssessments.unshift(session);
      this.backToPrevPage()
      this.closeAllSubSessions = false;
    }

    removeAssessment(assessments, sessionId) {
      for (let i = 0; i < assessments.length; i++) {
        const session = assessments[i];
        if (session.test_session_id == sessionId) {
          assessments.splice(i, 1);
        }
      }
    }
    getScheduledAssessmentById(sessionId) {
      return this.activeClassroom.scheduledAssessments.find(session => session.test_session_id === sessionId)
    }
    getAssessmentById(sessionId) {
      return this.activeClassroom.openAssessments.find(session => session.test_session_id === sessionId)
    }
    cancelScheduledSession() {
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id,
          is_session_completed: false
        }
      }
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('alert_cancel_session'),
        confirm: () => {
          this.classroomService
            .closeAssessmentSession(this.classroomId, this.session_id, params)
            .then(() => {
              this.backToPrevPage();
            })
        }
      })
    }

    clearStudentEntry() {
      this.studentEntry.oen = '';
      this.studentEntry.firstname = '';
      this.studentEntry.lastname = '';
      this.studentEntry.accommodation = '';
    }
    onStudentSelectionUpdate(studentSelections: ListSelector) {
      // this.isCloseSessionEnabled = this.isOpenSessionEnabled = studentSelections.isAnySelected;
    }

    cModal() { return this.pageModal.getCurrentModal(); }
    cmc() { return this.cModal().config; }

    sampleModalStart(studentIdx: number) {
      const config: IStudentInfoConfig = { studentList: this.studentList, curricShort: this.activeClassroom.curricShort, studentIdx, asmtSlug: this.asmtSlug};
      const isProceedOnly = this.showScanModal();
      this.pageModal.newModal({
        type: TeacherModal.SAMPLE_MODAL,
        config,
        isProceedOnly,
        finish: this.sampleModalFinish
      });
    }

    openStudentLoginIssueModal(student: IStudentAccount){
      const theStudentLoginIssue = this.studentLoginIssueList.find(studentLoginIssue => +studentLoginIssue.uid === +student.id)
      const config = {
        isSasnLogin: this.isSasnLogin,
        student_uid: student.id,
        student_gov_id: this.isSasnLogin?student.SASN:student.eqao_student_gov_id,
        lastNameInput: theStudentLoginIssue.lastNameInput,
        student_last_name: student.last_name,
      };
      this.pageModal.newModal({
        type: TeacherModal.STUDENT_LOGIN_ISSUE_MODAL,
        config,
        finish: this.StudentLoginIssueModalFinish
      });
    }

    showScanModal() {
      return showScanModal(this.activeClassroom.curricShort);
    }

    getBulkUploadTime() {
      return this.scanInfo.timePerUploadCheck;
    }

    getScanInfoMap() {
      return this.scanInfo.studentScanInfoMap;
    }
    
    sampleModalFinish = () => {
      this.pageModal.closeModal();
    }

    StudentLoginIssueModalFinish = () =>{
      if(this.cModal().type === TeacherModal.STUDENT_LOGIN_ISSUE_MODAL){
        this.pageModal.closeModal();
      }
    }

    // editModalStart(subSession) {
    //   const config = {};
    //   this.pageModal.newModal({
    //     type: TeacherModal.EDIT_MODAL,
    //     config,
    //     finish: config => this.editModalFinish(config,subSession)
    //   });
    // }
    // editModalFinish = (config,subSession) => {
    //   this.finalizeSubSessionEdit(config.payload,subSession)
    //   .then(res =>{ 
    //     this.loadSessionInfo()
    //     this.pageModal.closeModal();
    //   })
     
    // }
    canEditDate(){
      if(this.isPJSampleTest()) {
        if(this.subSessions && (this.subSessions[0] && this.subSessions[2])) {
          if(this.subSessions[0].date_time_start && this.subSessions[2].date_time_start) {
            if(!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[2].date_time_start)){
              return true;
            }
          }
          if(!this.subSessions[0].date_time_start && this.subSessions[2].date_time_start && !this.isLanguageVisibleForFIClass() && this.verifyPastDate(this.subSessions[2].date_time_start)) {
            return false;
          }
          if(!this.subSessions[0].date_time_start || !this.subSessions[2].date_time_start){
            return true;
          }
        } 
      }
      if(this.isPJOperationalTest()) {
        if(this.subSessions && (this.subSessions[0] && this.subSessions[4])) {
          if(this.subSessions[0].date_time_start && this.subSessions[4].date_time_start) {
            if(!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[4].date_time_start)){
              return true;
            }
          }
          if(!this.subSessions[0].date_time_start && this.subSessions[4].date_time_start && !this.isLanguageVisibleForFIClass() && this.verifyPastDate(this.subSessions[4].date_time_start)) {
            return false;
          }
          if(!this.subSessions[0].date_time_start || !this.subSessions[4].date_time_start){
            return true;
          }
        }        
      }
      if(this.isG9OrOsslt()){
        if(this.subSessions && this.subSessions[1]){
          if(this.subSessions && (!this.verifyPastDate(this.subSessions[0].date_time_start) || !this.verifyPastDate(this.subSessions[1].date_time_start))){
            return true;
          }
        }
        if(this.subSessions && !this.verifyPastDate(this.subSessions[0].date_time_start)){
          return true;
        }
        if(!this.subSessions[0].date_time_start || (this.subSessions[1] && !this.subSessions[1].date_time_start)){
          return true;
        }
      }
      return false;
    }

    reportModalStart(){
      const groupIdQuery = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      let params = {
        query: {
          school_class_group_id: groupIdQuery.query.school_class_group_id,
          test_window_id: this.currTestWindow.id,
        }
      }
      this.auth
      .apiFind(this.routes.EDUCATOR_REPORT_ISSUES_CATEGORIES, params)
      .then(result => {
        if(result.reportIssuesCategories.length) {
          this.reportIssuesCategories = result.reportIssuesCategories;

        }
        if(result.component_slugs.length) {
          this.component_slugs = result.component_slugs
        }

        const config = {};
        this.pageModal.newModal({
          type: TeacherModal.REPORT_MODAL,
          config,
          finish: config => this.reportModalFinish(config)
        });
      })
    }

    reportModalFinish = (config) =>{
      if (!config.selectedStudents.length){
        this.loginGuard.quickPopup(this.lang.tra('alert_ta_report_issue_select_student'));
        throw new Error();
      }

      if(!config.msg || !config.categorySelection){
        this.loginGuard.quickPopup(this.lang.tra('alert_ta_report_issue_empty'));
        throw new Error();
      }

      const msg:string = config.msg
      const categorySelection:string = config.categorySelection
      const selectedStudents:string | number[] = config.selectedStudents.map(student => student.uid || student.id)
      const subjectsSelected:string = config.subjectsSelected
      this.classroomService.teacherReportIssue(this.classroomId, this.session_id, msg, categorySelection, subjectsSelected, selectedStudents);

      this.pageModal.closeModal();
    }

    editModalStart() {
      const config = {};
      if(this.isPrimaryOrJunior()){
        if(this.isPJSampleTest()){
          if(this.subSessions[0] && this.subSessions[2]){ 
            if(this.subSessions[0].date_time_start && this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[2].date_time_start)? false: true;
            } 
            if(this.subSessions[0].date_time_start && !this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = true;
            } 
            if(!this.subSessions[0].date_time_start && this.subSessions[2].date_time_start){
              this.showSessionLangEdit = this.isLanguageVisibleForFIClass() ? true : false;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[2].date_time_start)? false: true;
            }
          }
        }
        if(this.isPJOperationalTest()){
          if(this.subSessions[0] && this.subSessions[4]){ 
            if(this.subSessions[0].date_time_start && this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[4].date_time_start)? false: true;
            } 
            if(this.subSessions[0].date_time_start && !this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.verifyPastDate(this.subSessions[0].date_time_start)? false: true;
              this.showSessionMathEdit = true;
            } 
            if(!this.subSessions[0].date_time_start && this.subSessions[4].date_time_start){
              this.showSessionLangEdit = this.isLanguageVisibleForFIClass() ? true : false;
              this.showSessionMathEdit = this.verifyPastDate(this.subSessions[4].date_time_start)? false: true;
            }
          }
        }
      }

      if(this.isG9OrOsslt()){
        if(this.subSessions[0]){
          this.showSessionAEdit = !this.subSessions[0].date_time_start || !this.verifyPastDate(this.subSessions[0].date_time_start)
        }
        if(this.subSessions[1]){
          this.showSessionBEdit = !this.subSessions[1].date_time_start || !this.verifyPastDate(this.subSessions[1].date_time_start)
        }
      }
      this.pageModal.newModal({
        type: TeacherModal.EDIT_MODAL,
        config,
        finish: config => this.editModalFinish(config)
      });
    }

    editModalFinish = async(config) => {
      const dateTime = [];
      let pjSessionStartDate;
      let pjSessionEndDate;
      let pjCaption;
      if(this.isPrimaryOrJunior()){
        if(config.payload.date_lang && config.payload.date_math){
          pjSessionStartDate = config.payload.date_lang[0] <= config.payload.date_math[0] ? config.payload.date_lang[0] : config.payload.date_math[0];
          pjSessionEndDate = config.payload.date_lang[1] >= config.payload.date_math[1] ? config.payload.date_lang[1] : config.payload.date_math[1];
          if(this.isPJSampleTest()){
            pjCaption = config.payload.date_lang[0] <= config.payload.date_math[0] ? this.subSessions[0].caption : this.subSessions[2].caption;
          }
          if(this.isPJOperationalTest()){
            pjCaption = config.payload.date_lang[0] <= config.payload.date_math[0] ? this.subSessions[0].caption : this.subSessions[4].caption;
          }
        }
        else if(config.payload.date_lang && !config.payload.date_math){
          let startDate = this.isPJOperationalTest() ? this.subSessions[4].date_time_start : this.subSessions[2].date_time_start; 
          let endDate = this.isPJOperationalTest() ? this.subSessions[4].date_time_end : this.subSessions[2].date_time_end; 
          if(startDate){
            pjSessionStartDate = config.payload.date_lang[0] <= startDate ? config.payload.date_lang[0] : startDate;
            pjSessionEndDate = config.payload.date_lang[1] <= endDate ? endDate : config.payload.date_lang[1];
            if(this.isPJSampleTest()){
              pjCaption = config.payload.date_lang[0] <= startDate ? this.subSessions[0].caption : this.subSessions[2].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = config.payload.date_lang[0] <= startDate ? this.subSessions[0].caption : this.subSessions[4].caption;
            }
          } else {   
            pjSessionStartDate = config.payload.date_lang[0];
            pjSessionEndDate = config.payload.date_lang[1];
            pjCaption = this.subSessions[0].caption;
          }
        }
        else if(config.payload.date_math && !config.payload.date_lang){
          let startDate = this.subSessions[0].date_time_start; 
          let endDate = this.subSessions[0].date_time_end; 
          if(startDate){
            pjSessionStartDate = config.payload.date_math[0] <= startDate ? config.payload.date_math[0] : startDate;
            pjSessionEndDate = config.payload.date_math[1] <= endDate ? endDate : config.payload.date_math[1];
            if(this.isPJSampleTest()){
              pjCaption = config.payload.date_math[0] <= startDate ? this.subSessions[2].caption : this.subSessions[0].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = config.payload.date_math[0] <= startDate ? this.subSessions[4].caption : this.subSessions[0].caption;
            } 
          } else {  
            pjSessionStartDate = config.payload.date_math[0];
            pjSessionEndDate = config.payload.date_math[1];
            if(this.isPJSampleTest()){
              pjCaption = this.subSessions[2].caption;
            }
            if(this.isPJOperationalTest()){
              pjCaption = this.subSessions[4].caption;
            } 
          }
        }
      }
      if(this.isPJSampleTest() && this.subSessions) { 
        for(let i = 0; i < this.subSessions.length; i++){
          dateTime[i] = {
            date_time_start: config.payload.date_lang ? config.payload.date_lang[0] : this.subSessions[i].date_time_start,
            date_time_end: config.payload.date_lang ? config.payload.date_lang[1]: this.subSessions[i].date_time_end,
            id: this.subSessions[i].id,
            caption: this.subSessions[i].caption,
            asmtSlug: this.asmtSlug,
          }
          if(i == 2 || i == 3) {
            dateTime[i] = {
              date_time_start: config.payload.date_math ? config.payload.date_math[0] : this.subSessions[i].date_time_start,
              date_time_end: config.payload.date_math ? config.payload.date_math[1]: this.subSessions[i].date_time_end,
              id: this.subSessions[i].id,
              caption: this.subSessions[i].caption,
              asmtSlug: this.asmtSlug,
            }
          }
        }
      }
      if(this.isPJOperationalTest() && this.subSessions) {
        for(let i = 0; i < this.subSessions.length; i++){
          dateTime[i] = {
            date_time_start: config.payload.date_lang ? config.payload.date_lang[0] : this.subSessions[i].date_time_start,
            date_time_end: config.payload.date_lang ? config.payload.date_lang[1]: this.subSessions[i].date_time_end,
            id: this.subSessions[i].id,
            caption: this.subSessions[i].caption,
            asmtSlug: this.asmtSlug,
          }
          if(i == 4 || i == 5 || i == 6 || i == 7) {
            dateTime[i] = {
              date_time_start: config.payload.date_math ? config.payload.date_math[0] : this.subSessions[i].date_time_start,
              date_time_end: config.payload.date_math ? config.payload.date_math[1]: this.subSessions[i].date_time_end,
              id: this.subSessions[i].id,
              caption: this.subSessions[i].caption,
              asmtSlug: this.asmtSlug,
            }
          }
        }
      }
      if(this.isG9OrOsslt()) {
        if(this.subSessions[0]){
          dateTime[0] = {
            date_time_start: config.payload.date_time_start_a, 
            id: this.subSessions[0].id,
            caption: this.subSessions[0].caption,
            asmtSlug: this.asmtSlug,
          }
        }
        if(this.subSessions[1]){
          dateTime[1] = {
            date_time_start: config.payload.date_time_start_b, 
            id: this.subSessions[1].id,
            caption: this.subSessions[1].caption,
            asmtSlug: this.asmtSlug,
          }
        }
      }
      const schoolClassId = this.route.snapshot.paramMap.get('classroomId');
      const schl_class_group_id = this.classroomService.getClassroomById(schoolClassId).group_id;
      const twDates = await this.auth.apiGet(this.routes.EDUCATOR_SCHOOL_SEMESTER_TW_DATES, schoolClassId, {
        query: {
          schl_class_group_id
        }
      })
      const windowStart = moment(twDates.date_start);
      const windowEnd = moment(twDates.date_end);
      let alertSlug
      if (this.activeSession.slug === ASSESSMENT.OSSLT_OPERATIONAL){
        alertSlug = 'msg_osslt_administration_window_warning';
        this.validateTestDatesWindow(config.payload.date_time_start_a,config.payload.date_time_start_b, windowStart, windowEnd, alertSlug)
      } else if(this.activeSession.slug === ASSESSMENT.G9_OPERATIONAL){
        alertSlug = 'msg_g9_administration_window_warning';
        this.validateTestDatesWindow(config.payload.date_time_start_a, config.payload.date_time_start_b, windowStart, windowEnd, alertSlug)
      } else if (this.activeSession.slug === ASSESSMENT.PRIMARY_OPERATIONAL){
        alertSlug = 'msg_primary_administration_window_warning';
        this.validateTestDatesWindow(pjSessionStartDate, pjSessionEndDate, windowStart, windowEnd, alertSlug)
      } else if (this.activeSession.slug === ASSESSMENT.JUNIOR_OPERATIONAL){
        alertSlug = 'msg_junior_administration_window_warning';
        this.validateTestDatesWindow(pjSessionStartDate, pjSessionEndDate, windowStart, windowEnd, alertSlug)
      }

      const getPromise = (date_time): Promise<void> => {
        return new Promise<void>((resolve, reject) => {
          if(this.isPrimaryOrJunior()){
            if (date_time.date_time_start && date_time.date_time_end){
              let payload = {
                ...date_time,
                pjSessionStartDate,
                pjSessionEndDate,
                pjCaption,
              }
              this.finalizePJSubSessionEdit(payload).then( res => {
                if(res.sessionSchedule){
                  const theSession = this.g9demoService.teacherClassrooms.map[this.classroomId].openAssessments.find(session => session.test_session_id === +this.session_id) ||
                                    this.g9demoService.teacherClassrooms.map[this.classroomId].scheduledAssessments.find(session => session.test_session_id === +this.session_id)
                  if(theSession){
                    theSession.date_time_start = res.sessionSchedule.date_time_start
                    const mStart = moment.tz(theSession.date_time_start, moment.tz.guess());
                    let timezone = mStart.zoneAbbr();
                    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
                    theSession.timeDirectStart = mStart.format(this.lang.tra('datefmt_day_month'));
                    theSession.dateTimeStartLong = `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`;
                    theSession.hourDirectStart = mStart.format(this.lang.tra('timefmt_hour_time'));
                  }
                }
                this.togglethisIsSessionInitializing(true);
                this.loadSessionInfo();
                resolve();
              }).catch((e) => {
                reject(e);
              });
            }
          }
          if(this.isG9OrOsslt()){
            if (date_time.date_time_start){
              this.finalizeSubSessionEdit(date_time).then( res => {
                if(res.subsessionRecord.slug === 'session_a'){
                  const theSession = this.g9demoService.teacherClassrooms.map[this.classroomId].openAssessments.find(session => session.test_session_id === +this.session_id) ||
                                    this.g9demoService.teacherClassrooms.map[this.classroomId].scheduledAssessments.find(session => session.test_session_id === +this.session_id)
                  if(theSession){
                    theSession.date_time_start = res.datetime_start
                    const mStart = moment.tz(theSession.date_time_start, moment.tz.guess());
                    let timezone = mStart.zoneAbbr();
                    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
                    theSession.timeDirectStart = mStart.format(this.lang.tra('datefmt_day_month'));
                    theSession.dateTimeStartLong = `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`;
                    theSession.hourDirectStart = mStart.format(this.lang.tra('timefmt_hour_time'));
                  }
                }
                this.togglethisIsSessionInitializing(true);
                this.loadSessionInfo();
                resolve();
              }).catch((e) => {
                reject(e);
              })
            }
          }
        });
      };

      const promisses = dateTime.map(date_time => {
        return getPromise(date_time)
      });

      Promise.all(promisses).then((results)=> {
        alert(this.lang.tra('msg_date_time_edit_success'))
        this.pageModal.closeModal();
      }).catch((e) => {
        if(e.message === "MSG_ADMIN_WINDOW_WARNING") {
          if(this.activeClassroom.curricShort == "EQAO_G3" || this.activeClassroom.curricShort == "EQAO_G6") {
            this.loginGuard.disabledPopup(this.lang.tra('msg_pj_administration_window_warning', undefined, {start: etz(windowStart), end: etz(windowEnd)}));
          } else if (this.activeClassroom.curricShort == "EQAO_G9") {
            this.loginGuard.disabledPopup(this.lang.tra('msg_g9_administration_window_warning', undefined, {start: etz(windowStart), end: etz(windowEnd)}));
          } else if (this.activeClassroom.curricShort == "EQAO_G10") {
            this.loginGuard.disabledPopup(this.lang.tra('msg_osslt_administration_window_warning', undefined, {start: etz(windowStart), end: etz(windowEnd)}));   
          }
        }
      });
    }

    private togglethisIsSessionInitializing(state: boolean): void {
      if(!this.isPrimaryOrJunior()){
        return;
      }
      this.isRefreshingSessionsInfo = state;
    }

    validateTestDatesWindow(aStart, bStart, windowStart, windowEnd, alertSlug){
      const a = aStart ? etz(aStart).utc() : undefined;
      const b = bStart ? etz(bStart).utc() : undefined;
      const alertProps = 
        {
          start: this.isPJOperationalTest() ? etz(windowStart).format(this.lang.tra('datefmt_dashboard_long_pj')) : etz(windowStart).format(this.lang.tra('datefmt_dashboard_long')),

          end: this.isPJOperationalTest() ? etz(windowEnd).format(this.lang.tra('datefmt_dashboard_long_pj')) : etz(windowEnd).format(this.lang.tra('datefmt_dashboard_long')),
        }

      if(!a && b){
        if (b.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        if (b.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
      else if(!b && a){
        if (a.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        if (a.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
      else if(a && b){
        if (a.isBefore(windowStart) || b.isBefore(windowStart)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
        else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){
          alert(this.lang.tra(alertSlug, null, alertProps))
          throw new Error();
        }
      }
    }
    
    finalizeSubSessionEdit(payload){
      const refinedPayload = payload;
      refinedPayload['test_session_id'] = this.session_id; 
      refinedPayload['session_rescheduled'] = false; 
      if(payload.caption === 'A'){
        refinedPayload['session_rescheduled'] = true; 
      }
      //Test Session start Date should remain the same when update session B test date. 
      // else{
      //   if(this.verifyPastDate(this.subSessions[0].date_time_start)){
      //     refinedPayload['session_rescheduled'] = true; 
      //   }
      // }
      const subSessionId = payload.id;
      const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
      return this.classroomService.updateSubSessionTime(subSessionId,refinedPayload,params)
    }

    finalizePJSubSessionEdit(payload){
      const refinedPayload = payload;
      refinedPayload['test_session_id'] = this.session_id; 
      refinedPayload['session_rescheduled'] = false; 
      if(payload.caption === refinedPayload.pjCaption){
        refinedPayload['session_rescheduled'] = true; 
      }
      const subSessionId = payload.id;
      const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
      return this.classroomService.updateSubSessionTime(subSessionId, refinedPayload, params);
    }

    verifyPastDate(dateStr) {
      let currDate = new Date();
      let selectedDate = new Date(dateStr)
      if (currDate.getTime() > selectedDate.getTime()) {
        return true;
      }
      return false;
    }

    async handleSessionInfoRes(res, fromInit = false) {
      this.getSessionStart(res)
      this.isSessionPaused = res.is_paused;
      this.checkStudentStateSubmitChange(res.subSessions.studentStates);
      this.studentStates = res.subSessions.studentStates;
      this.subSessions = res.subSessions.subSessionRecords;
      this.completedSubSessions = res.completedSubSessions;
      this.activeSubSessions = res.activeSubSessions;
      this.isNoTdOrder = res.is_no_td_order;
      this.isLoaded = true;
      this.isSessionInitializing = false;
      this.asmtSlug = res.asmt_slug;
      this.pausedByUser = res.paused_by_user
      this.confirmAssessmentModal()
        .then(() => {
          if (fromInit) {
            this.verifySessionInAutoCloseGracePeriod(res, this.currTestWindow)
          }
        }).catch(() => {
          console.log("User closed the Assessment modal without confirming.");
      });
      // if(this.showScanInfo()) {
      //   await this.scanInfo.loadScanInfo(+this.activeClassroom.id);
      // }
    }

    checkStudentStateSubmitChange(newStudentStates){
      if(this.studentStates){
        Object.entries(this.studentStates).forEach(([key, value]) => {
          value.subSessions.forEach(subsession =>{
            const newStudentStateSubsessionSubmitted = newStudentStates[key]["subSessions"].find( ss => ss.tass_id === subsession.tass_id )?.is_submitted
            if(+subsession.is_submitted === 0 && newStudentStateSubsessionSubmitted && +newStudentStateSubsessionSubmitted === 1){
              this.scanInfo.loadSingleScanInfo(this.activeClassroom.group_id, subsession.tass_id)
            }
          })
        });
      }
    }

    showScanInfo() {
      return [ASSESSMENT.PRIMARY_OPERATIONAL, ASSESSMENT.JUNIOR_OPERATIONAL].includes(this.asmtSlug);
    }

    onLockActionsUpdate(event) {
      const {studentLockActionMap, studentUids} = event;
      this.studentLockActionMap = studentLockActionMap;
      this.studentsToTrackLockAction = studentUids;
    }

    /**Reload Session Info after unsubmit request and show the unsubmit result */
    reloadSessionInfo(result){
      this.isAllowLoadRetry = false
      //this.activeClassroom = this.g9demoService.teacherClassrooms.map[this.classroomId];
      //this.activeSession = this.g9demoService.getSessionByClassroomId(this.classroomId, this.session_id);
      //const params = this.classroomService.constructClassGroupIdQuery(this.classroomId);
      const classroom = this.classroomService.getClassroomById(this.classroomId)
      const params = {
        query: {
          school_class_group_id: classroom.group_id,
          school_class_id:this.classroomId,
          fromInit: 0,  // Its not from init function ( Do not need to load the data into redis)
          client_app_version: APP_VERSION, // pass current client version
        }
      }
      this.auth
        .apiGet(this.routes.EDUCATOR_SESSION, this.session_id, params)
        .then(res => {
          this.g9demoService.setIsUnsubmitting(false);
          this.handleSessionInfoRes(res);
          if(result !== null){
            const log_entry_data = JSON.parse(result.data)
            if(+log_entry_data.reasonId === 1 || +log_entry_data.reasonId === 2 ){
              if(log_entry_data.isAllowAutoUnsubmit){
                //reason 1 or 2 auto approved unsubmit request
                setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_success_option_1_2')), 0);
              }else{
                //reason 1 or 2 deny unsubmit request
                const langOverride = undefined
                const props = {
                  "auto_approve_unsubmit_time_limit_h": log_entry_data.auto_approve_unsubmit_time_limit_h?''+log_entry_data.auto_approve_unsubmit_time_limit_h:'0'
                }
                setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_unsubmit_tass_deny_option_1_2', langOverride, props)), 0);
              }
            }
            if(+log_entry_data.reasonId === 3){
              //reason 3 pending unsubmit request
              setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_existing_pending_unsubmission_request')), 0); 
            }
          }  
          this.studentsToTrackLockAction.forEach(uid => {
            this.studentLockActionMap.set(uid, false);
          })
        })
        .catch(e =>{
          if(e.message === 'TEST_SESSION_IS_NOT_ACTIVE'){
            this.clearInterval()
            this.removeAssessment( this.activeClassroom.openAssessments, this.session_id )
            this.closedSessionDetectedPopup()
          }
          if(e.message === 'SESSION_INITIALIZING'){
            this.isAllowLoadRetry = true
          }
          this.g9demoService.setIsUnsubmitting(false);
          this.studentsToTrackLockAction.forEach(uid => {
            this.studentLockActionMap.set(uid, false);
          })
          setInterval(()=>{
            this.isAllowLoadRetry = true
          }, 30*1000)
        })
    }

    // Print Student Information Modal
    studentInfoModalStart(){
      const config = { 
        studentList: this.studentList, 
        activeClassroom: this.activeClassroom, 
      };  
      const isProceedOnly = true;
      this.pageModal.newModal({
        type: PrintScanModal.STUDENT_INFO,  
        config,
        isProceedOnly,
        finish: this.studentInfoModalFinish
      });
    }

    studentInfoModalFinish = () => {
      this.pageModal.closeModal();
    }

    initStudentReviewedMap(map: any) {
      this.isSubmitValid = map;
    }

    allStudentsReviewed() {
      let allReviewed = true;
      for (let reviewed of this.studentReviewedStatusMap.values()) {
        if (!reviewed) {
          allReviewed = false;
          break;
        }
      }
      return allReviewed;
    }

    checkIfOkDisabled() {
      if (!(this.cModal().type === TeacherModal.REVIEW_SUBM_MODAL)) return false;
      
      // review submission modal
      return !this.isSubmitValid;
    }

    // Print/Scan Instruction Modal
    instructionModalStart(){
      const config = {};  
      const isProceedOnly = true;
      this.pageModal.newModal({
        type: PrintScanModal.INSTRUCTION_MODAL,  
        config,
        isProceedOnly,
        finish: this.instructionModalFinish
      });
    }

    instructionModalFinish = () => {
      this.pageModal.closeModal();
    }

    showShowUnsubmitBtn(){
        return true;
        //temporary comment out for teacher to unsubmit*******************************************************
        // return   this.activeSession.slug == 'OSSLT_OPERATIONAL'
        //       || this.activeSession.slug == 'PRIMARY_OPERATIONAL'
        //       || this.activeSession.slug == 'JUNIOR_OPERATIONAL'
        //       || (this.activeSession.slug == 'G9_OPERATIONAL' && this.checkIfUnsubmitToggleVisible())
        //       || (this.activeSession.slug == 'PRIMARY_SAMPLE' && this.checkIfUnsubmitToggleVisible()) // let secret user see unsubmit in sample test as well
        //       || (this.activeSession.slug == 'JUNIOR_SAMPLE' && this.checkIfUnsubmitToggleVisible()) // let secret user see unsubmit in sample test as well
        //       || (this.activeSession.slug == 'G9_SAMPLE' && this.checkIfUnsubmitToggleVisible()) // let secret user see unsubmit in sample test as well
        //       || (this.activeSession.slug == 'OSSLT_SAMPLE' && this.checkIfUnsubmitToggleVisible()) // let secret user see unsubmit in sample test as well
        //temporary comment out for teacher to unsubmit*******************************************************
    }

    get isSasnLogin():boolean {
      return this.classroomService.isSASNLogin(+this.classroomId);
    }
    

    //---- Sorting Function by student Last Name & First Name----
  sorting = {
    fieldName: "last_name", //default
    direction: "ASC" //A-Z
  }

  updateSorting = (fieldUserClicked: string) => {
    if (fieldUserClicked === this.sorting.fieldName){
      this.reverseTheOrder();
    } else {
      // we change the sorting field to the one that got clicked 
      this.sortBy(fieldUserClicked);
    }
  }

  /**
   * Generates a fixed-length access code based on the provided input number.
   * - The access code is deterministic for a given input, meaning the same input number will always produce the same code.
   * - It consists of uppercase letters and numbers, excluding look-alike characters such as 'I', 'O', '1', and '0' to avoid confusion.
   * @param inputNumber - The number used as a seed to generate the access code.
   * @returns A string representing the generated access code.
   * 
   */
  public generateAccessCode(inputNumber: number): string {
    // Linear Congruential Generator for pseudo-random number generation
    const m = 4294967296; // 2^32
    const a = 1664525;
    const c = 1013904223;
    let state = inputNumber % m;

    const nextFloat = (): number => {
        state = (a * state + c) % m;
        return state / m;
    };

    // Define character set excluding look-alike characters
    const charset = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Excludes I, O, 0, 1

    // Set code length and hyphen position
    const codeLength = 7;
    const hyphenPosition = 3;

    let code = '';

    for (let i = 0; i < codeLength; i++) {
        if (i === hyphenPosition) {
            code += '-';
        } else {
            const randomIndex = Math.floor(nextFloat() * charset.length);
            code += charset.charAt(randomIndex);
        }
    }

    return code;
  }

  private reverseTheOrder = () => {
    this.sorting.direction = this.sorting.direction === "ASC"? "DESC":"ASC"    
  }

  private sortBy = (fieldUserClicked: string) =>{
    this.sorting.fieldName = fieldUserClicked; 
    this.sorting.direction ="ASC";
  }
  //---- Sorting Functions Ends ----

  submitBtnSlug() {
    if(this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.G9_SAMPLE){
      return "sample_submit_responses"
    }
    if(this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE){
      return "Practice_submit_response"
    }
    return "pj_teacher_submit_responses"
  }

  studentPostSlug(){
    if(this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE || this.asmtSlug === ASSESSMENT.G9_SAMPLE){
      return "lbl_student_post_sample"
    }
    if(this.asmtSlug === ASSESSMENT.OSSLT_SAMPLE){
      return "Lbl_student_post_practice"
    }
    return "lbl_student_post"
  }

  /**
   * Check if the test session is going to be auto closed soon.
   * If so, trigger autoCloseWarning popup 
   * @param test_session_data 
   * @param test_window 
   */
  verifySessionInAutoCloseGracePeriod(test_session_data, test_window: ITestWindow){
    if (test_session_data.auto_close_after && test_window.auto_close_grace_period_m !== null) {
      const currentDateTime = moment()
      const scheduledAutoCloseTime = moment(test_session_data.auto_close_after)
      const gracePeriodPriorCloseTime = scheduledAutoCloseTime.clone().subtract(test_window.auto_close_grace_period_m, 'minutes')
    
      if (currentDateTime < scheduledAutoCloseTime && currentDateTime >= gracePeriodPriorCloseTime) {
        const data = {
          testWindow: {
            auto_close_default_extension_m: test_window.auto_close_default_extension_m
          },
          testSession: {
            id: test_session_data.id,
            auto_close_after: test_session_data.auto_close_after
          }
        }
        this.handleAutoCloseWarning(data)
      }
    }
  }

  /**
   * takes in data coming from websocket OR EDUCATOR_SESSION with fromInit = 1 
   * and creates an auto submission warning popup to teachers
   */
  handleAutoCloseWarning({testWindow, testSession}){
    const closeTime = moment(testSession.auto_close_after).format("hh:mmA");
    const confirmationMsg = this.lang.tra('invig_auto_submission_warning', undefined, { auto_close_time: closeTime, auto_close_extension_time: testWindow.auto_close_default_extension_m}); 
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      btnProceedConfig:{
        caption:"invig_auto_submission_warning_extend_btn"
      },
      btnCancelConfig:{
        caption:"invig_auto_submission_warning_cancel_btn"
      },
      width: '40em', 
      confirm: () => {
        this.extendAutoSubmission(testWindow, testSession)
      },
      close: () => {
        this.confirmAutoSubmissionWarning(testWindow, testSession)
      }
    })
  }

  confirmAutoSubmissionWarning(testWindow, testSession){
    const config = {testWindow, testSession, closeCaption: this.lang.tra('invig_auto_submission_warning_cancel'), confirmationCaption: this.lang.tra('invig_auto_submission_warning_confirm')}

    this.pageModal.newModal({
      type: TeacherModal.AUTO_SUBMISSION_WARNING_CONFIRMATION,
      config,
      finish: () => {
        const closeModalConfig = undefined
        const executeCancel = false
        this.pageModal.closeModal(closeModalConfig, executeCancel)
      },
      cancel: config => this.extendAutoSubmission(config.testWindow, config.testSession),
    })
  }
  

  /**
   * handles teacher request to extend test session auto_close_after by auto_close_default_extension_m minutes 
   */
  extendAutoSubmission(testWindow, testSession){
    const params = this.classroomService.constructClassGroupIdQuery(this.classroomId)
    this.auth.apiUpdate(this.routes.EDUCATOR_TEST_SESSION_AUTO_SUBMISSION, testSession.id, {auto_close_default_extension_m: testWindow.auto_close_default_extension_m}, params)
    .then(() => this.pageModal.closeModal())
  }

  /**
   * Simulate attempt initilization process and increase completedInitilizedAttempts number to show on UI 
   */
  simulateAttemptInitialization(){
    const numberAttempts = {
      'PRIMARY_SAMPLE':   2,
      'JUNIOR_SAMPLE':    2,
      'G9_SAMPLE':        1,
      'OSSLT_SAMPLE':     1,
      'PRIMARY_OPERATIONAL': 3,
      'JUNIOR_OPERATIONAL':  3,
      'G9_OPERATIONAL':      2,
      'OSSLT_OPERATIONAL':   2, 
    }
    const assessmentNumberAttempts = numberAttempts[this.activeSession.slug] || 1 // default to 1 attempt per student

    const estimateAttemptsInitializePerSecond = 10;  // 10 on prod,  2 on local (This is just estimation)
    const interval = 1000 * 1  // 1 second
    const numberAttemptInilizatedPerInterval = estimateAttemptsInitializePerSecond * interval / 1000
    
    // reset completedInitilizedAttemptStudents
    this.completedInitilizedAttemptStudents = 0;
    
    const intervalId = setInterval(() => {
      if (this.completedInitilizedAttemptStudents <= this.studentList.length) {
        this.completedInitilizedAttemptStudents += Math.floor((numberAttemptInilizatedPerInterval/assessmentNumberAttempts));

        //completedInitilizedAttemptStudents should not exceed student numbers
        if(this.completedInitilizedAttemptStudents > this.studentList.length){
          this.completedInitilizedAttemptStudents = this.studentList.length
        }
      } else {
        clearInterval(intervalId);
      }
    }, interval);
  }

  /**
   * Get the estimate completed initilized attempts percentage number
   * example: Return 10  if its 10% completed
   * @returns percentage number
   */
  getInitilizeProgress(){
    if(this.studentList.length != 0){
      return ((this.completedInitilizedAttemptStudents * 100 )/this.studentList.length)
    }else{
      return 0;
    }  
  }

  /**
   * Upon the detection of an already closed or cancelled test session
   * show popup alert message 
   * and let user click OK button to be redirected
   */
  closedSessionDetectedPopup(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('test_session_closed_redirect_to_prev_page'),
      btnProceedConfig: {
        caption: this.lang.tra('btn_ok')
      },
      btnCancelConfig: {
        hide: true
      },
      confirm: () => {
        this.backToPrevPage()
      }
    })
  }

  /**
   * Go back to the previous page
   * School-admin back to assessment sessions, teacher back to classroom
   */
  backToPrevPage(){
    if( this.g9demoService.getIsFromSchoolAdmin() ){
      const queryParams = this.currentQueryParams
      this.router.navigate([ this.lang.c(), AccountType.SCHOOL_ADMIN, 'sessions' ], { queryParams });
    }else{
      this.classroomService.navigateToClassroom(this.classroomId, this.currentQueryParams);
    }
  }

  /**
   * Get test window info from classroom ID
   * @param {number} classroomId 
   * @returns {IClassroom} classroom information
   */
  getTestWindowInfo(classroomId: number){
    const currClassroomInfo = this.g9demoService.classrooms.find(c => c.id === classroomId)
    return this.g9demoService.getTestWindowFromClassroom(currClassroomInfo)
  }
  
  /**
   * Teacher confirm PJ assessment type modal 
   */
  confirmAssessmentModal(): Promise<void> {
    return new Promise((resolve, reject) => {
      // PJ Only
      if(this.isConfirmSessionPassed || !this.isPrimaryOrJunior()){
        return resolve();
      }
      const descriptionParam = this.isPJOperationalTest() ? this.lang.tra("g9_operational_assessment") : this.lang.tra("pj_sample_test")
      const description = this.lang.tra("invig_confirm_title", "", {ASSESSMENT: descriptionParam.toUpperCase()})
      const subCaptionParams = this.isPJOperationalTest() ? this.lang.tra("lbl_operational") : this.lang.tra("lbl_sample")
      const subCaption = this.lang.tra(this.isPJOperationalTest() ? "invig_confirm_sub_caption" : "invig_confirm_sub_caption_sample", "", {ASSESSMENT: subCaptionParams.toLowerCase() } )
      let requiredInputs;
      if(this.asmtSlug === ASSESSMENT.PRIMARY_OPERATIONAL || this.asmtSlug === ASSESSMENT.JUNIOR_OPERATIONAL){
        requiredInputs = ['operational', 'opérationnel', 'operationnel']
      }
      if(this.asmtSlug === ASSESSMENT.PRIMARY_SAMPLE || this.asmtSlug === ASSESSMENT.JUNIOR_SAMPLE){
        requiredInputs = ['sample', 'exemple']
      }
      this.loginGuard.confirmationReqActivate({
        description,
        subCaption,
        requireTextInput: requiredInputs,
        confirm: () => {
          this.isConfirmSessionPassed = true;
          resolve(); // Resolve when user confirms
        },
        close: () => {
          this.classroomService.navigateToClassroom(this.classroomId, this.currentQueryParams);
          reject();
        }
      })
    })
  }

  /**
   * Add student login issue to studentLoginIssueList, this List is passed to student list component
   * @param {IStudentLoginIssue} studentLoginIssue student login issue includes session Id and invalid last name input 
   */
  addStudentToLoginIssueList(studentLoginIssue: IStudentLoginIssue){
    if (!studentLoginIssue) {
      return;
    }
    if(this.studentSocketState[studentLoginIssue.uid]){ //student is online
      if(!this.studentLoginIssueList.find( studentLoginIssueInList => studentLoginIssueInList.uid === studentLoginIssue.uid)){ //student is not in the list
        this.studentLoginIssueList.push(studentLoginIssue);
      }
    }
  }

  /**
   * Remove student login issue to studentLoginIssueList
   * @param {IStudentLoginIssueResolved} student
   */
  removeStudentfromLoginIssueList(student:IStudentLoginIssueResolved){ //
    if(student){
      this.studentLoginIssueList = this.studentLoginIssueList.filter(studentLoginIssue =>+studentLoginIssue.uid !== +student.uid)
    }
  }

  /**
   * Call the API and allow student to process from login block by last name enter not match
   * Also remove the student from studentLoginIssueList
   * @param student_uid
   */
  resolveLoinIssueApprove(student_uid){
    const data = {
      school_class_id :this.classroomId,
      student_uid,
      action: STUDENT_LOGIN_VERIFICATION_TEACHER_ACTION.approve
    }
    const params = {
      query: {
        schl_class_group_id: this.classroomService.getClassroomById(this.classroomId).group_id
      }
    }
    this.auth
      .apiPatch(this.routes.EDUCATOR_RESOLVE_STU_LOGIN_ISSUE, -1, data, params)
      .then(res => {
        //this.removeStudentfromLoginIssueList( {uid:student_uid})  // comment out and use ws to update studentLoginIssueList instead 
      })
      .catch( error => {
        this.loginGuard.quickPopup(error.message)
      })
    this.StudentLoginIssueModalFinish()  
  }

  /**
   * Call the API and deny student to process from login block by last name enter not match
   * Also remove the student from studentLoginIssueList
   * @param student_uid
  */
  resolveLoinIssueDeny(student_uid){
    const data = {
      school_class_id :this.classroomId,
      student_uid,
      action: STUDENT_LOGIN_VERIFICATION_TEACHER_ACTION.deny
    }
    const params = {
      query: {
        schl_class_group_id: this.classroomService.getClassroomById(this.classroomId).group_id
      }
    }
    this.auth
      .apiPatch(this.routes.EDUCATOR_RESOLVE_STU_LOGIN_ISSUE, -1, data, params)
      .then(res => {
        //this.removeStudentfromLoginIssueList({uid:student_uid}) // comment out and use ws to update studentLoginIssueList instead 
      })
      .catch( error => {
        this.loginGuard.quickPopup(error.message)
      })
    this.StudentLoginIssueModalFinish()   
  }

  isOtherSessionAlreadyActive() {
    const activeSessions = this.activeClassroom.openAssessments;

    const otherActiveSession = activeSessions.find(session => {
        return session.test_session_id !== this.activeSession.test_session_id && !session.is_paused;
    });

    return otherActiveSession;
  }

  showLDBProctorPassword() {
    const accessCode = this.generateAccessCode(+this.classroomId || 123456)
    const popupText = this.lang.tra(IS_RESPONDUS_BETA_MODE ? 'ldb_proctor_password_popup_beta' : 'ldb_proctor_password_popup', this.lang.c(), {accessCode})
    this.loginGuard.quickPopup(popupText)
  }

  openLDBConfigModal(){
    this.pageModal.newModal({
      type: LDBModal.LDB_CONFIG_MODAL,  
      config: {
        currentLevel: LDBConfigLevel.CLASS,
        query: {
          school_class_id: +this.classroomId,
          school_id: this.g9demoService.schoolData.id,
          school_board_id: this.g9demoService.schoolData.sd_id,
        }
      },
      finish: () => {}
    });
  }

  getStudentPostSlug(){
    // todo:DB_DATA_MODEL lower priority
    if(this.whitelabel.isNBED() || this.whitelabel.isMBED()){
      return 'lbl_student_post_nbed';
    }
    if(this.whitelabel.isABED()){
      return this.whitelabel.getSiteText("invigilationIntro");
    }
    return 'lbl_student_post';
  }
}
