import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { Subscription } from 'rxjs';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { ClassroomsService } from '../../core/classrooms.service';
import { LangService } from '../../core/lang.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { FormControl, FormGroup } from '@angular/forms';
import * as moment from 'moment-timezone';
import { ASSESSMENT, ISessionBegin } from '../data/types';
import { Console, timeStamp } from 'console';
import { mtz, etz, tzLangAbbrs } from '../../core/util/moment'
@Component({
  selector: 'view-assessment-scheduler',
  templateUrl: './view-assessment-scheduler.component.html',
  styleUrls: ['./view-assessment-scheduler.component.scss']
})
export class ViewAssessmentSchedulerComponent implements OnInit {
  @Input() set state(state: any) {
    this.config = state.config;
    this.schoolClassId = state.schoolClassId;
    this.sessionDetails = state.sessionDetails;
    this.isFIClass = state.isFIClass;
    this.setupComponent();
  }
  config:ISessionBegin;
  schoolClassId:number | string;
  isFIClass:number;
  sessionDetails;
  @Output() back: EventEmitter<any> = new EventEmitter<any>();
  @Output() scheduleEvent: EventEmitter<ISessionBegin> = new EventEmitter();
  sessionASelected:boolean = false;
  sessionBSelected:boolean = false;
  sessionLangSelected:boolean = false;
  sessionMathSelected:boolean = false;
  selectedError:boolean = false;
  schedulePass:boolean = false;
  schedulerErrors:string;
  sessionBStart;
  ASSESSMENT = ASSESSMENT;
  sessionAStartDay;
  sessionAStartTime;
  sessionAEndDay;
  sessionAEndTime;
  sessionBStartDay;
  sessionBStartTime;
  sessionBEndDay;
  sessionBEndTime;
  private hourlyOffset = 4;
  formGroup = new FormGroup({
    session_a_date: new FormControl(),
    session_a_time: new FormControl(),
    session_b_date: new FormControl(),
    session_b_time: new FormControl(),
    session_lang_start_date: new FormControl(),
    session_lang_end_date: new FormControl(),
    session_math_start_date: new FormControl(),
    session_math_end_date: new FormControl(),
  })
  isDateValid:boolean = false;
  sessionAStart: any;
  sessionAEnd: any;
  dateStrA:any;
  timeStrA:any;
  dateStrB:any;
  timeStrB:any;
  dateTimeStartA: any;
  dateTimeStartB: any;
  // PJ
  dateStrLang:any;
  dateEndLang:any;
  dateStrMath:any;
  dateEndMath:any;
  startDateLang:any;
  endDateLang:any;
  startDateMath:any;
  endDateMath:any;
  langStartDate:any;
  langEndDate:any; 
  mathStartDate:any; 
  mathEndDate:any; 
  dateFormats = {
    dateAndTime: 'YYYY-MM-DD HH:mm',
    dateOnly: 'YYYY-MM-DD'
  }

  constructor(
    private route: ActivatedRoute,
    private auth: AuthService,
    private routes: RoutesService,
    private lang: LangService,
    private sidePanel: SidepanelService,
    public loginGuard: LoginGuardService,
    private g9demoService: G9DemoDataService,
    private breadcrumbsService: BreadcrumbsService,
    private classroomService: ClassroomsService,
    private pageModalService: PageModalService
  ) { }

  ngOnInit(): void {}

  private setupComponent(): void {
    // PJ Operational
    if(this.isPrimaryOrJunior()) {
      if(this.config.slug == ASSESSMENT.PRIMARY_SAMPLE || this.config.slug == ASSESSMENT.JUNIOR_SAMPLE) {
        if(this.sessionDetails.isSessionCompleted[0] && this.sessionDetails.isSessionCompleted[1] && 
          this.sessionDetails.isSessionCompleted[2] && this.sessionDetails.isSessionCompleted[3]
        ) {
        this.isDateValid = false
        this.schedulePass = false;
        }
      }
      if(this.config.slug == ASSESSMENT.PRIMARY_OPERATIONAL || this.config.slug == ASSESSMENT.JUNIOR_OPERATIONAL) {
        if(this.sessionDetails.isSessionCompleted[0] && this.sessionDetails.isSessionCompleted[1] && 
          this.sessionDetails.isSessionCompleted[2] && this.sessionDetails.isSessionCompleted[3] && 
          this.sessionDetails.isSessionCompleted[4] && this.sessionDetails.isSessionCompleted[5] && 
          this.sessionDetails.isSessionCompleted[6] && this.sessionDetails.isSessionCompleted[7] && this.sessionDetails.isSessionCompleted[8] 
        ) {
        this.isDateValid = false
        this.schedulePass = false;
        }
      }
    }
    // G9 and OSSLT
    if(this.isG9OrOsslt()) {
      if(this.sessionDetails.isSessionCompleted[0] && this.sessionDetails.isSessionCompleted[1]){
        this.isDateValid = false
        this.schedulePass = false;
      }
    }
    if(!this.sessionDetails.isStudentsPresent){
      this.isDateValid = false
      this.schedulePass = false;
    }
  }

  isPrimaryOrJunior() {
    return (this.config.slug == ASSESSMENT.PRIMARY_SAMPLE || 
            this.config.slug == ASSESSMENT.PRIMARY_OPERATIONAL || 
            this.config.slug == ASSESSMENT.JUNIOR_SAMPLE || 
            this.config.slug == ASSESSMENT.JUNIOR_OPERATIONAL
          );
  }

  /**
   * Check if select class assessment is junior assessment type
   * @return boolean
   */
  isJunior(){
    const juniorAssessments = [
      ASSESSMENT.JUNIOR_OPERATIONAL, 
      ASSESSMENT.JUNIOR_SAMPLE
    ]
    const slugAsEnum: ASSESSMENT | undefined = Object.values(ASSESSMENT).find(value => value === this.config.slug);
    return juniorAssessments.includes(slugAsEnum)
  }

  isG9OrOsslt() {
    return (this.config.slug == ASSESSMENT.G9_SAMPLE || 
            this.config.slug == ASSESSMENT.G9_OPERATIONAL || 
            this.config.slug == ASSESSMENT.OSSLT_SAMPLE || 
            this.config.slug == ASSESSMENT.OSSLT_OPERATIONAL
          );
  }

  isLanguageVisibleForFIClass(): boolean {
    const isFI = this.isFIClass == 1 ? true : false;
    if(this.g9demoService.schoolDist[0].fi_option === 'C' && isFI) {
      return false;
    }
    return true;
  }

  getAllStudentsCompletedSlug(){
    if (this.config.slug === ASSESSMENT.OSSLT_OPERATIONAL){
      return 'msg_all_students_comp_osslt'
    } else if(this.config.slug === ASSESSMENT.G9_OPERATIONAL){
      return 'msg_all_students_completed'
    }
  }


  async scheduleAssessment() {
    if(this.schedulePass){
      
      let sessionPJScheduledDates, sessionG9OrOssltStartTimes;
      let a, b, langStart, langEnd, mathStart, mathEnd;
      let config:ISessionBegin;
      const classroom = this.classroomService.getClassroomById(this.schoolClassId);
      const twDates = await this.auth.apiGet(this.routes.EDUCATOR_SCHOOL_SEMESTER_TW_DATES, this.schoolClassId, {
        query: {
          schl_class_group_id: classroom.group_id,
        }
      })
      const windowStart = moment(twDates.date_start);
      const windowEnd = moment(twDates.date_end);

      // Check past date
      const now = moment()
      if(this.isPrimaryOrJunior()) {
        sessionPJScheduledDates = [this.dateStrLang, this.dateEndLang, this.dateStrMath, this.dateEndMath]
                                  .map((userInputDate, idx) => {
                                    if (!this.isPastDate(userInputDate, false)){
                                      return userInputDate // If date is not in the past, return date
                                    } else {
                                      return idx % 2 === 0 ? now.format(this.dateFormats.dateOnly) : twDates.date_end // Else return now or tw end date
                                    }
                                  });
        [langStart, langEnd, mathStart, mathEnd] = sessionPJScheduledDates
      }
      if(this.isG9OrOsslt()) {
        sessionG9OrOssltStartTimes = [this.dateTimeStartA, this.dateTimeStartB]
                                    .map((userInputDate, idx) => {
                                      if(!this.isPastDate(userInputDate, true)){
                                        return userInputDate
                                      } else {
                                        return now.add(idx, 'm').format(this.dateFormats.dateAndTime) // Add 1 minute to session B for grace period
                                      }
                                    });
        a = sessionG9OrOssltStartTimes[0] ? moment(sessionG9OrOssltStartTimes[0]) : undefined
        b = sessionG9OrOssltStartTimes[1] ? moment(sessionG9OrOssltStartTimes[1]) : undefined
      }

      if (this.config.slug === ASSESSMENT.OSSLT_OPERATIONAL){
        const alertSlug = 'msg_osslt_administration_window_warning';
        this.validateTestDatesWindow(a, b, windowStart, windowEnd, alertSlug)
      } 
      else if(this.config.slug === ASSESSMENT.G9_OPERATIONAL){
        const alertSlug = 'msg_g9_administration_window_warning';
        this.validateTestDatesWindow(a, b, windowStart, windowEnd, alertSlug)
      }
      else if(this.config.slug === ASSESSMENT.PRIMARY_OPERATIONAL || this.config.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
        const alertSlug = 'msg_pj_administration_window_warning';
        this.validatePJTestDatesWindow(langStart, langEnd, mathStart, mathEnd, windowStart, windowEnd, alertSlug)
      } 
      config = {
        slug: this.config.slug,
        isScheduled: true,
        scheduled_time: this.isPrimaryOrJunior() ? sessionPJScheduledDates : sessionG9OrOssltStartTimes,
        windowStart,
        windowEnd
      }
      this.scheduleEvent.emit(config);
    }
    else{
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('msg_date_time_needed')
      });
      //this.loginGuard.disabledPopup(this.schedulerErrors)
    }

    // if(this.verifyPastDate(this.formGroup.controls.date.value)){
    //   this.loginGuard.disabledPopup("You have a selected a time that has passed!")
    // }
    // else{
    //   this.scheduleEvent.emit(this.formGroup.controls.date.value);
    // }
  }

  validateTestDatesWindow(a, b, windowStart, windowEnd, alertSlug){
    let isFailed = false;
    if(!a && b){
      if (      b.isBefore(windowStart)){ isFailed = true }
      else if ( b.isAfter(windowEnd)){ isFailed = true }
    }
    else if(!b && a){
      if (     a.isBefore(windowStart)){ isFailed = true }
      else if (a.isAfter(windowEnd)){ isFailed = true }
    }
    else if(a && b){
      if (     a.isBefore(windowStart) || b.isBefore(windowStart)){ isFailed = true }
      else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){ isFailed = true }
    }
    if (isFailed){
      const msg = this.lang.tra(
        alertSlug, 
        null, 
        {
          start: mtz(windowStart).format(this.lang.tra('datefmt_dashboard_long')),
          end: mtz(windowEnd).format(this.lang.tra('datefmt_dashboard_long')),
        },
      )
      alert(msg);
      throw new Error();
    }
  }

  private validatePJTestDatesWindow(langStart, langEnd, mathStart, mathEnd, windowStart, windowEnd, alertSlug){
    const langStartDate = langStart ? moment(langStart) : undefined;
    const langEndDate = langEnd ? moment(langEnd) : undefined;
    const mathStartDate = mathStart ? moment(mathStart) : undefined;
    const mathEndDate = mathEnd ? moment(mathEnd) : undefined;

    const msg = this.lang.tra(
      alertSlug, 
      null, 
      {
        start: mtz(windowStart).format(this.lang.tra('datefmt_dashboard_long')),
        end: mtz(windowEnd).format(this.lang.tra('datefmt_dashboard_long')),
      },
    )

    if(langStartDate && langEndDate){
      if(langStartDate.isBefore(windowStart) || langStartDate.isAfter(windowEnd) || langEndDate.isBefore(windowStart) || langEndDate.isAfter(windowEnd)){
        alert(msg)
        throw new Error();
      }
    }
    if(mathStartDate && mathEndDate){
      if(mathStartDate.isBefore(windowStart) || mathStartDate.isAfter(windowEnd) || mathEndDate.isBefore(windowStart) || mathEndDate.isAfter(windowEnd)){
        alert(msg)
        throw new Error();
      }
    }
  }
  
  checkSessionDate(config){
    this.schedulePass = false;
    this.selectedError = false;
    this.schedulerErrors = "";
    switch(config.session){
      case("A"):
                switch(config.input_type){
                  case("date"):
                  this.dateStrA = config.dateStr
                  break;
                  case("time"):
                  this.timeStrA = config.timeStr
                  break;
                }
      break;
      case("B"):
                switch(config.input_type){
                  case("date"):
                  this.dateStrB = config.dateStr
                  break;
                  case("time"):
                  this.timeStrB = config.timeStr
                  break;
                }
      break;
      case("L"): 
                switch(config.date_type){
                  case("start"):
                  this.dateStrLang = config.dateStrLang  
                  break;
                  case("end"):
                  this.dateEndLang = config.dateEndLang
                  break;
                }
      break;
      case("M"): 
                switch(config.date_type){
                  case("start"):
                  this.dateStrMath = config.dateStrMath   
                  break;
                  case("end"):
                  this.dateEndMath = config.dateEndMath
                  break;
                }             
      break;
    }
    try {
      if(this.isPrimaryOrJunior()) {
        if(this.dateStrLang && this.dateEndLang){
          this.setSessionLangSchedule(this.dateStrLang, this.dateEndLang);
        }
        if(this.dateStrMath && this.dateEndMath){
          this.setSessionMathSchedule(this.dateStrMath, this.dateEndMath);
        }
      }
      if(this.isG9OrOsslt()) {
        if(this.dateStrA && this.timeStrA){
          const dateStr = `${this.dateStrA}T${this.timeConvert(this.timeStrA)}`
          this.setSessionASchedule(dateStr);
        }
        if(this.dateStrB && this.timeStrB){
          const dateStr = `${this.dateStrB}T${this.timeConvert(this.timeStrB)}`
          this.setSessionBSchedule(dateStr);
        }
      }
    } catch (err){
      this.schedulerErrors = err.message
      this.selectedError = true;
      this.schedulePass = false;
    }
  }

  setSessionLangSchedule(dateStrLang, dateEndLang) {
    if(dateStrLang > dateEndLang){ 
      throw new Error('msg_pj_end_before_start');
    }

    this.startDateLang = new Date(dateStrLang);
    this.endDateLang = new Date(dateEndLang);
    const langStart = moment.tz(this.startDateLang, moment.tz.guess());
    const langEnd = moment.tz(this.endDateLang, moment.tz.guess());
    this.langStartDate = langStart.format(this.lang.tra('datefmt_day_month'));
    this.langEndDate = langEnd.format(this.lang.tra('datefmt_day_month'));
    this.sessionLangSelected = true;
    this.schedulePass = true;
  }

  setSessionMathSchedule(dateStrMath, dateEndMath) {
    if(dateStrMath > dateEndMath){ 
      throw new Error('msg_pj_end_before_start');
    }

    this.startDateMath = new Date(dateStrMath);
    this.endDateMath = new Date(dateEndMath);
    const mathStart = moment.tz(this.startDateMath, moment.tz.guess());
    const mathEnd = moment.tz(this.endDateMath, moment.tz.guess());
    this.mathStartDate = mathStart.format(this.lang.tra('datefmt_day_month'));
    this.mathEndDate = mathEnd.format(this.lang.tra('datefmt_day_month'));
    this.sessionMathSelected = true;
    this.schedulePass = true;
  }

  setSessionASchedule(dateStr){
    const offset = this.hourlyOffset;
    this.dateTimeStartA = dateStr;
    this.sessionAStart = new Date(dateStr).getTime();
    this.sessionAEnd = this.sessionAStart + (offset*60*60*1000);
    const mStart = moment.tz(this.sessionAStart, moment.tz.guess());
    const mEnd = moment.tz(this.sessionAEnd, moment.tz.guess());
    this.sessionAStartDay = mStart.format(this.lang.tra('datefmt_day_month'));
    this.sessionAStartTime = mStart.format(this.lang.tra('timefmt_hour_time'));
    this.sessionAEndDay = mEnd.format(this.lang.tra('datefmt_day_month'));
    this.sessionAEndTime = mEnd.format(this.lang.tra('timefmt_hour_time'));
    this.sessionASelected = true;

    if(this.config.slug === ASSESSMENT.G9_SAMPLE){
      this.schedulePass = true;
    }
  }

  getSessionProps(order:number){
    let slugParams = {};
    if (order === 0){
      slugParams = {
        date: this.sessionAStartDay,
        first_time:this.sessionAStartTime,
        second_time:this.sessionAEndTime
      }
    } else{
      slugParams = {
        date:this.sessionBStartDay,
        first_time:this.sessionBStartTime,
        second_time:this.sessionBEndTime 
      }
    }
    return this.lang.tra('txt_sheduled_for','',slugParams);
  }

  setDateValidity($event){
    this.isDateValid = $event;
  }

  setSessionBSchedule(dateStr){
    const offset = 4;
      
    this.sessionBStart = new Date(dateStr).getTime();
    const mStart = moment.tz(this.sessionBStart, moment.tz.guess());
    this.sessionBSelected = true;
    this.sessionBStartDay = mStart.format(this.lang.tra('datefmt_day_month'));
    this.sessionBStartTime = mStart.format(this.lang.tra('timefmt_hour_time'));
    this.dateTimeStartB = dateStr;
    const sessionBEnd = this.sessionBStart + (offset*60*60*1000);
    const mEnd = moment.tz(sessionBEnd, moment.tz.guess());
    this.sessionBSelected = true;
    this.sessionBEndDay = mEnd.format(this.lang.tra('datefmt_day_month'));
    this.sessionBEndTime = mEnd.format(this.lang.tra('timefmt_hour_time'));
    this.sessionBEndDay = mEnd.format(this.lang.tra('datefmt_day_month'));
    this.sessionBEndTime = mEnd.format(this.lang.tra('timefmt_hour_time'));
    
    if(this.sessionASelected){
      if(this.sessionBStart < this.sessionAStart){
        throw new Error('msg_ses_b_before_ses_a');
      }

      if(!this.verifyScheduleGracePeriod(this.sessionAStart,this.sessionBStart)){
        throw new Error('msg_time_gap');
      }
    }

    this.schedulePass = true;
  }
  checkDateOverlap(sessionAStart,sessionAEnd,dateToCompare){
    const endDate = dateToCompare + (this.hourlyOffset*60*60*1000);
    //let selectedDate = new Date(dateStr)
    if (dateToCompare >= sessionAStart && dateToCompare <= sessionAEnd)  {
      return true;
    }
    else{
      if (endDate >= sessionAStart && endDate <= sessionAEnd)  {
      return true;
      }
    }
    return false;
  }

  verifyScheduleGracePeriod(sessionAStart, dateToCompare){
    const gracePeriod = 1 * (60 * 1000); // 1 minute
    const startDate = sessionAStart + gracePeriod;
    if(dateToCompare < startDate){
      return false;
    }
    return true;
  }
  renderTitle(title:string){
    switch(title){
      case "G9_OPERATIONAL": return 'g9_assess_title';
      case "G9_SAMPLE": return "g9_sample_test";
    }
  }

  /**
   * Returns start and end date of administration window of assessment session as string in Eastern timezone
   * @returns string
   */
  renderTwStartEndStrEtz(){
    const classroomSearch = this.g9demoService.classrooms.find(c => c.id === +this.schoolClassId);
    const classTestWindow = this.g9demoService.getTestWindowFromClassroom(classroomSearch)
    let formattedStartDate = etz(classTestWindow.date_start).format(this.lang.tra('datefmt_month_year_time'));
    let formattedEndDate = etz(classTestWindow.date_end).format(this.lang.tra('datefmt_month_year_time'));
    if(this.lang.c() === 'fr'){
      formattedStartDate = tzLangAbbrs(formattedStartDate, 'fr')
      formattedEndDate = tzLangAbbrs(formattedEndDate, 'fr')
    }
    return `${formattedStartDate} - ${formattedEndDate}`; 
  }
  renderParagraphOne(title:string){
    switch(title){
      case "G9_OPERATIONAL": return this.lang.tra('txt_schedule_assessments_1');
      case "G9_SAMPLE": return this.lang.tra('txt_schedule_sample_tests_1');
    }
  }

  renderParagraphTwo(title:string){
    switch(title){
      case "G9_OPERATIONAL": return this.lang.tra('txt_schedule_assessments_2');
      case "G9_SAMPLE": return this.lang.tra('txt_schedule_sample_tests_2');
    }
  }
  
  goBack(){
    this.back.emit(false)
  }
  timeConvert(tm) {
    let time;
    if (tm.includes('pm')){
      time = tm.replace('pm',' PM')
    }
    else{
      time = tm.replace('am', ' AM')
    }
    var hours = Number(time.match(/^(\d+)/)[1]);
    var minutes = Number(time.match(/:(\d+)/)[1]);
    var AMPM = time.match(/\s(.*)$/)[1];
    if (AMPM == "PM" && hours < 12) hours = hours + 12;
    if (AMPM == "AM" && hours == 12) hours = hours - 12;
    var sHours = hours.toString();
    var sMinutes = minutes.toString();
    if (hours < 10) sHours = "0" + sHours;
    if (minutes < 10) sMinutes = "0" + sMinutes;
    return (sHours + ":" + sMinutes);
  }

  newSessionNoteSlug(){
    if(this.isPrimaryOrJunior()){
      return 'lbl_new_session_note_pj'
    }
    return 'lbl_new_session_note'
  }

  /**
   * Verify if the date/dateTime is before the current date/time
   * @param {string} date
   * @param {Boolean} isDateAndTime "dateAndTime" format for G9/OSSLT, "dateOnly" format for PJ
   * @returns {Boolean} return true if date contains past date, undefined returns false
   */
  isPastDate(date:string, isDateAndTime:boolean):boolean {
    const currentDateFormat = this.dateFormats[`${isDateAndTime ? 'dateAndTime' : 'dateOnly'}`]
    const now = moment().format(currentDateFormat)
    return moment(date, currentDateFormat).isBefore(now)
  }
}
