import { I } from '@angular/cdk/keycodes';
import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { LangService } from 'src/app/core/lang.service';
import { AssessmentCode } from 'src/app/ui-schooladmin/data/mappings/eqao-g9';
import { IStudentAccount } from 'src/app/ui-schooladmin/data/types';
import { G9DemoDataService } from 'src/app/ui-schooladmin/g9-demo-data.service';
import { IQuestionConfig } from 'src/app/ui-testrunner/models';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
import { ASSESSMENT } from '../data/types';
import { IGenRespSheetConfig } from '../scan-button-bar/scan-button-bar.component';
import { IScanResponse, ScanInfoService } from '../scan-info.service';
import { IStudentInfoConfig } from '../t-modal-student-info-chooser/t-modal-student-info-chooser.component';
import { downloadFile } from '../../core/download-string';
import { getActivatedExemptions } from 'src/app/ui-schooladmin/data/mappings/accommodations';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { skip } from 'rxjs/operators';

export enum EResponseType {
  PAPER,
  ONLINE
}

@Component({
  selector: 't-modal-student-scan',
  templateUrl: './t-modal-student-scan.component.html',
  styleUrls: ['./t-modal-student-scan.component.scss']
})
export class TModalStudentScanComponent implements OnInit, OnDestroy {

  @Input() config: IStudentInfoConfig
  @Input() studentAccount;
  @Input() getAccLabel: (acc) => string;
  @Input() activatedAccommodations: string[]; 
  @Input() groupedActivatedAccommodations: Array<{
    key: string;
    value: string[];
  }>;
  @Input() testSessionId: string;
  @Input() classId: string;
  @Input() isSasnLogin;
  @Input() asmtSlug: ASSESSMENT;
  @Input() pageModal;
  @Input() isFIClass: boolean;
  @Input() isPrimary: boolean;
  @Output() currentIndex = new EventEmitter<number>();
  @Output() currentStudent = new EventEmitter<any>();
  @ViewChild('singleUploadInput', { static: false }) singleUploadInput: ElementRef<HTMLInputElement>;

  showCrop: boolean = false;
  responseTypeFc = new FormControl();
  largePrintFc = new FormControl();

  indToSectionMap;

  _selectedQIndex = 0;

  fcSubs: any[];

  isHoveringAccommBar: boolean = false;

  responseTemplateLoader: any;

  selectedRespIndex: number = 0;

  EResponseType = EResponseType;

  showAllScanningDetails: boolean = false;

  responseOptions = [
    {
      id: EResponseType.PAPER,
      caption: 'scan_paper_response',
      prop: 'lbl_format_paper'
    },
    {
      id: EResponseType.ONLINE,
      caption: 'scan_online_response',
      prop: 'lbl_format_online'
    }
  ];

  get student(): IStudentAccount {
    const student = this.studentList[this.studentIdx];
    this.currentStudent.emit(student);
    return student;
  }

  get studentList(): IStudentAccount[] {
    return this.config.studentList;    
  }
  get studentIdx(): number {
    return this.config.studentIdx;
  }

  get curricShort(): AssessmentCode {
    return this.config.curricShort;
  }

  get selectedQIndex(): number {
    const ind = Math.min(this._selectedQIndex, Object.keys(this.scanInfo.getCrQuestions(this.student.uid)).length);
    this.currentIndex.emit(ind);
    return ind;
  }

  set selectedQIndex(val: number) {
    this._selectedQIndex = val;
    this.currentIndex.emit(val);
  }

  get currentCrQuestionId(): number {
    const currQues = this.currentCrQuestion;
    return +(Object.keys(currQues))[0];
  }

  get currentCrQuestion() {
    const crQuestionsBySec = this.scanInfo.getCrQuestions(this.student.uid);
    if(Object.keys(crQuestionsBySec).length) {
      const sectionSlug = this.getSessionSlugByInd(this.selectedQIndex);
      const crQuestion = crQuestionsBySec[sectionSlug];
      return crQuestion;
    }
    return undefined;
  }

  getAllQuestions(): SessionDetails[] {
    const responses: SessionDetails[] = [];
    const questionsBySec = this.scanInfo.getCrQuestions(this.student.uid);
    if(Object.keys(questionsBySec).length) {
      Object.keys(questionsBySec).forEach(key => {
        const question = questionsBySec[key]
        const questionID = +(Object.keys(question))[0];
        const qResponses = question[questionID];
        const hasTaqr = question.hasTaqr;
        const isPaperFormat = question.isPaperFormat;
        if (qResponses !== null && qResponses !== undefined)
          responses.push({sessionType: key, sessionData: qResponses, hasTaqr, isPaperFormat});
      });
    }

    if (responses.length > 0) {
      responses.sort((a: SessionDetails, b: SessionDetails) => {
        if(a.sessionType < b.sessionType) { return -1; }
        if(a.sessionType > b.sessionType) { return 1; }
        return 0;
      });
      return responses;
    }

    return null;
  }

  trackBySessionType(index: number, session: SessionDetails) {
    return session.sessionType
  }

  set studentIdx(val: number) {
    if(val < 0 || val > this.numStudents) {
      return;
    } 

    this.config.studentIdx = val;
    this.refreshInfo();
  }

  get numStudents(): number {
    return this.config.studentList.length;
  }

  get currentResponse(): IScanResponse {
    return this.currentCrQuestion[this.currentCrQuestionId].responses[this.selectedRespIndex];
  }

  isUpdating:any = {}

  isAccommsExpanded = false;

  constructor(private auth: AuthService,
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
    public scanInfo: ScanInfoService,
    private onlineOrPaper: OnlineOrPaperService,
    private loginGuard: LoginGuardService, 
    private classroomService: ClassroomsService,
    public whitelabelService: WhitelabelService,
    public lang: LangService
  ) { }

  ngOnInit(): void {
    this.fcSubs = [
      this.responseTypeFc.valueChanges.pipe(skip(1)).subscribe( // Skip inital setValue call
        val => { 
            this.invigConfirmResponseFormatChange(val)
        }
      ), 
      this.largePrintFc.valueChanges.pipe(skip(1)).subscribe( // Skip inital setValue call
        val => { this.fcBoolValueChange(val, 'IsLargePrint')}
      )
    ]
    const classInfo = this.classroomService.getClassroomById(this.classId)
    this.scanInfo.loadScanInfo(classInfo.id, classInfo.group_id)
    this.refreshInfo();
  }

  ngOnDestroy(): void {
    for(const sub of this.fcSubs) {
      sub.unsubscribe();
    }
    this.scanInfo.isGeneratingStudentResSheet = false;
  }

  fcBoolValueChange(val: boolean, prop: string) {
    let patchValue;
    if(val) {
      patchValue = '1'
    } else {
      patchValue = '#';
    }

    const currIdx = this.studentIdx;
    this.isUpdating[prop] = true;
    this.auth.apiPatch(this.routes.EDUCATOR_USER_METAS, this.student.uid, {
      key: prop,
      value: patchValue,
      key_namespace: this.getKeyNamespace()
    },
    { 
      query: { 
        schl_class_group_id: this.classroomService.getClassroomById(this.classId).group_id
      }
    }).then(() => {
      this.studentList[currIdx][this.getPropName(prop)] = patchValue;
      this.isUpdating[prop] = false;
      this.onlineOrPaper.toggleStudentWritingType(val, +this.student.id);
    }).catch(()=> {
      this.isUpdating[prop] = false;
    });
  }

  getSessionSlugByInd(selectedInd: number): string {
    return this.scanInfo.getSessionSlug(this.student.uid, selectedInd);
  }

  async initStudentResponseTemplate() {
    const classroom = this.classroomService.getClassroomById(this.classId);
    const respSheetConfig: IGenRespSheetConfig = {
      testSessionId: +this.testSessionId,
      classroomId: +this.classId,
      schl_class_group_id: classroom.group_id,
      asmtSlug: this.asmtSlug,
      isIndividual: true,
      isSasn: this.classroomService.isSASNLogin(parseInt(this.classId)) ? 1 : 0,
      students: [
        {
          uid: this.student.uid,
          slugs: this.scanInfo.getScanSlugs(this.student.uid)
        }
      ]
    }
    await this.scanInfo.getStudentResponseTemplates(respSheetConfig)
      .catch((e) => console.error('ERROR_FETCHING_RESPONSE_SHEETS'));
  }

  isGeneratingStudentResSheet() {
    return this.scanInfo.isGeneratingStudentResSheet;
  }

  confirmSubmission() {
    this.pageModal.confirmModal();
    this.pageModal.closeModal();
  }

  getResponseTemplateUrl(uid, selectedQIndex) {
    const studentTemplates = this.scanInfo.currStudentResponseTemplates;
    const slug = this.scanInfo.getSessionSlug(uid, selectedQIndex);
    return studentTemplates.get(slug);
  }

  getStudentScanTrackerProps() {
    let props = {
      studentIdx: this.studentIdx + 1,
      numStudents: this.numStudents
    }
    return this.lang.tra('pj_current_student_scan_display', this.lang.c(), props);
  }

  refreshInfo(studentIdx: number = null) {
    this.selectedRespIndex = 0;
    this.responseTypeFc.setValue(this.getResponseType());
    this.largePrintFc.setValue(this.student[this.getPropName('IsLargePrint')] === '1');
  }

  goToNextStudent() {
    this.studentIdx++;
  }

  goToPrevStudent() {
    this.studentIdx--;
  }

  isFirstStudent() {
    return this.studentIdx === 0;
  }

  isLastStudent() {
    return this.studentIdx === this.numStudents - 1;
  }

  getPropName(prop: string) {
    return this.g9DemoData.getPropName(prop, this.curricShort);
  }

  getPropVal(prop: string) {
    return this.g9DemoData.getPropVal(this.student, prop, this.curricShort);
  }

  getResponseType() {
    const isPaper =  this.onlineOrPaper.getPaperVal(this.getPropVal('IsCrScanDefault'), this.curricShort) ;

    if(isPaper) {
      return EResponseType.PAPER
    } 

    return EResponseType.ONLINE
  }

  isWritingPaper() {
    return this.getResponseType() === EResponseType.PAPER;
  }

  getKeyNamespace() {
    return this.g9DemoData.getKeyNamespace(this.curricShort);
  }

  // unused
  // getScanCaption(question: IScanQuestion) {
  //   return question.subsessionSlug || question.question?.caption;
  // }

  selectQ(qIndex: number) {
    this.selectedQIndex = qIndex;
    this.selectedRespIndex = 0;
  }

  isQSelected(qIndex: number) {
    return this.selectedQIndex === qIndex;
  }
  
  isConfirmed(qIndex: number) {
    const session = this.scanInfo.getCrSessionQuestions(this.student.uid, qIndex);
    const quesIds = Object.keys(session);
    // console.log("Session is", session);
    // console.log("this.getCrQuestions() is", this.getCrQuestions());
    // console.log(!this.currentCrQuestion.isPaperFormat 
    //   && this.currentCrQuestion.hasTaqr)

    if (session.isPaperFormat){
      // return session[quesIds[0]]?.isConfirmed; //if there is a question 0 in session then call .isConfirmed - this line return true or false only
      if (session[quesIds[0]]) {
        return session[quesIds[0]].isConfirmed
      } else {
        return false
      }
    }
    else {
      return session.hasTaqr; //Taqr =test_attempt_question_responses 
    }
  }


  numResponses(qIndex: number){
    const session = this.scanInfo.getCrSessionQuestions(this.student.uid, qIndex);
    const currQuesId = this.currentCrQuestionId
    //console.log("Current Question ID", currQuesId)
    //console.log("ALL SESSION ARRAY -----", this.getCrQuestions())
    //console.log("This session you selected is ------", this.getSessionSlugByInd(qIndex))
    
    if (session.isPaperFormat){
      return this.getCrQuestions()[this.getSessionSlugByInd(qIndex)][currQuesId]?.responses?.length
    }
  }

  hasResponses(qIndex: number) {
    //console.log("------hasResponses FUNC----", this.numResponses(qIndex)) // 0 false 1 true
    const session = this.scanInfo.getCrSessionQuestions(this.student.uid, qIndex);
    const quesIds = Object.keys(session);

    //Original code before fix yellow btn
    //return this.numResponses(qIndex);
    // New solution mimic the logic from isConfirmed
    return session[quesIds[0]]?.responses.length
  }

  /**
   * Get individual question hasTaqr value
   * @param qIndex 
   * @returns {number}
   */
  hasTaqr(qIndex:number):number {
    const session = this.scanInfo.getCrSessionQuestions(this.student.uid, qIndex);
    return session.hasTaqr
  }

  /**
   * Get individual question isPaperFormat value 
   * @param qIndex 
   * @returns {number}
   */
  getSessionIsPaperFormat(qIndex:number):number {
    const session = this.scanInfo.getCrSessionQuestions(this.student.uid, qIndex);
    return session.isPaperFormat
  }

  async enablePrintRespSheet() {
    await this.initStudentResponseTemplate();
    const studentScanInfo = this.scanInfo.currStudentResponseTemplates;
    const slug = this.scanInfo.getSessionSlug(this.student.uid, this.selectedQIndex);
    const base64 = studentScanInfo.get(slug);
    this.scanInfo.generateResponseSheetPdf(base64)
  }

  numCurrResponses() {
    return this.numResponses(this.selectedQIndex);
  }

  getCrQuestions() {
    return this.scanInfo.getCrQuestions(this.student.uid);
  }

  getSessionSlugs() {
    return this.scanInfo.getSessionSlugs();
  }

  showScanInfo() {
    return !this.config.asmtSlug?.includes('SAMPLE');
  }

  getCurrentStudentAltRequest(){
    const currentClass = this.g9DemoData.classrooms.find(classroom => +classroom.id === +this.classId)    ;
    const currentSemester = this.g9DemoData.semesters.map[+currentClass.semester];
    const currentTestWindow = this.g9DemoData.testWindows.find( testWindow => +testWindow.id === +currentSemester.testWindowId)

    const altVersionRequestName = this.g9DemoData.getStudentAltVersionRequestName(currentTestWindow)
    const studentAltVersionRequest = this.studentAccount[altVersionRequestName]

    return studentAltVersionRequest
  }

  getAltVersionInfoValue(valProp){
    const studentAltVersionRequest = this.getCurrentStudentAltRequest()
    if(studentAltVersionRequest){
      return studentAltVersionRequest[valProp]
    }
    return 0;
  }

  getLinearAssignFormat(){
    const braille_format_value = this.getAltVersionInfoValue('braille_format')
    const pdf_format_value = this.getAltVersionInfoValue('pdf_format')
    const audio_delivery_format_value = this.getAltVersionInfoValue('audio_delivery_format')
    const audio_format_value = this.getAltVersionInfoValue('audio_format')
    const asl_format_value = this.getAltVersionInfoValue('asl_format')
    const alt_version_online_require = this.getAltVersionInfoValue('alt_version_online_require')
    return this.g9DemoData.getAltRequestFormatList({
      braille_format_value, pdf_format_value, audio_delivery_format_value, audio_format_value, asl_format_value, alt_version_online_require
    });
  }


  getLinear() {
    let linear = this.student[this.getPropName('eqao_dyn_linear')];
    if(!linear || linear === '#') {
      linear = 0;
    }
    return +linear;  
  }

  hasAltRequest(){
    const studentAltVersionRequest = this.getCurrentStudentAltRequest();
    if(!studentAltVersionRequest || studentAltVersionRequest.alt_version_requests_id === ""){
      return false
    }
    return true
  }

  getAltAssigned(){
    //const linAssignedDate = this.g9demoService.getLinearAssignDate(this.studentAccount.uid, this.classId )
    const studentAltVersionRequest = this.getCurrentStudentAltRequest();
    const selfOrderAltAssigned = studentAltVersionRequest && studentAltVersionRequest.approved_on && !studentAltVersionRequest.is_revoked

    if(selfOrderAltAssigned){
      return this.lang.tra("lbl_yes")
    } else {
      return this.lang.tra("lbl_no")
    }
  }

  getLinearAssignStatus(){
    const studentAltVersionRequest = this.getCurrentStudentAltRequest();
    if(!studentAltVersionRequest){
      return ""
    }
    const status = studentAltVersionRequest["alt_version_requests_status"]
    switch(status){
      case 'Pending':
        return this.lang.tra("alt_version_requests_status_pending")
      case 'Canceled':
        return this.lang.tra("alt_version_requests_status_canceled")
      case 'Approved':
        return this.lang.tra("alt_version_requests_status_approved")
      case 'Rejected':
        return this.lang.tra("alt_version_requests_status_rejected")
      case 'Shipment Send':
        return this.lang.tra("alt_version_requests_status_shipment_sent")
      case 'Operational Link Send':
        return this.lang.tra("alt_version_requests_status_link_sent") 
      default:
        return ""
    }
  }

  hasAccommodations() {
    return this.getLinearAssignFormat()?.length || this.groupedActivatedAccommodations?.length || this.student[this.getPropName('teacher_notes')] || this.student[this.getPropName('IsLargePrint')] === '1';
  }

  showAccommBar() {
    return this.isWritingPaper() || this.hasAccommodations();
  }

  isShowingAccomms() {
    return this.isAccommsExpanded && this.showAccommBar();
  }

  toggleShowAccomm() {
    if(!this.showAccommBar()) {
      return;
    }
    
    this.isAccommsExpanded = !this.isAccommsExpanded;
  }

  isFirstResponse() {
    return this.selectedRespIndex === 0;
  }

  isLastResponse() {
    return this.selectedRespIndex === this.numCurrResponses() - 1;
  }

  goToNextResponse() {
    this.selectedRespIndex++;
  }

  goToPrevResponse() {
    this.selectedRespIndex--;
  }

  isConfirmedAndSubmitted(questionInfo: any) {
    if (questionInfo.isConfirmed && questionInfo.is_test_session_submitted) {
      return true;
    } return false;
  }

  handleSelectedFile(files: FileList) {
    if(files.length !== 1) {
      return;
    }
    const test_attempt_id = this.scanInfo.getTestAttemptId(this.student.uid);
    const selectedSession = this.scanInfo.getSessionSlug(this.student.uid, this.selectedQIndex);
    const questionId = this.scanInfo.getQuesIdBySection(this.student.uid, selectedSession);
    const sessionInfo = this.getCrQuestions()[selectedSession];
    const tassId = sessionInfo.tassId;
    const fileConfirmedAndSubmitted = this.isConfirmedAndSubmitted(sessionInfo[questionId]);
    if (fileConfirmedAndSubmitted && sessionInfo[questionId].responses.length > 0) {
      this.loginGuard.quickPopup('Cannot override previous session scans!');
    } else {
      const classroom = this.classroomService.getClassroomById(this.classId);
      this.scanInfo.initUploadSingleScan(test_attempt_id, this.currentCrQuestionId, this.student.uid, files.item(0), this.getSessionSlugByInd(this.selectedQIndex), tassId, selectedSession, classroom.group_id).finally(() => {
        this.singleUploadInput.nativeElement.value = '';
      });
    }
  }

  chooseSingleScanUpload() {
    if (!this.getNeedsReupload(this.selectedQIndex) && this.getHasTasr(this.selectedQIndex)){
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('pj_confirm_single_upload_no_reupload_flag'),
        btnProceedConfig: {caption : 'lbl_yes'},
        btnCancelConfig: {caption: 'lbl_no'},
        confirm: () => {
          this.singleUploadInput.nativeElement.click();
        },
      })
    } else {
      this.singleUploadInput.nativeElement.click();
    }
  }
  
  async confirmResponse(selectedQIndex) {
    const scanInfo = this.scanInfo.getScanInfo(this.student.uid);
    const testAttemptId = scanInfo.test_attempt_id;
    const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
    const scanningQuesId = Object.keys(scanInfo.scanningQues[slug])[0];
    const classroom = this.classroomService.getClassroomById(this.classId);
    this.auth.apiPatch(this.routes.EDUCATOR_RESP_SHEET_CONFIRMATION, testAttemptId, {
      testSessionId: this.testSessionId,
      testQuestionId: scanningQuesId,
      isConfirming: 1,
      schl_class_group_id: classroom.group_id
    }).then((res) => {
      const sectionQues = scanInfo.scanningQues[slug][scanningQuesId];
      sectionQues.isConfirmed = true;
    }).catch((e) => {
      console.error('CONFIRM_RESPONSE_ERROR');
    })
  }

  getApiParamsFactory(selectedQIndex: number) {
    const scanInfo = this.scanInfo.getScanInfo(this.student.uid);
    const classroom = this.classroomService.getClassroomById(this.classId);
    const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
    return {
      scanInfo: scanInfo,
      testAttemptId: scanInfo.test_attempt_id,
      scanningQuesId: Object.keys(scanInfo.scanningQues[slug])[0],
      classroom: classroom,
      params: {
        query: {
          schl_class_group_id: classroom.group_id
        }
      }
    };
  }

  onSetIsNeedsReupload(selectedQIndex, isNeedsReupload:boolean, event?){
    const doToggleNeedsReupload = () => {
      const factoryObjects = this.getApiParamsFactory(selectedQIndex);
      this.auth.apiPatch(this.routes.EDUCATOR_RESP_SHEET_NEEDS_REUPLOAD, factoryObjects.testAttemptId, {
        testSessionId: this.testSessionId,
        testQuestionId: factoryObjects.scanningQuesId,
        isNeedsReupload: isNeedsReupload
      }, factoryObjects.params)
      .then((res) => {
        const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
        const sectionQues = factoryObjects.scanInfo.scanningQues[slug];
        const scanningQuesId = Object.keys(sectionQues)[0]
        sectionQues.isNeedsReupload = isNeedsReupload;
        sectionQues[scanningQuesId].isNeedsReupload = isNeedsReupload;
        if (!isNeedsReupload){
          sectionQues.isNeedsReuploadScorLeadFlagged = false;
        }
      })
      .catch((e) => {
        console.error('NEEDS_REUPLOAD_TOGGLE_ERROR');
      })
    }

    const isNotLegible = this.getIsNotLegible(selectedQIndex)
    if (isNotLegible && isNeedsReupload){
      if (event) event.target.checked = false;
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('pj_confirm_force_remove_is_not_legible'),
        btnProceedConfig: {caption : 'lbl_yes'},
        btnCancelConfig: {caption: 'lbl_no'},
        confirm: () => {
          doToggleNeedsReupload();
          this.onSetIsNotLegible(selectedQIndex, false)
        },
      })
    } else {
      doToggleNeedsReupload();
    }
  }


  onSetIsNotLegible(selectedQIndex, isNotLegible:boolean, event?) {

    const doToggleNotLegible = () => {
      const factoryObjects = this.getApiParamsFactory(selectedQIndex);
      this.auth.apiPatch(this.routes.EDUCATOR_RESP_SHEET_NOT_LEGIBLE_CONFIRMATION, factoryObjects.testAttemptId, {
        testSessionId: this.testSessionId,
        testQuestionId: factoryObjects.scanningQuesId,
        isNotLegible: isNotLegible
      }, factoryObjects.params)
      .then((res) => {
        const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
        const sectionQues = factoryObjects.scanInfo.scanningQues[slug];
        sectionQues.isNotLegible = isNotLegible;
      }).catch((e) => {
        console.error('NOT_LEGIBLE_TOGGLE_ERROR');
      })
    }

    const isNeedsReupload = this.getNeedsReupload(selectedQIndex)
    if (isNeedsReupload && isNotLegible){
      if (event) event.target.checked = false;
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('pj_confirm_force_remove_scan_needs_reupload'),
        btnProceedConfig: {caption : 'lbl_yes'},
        btnCancelConfig: {caption: 'lbl_no'},
        confirm: () => {
          doToggleNotLegible();
          this.onSetIsNeedsReupload(selectedQIndex, false)
        },
      })
    } else {
      doToggleNotLegible();
    }

  }

  getScanInfoProperty(propertyName: string, selectedQIndex: number){
    const scanInfo = this.scanInfo.getScanInfo(this.student.uid);
    const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
    const propValue = scanInfo.scanningQues[slug][propertyName];
    return propValue;
  }

  getIsNotLegible(selectedQIndex) {
    return this.getScanInfoProperty('isNotLegible', selectedQIndex)
  }
  
  getNeedsReupload(selectedQIndex) {
    return this.getScanInfoProperty('isNeedsReupload', selectedQIndex)
  }

  getIsNeedsReuploadScorLeadFlagged(selectedQIndex){
    return this.getScanInfoProperty('isNeedsReuploadScorLeadFlagged', selectedQIndex)
  }

  getHasTasr(selectedQIndex){
    return this.getScanInfoProperty('hasTasr', selectedQIndex)
  }

  onSetIsNoPaperResponse(selectedQIndex, isNoPaperResponse:boolean) {
    const factoryObjects = this.getApiParamsFactory(selectedQIndex);
    this.auth.apiPatch(this.routes.EDUCATOR_RESP_SHEET_NOT_PAPER_RESP, factoryObjects.testAttemptId, {
      testSessionId: this.testSessionId,
      testQuestionId: factoryObjects.scanningQuesId,
      isNoPaperResponse: isNoPaperResponse
    }, factoryObjects.params)
    .then((res) => {
      const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
      const sectionQues = factoryObjects.scanInfo.scanningQues[slug];
      sectionQues.isNoPaperResponse = isNoPaperResponse;
    }).catch((e) => {
      console.error('CONFIRM_RESPONSE_ERROR');
    })
  }

  getIsNotPaperResponse(selectedQIndex) {
    const scanInfo = this.scanInfo.getScanInfo(this.student.uid);
    const slug = this.scanInfo.getSessionSlug(this.student.uid, selectedQIndex);
    const isNoPaperResponse = scanInfo.scanningQues[slug]['isNoPaperResponse'];
    return isNoPaperResponse;
  }

  toggleCrop() {
    this.showCrop = !this.showCrop;
  }

  toggleScanningDetails() {
    this.showAllScanningDetails = !this.showAllScanningDetails;
  }

  mapSessionSlug(sessionType: string) {
    const sessionSlugToTranslationSlug: {[key: string]: string} = {
      'SESSION_A': 'lang_session_A',
      'SESSION_B': 'lang_session_B',
      'SESSION_C': 'lang_session_C',
      'SESSION_DW': 'lang_session_D',
      'SESSION_DR': 'lang_session_D',
    }
    return sessionSlugToTranslationSlug[sessionType];
  }

  getStudentExemptions(student) {
    return this.g9DemoData.getStudentExemptions(student, this.curricShort, this.classroomService);
  }

  /**
   * Display response format change popup warning message to invigilator
   * If confirm, 
   * 1. update value change
   * 2. And log user confirmation
   * If cancel: revert UI value back
   */
  invigConfirmResponseFormatChange(newValue){
    const prevValue:number = newValue === EResponseType.PAPER ? EResponseType.ONLINE : EResponseType.PAPER
    
    this.loginGuard.confirmationReqActivate({
      caption: 'pj_response_format_changed_warning',
      props: { 
        "CURRENT_FORMAT": this.lang.tra(this.responseOptions[prevValue].prop).toLowerCase(), 
        "NEW_FORMAT": this.lang.tra(this.responseOptions[newValue].prop).toLowerCase()
      },
      btnProceedConfig: { caption: 'lbl_confirm' },
      confirm: () => {
        this.fcBoolValueChange(newValue === EResponseType.PAPER, 'IsCrScanDefault');
        this.auth.apiCreate(this.routes.LOG, {
          slug: 'INVIG_CONFIRM_RESPONSE_FORMAT_CHANGED',
          data: {
            testSessionId: this.testSessionId,
            student_uid: this.student.uid,
            uid: this.auth.getUid(),
            is_paper_format: newValue === EResponseType.PAPER ? 1 : 0
          }
        })
      },
      close: () => {
        this.responseTypeFc.setValue(prevValue, {emitEvent: false})
      }
    })
  }
}

type SessionDetails = {
  sessionType: string;
  sessionData: object;
  hasTaqr: boolean;
  isPaperFormat: boolean;
}