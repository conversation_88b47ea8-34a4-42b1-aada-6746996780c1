<div style="display: flex; flex-direction: column; align-items: center; width: 100%">
    <div class="name-header-box" [class.show-accomm-bar]="showAccommBar()" [class.is-hovered]="isHoveringAccommBar">

       <!-- EXPAND SCAN BNT -->
       <div style=" line-height: 1em; display: flex; width: 100%; justify-content: flex-end; margin-top: 0.5rem;
       margin-bottom: -1.5rem; z-index: 9;">
          <button class="expand-scan-btn" (click)="toggleScanningDetails()"  >
              <i *ngIf="!showAllScanningDetails" class="fa fa-expand" aria-hidden="true"></i>
              <i *ngIf="showAllScanningDetails" class="fa fa-compress" aria-hidden="true"></i>
          </button>
        </div>

        <!-- DISPLAY STUDENT NAME -->
        <div style="font-size: 1.8em; font-weight: 600; line-height: 1em; text-align: center; margin-top: 0.55em; z-index: 1; display: flex; width: 100%; justify-content: center; border-bottom: 2px black solid;">
            <div class="student_display-name">
                {{student.displayName}}
            </div>
        </div>
        <hr style="margin-top: 0; margin-bottom: 0.75em; width: 100%; background-color: transparent; z-index: 1">
        <!-- ON TOP of DROPDOWN response format selection -->
        <span><b><tra slug="pj_scan_response_format_current"></tra></b></span>
        <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; width: 100%; z-index: 1">        
            <div style="display: flex; flex-direction: row; justify-content: center; width: 100%">
              <!-------- ODEN NUMBER BTN ---------- -->
                <div *ngIf="!isSasnLogin" style="background-color: white; border: gray 1px solid; border-radius:0.4em; padding: 0.3em; height: 2.7em; display: flex; align-items: center; justify-content: center; width: 80%">
                    <tra style="font-weight: 600" slug="pj_students_scan_modal_oen"></tra>&nbsp;{{ student.eqao_student_gov_id }}
                </div>
                <div *ngIf="isSasnLogin" style="background-color: white; border: gray 1px solid; border-radius:0.4em; padding: 0.3em; height: 2.7em; display: flex; align-items: center; justify-content: center; width: 80%">
                    <tra style="font-weight: 600" slug="pj_students_scan_modal_sasn"></tra>&nbsp;{{ student.SASN }}
                </div>
            </div>

            <!-- ------ DROP DOWN paper response/ online response ------- -->
            <div style="display: flex; width: 100%; align-items: center; justify-content: center; margin-right: 0.5em; margin-left: 0.5em">
                <div style="height: 2.7em" class="select is-fullwidth" [class.is-disabled]="isUpdating['IsCrScanDefault']">
                    <select [formControl]="responseTypeFc" style="height: 2.7em; border-radius: 20rem; border: gray solid 1px">
                        <option [ngValue]="op.id" *ngFor="let op of responseOptions"><tra [slug]="op.caption"></tra></option>
                    </select>
                </div>
            </div>
            <div style="width: 100%; display: flex; justify-content: center; align-items: center">
                <div style="display: flex; align-items: center; justify-content: space-evenly; width: 80%; height: 2.7em; padding: 0.3em; background-color: white; border: gray solid 1px; border-radius: 0.4em">
                    <div style="display: flex; flex-direction: row; align-items: center; margin-right: 0.5em;">
                        <button  [class.is-disabled]="isFirstStudent()" [disabled]="isFirstStudent()" (click)="goToPrevStudent()" class="button student-nav-button" style="margin-right: 0.3em"><i class="fas fa-arrow-left"></i></button>
                        <button [class.is-disabled]="isLastStudent()" [disabled]="isLastStudent()" (click)="goToNextStudent()" class="button student-nav-button"><i class="fas fa-arrow-right"></i></button>
                    </div>
                    <div style="display: flex; align-items: center; color: #7c7f9d; margin-right: 0.5em" [class.is-condensed]="true">{{ getStudentScanTrackerProps() }}</div>
                </div>
            </div>
        </div>

        <div *ngIf="isShowingAccomms()" style="padding-top:1.0em; padding-bottom:0.8em">
            <div *ngIf="false">
                <mat-slide-toggle [disabled]="isUpdating['IsLargePrint']" [formControl]="largePrintFc"><tra slug="scan_large_print"></tra></mat-slide-toggle>
            </div>
            <table class="table is-bordered">

                <tr *ngIf ='hasAltRequest()'>
                  <td><tra slug="sdc_format_alternate_request"></tra></td>
                  <td>
                    <tra slug="eqao_assessment_title"></tra>
                    <ul *ngFor="let requestFormat of getLinearAssignFormat()">
                      <li>{{requestFormat}}</li>
                    </ul>
                  </td>
                </tr>
                <tr *ngIf ='hasAltRequest()'>
                  <td><tra slug="sdc_status_alternate_request"></tra></td>
                  <td><input [disabled]="true" type="text" class="input is-small" [value] = "getLinearAssignStatus()"></td>
                </tr>
                <tr *ngIf ='hasAltRequest()'>
                  <td><tra slug="sdc_alt_test_assigned"></tra></td>
                  <td><input [disabled]="true" type="text" class="input is-small" [value] = "getAltAssigned()"></td>
                </tr>
                <tr *ngIf="groupedActivatedAccommodations?.length">
                    <td><tra slug="eqao_lbl_accommodation_support"></tra></td>
                    <td>
                      <!-- <ul>
                        <li *ngFor="let acc of activatedAccommodations">
                          <tra-md [isCondensed]="acc === getPropName('eqao_acc_assistive_tech_2_other')" [slug]="getAccLabel(acc)" style="display: block;"></tra-md> 
                          <p *ngIf="acc === getPropName('eqao_acc_assistive_tech_2_other')">{{student[getPropName('eqao_acc_assistive_tech_custom')]}}</p>
                        </li>
                      </ul> -->
                      <ul>
                        <li *ngFor="let acc of groupedActivatedAccommodations">
                          <ng-container *ngIf="acc.value; else defaultTranslation">
                            <tra-md [isCondensed]="acc.key === getPropName('eqao_acc_assistive_tech_2_other')" [slug]="acc.key"></tra-md> 
                            <span *ngFor="let gpAcc of acc.value; let i = index;">
                              {{i == 0 ? '(' : ''}}
                              <tra-md [isCondensed]="gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_reading_other') || gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_writing_other') || gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_mathematics_other')" [slug]="getAccLabel(gpAcc)" style="display: inline-block;"></tra-md>
                              <p *ngIf="gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_reading_other')" style="margin-bottom:0.5em;">
                                {{student[getPropName('eqao_acc_assistive_tech_custom_pj_reading')]}}
                              </p>
                              <p *ngIf="gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_writing_other')" style="margin-bottom:0.5em;">
                                {{student[getPropName('eqao_acc_assistive_tech_custom_pj_writing')]}}
                              </p>
                              <p *ngIf="gpAcc === getPropName('eqao_acc_assistive_tech_2_pj_mathematics_other')" style="margin-bottom:0.5em;">
                                {{student[getPropName('eqao_acc_assistive_tech_custom_pj_mathematics')]}}
                              </p>
                              {{i !== acc.value.length-1 ? ', ' : ')'}}
                            </span>
                          </ng-container>
                          <ng-template #defaultTranslation>
                            <tra-md [isCondensed]="acc.key === getPropName('eqao_acc_assistive_tech_2_other')" [slug]="getAccLabel(acc.key)" style="display: block;"></tra-md> 
                            <p *ngIf="acc.key === getPropName('eqao_acc_assistive_tech_2_other')">{{student[getPropName('eqao_acc_assistive_tech_custom')]}}</p>
                          </ng-template>
                        </li>
                      </ul>
                    </td>
                </tr>
                <tr *ngIf="student[getPropName('teacher_notes')]">
                  <td><tra slug="additional_accommodations"></tra></td>
                  <td>{{student[getPropName('teacher_notes')]}}</td>
                </tr>
              </table>
        </div>
        
        <div class="bottom-header-bar" [class.show-accomm-bar]="showAccommBar()" (click)="toggleShowAccomm()" (mouseenter)="isHoveringAccommBar = true" (mouseleave)="isHoveringAccommBar = false">
            <ng-container *ngIf="showAccommBar()">
                <ng-container *ngIf="!isShowingAccomms()">
                    <i *ngIf="hasAccommodations() || hasAltRequest()" class="fas fa-universal-access"></i>
                </ng-container>
                <i *ngIf="isShowingAccomms()" class="fas fa-caret-up"></i>
            </ng-container>
        </div>
        <div *ngIf="showAccommBar() && !isShowingAccomms()" class="bottom-highlight" [class.is-highlighted]="hasAccommodations()" [class.is-hovered]="isHoveringAccommBar" [class.is-neutral]="!hasAccommodations()"></div>
    </div>

    <div style="display: flex; width: 100%; flex-direction: column; align-items: center; margin-top: 1.0em; border-radius: 0.5em; padding: 2em; background-color: rgba(240, 240, 240, 0.5); box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.05);" *ngIf="scanInfo.isLoaded() && showScanInfo() && currentCrQuestion">
      <span>
        <b><tra slug="pj_scan_response_format_assessed"></tra></b>
      </span>
        <div *ngIf="!showAllScanningDetails" style="display: flex; flex-direction: row; justify-content: center; align-items: center; font-size: 1.0em; width: 100%">
            <!-- <ng-container *ngFor="let question of getCrQuestions(); let i = index">
                <button (click)="selectQ(i)" class="button cr-question-button" [class.is-selected]="isQSelected(i)" [class.is-not-selected]="!isQSelected(i)">
                    <div style="display: flex; flex-direction: row; align-items: center;">
                        <div class="question-circle">
                            <i *ngIf="!isConfirmed(i)" class="question-circle-icon" [class.fas]="!hasResponses(i)" [class.far]="hasResponses(i)" [class.fa-dot-circle]="hasResponses(i)" [class.fa-circle]="!hasResponses(i)"></i>
                            <i *ngIf="isConfirmed(i)" class="fas fa-check-circle question-circle-icon"></i>
                        </div>
                        <tra [slug]="getScanCaption(question)"></tra>
                    </div>
                </button>
            </ng-container> -->
            <ng-container *ngFor="let sessionSlug of getSessionSlugs(); let i = index">
                <button 
                  (click)="selectQ(i)" 
                  class="button cr-question-button" 
                  [class.is-selected]="isQSelected(i)" 
                  [class.is-not-selected]="!isQSelected(i)"
                >
                    <div style="display: flex; flex-direction: row; align-items: center;">
                        <div class="question-circle">
                            <i *ngIf="!isConfirmed(i)" 
                              class="question-circle-icon" 
                              [class.fas]="!hasResponses(i)" 
                              [class.fa-circle]="!hasResponses(i)">
                            </i>

                            <!------- Green Check Mark ------->
                            <i *ngIf="isConfirmed(i)" 
                              class="fas fa-check-circle question-circle-icon">
                            </i>

                            <!------- Yellow Dot ------->
                            <i *ngIf="hasResponses(i) && !isConfirmed(i)" 
                              class="far fa-dot-circle question-circle-icon">
                            </i>
                            
                        </div>
                        <div class="question-circle-text">
                          <tra class="question-session-slug" [slug]="sessionSlug"></tra>
                          <ng-container *ngIf="hasTaqr(i)">
                            <tra class="question-response-format" [slug]="getSessionIsPaperFormat(i) ? 'lbl_format_paper' : 'lbl_format_online'"></tra>
                          </ng-container>
                        </div>
                    </div>
                </button>
            </ng-container>
        </div>

        <div *ngIf="((isWritingPaper() 
              && !currentCrQuestion.hasTaqr) 
              || (!!currentCrQuestion.isPaperFormat 
              && !!currentCrQuestion.hasTaqr)) 
              && !showAllScanningDetails" 
              class="sub-grouping">
            <div style="display: flex; white-space: normal; width: 5.0em; margin-right: 0.5em; text-align: center; justify-content: center; align-items: center">
              <tra slug="pj_scan_response_sheets"></tra>
            </div>
            <!-- --------------PRINT BTN------------ -->
            <button 
              class="button is-warning is-orange-print-btn" 
              [class.is-loading]="isGeneratingStudentResSheet()"
              [disabled]="isFIClass && isPrimary" 
              (click)="enablePrintRespSheet()">  
                <i class="fas fa-print"></i>&nbsp;<tra slug="ie_print"></tra>
            </button>
            <!-- -----------------UPLOAD BTN ----------------------->
            <button 
              class="button upload-btn-scan-pj" 
              style="margin-right: 0;" 
              [disabled]="scanInfo.isUploadingSingle || (isFIClass && isPrimary)" 
              (click)="chooseSingleScanUpload()">
                <i class="fas fa-upload"></i>&nbsp;<tra slug="btn_upload"></tra>
            </button>
            <input 
              #singleUploadInput 
              style="display: none" 
              type="file" 
              accept=".pdf" 
              (change)="handleSelectedFile($event.target.files)"
            >
        </div>

        <div 
          *ngIf="((isWritingPaper() 
          && !currentCrQuestion.hasTaqr)
          || (!!currentCrQuestion.isPaperFormat 
          && !!currentCrQuestion.hasTaqr)) 
          && !showAllScanningDetails" 
          style="margin-top: 0.5em;">
            <tra slug="pj-scan-modal-refresh-mssg"></tra>
        </div>

        <div 
          *ngIf="getStudentExemptions(student).length" 
          style="margin-top: 0.5em;">
          <tra slug="pj_scan_modal_exempt_warning"></tra>
        </div>


        <div  
          *ngIf="(!!currentCrQuestion.isPaperFormat && !!currentCrQuestion.hasTaqr) 
          && !hasResponses(selectedQIndex) 
          && !showAllScanningDetails" 
          style="margin-top: 2rem; text-align: center;">
            <p style="text-align: justify;"><tra-md slug="pj_open_paper_response_note"></tra-md></p>
            <div style="display: flex; margin-left: auto; margin-right: auto; color: #000; align-items: flex-start; gap: 0.5em;">
                <input 
                #setIsNoPaperResponse 
                type="checkbox" 
                [disabled]="isConfirmed(selectedQIndex)" 
                [checked]="getIsNotPaperResponse(selectedQIndex)" 
                (change)="onSetIsNoPaperResponse(selectedQIndex, setIsNoPaperResponse.checked)" 
                style="margin-right: 0.2rem; margin-top: 0.25rem;"/>
                  <span 
                    [ngStyle]="{'color': isConfirmed(selectedQIndex) ? '#888' : null}">
                      <b><tra slug="pj_checkbox_no_paper_response_text"></tra></b>
                  </span>
            </div>
            <button 
              class="button confirm-btn-pj-scan" 
              (click)="confirmResponse(selectedQIndex)"
              [disabled]="!getIsNotPaperResponse(selectedQIndex) || isConfirmed(selectedQIndex) || getNeedsReupload(selectedQIndex)" 
             
              style="margin-top: 1rem; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(201, 201, 201, 0.5))">
                <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; width: 100%;">
                    <div *ngIf="!isConfirmed(selectedQIndex)">
                      <i class="fa fa-check-circle"></i>&nbsp;<tra slug="btn_confirm"></tra>
                    </div>
                    <div *ngIf="isConfirmed(selectedQIndex)" style="text-align: center; width: 100%">
                      <tra slug="btn_confirmed"></tra>
                    </div>
                </div>
            </button>
        </div>

        <ng-container *ngIf="!showAllScanningDetails else allSessions">
            <div *ngIf="hasResponses(selectedQIndex) && !!currentCrQuestion.hasTaqr && !!currentCrQuestion.isPaperFormat" style="display: flex; flex-direction: column; justify-content: center; width: 100%; padding: 1em;">
                <div style="display: flex; flex-direction: column; align-items: center; width: 100%; border-radius: 0.5em; background-color: rgba(0,0,0,0.2); box-shadow: inset 0 0 0.5em rgba(0,0,0,0.25);">
                    <div *ngIf="numCurrResponses() > 1" style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100%; background-color: rgba(0,0,0,0.2); border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; padding: 0.5em;">
                        <ng-container *ngIf="currentCrQuestion?.responses?.length">
                            <button *ngFor="let response of currentCrQuestion.responses; let i = index;" class="button response-button" [class.is-transparent]="i !== selectedRespIndex" style="padding: 0.4em" (click)="selectedRespIndex = i">
                                <i class="far" [class.fa-file-pdf]="response.responseType === EResponseType.PAPER" [class.fa-file]="response.responseType === EResponseType.ONLINE" style="font-size: 1.75em"></i>
                            </button>
                        </ng-container>
                    </div>
                    <div style="width: 100%; padding: 1.0em; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <div style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100%">
                            <div>
                                <button *ngIf="numCurrResponses() > 1" [disabled]="isFirstResponse()" [class.is-disabled]="isFirstResponse()" class="button response-nav-button" style="margin-right: 0.75em" (click)="goToPrevResponse()"><i class="fas fa-arrow-left"></i></button>
                                <div style="width: 100%; display: flex; justify-content: center; background-color: white; padding: 1.0em; box-shadow: 0 0 0.25em 0.25em rgba(0,0,0,0.2); height: 32em;">
                                    <question-runner
                                        *ngIf="!currentCrQuestion.isPaperFormat"
                                        [currentQuestion]="currentCrQuestion.question"
                                        [isSubmitted]="true"
                                        [questionState]="currentResponse.info"
                                    ></question-runner>
                                    <img *ngIf="currentCrQuestion.isPaperFormat" [src]="!showCrop ? currentResponse.info.url : currentResponse.info.url_cropped" style="height: 100%; object-position: 0% -80% !important;">
                                </div>
                                <button *ngIf="numCurrResponses() > 1" [disabled]="isLastResponse()" [class.is-disabled]="isLastResponse()" class="button response-nav-button" style="margin-left: 0.75em" (click)="goToNextResponse()"><i class="fas fa-arrow-right"></i></button>
                            </div>
                        </div>
                        <div 
                          *ngIf="!isConfirmed(selectedQIndex)" 
                          style="margin-top: 1.0em">
                            <div style="display: flex; margin: auto; width: 69%; color: #000; align-items: flex-start;"> 
                                <input 
                                  #setIsNotLegible 
                                  type="checkbox" 
                                  [checked]="getIsNotLegible(selectedQIndex)" 
                                  (change)="onSetIsNotLegible(selectedQIndex, setIsNotLegible.checked, $event)" 
                                  style="margin-right: 0.2rem; margin-top: 0.25rem;"
                                />
                                <tra-md slug="pj_checkbox_not_legible_text"></tra-md>
                            </div>
                            <div style="display: flex; margin: auto; width: 69%; color: #000; align-items: flex-start;">
                              <input 
                                #setNeedsReupload 
                                id="setNeedsReupload"
                                type="checkbox" 
                                [checked]="getNeedsReupload(selectedQIndex)" 
                                [disabled]="getIsNeedsReuploadScorLeadFlagged(selectedQIndex)"
                                (change)="onSetIsNeedsReupload(selectedQIndex, setNeedsReupload.checked, $event)" 
                                style="margin-right: 0.2rem; margin-top: 0.25rem;"
                              />
                              <label for="setNeedsReupload">
                                <tra-md *ngIf="!getIsNeedsReuploadScorLeadFlagged(selectedQIndex)"  slug="pj_checkbox_scan_needs_reupload"></tra-md>
                                <tra-md *ngIf="getIsNeedsReuploadScorLeadFlagged(selectedQIndex)" slug="pj_checkbox_scan_needs_reupload_scor_lead"></tra-md>
                              </label> 
                            </div>
                            <div style="display: flex; justify-content: center; margin-top: 1.0em">
                                <button 
                                  class="button tooltip" 
                                  [class.is-light]="!showCrop" 
                                  [class.is-dark]="showCrop" 
                                  (click)="toggleCrop()" 
                                  style="border-radius: 20em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))"
                                >
                                  <i 
                                    class="fa fa-crop" 
                                    [class.is-selected]="showCrop" 
                                    aria-hidden="true">
                                  </i>
                                  <span class="tooltiptext">
                                    <tra slug="txt_crop_pj"></tra>
                                  </span>
                                </button>

                                <button *ngIf="numCurrResponses() > 1" class="button is-danger" style="margin-right:1em; border-radius: 20em; width: 2.5em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5)) " [tooltip]="lang.tra('ie_discard')" offset="12" placement="left" theme="light" animationDuration="50" hideDelay="0">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <button 
                                  (click)="confirmResponse(selectedQIndex)" 
                                  [disabled]="numCurrResponses() > 1 || getNeedsReupload(selectedQIndex)" 
                                  class="button confirm-btn-pj-scan" 
                                  style="margin-right:0; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">
                                    <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; width: 100%; color:#000;">
                                      <i class="fa fa-check-circle"></i>&nbsp;<tra slug="btn_confirm"></tra>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div *ngIf="isConfirmed(selectedQIndex)" style="display: flex; justify-content: center; margin-top: 1.0em">
                            <button 
                              class="button tooltip" 
                              [class.is-light]="!showCrop" 
                              [class.is-dark]="showCrop" 
                              (click)="toggleCrop()" 
                              style="border-radius: 20em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">
                                <i class="fa fa-crop" [class.is-selected]="showCrop" aria-hidden="true"></i>
                                <span class="tooltiptext"><tra slug="txt_crop_pj"></tra></span>
                            </button>
                            <button 
                              class="button is-success" 
                              [disabled]="true" 
                              style="margin-right:0; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">
                                <div style="text-align: center; width: 100%">
                                  <tra slug="btn_confirmed"></tra>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="
                !currentCrQuestion.isPaperFormat 
                && currentCrQuestion.hasTaqr" 
                class="accessed_online">
                <p><tra-md slug="pj_open_online_response_note"></tra-md></p>
                   <!-- <b><tra slug="pj_accessed_online_txt"></tra></b>   -->
            </div>
        </ng-container>

        <ng-template #allSessions>
            <div *ngFor="let session of getAllQuestions(); trackBy: trackBySessionType" style="display: flex; flex-direction: column; justify-content: center; width: 100%; padding: 1em;">
                <div>
                    <h4>{{mapSessionSlug(session.sessionType)}}</h4>
                </div>
                <div *ngIf="session.sessionData && session.sessionData.responses && session.isPaperFormat" style="display: flex; flex-direction: column; align-items: center; width: 100%; border-radius: 0.5em; background-color: rgba(0,0,0,0.2); box-shadow: inset 0 0 0.5em rgba(0,0,0,0.25);" [style.height]="session.sessionData.responses.length == 0 ? '30rem' : null">
                    <div *ngIf="session.sessionData?.responses?.length > 1" style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100%; background-color: rgba(0,0,0,0.2); border-top-left-radius: 0.5em; border-top-right-radius: 0.5em; padding: 0.5em;">
                        <button *ngFor="let response of session.sessionData.responses; let i = index;" class="button response-button"  style="padding: 0.4em">
                            <i 
                              class="far" 
                              [class.fa-file-pdf]="response.responseType === EResponseType.PAPER" 
                              [class.fa-file]="response.responseType === EResponseType.ONLINE" 
                              style="font-size: 1.75em">
                            </i>
                        </button>
                    </div>
                    <div style="width: 100%; padding: 1.0em; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                        <div *ngFor="let response of session.sessionData?.responses" style="display: flex; flex-direction: row; align-items: center; justify-content: center; width: 100%">
                            <div>
                                <div style="width: 100%; display: flex; justify-content: center; background-color: white; padding: 1.0em; box-shadow: 0 0 0.25em 0.25em rgba(0,0,0,0.2); height: 32em;">
                                    <question-runner 
                                        *ngIf="!session.isPaperFormat"
                                        [currentQuestion]="response.question"
                                        [isSubmitted]="true"
                                        [questionState]="response.info"
                                    ></question-runner>
                                    <img *ngIf="!!session.isPaperFormat" [src]="!showCrop ? response.info.url : response.info.url_cropped" style="height: 100%; object-position: 0% -80% !important;">
                                </div>
                            </div>
                        </div>
                        <div 
                          *ngIf="!session.sessionData.isConfirmed && session.sessionData.responses.length > 0" 
                          style="margin-top: 1.0em;">
                            <div style="display: flex; margin: auto; width: 69%; color: #3a3a3a; align-items: flex-start;"> 
                              <input 
                                #setIsNotLegible 
                                type="checkbox" 
                                [checked]="getIsNotLegible(selectedQIndex)" 
                                (change)="onSetIsNotLegible(selectedQIndex, setIsNotLegible.checked, $event)" 
                                style="margin-right: 0.2rem; margin-top: 0.25rem;"
                              />
                              <tra-md slug="pj_checkbox_not_legible_text"></tra-md>
                            </div>
                            <div style="display: flex; margin: auto; width: 69%; color: #000; align-items: flex-start;">
                              <input 
                                #setNeedsReupload 
                                id="setNeedsReupload"
                                type="checkbox" 
                                [checked]="getNeedsReupload(selectedQIndex)" 
                                [disabled]="getIsNeedsReuploadScorLeadFlagged(selectedQIndex)"
                                (change)="onSetIsNeedsReupload(selectedQIndex, setNeedsReupload.checked, $event)" 
                                style="margin-right: 0.2rem; margin-top: 0.25rem;"
                              />
                              <label for="setNeedsReupload">
                                <tra-md *ngIf="!getIsNeedsReuploadScorLeadFlagged(selectedQIndex)"  slug="pj_checkbox_scan_needs_reupload"></tra-md>
                                <tra-md *ngIf="getIsNeedsReuploadScorLeadFlagged(selectedQIndex)" slug="pj_checkbox_scan_needs_reupload_scor_lead"></tra-md>
                              </label> 
                            </div>
                            <div style="display: flex; justify-content: center; margin-top: 1.0em">
                              <button 
                                class="button tooltip" 
                                [class.is-light]="!showCrop" 
                                [class.is-dark]="showCrop" 
                                (click)="toggleCrop()" 
                                style="border-radius: 20em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))"
                              >
                                <i 
                                  class="fa fa-crop" 
                                  [class.is-selected]="showCrop" 
                                  aria-hidden="true">
                                </i>
                                <span class="tooltiptext">
                                  <tra slug="txt_crop_pj"></tra>
                                </span>
                              </button>
                              <button 
                                (click)="confirmResponse(selectedQIndex)" 
                                [disabled]="getNeedsReupload(selectedQIndex) || numCurrResponses() > 1" 
                                class="button confirm-btn-pj-scan" 
                                style="margin-right:0; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))"
                              >
                                <div style="display: flex; flex-direction: row; justify-content: space-between; align-items: center; width: 100%; color:#000;">
                                  <i class="fa fa-check-circle"></i>&nbsp;<tra slug="btn_confirm"></tra>
                                </div>

                              </button>
                            </div>
                        </div>
                        <div *ngIf="session.sessionData.isConfirmed" style="display: flex; justify-content: center; margin-top: 1.0em">
                            <button *ngIf="session.sessionData?.responses.length > 0" class="button tooltip" [class.is-light]="!showCrop" [class.is-dark]="showCrop" (click)="toggleCrop()" style="border-radius: 20em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">
                                <i class="fa fa-crop" [class.is-selected]="showCrop" aria-hidden="true"></i>
                                <span class="tooltiptext"><tra slug="txt_crop_pj"></tra></span>
                            </button>
                            <div style="display: flex; flex-direction: column;">
                                <div *ngIf="session.sessionData?.responses.length == 0" style="margin-top: 2rem; text-align: center;">
                                    <div style="display: flex; margin-left: auto; margin-right: auto; color: #3a3a3a; align-items: flex-start;">
                                        <input 
                                          #setIsNoPaperResponse type="checkbox" 
                                          [checked]="getIsNotPaperResponse(selectedQIndex)" 
                                          [disabled]="true" 
                                          style="margin-right: 0.2rem; margin-top: 0.25rem;"/>
                                        <span><b><tra slug="pj_checkbox_no_paper_response_text"></tra></b></span>
                                    </div>
                                </div>
                                <div [class.confirmed_btn] = "session.sessionData?.responses.length == 0">
                                    <button   
                                      class="button is-success" 
                                      [disabled]="true" 
                                      style="margin-right:0; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">
                                        <div style="text-align: center; width: 100%"><tra slug="btn_confirmed"></tra></div>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="session.sessionData.responses.length == 0 && !!session.hasTaqr && !session.sessionData.isConfirmed" style="margin-top: 3rem; text-align: center;">
                            <p style="text-align: justify;"><tra-md slug="pj_open_paper_response_note"></tra-md></p>
                            <div style="display: flex; margin-left: auto; margin-right: auto; color: #3a3a3a;; align-items: flex-start;">
                                <input 
                                #setIsNoPaperResponse 
                                type="checkbox" 
                                [disabled]="isConfirmed(selectedQIndex)" 
                                [checked]="getIsNotPaperResponse(selectedQIndex)" 
                                (change)="onSetIsNoPaperResponse(selectedQIndex, setIsNoPaperResponse.checked)" 
                                style="margin-right: 0.2rem; margin-top: 0.25rem;"/>
                                <span><b><tra slug="pj_checkbox_no_paper_response_text"></tra></b></span>
                            </div>
                            <button 
                              class="button confirm-btn-pj-scan" 
                              (click)="confirmResponse(selectedQIndex)"
                              [disabled]="!getIsNotPaperResponse(selectedQIndex) || getNeedsReupload(selectedQIndex)" 
                              style="margin-top: 1rem; border-radius: 20em; width: 7.0em; height: 2.5em; filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5))">                        
                                <div style="text-align: center; width: 100%"><tra slug="btn_confirm"></tra></div>
                            </button>
                        </div>
                    </div>
                </div>
  <!-- -----------------------------ONLINE RESPONSE show access_online div-------------------------  -->
                <div *ngIf="!session.isPaperFormat">
                    <!-- If Student response => show div -->
                    <div 
                      *ngIf="!!session.hasTaqr" 
                      class="accessed_online">
                      <p><tra-md slug="pj_open_online_response_note"></tra-md></p>
                      <!-- <b><tra slug="pj_accessed_online_txt"></tra></b> -->
                    </div>

                    <!-- If Student has not response => hide div -->
                    <div 
                      *ngIf="!session.hasTaqr" 
                      class="accessed_online" 
                      style="width: 100%; height: 30rem; border-radius: 0.5em; background-color: rgba(0,0,0,0.2); box-shadow: inset 0 0 0.5em rgba(0,0,0,0.25);">
                    </div>
                </div>      
            </div>
        </ng-template>
        <textarea *ngIf="false" class="textarea" [placeholder]="lang.tra('scan_teacher_notes')" rows="2" style="margin-top: 0.5em"></textarea>
    </div>
</div>
<div class="ok-footer">
    <button class="button is-info" (click)="confirmSubmission()">
        <tra slug="btn_close_scan_modal"></tra>
    </button>
</div>

