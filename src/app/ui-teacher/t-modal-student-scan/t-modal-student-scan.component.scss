.name-header-box {
    position: relative; 
    display: flex; 
    flex-direction: column; 
    align-items: center; 
    padding-left: 1.3em;
    padding-right: 1.3em; 
    border-radius: 1em;
    background-color: #B7DAFA; 
    width: 100%; 
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.05);
    transition: 0.1s ease;
    border: black solid 2px;

    &.show-accomm-bar {
        &.is-hovered {
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.07);
        }
    }

    .student_display-name{
      padding: 0.5rem 0 0.5rem 0;
      margin-bottom: 0.5rem;
    }

    .accessibility-icon {
        cursor: pointer;
        color: blue;
        &:hover {
            filter: brightness(1.1);
        }
    }

    .bottom-highlight {
        position: absolute; 
        border-radius: 1.0em;
        transition: .1s ease; 
        &.is-highlighted {
            background: linear-gradient(transparent 0 85%, rgba(255, 255, 0, 0.12) 97% 100%); 
        } 
        &.is-hovered {
            filter:brightness(3);
        }
        &.is-neutral {
            background: linear-gradient(transparent 0 85%, rgba(30, 30, 30, 0.05) 97% 100%); 
        }
        pointer-events: none; 
        width: 100%; 
        height: 100%;
    }

    .is-ease {
        transition: .05s ease;
    }

    .bottom-header-bar {
        display: flex; 
        flex-direction: row; 
        justify-content: center; 
        align-items: center; 
        width: 100%; 
        background: transparent; 
        border-radius: 1.0em; 
        z-index: 1;
        height: 1.3em;

        &.show-accomm-bar {
            cursor: pointer;
            &:hover {
                filter: brightness(10);
            }
        }
    }

    .student-nav-button {
        border-radius: 50em;
        background-color: transparent;
        border: none;
        width: 2em;
        height: 2em;
        transition: .25s ease;
        margin-right: 0;
        font-size: 1.15em;
        &:hover {
            background-color: rgba(255,255,255,0.3)
        }
    
        &.is-disabled {
            pointer-events: none;
        }
    }
}


.response-nav-button {
    border-radius: 50em;
    background-color: transparent;
    border: none;
    width: 2.5em;
    height: 2.5em;
    transition: .25s ease;
    transition-property: background-color, opacity;
    margin-right: 0;
    font-size: 1.15em;
    background-color: rgb(230, 230, 230);
    filter: drop-shadow(0px 0px 0.2em rgba(0,0,0,0.5));

    &:hover {
        background-color: rgb(255,255,255)
    }

    &.is-disabled {
        pointer-events: none;
    }
}


.sub-grouping {
    display: flex; 
    flex-direction: row; 
    align-items: center; 
    justify-content: center; 
    background-color: #eeeeee; 
    border-radius: 0.5em; 
    margin-top: 0.5em; 
    padding: 0.5em;
}

.fa-pencil-alt {
    color: orange;
}

.select.is-disabled {
    pointer-events: none;
    &::after {
        border-color: #7a7a7a;
    }
}

.response-button {
    &.is-transparent {
        opacity: 0.65;
        &:hover {
            opacity: 0.75;
        }
    }
    transition: .25s ease;
}
.cr-question-button {
    border-radius: 10em;
    transition: .25s ease;

    .question-circle {
        font-size: 1em;
        margin-right: 0.6em;
        margin-left: -0.25em;
        background-color:white;
        border-radius :20em;
        width: 0.844em;
        height: 0.844em;
        position: relative;

        .question-circle-icon {
            position: absolute;
            top: -0.05em;
            left: -0.1em;

            &.fa-circle {
                color: white;
            }
            
            &.fa-dot-circle {
                color: rgb(255, 174, 43);
            }
            &.fa-check-circle {
                color: #14ad27
            }
            
        }

    }

    &.is-not-selected {
        background-color: white;
        &:hover {
            background-color: rgb(215, 215, 215);
        }
    }

    &.is-selected {
        box-shadow: inset 0 0 0.25em rgba(0,0,0,0.2);
        background-color: rgb(215, 215, 215);
        border: 1px solid rgba(0,0,0,0.5)
    }
    .question-circle-text {
        display: flex;
        flex-flow: column wrap;
        font-size: small;
        .question-session-slug {
            font-size: small;
            font-weight: bold;
        }
        .question-response-format {
            font-size: small;
            text-transform: capitalize;
        }
    }
}

.ok-footer {
    margin-top: 1em;
    float: right;
    position: sticky;
    bottom: 0;
}

.fa.fa-crop{
    font-size: 1.2em;
    color: black;
    opacity: 0.5;
    &.is-selected {
        color: white;
        opacity: 0.8;    
    }
}

.is-orange-print-btn {
    &:hover {
        border: solid 1px rgb(166, 166, 166);
    }
}

.tooltip {
    position: relative;

    .tooltiptext {
        display: none !important;
        visibility: hidden;
        height: -webkit-fit-content;
        height: -moz-fit-content;
        height: fit-content;
        width: 7rem;
        word-break: normal;
        word-wrap: break-word;
        background-color: #fff;
        color: #000;
        text-align: center;
        padding: 1px;
        border-radius: 6px;
        border: 1px solid #656565;
        position: absolute;
        top: -2px;
        right: 105%;
        font-size: 0.75rem;
    }

    &:hover .tooltiptext {
        visibility: visible;
        display: inline-block !important;
        z-index: 999;
        white-space: initial !important;
    }
}

.accessed_online {
    display: flex;
    flex-direction: column;
    justify-content: center;
    p {
        padding-top: 2rem;
        text-align: justify;
    }
}

.confirmed_btn {
    text-align: center;
    margin-top: 2rem;
}

.fa-circle{
  border: 1px black solid;
  border-radius: 50%;
}

.expand-scan-btn{
  height: 1.5rem;
  width: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.confirm-btn-pj-scan{
  background-color:  #ACE4C1 ;
  width: 8rem !important;
}

.upload-btn-scan-pj{
 background-color: #fad3b1;
 
  &:hover {
    background-color: #fccfa7;
  }
}