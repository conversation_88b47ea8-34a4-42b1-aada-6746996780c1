import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { ClassroomsService } from 'src/app/core/classrooms.service';
import { LangService } from 'src/app/core/lang.service';
import { IStudentAccount } from 'src/app/ui-schooladmin/data/types';
import { G9DemoDataService } from 'src/app/ui-schooladmin/g9-demo-data.service';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
import { ScanInfoService } from '../scan-info.service';
export interface IGenRespSheetConfig {
  testSessionId: number,
  classroomId: number,
  schl_class_group_id?: number,
  students?: { uid: string | number, slugs: string[] }[],
  asmtSlug: string,
  isIndividual?: boolean,
  isSasn?: number
}

const UPLOAD_FILE_SIZE_LIMIT = 8000000;

@Component({
  selector: 'scan-button-bar',
  templateUrl: './scan-button-bar.component.html',
  styleUrls: ['./scan-button-bar.component.scss']
})
export class ScanButtonBarComponent implements OnInit {

  @Input() testSessionId: number;
  @Input() classroomId: number;
  @Input() asmtSlug: string;
  @Input() studentList: IStudentAccount[];
  @Input() currentQueryParams;
  @ViewChild('singleUploadInput', { static: false }) singleUploadInput: ElementRef<HTMLInputElement>;

  @Output() reviewClicked = new EventEmitter();

  bulkResponseSheetLoader: any;
  constructor(
    private auth: AuthService,
    private g9DemoData: G9DemoDataService,
    public scanInfo: ScanInfoService,
    private onlineOrPaper: OnlineOrPaperService,
    public lang: LangService,
    private loginGuard: LoginGuardService, 
    private classroomService: ClassroomsService,
  ) { }
  @Output() showPrintStudentInfo = new EventEmitter();
  @Output() showPrintScanInstruction = new EventEmitter();
  @Input() invigilateView: boolean;
  @Input() isFIOptionC: boolean;
  @Input() isSasnLogin: boolean;
  @Input() isPrimary: boolean;
  @Input() isPrintingBulk: boolean;
  @Input() showResponseFormatInstruction: boolean = false;

  ngOnInit(): void { }

  async getAllStudentsResponseSheets() {
    const classroom = this.classroomService.getClassroomById(this.classroomId);
    const respSheetConfig: IGenRespSheetConfig = {
      testSessionId: +this.testSessionId,
      classroomId: +this.classroomId,
      schl_class_group_id: classroom.group_id,
      isIndividual: false,
      asmtSlug: this.asmtSlug,
      isSasn: this.classroomService.isSASNLogin(this.classroomId) ? 1 : 0
    }
    
    const paperStudentUids = this.onlineOrPaper.getPaperStudents();

    if(!paperStudentUids.length){
      this.loginGuard.quickPopup(this.lang.tra('no_stu_for_paper_resp'));
      return;
    }

    respSheetConfig.students = paperStudentUids.map(uid => {
      return {
        uid,
        slugs: this.scanInfo.getScanSlugs(uid)
      }
    })

    this.scanInfo.getClassResponseTemplate(respSheetConfig);
  }

  getUploadBtnText() {
    return this.lang.tra('upload_resp_sheets');
  }

  selectResponseSheetUpload() {
    this.singleUploadInput.nativeElement.value = '';
    this.singleUploadInput.nativeElement.click();
  }

  showStudentInfoDetails() {
    this.showPrintStudentInfo.emit();
  }

  /** Deprecated - replaced by kb link */
  showInstructionDetails() {
    this.showPrintScanInstruction.emit();
  }

  getBulkScanRouterLink(){
    return `/{{lang.c()}}/educator/assessment/${this.classroomId}/${this.testSessionId}/bulk-scan`
  }

  openScanInstrLink(){
    const linkUrl = this.lang.tra('scan_print_instr_kb_link');
    window.open(linkUrl, '_blank');
  }

}
