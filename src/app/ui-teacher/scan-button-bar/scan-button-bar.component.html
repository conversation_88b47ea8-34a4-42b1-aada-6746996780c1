
<div style="display: flex; flex-direction: row; align-items: center; width: 100%;">
    <div class="scan-button-div" style="display:flex; flex-direction:column; justify-content:space-around;">
      <!-- -----Print Class List with ODENs btn----- -->
      <button 
        class="button scan-button" 
        style="background-color: #B7DAFA; margin-left: 0em; height: 8rem; margin-top: 1rem; margin-right: 1.5rem" 
        (click)="showStudentInfoDetails()">
            <div class="scan-button-content" style="margin-left: 0.4em; margin-right: 0.4em; margin-top: 0; ">
                <tra-md *ngIf="!isSasnLogin" [isCondensed]="true" style="color: black;" slug="print_student_information"></tra-md>
                <tra-md *ngIf="isSasnLogin" [isCondensed]="true" style="color: black;" slug="print_student_information_SASN"></tra-md>
            </div>
        </button>
    </div>
    <!-- PJ display response format changing instruction -->
    <div *ngIf="showResponseFormatInstruction">
        <tra-md slug="pj_response_format_instruction"></tra-md>
    </div>
    <!-- Div that hold Instructions, Print, Upload, Confirm btns -->
    <div 
      *ngIf="invigilateView" 
      class="scan-button-div" 
      style="display:flex; flex-direction:column; justify-content:space-around">
        <div style="color:black">
          <b><tra slug="lbl_pj_lang_only"></tra></b>
        </div>
        <div class="scan-button-container" style="max-height: 73%;">
            <button class="button scan-button is-instruction" (click)="openScanInstrLink()" [disabled]="isFIOptionC && isPrimary">
              <div class="scan-button-content">
                    <tra-md
                      class="vertical-center-text" 
                      [isCondensed]="true" 
                      slug="scan_print_instr">
                    </tra-md>
                    <div class="button-inner-icon">
                        <i class="fa fa-exclamation-circle" aria-hidden="true"></i>
                    </div>
                </div>
            </button>
            <div class="arrow-divider">
                <i class="fa fa-arrow-right"></i>
            </div>
            <button class="button scan-button is-print-response" (click)="getAllStudentsResponseSheets()" [disabled]="(isFIOptionC && isPrimary) || isPrintingBulk">
                <div class="scan-button-content">
                    <tra-md class="vertial-center-text" [isCondensed]="true" slug="print_resp_sheets"></tra-md>
                    <div class="button-inner-icon">
                        <i class="fas fa-print" aria-hidden="true"></i>
                    </div>
                </div>
            </button>
            <div class="arrow-divider">
                <i class="fa fa-arrow-right"></i>
            </div>
            <button class="button scan-button is-upload-response" [disabled]="isFIOptionC && isPrimary" routerLink="bulk-scan" [queryParams]="currentQueryParams">
                <div class="scan-button-content">
                    <tra-md class="vertial-center-text" [isCondensed]="true" slug="upload_resp_sheets"></tra-md>
                    <div class="button-inner-icon">
                        <i class="fas fa-upload" aria-hidden="true"></i>
                    </div>
                </div>
            </button>
            <div class="arrow-divider">
                <i class="fa fa-arrow-right"></i>
            </div>
            <button (click)="reviewClicked.emit($event)" class="button scan-button is-confirm-response" [disabled]="isFIOptionC && isPrimary">
                <div class="scan-button-content">
                    <tra-md class="vertial-center-text" [isCondensed]="true" slug="review_resp_sheets"></tra-md>
                    <div class="button-inner-icon">
                        <i class="fa fa-check-circle" aria-hidden="true"></i>
                    </div>
                </div>
            </button>
        </div>
    </div>
</div>