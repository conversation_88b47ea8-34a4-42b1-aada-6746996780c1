<panel-sl-new-message *ngIf="showMessageCentreModal" [markingWindowId]="markingWindowId" [markingWindowGroupId]="mrkg_group_id" [prefillRecipients]="[getCurrentResponseScorer()]" (close)="showMessageCentreModal = false;"></panel-sl-new-message>



<div class="page-body">
  <div>
  
    <header 
      [breadcrumbPath]="breadcrumb" 
      [hasSidebar]="true"
      [hideMessageCentre]="true"
      techSupportDataKey="SCORING_SUPPORT"
    ></header>
        
    <div class="page-content is-fullpage" >
        <div class="page-container">
          <div class="content-container">
    
            <div style="display:flex; flex-direction:row; align-items: center; font-size:1em; margin-bottom:0.4em;" >
              Item: <div style="margin-left:1em;">
                  <select [(ngModel)]="focusedWindowItemId" (change)="selectItem()" [disabled]="isLoading">
                    <option [value]="-1">All</option>
                    <option *ngFor="let item of items" [value]="item.window_item_id">{{item.item_slug}} ({{item.item_caption}})</option>
                  </select>
              </div>
            </div>

            <div style="margin-bottom:1em;">
              <mat-slide-toggle  [(ngModel)]="isShowPreFilters" (ngModelChange)="onPreFilterToggleChange($event)">
                <span>Use pre-filters</span>
              </mat-slide-toggle>
              <div *ngIf="isShowPreFilters" >
                <p>Use pre-filters to narrow down the initial records returned and have them returned faster.</p>
                <p>Can only use <i>either</i> "Scored On" <i>or</i> "Scored After" + "Scored Before" filters at once.</p>
                <p>Up to {{ (limit ? limit : MAX_RESPONSE_LIMIT) | number:'1.0-0' }} most recent scorings will be returned at once. The maximum allowed limit is {{MAX_RESPONSE_LIMIT | number:'1.0-0' }}.</p>
                <table class="pre-filters">
                  <tbody>
                    <tr>
                      <td><b>Response ID</b></td>
                      <ng-container *ngIf="allowScoringFilters()">
                        <td><b>Scorer Profile ID</b></td>
                        <td><b>Scored On</b></td>
                        <td><b>Scored After</b></td>
                        <td><b>Scored Before</b></td>
                      </ng-container>
                    </tr>
                    <tr>
                      <td>
                        <input class="input is-small" type="number" [(ngModel)]="preFilters.responseId" placeholder="(number)">
                      </td>
                      <ng-container *ngIf="allowScoringFilters()">
                        <td>
                          <input class="input is-small" type="number" [(ngModel)]="preFilters.scorProfileId" placeholder="(number)">
                        </td>
                        <td>
                          <input class="input is-small" type="date" [(ngModel)]="preFilters.scoredOn" (ngModelChange)="onScoredOnDateChange($event)">
                        </td>
                        <td>
                          <input class="input is-small" type="datetime-local" [(ngModel)]="preFilters.scoredAfter" (ngModelChange)="onScoredBeforeAfterChange($event)">
                        </td>
                        <td>
                          <input class="input is-small" type="datetime-local" [(ngModel)]="preFilters.scoredBefore" (ngModelChange)="onScoredBeforeAfterChange($event)">
                        </td>
                      </ng-container>
                    </tr>
                  </tbody>
                </table>              
              </div>
            </div>

            <div style="margin-bottom:2em;">
              Include up to <input class="input is-small" placeholder="(number)" type="number" style="width: 6em; text-align: center;" [(ngModel)]="limit"> 
              most recent scorings 
              <!-- from before 
              <input type="checkbox" [(ngModel)]="isEndDateEnabled"> 
              <input [disabled]="!isEndDateEnabled" type="datetime" [(ngModel)]="endDate"> -->
              <button class="button is-warning" style="margin-left:2em;" [disabled]="!selectedView || isLoading" (click)="loadResponses()"> Refresh</button>
            </div>

            <div  *ngIf="!selectedView" class="notification is-warning">Select an item and a view below to load the responses.</div>
 
            <menu-bar 
              [menuTabs]="views"
              [tabIdInit]="selectedView"
              [isLocked]="isLoading"
              (change)="selectView($event)"
            ></menu-bar>

            <div *ngIf="isLoading" class="notification is-info">Loading...</div>

            <div *ngIf="selectedView!='reliability'"  style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
              <div>
                  You may export the filtered data below using the button on the right.
              </div>
              <div class="buttons">
                <button 
                  *ngIf="selectedView != ScorerMenuView.UPCOMING && selectedView != ScorerMenuView.RELIABILITY"
                  [disabled]="!getMcbrIdsToInvalidate().length"
                  class="button is-danger is-outlined" 
                  (click)="invalidateScoresManually()">
                  Invalidate Score(s)
                </button>
                <button class="button" (click)="exportCSV()">Export CSV</button>
                  <!-- <export-table-contents [tableContents]="sanitizedResponses" [filename]=getExportFilename()></export-table-contents> -->
              </div>
            </div>
  
            <!-- [ngSwitch]="selectedView" -->
            <div class="flex-container"  *ngIf="!isLoading">
              <div class="panel-content">
                <div  class="table-container" *ngIf="markingWindowId && isLoaded">
                  <ng-container [ngSwitch]="selectedView">
                    
                    <ng-container *ngSwitchCase="ScorerMenuView.SCORINGS">
                      <ag-grid-angular
                        style="min-width:36em; height: 100%;"
                        class="ag-theme-alpine"
                        [rowData]="scorings"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefs"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'single'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>

                    <ng-container *ngSwitchCase="ScorerMenuView.RELIABILITY">
                      <ag-grid-angular
                        style="width: auto; min-width:36em; height: 100%;"
                        class="ag-theme-alpine"
                        [rowData]="reliability"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefsReliability"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'multiple'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)"
                      >
                      </ag-grid-angular>
                    </ng-container>
                    
                    <ng-container *ngSwitchCase="ScorerMenuView.VALIDITY">
                      <ag-grid-angular
                        style="width: auto; min-width:36em; height: 100%;"
                        class="ag-theme-alpine"
                        [rowData]="validity"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefsValidity"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'single'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>

                    <ng-container *ngSwitchCase="ScorerMenuView.QUALIFYING">
                      <ag-grid-angular
                        style="width: auto; min-width:36em; height: 100%;"
                        class="ag-theme-alpine"
                        [rowData]="qualifying"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefsQualifying"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'single'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>
    
                    <ng-container *ngSwitchCase="ScorerMenuView.S2S">
                      <ag-grid-angular
                        style="width: auto; min-width:36em; height: 100%;"
                        class="ag-theme-alpine"
                        [rowData]="s2s"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefsS2s"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'single'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>
                    
                    <ng-container *ngSwitchCase="ScorerMenuView.CAS">
                      <p>
                        Sensitive Student Responses are only made available to the Scoring Leader with the designated CAS role.
                      </p>
                      <ag-grid-angular
                        class="ag-theme-alpine ag-grid-fullpage"
                        style="min-width:36em; "
                        [rowData]="cas"
                        [gridOptions]="gridOptions"
                        [defaultColDef]="defaultColDef"
                        [columnDefs]="columnDefsCas"
                        rowSelection="'single'"
                        [enableCellTextSelection]="true"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>

                    <ng-container *ngSwitchCase="ScorerMenuView.SCAN">
                      <ag-grid-angular
                        class="ag-theme-alpine ag-grid-fullpage"
                        style="min-width:36em; "
                        [rowData]="scan"
                        [gridOptions]="gridOptions"
                        [defaultColDef]="defaultColDef"
                        [columnDefs]="columnDefsScan"
                        rowSelection="'single'"
                        [enableCellTextSelection]="true"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>

                    <ng-container *ngSwitchCase="ScorerMenuView.UPCOMING">
                      <ag-grid-angular
                        style="width: auto; min-width:36em; height: 100%; max-height: 70vh;"
                        class="ag-theme-alpine"
                        [rowData]="responses"
                        [gridOptions]="gridOptions"
                        [columnDefs]="columnDefsUpcoming"
                        [defaultColDef]="defaultColDef"
                        [rowSelection]="'single'"
                        (gridReady)="onGridReady($event)"
                        (rowSelected)="onRowClick($event)">
                      </ag-grid-angular>
                    </ng-container>


                  </ng-container>

         
                </div>
              </div>
                
                <div class="flex-container" *ngIf="selectedView != 'reliability' && isLoaded && gridApi && getCurrentResponseId()">
                  <div class="panel-response-content">
                    <div style="padding:  0em 1em;" class="space-between">
                      <strong>{{currResp().item_caption}}</strong>
                      <div>Scoring ID: {{currResp().mcbr_id}}</div>
                      <div>
                        <span *ngIf="currResp().is_invalid == 'Yes'" class="tag is-danger">INVALID</span>
                        {{currResp().scorer_name}}
                      </div>
                    </div>
                    <panel-score-content
                      [rawResponseId]="getCurrentResponseId()"
                      [windowItemId]="getCurrentResponseItemId()"
                      responseType="TEXT"
                      [responseContent]="getCurrentResponseContent()"
                    ></panel-score-content>
                    
                  </div>
                  <div class="panel-score">
                    <div class="controls" *ngIf="selectedView != ScorerMenuView.UPCOMING" > 
                      <button (click)="markUninspected()" *ngIf="getIsCurrentResponseInspected()" class="inspect-button inspected">
                          <i class="fas fa-times"></i>
            
                          <div class="big">
                            Mark as Uninspected
                            <span *ngIf="selectedView == ScorerMenuView.RELIABILITY"><br>(both scores)</span>
                          </div>
                      </button>
                      <button (click)="markInspected()" *ngIf="!getIsCurrentResponseInspected()" class="inspect-button uninspected" >
                          <i class="fas fa-check"></i>
        
                          <div class="big"> 
                            Mark as Inspected
                            <span *ngIf="selectedView == ScorerMenuView.RELIABILITY"><br>(both scores)</span>
                          </div>
                        
                      </button>
                      
                      <button *ngIf="selectedView != ScorerMenuView.RELIABILITY" class="message-button" (click)="messageScorer()">
                        <i class="fas fa-envelope"></i>
                        
                        <div class="big">Message Scorer</div>
                      </button>
                    </div>
                    <div *ngIf="selectedView == ScorerMenuView.UPCOMING && currResp() ">
                      <div class="notification is-warning is-small" *ngIf="!currResp().mrs_id">
                        Not Yet Scored
                      </div>
                      <div class="notification is-success is-small" *ngIf="currResp().mrs_id">
                        Scored ({{currResp().mrs_id}})
                      </div>
                    </div>
                    <div class="score-assign-container">
                      <div>
                        
                      </div>
                      <div class="score-option-container" *ngIf="hasScoreOptions()">
                        <div 
                          *ngFor="let scoreOption of getScoreOptions()" 
                          [class.is-offset]="scoreOption.is_offset"
                          class="score-option"
                        >
                          <button 
                            class="button is-fullwidth" 
                            [class.is-info]="isScoreOptionSelected(scoreOption.id)"
                            (click)="_assignScore(scoreOption)"
                          >
                            <!-- {{currResp().score_option_id}} : {{scoreOption.id}} -->
                            <tra [slug]="_renderOptionCaption(scoreOption)"></tra>
                          </button>
                        </div>
                      </div>    
                      <div *ngIf="getCurrentResponseFlag() || getCurrentResponseMessage()" class="s2s-section">
                        <div *ngIf="getCurrentResponseFlag()" >Flag: <strong>{{getCurrentResponseFlag()}}</strong></div>
                        <div *ngIf="getCurrentResponseMessage()" style="overflow-wrap: anywhere;">Message: <strong>{{getCurrentResponseMessage()}}</strong></div>
                      </div>

                      <div *ngIf="isSenstiveResponseView()" style="margin-top:1em;">
                        <button (click)="unmarkAsSensitive()" class="button is-light" >
                          Not sensitive - flag S2S
                        </button>
                      </div>

                      <div *ngIf="!isSenstiveResponseView() && !isScanIssuesView()" style="margin-top:1em;">
                        <button (click)="markAsSensitive()" class="button is-light" >
                          Flag as Sensitive
                        </button>
                        <button (click)="markAsSII()" class="button is-light" >
                          Flag as Scan Issue
                        </button>
                      </div>
                      <div style="margin-top: 1rem;">
                        <button *ngIf="showReportIssueSection(selectedView)"
                                class="button is-danger is-outlined" 
                                (click)="toggleInitReportIssue()"
                        >Report New Issue</button>
                        <div *ngIf="reportIssueExtend" style="margin-top: 0.5rem;">
                          <div *ngIf="selectedView === ScorerMenuView.SCAN">
                            <label><b><tra slug="lbl_report_issue_category"></tra></b></label>
                            <div style="margin-top: 0.8rem; margin-bottom: 1rem;">
                                <select [ngModel]="categorySelection" (ngModelChange)="categorySelectionChange($event)" style="width: 100%; min-width: 1em;">
                                    <option [value]="null"></option>
                                    <option *ngFor="let category of scanIssueCategoriesList" [value]="category.id">{{ category.caption }}</option>
                                </select>
                            </div>
                          </div>
                          <form>
                            <textarea [(ngModel)]="reportIssueComment" class="textarea is-small"></textarea>
                          
                            <div class="buttons" style="margin-top: 0.5rem;">
                                <button [disabled]="!this.reportIssueComment" (click)="createReportedIssue()" class="button is-small">Send</button>
                                <button (click)="cancelReportedIssue()" class="button is-danger is-inverted is-small">Cancel</button>
                            </div>
                          </form>
                        </div>
                      </div>
                      <div class="score-header" *ngIf="showReportIssueSection(selectedView)"> 
                        <div class="scores-list" style="font-weight:500;font-size:1.5em;margin-top:1em;margin-bottom:0.5em;">
                          Reported Issues Log:
                        </div>
                        <ul style="list-style: none; margin: 0;">
                          <li *ngFor="let issue of reportedIssueHistory; let i = index">
                            <i>{{formatTimestamp(issue.created_on)}}</i>: {{issue.first_name}} {{issue.last_name}} 
                          </li>
                        </ul>
                      </div>  
                      <div class="score-header" *ngIf="selectedView != ScorerMenuView.UPCOMING"> 

                        <div class="scores-list" style="font-weight:500;font-size:1.5em;margin-top:1em;margin-bottom:0.5em;">
                          Scorer scores:
                        </div>
                        <ul style="list-style: none; margin: 0;">
                          <li *ngFor="let score of scorerScores; let i = index">
                            <i>{{formatTimestamp(score.last_touched_on)}}</i>: {{score.first_name}} {{score.last_name}} scored <strong>{{score.value}}{{score.slug? '(' + lang.tra(score.slug) + ')': ''}}</strong>
                            <span *ngIf="score.is_invalid"> - invalid</span>
                            <span *ngIf="selectedView != ScorerMenuView.RELIABILITY && scorerScores.length > 1 && currResp().mcbr_id == score.batch_response_id"><b> (This score)</b></span>
                          </li>
                        </ul>

                        <div class="scores-list" style="font-weight:500;font-size:1.5em;margin-top:1em;margin-bottom:0.5em;">
                          Leader / RF Score Log:
                        </div>
                        <ul style="list-style: none; margin: 0;">
                         <li *ngFor="let log of leaderOrRfScoreLogs; let i = index">

                          <ng-container [ngSwitch]="log.type">
                            <ng-container *ngSwitchCase="leaderOrRfScoreLogType.SCORE_CHANGE">
                              <i>{{formatTimestamp(log.created_on)}}</i>: {{log.first_name}} {{log.last_name}} scored <strong>{{log.value}}{{log.slug? '(' + lang.tra(log.slug) + ')': ''}}</strong> {{log.is_expert_score ? '(EXPERT SCORE)' : ''}} {{log.is_issue_reviewer ? ' - from Issue Reviewer' : ''}}
                            </ng-container>
                            <ng-container *ngSwitchCase="leaderOrRfScoreLogType.IS_EXPERT_FLAG_CHANGE">
                              <i>{{formatTimestamp(log.created_on)}}</i>: {{log.first_name}} {{log.last_name}} {{log.is_expert_score ? 'marked' : 'unmarked'}} as EXPERT SCORE
                            </ng-container>
                          </ng-container>
                                                    
                        </li>
                        </ul>
                      </div>  
                    </div>
                  </div>
                </div>
              </div>
            


          </div>
        </div>
      </div>
    </div>
    <footer [hasLinks]="false" techSupportDataKey="SCORING_SUPPORT"></footer>
</div>
  

<div class="custom-modal" *ngIf="cModal()">
  <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
    <div *ngSwitchCase="SlResponsesModal.SCORE_INVALIDATE">
      <p class="has-text-danger">{{cmc().mcbrIds.length}} records will be invalidated.</p>
      <p><tra-md slug="scor_lead_manual_invalidate_note"></tra-md></p>
      <div><b>Provide an invalidation note:</b></div>
      <div>
        <input [(ngModel)]="cmc().invalidationNote" type="text" class="input" placeholder="Enter a note..." maxlength="150">
      </div>
      <modal-footer [pageModal]="pageModal" [isConfirmAlert]="true" [confirmationMessage]="'scor_lead_manual_invalidate_confirm'" [isEditDisable]="!cmc().invalidationNote"></modal-footer>
    </div>
  </div>
</div>