@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/pseudo-objects/dashboard-view-summary.scss';
@import '../../../styles/partials/_modal.scss';

.custom-modal { 
  @extend %custom-modal;
}

.page-body {
    @extend %page-body;
    background-color:$off-white;
}


.page-content{
    display:flex;
    .page-container{
        flex-grow:1;
        .content-container{
            height:100%;
            div.flex-container{
                height: 80vh;
                display:flex;
                .panel-content, .panel-score, .panel-response-content, .flex-container{
                    height:100%;
                    overflow: auto;
                }
                .panel-content {
                    flex-basis: 20%;
                    flex-grow: 1;
                    .table-container{
                        height:100%;
                    }
                }
                div.flex-container {
                    display: flex;
                    flex-grow: 10;
                    .panel-response-content {
                        flex-grow: 2;
                        max-width: 50vw;
                    }
                    .panel-score{
                        flex-grow: 1;
                        max-width: 21vw;
                    }
                }

            }
        }
    }
}


.controls{
    display:flex;
    flex-direction: row;;
    .message-button{
        // width:40%;
        cursor:pointer;
        font-weight:bold;
        display:flex;
        justify-content: center;
        border:0;
        border-radius:4px;
        padding: 0.5em 1em;
        margin:5px 0;
        background:white;
        i{
            font-size: 1.5em;
            margin-right:1em;
        }
        .small{
            font-size:0.8em;
        }
        .big{
            font-size:1.1em;
        }
       
    }
    .inspect-button {
        width:calc(60% - 0.5em);
        
        cursor:pointer;
        font-weight:bold;
        display:flex;
        justify-content: center;
        border:0;
        border-radius:4px;
        padding: 0.5em 1em;
        margin:5px 0;
        margin-right:0.5em;
        i{
            font-size: 1.5em;
            margin-right:1em;
        }
        .small{
            font-size:0.8em;
        }
        .big{
            font-size:1.1em;
        }
        &.uninspected{
            background:#9874ff;
            color:white;
        }
        &.inspected {
            color:#9874ff;
            background:white;
            border:2px solid #9874ff;
    
        }
    }
}


.s2s-section {
    margin-top:1em;
    background:#fddcdc;
    padding:0.5em;
}

.pre-filters {
  width: 75em;
  tbody {
    tr {
      td {
        input {
          max-width: 15em;
        }
        border: none;
      }
    }
  }
}