import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/app/api/auth.service';
import { UserRoles } from 'src/app/api/models/roles';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AccountType } from 'src/app/constants/account-types';
import { BreadcrumbsService, IBreadcrumbRoute } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { ScrollService } from 'src/app/core/scroll.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { MyCtrlOrgService } from '../my-ctrl-org.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { userGroupSummaryColumns, userGroupTypes } from '../view-tc-dashboard/types/tc-summary';
import { FormControl, FormGroup } from '@angular/forms';

export interface ICtrlRole {
  uid: number,
  ur_id: number,
  first_name: string,
  last_name: string,
  contact_email: string,
  role_type: string,
  created_on: string | Date | null,
  is_revoked: string | boolean | number,
  revoked_on: string | Date | null
}

export interface IAuthorityClassification {
  AuthorityClassification?: IClassificationType[]
}

export interface IClassificationType {
  ClassificationType?: string,
}

interface ICtrlRoleType{
  name: string,
  value: string
  group_ids?: number[]
}

@Component({
  selector: 'view-tc-roles',
  templateUrl: './view-tc-roles.component.html',
  styleUrls: ['./view-tc-roles.component.scss']
})
export class ViewTcRolesComponent implements OnInit {
  
  public userGroupSummaryColumns = userGroupSummaryColumns;
  public userGroupTypes = userGroupTypes;
  public routeSub: Subscription;
  public breadcrumb: IBreadcrumbRoute[];
  public ctrlRoles: ICtrlRole[];
  
  public ctrlRoleTypes:ICtrlRoleType[] = [
    {name: "Translator", value: UserRoles.TRANSLATOR, group_ids: [1]},
    {name: "Support", value: UserRoles.DEBUG, group_ids: [1]},
    {name: "Cron", value: UserRoles.CRON, group_ids: [1]},
    {name: "Alt Version Req Controller", value: UserRoles.ALT_VERSION_REQ_CONTROLLER, group_ids: [378562]},
    {name: "Alt Version Req Shipping Vender", value: UserRoles.ALT_VERSION_REQ_SHIPPING_VENDOR, group_ids: [379231]},
    {name: "Issue Reviewer", value: UserRoles.TEST_CTRL_ISSUE_REVW, group_ids: [24, 5403]},
    {name: "Issue Reviewer (Exemption Override)", value: UserRoles.TEST_CTRL_ISSUE_REVW_EXEMPT_OVR},
    {name: "Payment Controller", value: UserRoles.PAYMENT_CTRL, group_ids: [344772]},
    {name: "Test_Controller (Data Retriever)", value: UserRoles.TEST_CTRL_DATA_RETR, group_ids: [24, 5403]},
    {name: "Test_Controller (Data Exporter)", value: UserRoles.TEST_CTRL_DATA_EXPORTER, group_ids: [24, 5403]},
    {name: "Test_Controller (Internal Liaison)", value: UserRoles.TEST_CTRL_LIAS_INTERNAL, group_ids: [24]},
    {name: "Test_Controller (Test Administrator Liaison)", value: UserRoles.TEST_CTRL_LIAS_TEST_ADMIN, group_ids: [24]},
    {name: "Test_Controller (Meta Regulator)", value: UserRoles.TEST_CTRL_META_REG, group_ids: [24, 5403]},
    {name: "Test_Controller (Notification Center Controller)", value: UserRoles.TEST_CTRL_NTF_CTRL, group_ids: [24, 5403]},
    {name: "Test_Controller (MFA Controller)", value: UserRoles.TEST_CTRL_MFA_CTRL, group_ids: [24, 5403]},
    {name: "Test_Controller (Window Monitor)", value: UserRoles.TEST_CTRL_WINDOW_MONITOR, group_ids: [24, 5403]},
    {name: "Test_Controller (Auto Submit Controller)", value: UserRoles.TEST_CTRL_AUTO_SUBMIT_CTRL, group_ids: [24, 5403]},
    {name: "Control_System_Monitor (Recieve Daily Report Email)", value: UserRoles.CTRL_SYSTEM_MONITOR, group_ids: [1]},
    {name: "MFA Exempted User", value: UserRoles.MFA_EXEMPT, group_ids: [1]},
  ]
    
  districts = 'districts';
  public ctrlRolesTable: MemDataPaginated<ICtrlRole>;
  pageModal: PageModalController;
  formGroup = new FormGroup({
    form_email: new FormControl(),
    form_uid: new FormControl(),
    form_role_type: new FormControl(),
  })
  isLoaded: boolean = false;
  constructor(private loginGuard: LoginGuardService, // 
    private router: Router,
    private route: ActivatedRoute,
    private breadcrumbsService: BreadcrumbsService,
    private scrollService: ScrollService,
    private lang: LangService,
    private auth: AuthService,
    private pageModalService: PageModalService,
    private routes: RoutesService,
    private myCtrlOrg: MyCtrlOrgService,
    private whitelabel: WhitelabelService,) { }
    private ctrlRoleTypeMap: Map<string, string> = new Map();
  ngOnInit(): void {
    this.scrollService.scrollToTop();
    this.loginGuard.activate([AccountType.TEST_CTRL]);
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.routeSub = this.route.params.subscribe(routeParams => {
      // this.setupId = routeParams['setupId'];
      this.breadcrumb = [
        this.breadcrumbsService.TESTCTRL_DASHBOARD(),
        this.breadcrumbsService._CURRENT('Test_Controller Roles', this.router.url),
      ]
      this.loadControllerRoles()
    });
    this.ctrlRoleTypes.map(
      ctrlRoleType => {
        this.ctrlRoleTypeMap.set(ctrlRoleType.value, ctrlRoleType.name)
      }
    )
    console.log('init',this.ctrlRolesTable )
  }

  loadControllerRoles(isUpdating = false) {
    this.auth.apiGet(this.routes.TEST_CTRL_ROLES, 0).then(res => {
      this.ctrlRoles = res[0].records;
      //processing the classification, turning it from an object of array to a string of types seperated by comas.
      for(let ctrlRole of this.ctrlRoles){
        ctrlRole.role_type = this.ctrlRoleTypeMap.get(ctrlRole.role_type)
        ctrlRole.created_on = ctrlRole.created_on? new Date(ctrlRole.created_on) : null
        ctrlRole.revoked_on = ctrlRole.revoked_on? new Date(ctrlRole.revoked_on) : null
        ctrlRole.is_revoked = ctrlRole.is_revoked == 1? "True":"False"
      }
      if(isUpdating){
        this.ctrlRolesTable.injestNewData(this.ctrlRoles)
      }else{
        this.ctrlRolesTable = new MemDataPaginated({
          data: this.ctrlRoles,
          pageSize: 10,
          sortSettings: {}
        });
      }
      this.isLoaded = true;
    })
  }
  formateDate(date : Date | null){
    if(!date){
      return "";
    }
    const readableDate = date.toLocaleDateString('en-US', {
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
    
    const readableTime = date.toLocaleTimeString('en-US', {
      hour: '2-digit', 
      minute: '2-digit', 
      hour12: true
    });
    return `${readableDate}, ${readableTime}`
  }
  cModal() {
    return this.pageModal.getCurrentModal();
  }

  cmc() { return this.cModal().config; }
  newCtrlRoleModalStart() {
    const config: any = {};
    this.pageModal.newModal({
      type: 'ctrl_role',
      config,
      finish: config => this.newCtrlRoleModalFinish(config)
    });
  }
  newCtrlRoleModalFinish(config) {
    const controls = this.formGroup.controls;

     // Find the matching role_type entry in ctrlRoleTypes
     const selectedRole = this.ctrlRoleTypes.find(role => role.value === controls.form_role_type.value);

    const payload = {
      email: controls.form_email.value,
      target_uid: controls.form_uid?.value,
      role_type:controls.form_role_type.value||'',
      group_ids: selectedRole?.group_ids ? selectedRole?.group_ids : [this.whitelabel.getSiteText("tc_group_id")]
    }
    if(!payload.group_ids){
      this.loginGuard.quickPopup("Group Id is missing.")
    }
    else{
      this.auth.apiCreate(this.routes.TEST_CTRL_ROLES, payload).then(res => {
        this.resetForm();
        this.loginGuard.quickPopup("Created role successfully.")
        this.loadControllerRoles(true);
      }).catch(e => {
          if(e.message){
            this.loginGuard.quickPopup(e.message)
          }
          else{
            this.loginGuard.quickPopup("Failed to create new user role.")
          }
        }
      )
    }
  }
  revokeRole(ur_id: number){
    this.loginGuard.confirmationReqActivate({
      caption: "Do you confirm to revoke this user role?",
      confirm: () => {
        this.auth.
        apiRemove(this.routes.TEST_CTRL_ROLES, ur_id)
        .then(res => {
          for(let ctrlRole of this.ctrlRoles){
            if(ctrlRole.ur_id == ur_id){
              ctrlRole.is_revoked = "True";
              ctrlRole.revoked_on = new Date()
            }
          }
        })
        .catch(e => {
          if(e.message){
            this.loginGuard.quickPopup(e.message)
          }
          else{
            this.loginGuard.quickPopup("Failed to revoke user role.")
          }
        })
      },
      close: () => {}
  });
  }
  pageChanged() {
    // if (!this.classroomSelections.isAllSelected) {
    //   this.classrooms.forEach(classroom => classroom.__isSelected = false);
    // }
  }
  resetForm() {
    this.formGroup.controls.form_email.reset();
    this.formGroup.controls.form_uid?.reset();
    this.formGroup.controls.form_role_type.reset();
  }
  

}
