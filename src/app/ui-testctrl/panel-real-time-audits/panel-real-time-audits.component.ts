import { Component, OnInit, Input, SimpleChanges } from '@angular/core';
import { formatNumber } from '@angular/common';
import { AuthService } from '../../api/auth.service';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';
import { mtz } from '../../core/util/moment';
import { processItemConfigLangLink } from '../../ui-scorer/panel-score-content/util/lang-link';
import moment from 'moment';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { FormControl, FormGroup } from '@angular/forms';
import { RowEvent } from 'ag-grid-community/dist/lib/main';
import { ExpectedAnswer } from 'src/app/ui-item-maker/item-set-editor/models';
import { Router } from '@angular/router';
import { ColumnApi, GridApi } from 'ag-grid-community';
import { IRealTimeAuditdata, EAuditSlugs, LogType, ACTIONS, Comment, LogTypeToComment, scoreNotAlignedSlugs, responseNotAlignedSlugs} from './data/model'
import {MAX_BATCH_AUDIT_LIMIT, MAX_PROCESSING_INTERVAL, HOUR_TO_SEC} from './data/constants'
import { StyleprofileService } from 'src/app/core/styleprofile.service';


@Component({
  selector: 'panel-real-time-audits',
  templateUrl: './panel-real-time-audits.component.html',
  styleUrls: ['./panel-real-time-audits.component.scss']
})
export class PanelRealTimeAuditsComponent implements OnInit {
  LogType = LogType;
  LogTypeToComment = LogTypeToComment;
  comments: Comment[];
  selectedComments: Comment[]
  commentInput: string;

  @Input() roleContext : any;

  records: IRealTimeAuditdata[];
  auditHistory:any[];
  auditProgress:any = null;

  isLoading: boolean
  selectedIssue: any;
  eAuditSlugs: EAuditSlugs;

  requestGridColumnApi: ColumnApi;
  requestGridApi: GridApi;

  defaultColumnDefs = [
    //{ headerName:'ID', field:'audit_ids', width:50 },
    { headerName:'Item Id', field:'test_question_id', sort: 'asc' },
    { headerName:'Item Label', field:'question_label' },
    { headerName:'Score', field:'score' },
    { headerName:'Formatted Response', field:'formatted_response' },
    { headerName:'Lang', field:'lang', width:100 },
    { headerName:'Audit Description', field:'audit_description', width:400},
    { headerName:'Number of Cases', field:'num_cases', width:100 },
    { headerName:'Resolved?', field:'is_resolved', valueGetter: this.formatYesNo.bind(this) },
    { headerName:'Section', field:'section_id' },
    { headerName:'First Flagged On', field:'first_flagged_on', valueFormatter: (params) => this.renderTime(params.value) ,width:100 },
    { headerName:'Admin. Window', field:'test_window_id', width:100 },
    { headerName:'Form Code', field:'slug' , width:200, hide: true},
    { headerName:'attempt_id', field:'attempt_id', width:100, hide: true},
    { headerName:'test_form_id', field:'test_form_id', width:100, hide: true},
    { headerName:'OEN', field:'StudentOEN', width:100, hide: true }, 
    { headerName:'uid', field:'uid', width:100, hide: true},
    { headerName:'First Name', field:'first_name', width:120 , hide: true},
    { headerName:'Last Name', field:'last_name', width:150 , hide: true},
    { headerName:'Access Code', field:'access_code', width:100 , hide: true},
  ];

  auditHistoryColumnDefs = [
    { headerName:'ID', field:'id', width:100 },
    { headerName:'Status', field:'audit_status', width:150 },
    { headerName:'Created By', field:'created_by', width:150 },
    { headerName:'Created On', field:'created_on', valueFormatter: (params) => this.renderTime(params.value), width:150 },
    { headerName:'Completed On', field:'completed_on', valueFormatter: (params) => this.renderTime(params.value), width:150 },
    { headerName:'Last Updated On', field:'updated_on', valueFormatter: (params) => this.renderTime(params.value), width:150 },
    { headerName:'Minimum Seconds / Attempt', field:'ta_processing_interval', width:150 },
    { headerName:'Attempts to Process', field:'num_ta_requested', width:150 },
    { headerName:'Attempts Processed', field:'num_ta_completed', width:150 },
  ]

  auditHistoryGridOptions:any = {
    rowSelection: 'single',
    columnDefs: this.auditHistoryColumnDefs,
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true
    }
  }

  gridOptions:any = {
    rowSelection: 'single',
    columnDefs: this.defaultColumnDefs,
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
    getRowId: (params) => params.data.audit_id,
  };

  selectedItemId: number | null;
  selectedItemResponse: any;
  isResolvingIssue: boolean
  isIntervalInitialized: boolean;
  selectedNode: any;
  lastSelectedRowIndex: number;
  lastSelectedAuditId: string;
  isDetailedView : boolean
  messageOngoingAuditError:string;
  messageAuditInit:string;
  isErrorAuditInfoLoad = false;
  runAuditConfigs = new FormGroup({
    isBatchProcess: new FormControl(false),
    attemptProcessingInterval : new FormControl(),
    attemptProcessingLimit : new FormControl(), 
    studentOEN: new FormControl()
  })

  runRealTimeAuditsOnTestwindow = new FormControl(false);
  queryStateMeta = {
    isVerifyQuesExistInTestFormRunning : false,
    isAuditInitRunning: false,
  }

  dataFilters = new FormGroup({
    isAssessmentImpactToggled : new FormControl(false),
    isShowingResolvedIssues: new FormControl(false), 
  })

  defaultAuditSlugs = new Set([
    EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED,
    EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_EA,
    EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE
  ]);

  assessImpactAuditSlugs = new Set([
    EAuditSlugs.MSCAT_IMPOSSIBLE_PANEL_PATHWAY,
    EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM
  ])

  defaultFilteredAudits: IRealTimeAuditdata[] =  [];


  constructor(
    private auth: AuthService,
    private lang: LangService,
    private routes: RoutesService,
    private router:  Router,
    private profile: StyleprofileService,
  ) { }

  ngOnInit(): void {
    this.loadData();
    this.setSubscribers()
    // this.initInterval();
  }
  setSubscribers() {
    this.dataFilters.valueChanges.subscribe(changes => this.applyFilters())
    this.dataFilters.controls.isAssessmentImpactToggled.valueChanges.subscribe(val => this.setGridOptionColumnDefs());
  }

  exportCsv(){
    const fileName = `flagged_responses_tw${this.roleContext.test_window_id}_${+(new Date())}.csv`
    this.gridOptions.api.exportDataAsCsv({
      fileName,
    });
  }


  applyFilters() {
    let data = this.records
    const { controls } = this.dataFilters

    data = data?.filter(record => controls.isShowingResolvedIssues.value ? record.is_resolved : !record.is_resolved);
     
    if(controls.isAssessmentImpactToggled.value) {
      data = data?.filter((record) => this.assessImpactAuditSlugs.has(record.audit_slug) )
    }
    
    this.defaultFilteredAudits = data;
  }

  private initInterval(){
    if (!this.isIntervalInitialized){
      this.isIntervalInitialized = true;
      setInterval(() => this.loadData(true), 5*1000);
    }
  }

  async runAudits() {
    const configVar = this.runAuditConfigs.controls;

    if(!configVar.studentOEN.value){
      // check for batch process options
      if(configVar.isBatchProcess.value ){
        let attemptProcessingLimit = configVar.attemptProcessingLimit.value
        let attemptProcessingInterval = configVar.attemptProcessingInterval.value
        if(attemptProcessingLimit && (!Number.isInteger(attemptProcessingLimit) || attemptProcessingLimit < 1)) {
          return alert ("Invalid input for test attempts to be processed.");
        }
        if(attemptProcessingInterval != 0 && attemptProcessingInterval && (!Number.isInteger(attemptProcessingInterval) || attemptProcessingInterval < 1)) {
          return alert ("Invalid input for processing interval.");
        }
        if(!attemptProcessingLimit || attemptProcessingLimit  > MAX_BATCH_AUDIT_LIMIT){
          const confirmation = confirm(`One audit can process a maximum of ${MAX_BATCH_AUDIT_LIMIT} test attempts, this maximum will be used.`);
          if(!confirmation) return;
          attemptProcessingLimit = MAX_BATCH_AUDIT_LIMIT
          configVar.attemptProcessingLimit.setValue(MAX_BATCH_AUDIT_LIMIT)
        }
        if(attemptProcessingInterval && attemptProcessingInterval > MAX_PROCESSING_INTERVAL){
          const confirmation = confirm(`The maximum processing interval is ${MAX_PROCESSING_INTERVAL} seconds, this maximum will be used.`);
          if(!confirmation) return;
          attemptProcessingInterval = MAX_PROCESSING_INTERVAL
          configVar.attemptProcessingInterval.setValue(MAX_PROCESSING_INTERVAL)
        }
        if(attemptProcessingInterval){
          const minCompletionHours =  (attemptProcessingLimit * attemptProcessingInterval)/HOUR_TO_SEC
          let minCompletionHoursRound = parseFloat(minCompletionHours.toFixed(1));
          if (minCompletionHoursRound > 10){
            const confirmation = confirm(`With these configurations, the audit will take at least ${minCompletionHoursRound} hours.\nI understand that an audit can not be stopped once it started and other audits can not be started on this test window until it completes.`);
            if(!confirmation) return;
          }
        }
      } else {
        return alert("StudentOEN is not provided, please enable and configure Batch process options to run audits on the test_attempts associated with selected test_window");
      }
    }

    const data:any = { 
      test_window_id : this.roleContext.test_window_id,
      includeProcessedAttempts: configVar.studentOEN.value ? true : false, 
      includeProcessedTaqrs: configVar.studentOEN.value ? true : false, 
    }
    
    if(configVar.isBatchProcess.value){
      data['attemptProcessingInterval'] = configVar.attemptProcessingInterval.value;
      data['attemptProcessingLimit'] = configVar.attemptProcessingLimit.value;
    } else {
      data['studentOEN'] = configVar.studentOEN.value;
    }

    console.log(" Running real time audit on", data)
    this.queryStateMeta.isAuditInitRunning = true;
    this.messageOngoingAuditError = ""
    this.messageAuditInit = ""
    this.auth.apiCreate(this.routes.SESSION_QUESTION_AUDIT, data, {query: {tc_group_id: this.roleContext.tc_group_id}})
      .then((res) => {
        if (res.message == "ONGOING_AUDIT"){
          const createdOn = new Date(Date.parse(res.created_on));
          const createdOnLocal = createdOn.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric',  hour: '2-digit', minute: '2-digit',});
          this.messageOngoingAuditError = `Can not run batch audit for test window ${this.roleContext.test_window_id} because an audit initiated by ${res.username} on ${createdOnLocal} is currently running.`
        }
        if (res.batchAuditId){
          this.messageAuditInit = `Batch audit (ID: ${res.batchAuditId}) for test window ${this.roleContext.test_window_id} has been started. Click 'Refresh Audit Data' to see audit progress and new flags.`
        } 
        else if(res.studentOEN){
          this.messageAuditInit = `Audit for OEN ${res.studentOEN} in test window ${this.roleContext.test_window_id} has been started. Click 'Refresh Audit Data' to see new flags.`
        }
       })
      .catch((e) => {
        console.log("REAL_TIME_AUDIT_ERR",e);
      });
  }

  async loadData(isSilent?: boolean) {
    this.messageOngoingAuditError = ""
    this.isErrorAuditInfoLoad = false
    this.messageAuditInit = ""
    this.isLoading = isSilent ? false : true;

    const auditFlagsReq = this.auth.apiFind(this.routes.SESSION_QUESTION_AUDIT, {
      query: { 
        test_window_id: this.roleContext.test_window_id,
        tc_group_id: this.roleContext.tc_group_id,
        action: ACTIONS.AuditFlags
      }
    });

    const auditHistoryReq = this.auth.apiFind(this.routes.SESSION_QUESTION_AUDIT, {
      query: { 
        test_window_id: this.roleContext.test_window_id,
        tc_group_id: this.roleContext.tc_group_id,
        action: ACTIONS.AuditHistory
      }
    });

    const auditProgressReq = this.auth.apiFind(this.routes.SESSION_QUESTION_AUDIT, {
      query: { 
        test_window_id: this.roleContext.test_window_id,
        tc_group_id: this.roleContext.tc_group_id,
        action: ACTIONS.AuditProgress
      }
    });

    Promise.all([auditFlagsReq, auditHistoryReq, auditProgressReq])
    .then(([auditFlagsRes, auditHistoryRes, auditProgressRes]) => {
      this.records = auditFlagsRes
      this.auditHistory = auditHistoryRes
      if(this.auditHistory.length > 0){
        if(this.auditHistory[0].audit_status === 'RUNNING'){
          this.queryStateMeta.isAuditInitRunning = true
        }else if(this.auditHistory[0].audit_status === 'COMPLETED'){
          this.queryStateMeta.isAuditInitRunning = false
        }
      }

      this.auditProgress = {
        audit_processed_en_ta: formatNumber(auditProgressRes.audit_processed_en_ta, 'en-US', '1.0'),
        total_en_ta: formatNumber(auditProgressRes.total_en_ta, 'en-US', '1.0'),
        audit_processed_fr_ta: formatNumber(auditProgressRes.audit_processed_fr_ta, 'en-US', '1.0'),
        total_fr_ta: formatNumber(auditProgressRes.total_fr_ta, 'en-US', '1.0')
      }
      this.applyFilters();
      this.isLoading = false;
      this.resetAuditDetailedView();
    })
    .catch(e => {
      this.isErrorAuditInfoLoad = true;
      this.isLoading = false;
    })
  }

  getFilteredResults = () => this.defaultFilteredAudits;

  async runVerifyQuestionExistIntestForm() {
    this.queryStateMeta.isVerifyQuesExistInTestFormRunning = true;

    this.auth.apiGet(this.routes.SESSION_QUESTION_AUDIT, this.roleContext.test_window_id , {query: {tc_group_id: this.roleContext.tc_group_id}})
    .then(() => {
      this.queryStateMeta.isVerifyQuesExistInTestFormRunning = false;
      this.loadData();
    })
    .catch((e) => console.log(e));
  }

  async logComment(new_status: LogType, comment_text: string, is_resolved?: 0|1) {
    const data: Comment = { 
      session_question_audit_ids: this.selectedIssue.audit_ids?.split(","),
      comment_text,
      log_type: new_status,
      is_resolved
    }
    await this.auth.apiCreate(this.routes.SESSION_QUESTION_AUDIT_COMMENTS, data, {query: {tc_group_id: this.roleContext.tc_group_id}})

   //Render new comment on browser after click save btn
    this.selectedComments.push({
      ...data,
      username: this.auth.getFirstName() + " " + this.auth.getLastName(),
      created_on: Date.now(),
    });

    //Set textarea to empty after save comment
    this.commentInput="";

    //undefined only if we do custom comment
    if (is_resolved !== undefined) {
      await this.auth.apiPatch(this.routes.SESSION_QUESTION_AUDIT, new_status, data, {query: {tc_group_id: this.roleContext.tc_group_id}});

      this.records.forEach(record => {
        if(record.audit_ids === this.selectedIssue.audit_ids) { 
          record.is_resolved = !!is_resolved
        }
      })
    }
  }

  async saveComment(){
    const data = { 
      session_question_audit_ids: this.selectedIssue.audit_ids?.split(","),
      comment_text: this.commentInput,
      log_type: LogType.Comment,
    }

    if( this.commentInput == null || this.commentInput.length === 0 || this.commentInput.trim().length === 0) {
      return;
    }

    await this.logComment(LogType.Comment, this.commentInput);
  }

  async changeAuditStatus(new_status: LogType, is_resolved: 0|1) {
    await this.logComment(new_status, "", is_resolved);
  }

  async resolveAuditIssue() {
    if (this.selectedIssue && this.selectedItemResponse) {
      const is_currently_resolved = this.selectedIssue.is_resolved;

      if(confirm( is_currently_resolved
        ? this.lang.tra("issue_real_time_audit_mark_unresolved")
        : this.lang.tra("issue_real_time_audit_mark_resolved") )) {
        this.isResolvingIssue = true;

        this.changeAuditStatus(
          is_currently_resolved ? LogType.Unresolved : LogType.Resolve,
          is_currently_resolved ? 0 : 1
        ).finally(() => {
          this.isResolvingIssue = false;
        });        
      }
    }
  }

  async addFormattedAnswerToSimulatedSubmission() {
    if(!this.selectedIssue ) return;
    
    const {test_question_id, item_set_id, audit_slug, lang} = this.selectedIssue
    const {response_raw} = this.selectedItemResponse

    this.auth
    .apiCreate(this.routes.SIMULATE_EXTRACT_RESPONSE, {
      response_raw,
      test_question_id,
      item_set_id,
      lang,
      create_expected_response: true
    })
    .then(() => {
      return this.getFormattedResponsesForSelectedIssue()
    })
    .then((simulatedRes) => {
      this.selectedItemResponse.simulated_responses = simulatedRes
      if(this.isPossibleEaNotAlignedSlug(audit_slug)){
        this.changeAuditStatus(LogType.AddSimulatedSubmission, 1);
       }
      else if (this.isScoreFlag(audit_slug)){
        this.logComment(LogType.AddExpectedAnswerScoreFlag, "")
      }
    })
  }

  isPossibleEaNotAlignedSlug = (slug: EAuditSlugs) => responseNotAlignedSlugs.has(slug);
  isScoreFlag = (slug: EAuditSlugs) => scoreNotAlignedSlugs.has(slug);

  onRowClicked($event: RowEvent) {
    const { data, rowIndex} = $event
    if(this.lastSelectedAuditId === data.audit_ids && this.lastSelectedRowIndex === rowIndex && this.isDetailedView){
      this.resetAuditDetailedView();
    } else {
      this.lastSelectedRowIndex= rowIndex;
      this.lastSelectedAuditId = data.audit_ids;
    }
  }

  async onSelected($event: RowNodeEvent){

    const selectedRows = this.gridOptions.api.getSelectedRows();
    const selectedNodes = this.gridOptions.api.getSelectedNodes();

    this.commentInput = null;

    if(selectedNodes.length > 0) this.selectedNode = selectedNodes[0];

    if (selectedRows.length > 0){
      this.selectedIssue = selectedRows[0];
      this.loadItemResponseAndDisplay();
      const auditString =this.selectedIssue.audit_ids.split(',')
      const auditID = auditString[0]
  
      this.selectedComments = await this.auth.apiGet(this.routes.SESSION_QUESTION_AUDIT_COMMENTS,auditID, {
        query: { 
          tc_group_id: this.roleContext.tc_group_id
        }
      });
    }
    else {
      this.selectedIssue = null;
      this.selectedComments = []
    }

  }

  async loadItemResponseAndDisplay(){

    if(!this.selectedIssue) {
      // reset view 
    }

    this.selectedItemResponse = null;
    let { test_question_id, lang, question_label, score, weight, section_id, module_id , taqr_created_on, taqr_updated_on, resolve_note, is_resolved, audit_slug, response_raw, formatted_response, taqr_id} = this.selectedIssue;
    
    const position = { section: section_id };

    const taqr = { 
      itemId: test_question_id,
      itemDisplay: null, 
      responseState: null, 
      position, 
      question_label, 
      score, 
      weight, 
      taqr_created_on,
      taqr_updated_on,
      reviewerNote: resolve_note,
      is_resolved,
      audit_slug,
      response_raw,
      formatted_response
    } ;
    
    this.selectedItemId = test_question_id;

    const {config, style_profile} = await this.auth.apiGet('/public/test-ctrl/schools/student', taqr_id || -1, { query: {... this.roleContext, itemId: test_question_id} })
    this.profile.applyStyleProfileOrFallback(style_profile, null, 'panel-real-time-audits', 'loadItemResponseAndDisplay');
   
    // fetch simulated responses of question
    const simulatedRes = await this.getFormattedResponsesForSelectedIssue();
    taqr['simulated_responses'] = simulatedRes;

    if (config){
      let itemConfig = JSON.parse(config);
      taqr.itemDisplay = processItemConfigLangLink(itemConfig, lang);
      taqr.responseState = JSON.parse( taqr.response_raw || '{}');
    }

    this.selectedItemResponse = taqr;
    this.isDetailedView = true;
    console.log('loadItemResponseAndDisplay', taqr)

  }

  renderitemScoreOf = (selectedItemResponse:any)  => {
    let score = selectedItemResponse.score;
    let weight = selectedItemResponse.weight;

    if(!score) score  = 0 ;
    if(!weight) weight = 0
    return `${score} / ${weight}`;
  }

  renderTime(dt: Date) {
    if (!dt) return null
    return moment(dt).format('LL, HH:mm') // mtz(dt).format('MMM D [at] h:mm A')
  }

  renderYesNo(input){
    return input ? this.lang.tra('lbl_yes') : this.lang.tra('lbl_no')
  }

  formatYesNo(params){
    return params.data[params.colDef.field] ? this.lang.tra('lbl_yes') : this.lang.tra('lbl_no')
  }

  resetAuditDetailedView() {
    this.selectedIssue = null;
    this.selectedItemResponse = null;
    this.selectedItemId = null;
    this.isDetailedView = false;
    if(this.selectedNode) this.selectedNode.setSelected(false);
  }

  boolToInt(val: any) {
    return val ? 1 : 0 ;
  }

  // Switch text in resolved/unresolved button
  // getResolvedIssueSlug() {
  //   if(this.selectedItemResponse){
  //     if(this.selectedItemResponse.is_resolved) return 'issue_real_time_audit_unresolve'
  //   }    
  //   return 'issue_real_time_audit_resolve'
  // }

  setGridOptionColumnDefs(val?: boolean) {
    const { controls } = this.dataFilters
    if(controls.isAssessmentImpactToggled.value){
      const excludeColFields = new Set(['attempt_id', 'question_label', 'test_question_id', 'section_id', 'score']);
      const cols = this.defaultColumnDefs.filter(colDef => !excludeColFields.has(colDef.field))
      return this.gridOptions.api.setColumnDefs(cols);      
    }

    return this.gridOptions.api.setColumnDefs(this.defaultColumnDefs);      
  }

  getNumberOfIssuesForSelectedCase = () => {
    if(this.selectedIssue) {
      const audit_ids = this.selectedIssue.audit_ids?.split(",");
      if(audit_ids) return audit_ids.length
    }
    return 0;
  }

  getFormattedResponsesForSelectedIssue = async () => {
    if(!this.selectedIssue) return [];
    const formattedAns: ExpectedAnswer[] =  await this.auth.apiFind(this.routes.SIMULATE_EXTRACT_RESPONSE, {
      query: { test_question_id: this.selectedIssue.test_question_id }
    })

    return formattedAns.filter(ans => ans.lang === this.selectedIssue.lang)
  }

  getItemRoute = () => {
    if(!this.selectedIssue) return;
    let { lang, question_label, question_set_id } = this.selectedIssue;
    return `/${lang}/test-auth/item-set-editor/${question_set_id}/${encodeURIComponent(question_label)}`;
  }
}
