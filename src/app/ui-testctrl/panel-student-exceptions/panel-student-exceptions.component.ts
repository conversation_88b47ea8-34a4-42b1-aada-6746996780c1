import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RowNodeEvent } from 'ag-grid-community/dist/lib/entities/rowNode';

enum ExceptionTable {
  OUTCOME_STUDENT = 'OUTCOME_STUDENT',
  OUTCOME_SCHOOL = 'OUTCOME_SCHOOL',
  ITEMSCORE_STUDENT = 'ITEMSCORE_STUDENT',
  ITEMSCORE_GLOBAL = 'ITEMSCORE_GLOBAL',
}
enum IssueReviewModal {
  CREATE_EXCEPTION = "CREATE_EXCEPTION",
}

interface FormFieldConfig {
  name: string;
  label: string;
  type: 'text' | 'radio' | 'number' | 'select';
  value?: string
  options?: string[]; // Used for radio button or select options
  condition?: string; // display condition
  validators?: Validators;
}

@Component({
  selector: 'panel-student-exceptions',
  templateUrl: './panel-student-exceptions.component.html',
  styleUrls: ['./panel-student-exceptions.component.scss']
})
export class PanelStudentExceptionsComponent implements OnInit {

  @Input() roleContext:any;
  pageModal: PageModalController;

  ExceptionTable = ExceptionTable
  IssueReviewModal = IssueReviewModal;

  public itemIdFormCtrl: FormGroup;
  public exceptionFormCtrl: FormGroup;
  
  isItemIdChecked:boolean = false;
  exceptionElementListFinal: FormFieldConfig[];
  globalExceptionQuestionLabel: string;
  
  // Form Templates
  itemIdFormattedResForm: FormFieldConfig[] = [
    { name: 'item_id', label: 'Item Id', type: 'number', validators: [Validators.required, Validators.min(1)] },
    { name: 'lang', label: 'Language', type: 'select', options: ['en', 'fr'] }
  ];

  baseExceptionForm: FormFieldConfig[] = [
    { name: 'item_id', label: 'Item Id', type: 'number', validators: [Validators.required, Validators.min(1)] },
    { name: 'lang', label: 'Language', type: 'select', options: ['en', 'fr'] },
    { name: 'exceptionAction', label: 'exception Action', type: 'radio', options: ['is_prorated', 'is_score_override'], validators: [Validators.required] },
    { name: 'new_score', label: 'New Score', type: 'number', condition: 'is_score_override' },
    { name: 'notes', label: 'Notes', type: 'text' }
  ];
  
  studentExceptionFormAddon: FormFieldConfig[] = [
    { name: 'uid', label: 'Student UID', type: 'number', validators: [Validators.required, Validators.min(1)] },
    { name: 'ta_id', label: 'Test Attempt ID', type: 'number', validators: [Validators.required, Validators.min(1)] },
    { name: 'ric_id', label: 'Reported Issue ID', type: 'number' },
    { name: 'match_response_value', label: 'Match Response Value', type: 'text'}
  ];
  
  studentExceptionActionAddon: string[] = ['is_invalidated'];

  globalExceptionFormAddon: FormFieldConfig[] = [
    { name: 'match_response_value', label: 'Match Response Value', type: 'select'} // options added item id checked
  ]

  currentSelection:ExceptionTable;
  isRevokedShown:boolean = false;

  gridApi;
  gridColumnApi;
  gridSelectedExceptionItem;
  dataOutcomeStudent:any[] = [];
  gridOptionsOutcomeStudent:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:'id', field:'id', checkboxSelection:true,  width:100 }, // 
      { headerName:'uid', field:'uid',  width:100 }, // 
      { headerName:'test_window_id', field:'test_window_id',  width:100 }, // 
      { headerName:'category', field:'category',  width:100 }, // 
      { headerName:'ric_id', field:'ric_id',  width:100 }, // 
      { headerName:'BoardMident', field:'BoardMident',  width:100 }, // 
      { headerName:'SchoolMident', field:'SchoolMident',  width:100 }, // 
      { headerName:'sc_id', field:'sc_id',  width:100 }, // 
      { headerName:'StudentOEN', field:'StudentOEN',  width:100 }, // 
      { headerName:'is_pended', field:'is_pended',  width:100 }, // 
      { headerName:'notes', field:'notes',  width:100 }, // 
      { headerName:'created_on', field:'created_on',  width:100 }, // 
      { headerName:'created_by_uid', field:'created_by_uid',  width:100 }, // 
      { headerName:'created_by_email', field:'created_by_email',  width:100 },
      { headerName:'is_revoked', field:'is_revoked',  width:100 }, // 
      { headerName:'revoked_by_uid', field:'revoked_by_uid',  width:100 }, // 
      { headerName:'revoke_notes', field:'revoke_notes',  width:100 }, // 
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  dataItemscoreStudent:any[] = [];
  gridOptionsItemscoreStudent:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:'id', field:'id', checkboxSelection:true,  width:100 }, // 
      { headerName:'uid', field:'uid',  width:100 }, // 
      { headerName:'test_window_id', field:'test_window_id',  width:100 }, // 
      { headerName:'item_id', field:'item_id',  width:100 }, // 
      { headerName:'item_label', field:'item_label',  width:100 }, // 
      { headerName:'ric_id', field:'ric_id',  width:100 }, // 
      { headerName:'BoardMident', field:'BoardMident',  width:100 }, // 
      { headerName:'SchoolMident', field:'SchoolMident',  width:100 }, // 
      { headerName:'sc_id', field:'sc_id',  width:100 }, // 
      { headerName:'StudentOEN', field:'StudentOEN',  width:100 }, // 
      { headerName:'is_prorated', field:'is_prorated',  width:200 }, 
      { headerName:'is_score_override', field:'is_score_override',  width:200 }, 
      { headerName:'new_score', field:'new_score',  width:200 }, 
      { headerName:'notes', field:'notes',  width:100 }, // 
      { headerName:'created_on', field:'created_on',  width:100 }, // 
      { headerName:'created_by_uid', field:'created_by_uid',  width:100 }, // 
      { headerName:'is_revoked', field:'is_revoked',  width:100 }, // 
      { headerName:'revoked_by_uid', field:'revoked_by_uid',  width:100 }, // 
      { headerName:'revoke_notes', field:'revoke_notes',  width:100 }, // 
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  dataItemscoreGlobal:any[] = [];
  gridOptionsItemscoreGlobal:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:'id', field:'id', checkboxSelection:true,  width:100 }, 
      { headerName:'test_window_id', field:'test_window_id',  width:200 }, 
      { headerName:'item_id', field:'item_id',  width:200 },
      { headerName:'item_label', field:'item_label',  width:200 }, 
      { headerName:'is_prorated', field:'is_prorated',  width:200 }, 
      { headerName:'is_score_override', field:'is_score_override',  width:200 }, 
      { headerName:'score_override', field:'score_override',  width:200 }, 
      { headerName:'match_response_value', field:'match_response_value',  width:250 },
      { headerName:'notes', field:'notes',  width:200 }, 
      { headerName:'created_by_uid', field:'created_by_uid',  width:200 }, 
      { headerName:'created_on', field:'created_on',  width:200 }, 
      { headerName:'is_revoked', field:'is_revoked',  width:200 }, 
      { headerName:'revoked_on', field:'revoked_on',  width:200 }, 
      { headerName:'revoked_by_uid', field:'revoked_by_uid',  width:200 }, 
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };

  constructor(
    private auth: AuthService,
    private pageModalService: PageModalService,
    private loginGuard: LoginGuardService,
    private formBuilder: FormBuilder
  ) {
    this.itemIdFormCtrl = this.formBuilder.group({});
    this.exceptionFormCtrl = this.formBuilder.group({});
  }

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  isLoading:boolean;
  async select(id: ExceptionTable){
    this.currentSelection = id;
    const queryParams = {query: {... this.roleContext}}
    this.dataOutcomeStudent = []
    this.dataItemscoreStudent = []
    this.dataItemscoreGlobal = []
    this.isLoading = true;
    switch(id){
      case ExceptionTable.OUTCOME_SCHOOL: 
        break;
      case ExceptionTable.OUTCOME_STUDENT: 
        this.dataOutcomeStudent = await this.auth.apiFind('public/test-ctrl/schools/student-exceptions', queryParams)
        break;
      case ExceptionTable.ITEMSCORE_STUDENT: 
        this.dataItemscoreStudent = await this.auth.apiFind('public/test-ctrl/schools/student-item-exceptions', queryParams)
        break;
      case ExceptionTable.ITEMSCORE_GLOBAL: 
        this.dataItemscoreGlobal = await this.auth.apiFind('public/test-ctrl/schools/global-item-exceptions', queryParams)
        break;
    }
    setTimeout(() => {
      this.applyDefaultRevokedFilter(this.currentSelection)
    }, 500)
    this.isLoading = false;
  }
  isSelected(id: ExceptionTable){
    return (this.currentSelection === id);
  }

  async onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  onRowClick($event: RowNodeEvent){
    let selectedNodes = this.gridApi.getSelectedNodes();
    let selectedData = selectedNodes.map(node => node.data);
    if(selectedData.length != 1){
      this.gridSelectedExceptionItem = null;
      return;
    }
    this.gridSelectedExceptionItem = selectedData[0]
  }
  
  exportCsvOutcomeStudent(){
    this.gridOptionsOutcomeStudent.api.exportDataAsCsv();
  }
  exportCsvItemscoreStudent(){
    this.gridOptionsItemscoreStudent.api.exportDataAsCsv();
  }
  exportCsvItemscoreGlobal(){
    this.gridOptionsItemscoreGlobal.api.exportDataAsCsv();
  }
  
  /**
   * Apply Revoked filter to show or hide revoked exceptions 
   * according to the Include Revoked Exceptions toggle value
   * @param selectedType 
   * @returns 
   */
  applyDefaultRevokedFilter(selectedType:ExceptionTable){
    let selectedGridTable
    switch(selectedType){
      case (ExceptionTable.OUTCOME_STUDENT):
        selectedGridTable = this.gridOptionsOutcomeStudent
        break;
      case (ExceptionTable.ITEMSCORE_STUDENT):
        selectedGridTable = this.gridOptionsItemscoreStudent
        break;
      case (ExceptionTable.ITEMSCORE_GLOBAL):
        selectedGridTable = this.gridOptionsItemscoreGlobal
        break;
      default: 
        return
    }
    const filterIsRevoked = selectedGridTable.api?.getFilterInstance('is_revoked')
    if (this.isRevokedShown){
      filterIsRevoked?.setModel({ })
    } else {
      filterIsRevoked?.setModel({
        filter: "1",
        filterType: "text",
        type: "notContains",
      })
    }
    selectedGridTable.api?.onFilterChanged()
  }

  cModal() { 
    return this.pageModal.getCurrentModal(); 
  }

  cmc() {
    return this.cModal().config; 
  }
  
  /**
   * Open the create new item exception modal
   * @param {ExceptionTable} selectedTable
   */
  async showCreatingNewItemExceptionModal(selectedTable: ExceptionTable){
    this.initializeExceptionForms(selectedTable)
    
    const config = {
      test_window_id: this.roleContext.test_window_id,
      item_exception_type: selectedTable.split('_').pop()
    }
    this.pageModal.newModal({
      type: IssueReviewModal.CREATE_EXCEPTION,
      config: config,
      finish: () => {
        if(selectedTable === ExceptionTable.ITEMSCORE_STUDENT){
          this.createStudentItemException(this.roleContext.test_window_id);
        }else if(selectedTable === ExceptionTable.ITEMSCORE_GLOBAL){
          this.createGlobalItemException(this.roleContext.test_window_id);
        }
        this.pageModal.closeModal();
        this.itemIdFormCtrl.reset();
        this.exceptionFormCtrl.reset();
      }
    })   
  }

  /**
   * Reset and initialize item exception form according to exception table selected
   * @param {ExceptionTable} selectedTable
   */
  initializeExceptionForms(selectedTable: ExceptionTable){
    this.globalExceptionQuestionLabel = ''
    this.isItemIdChecked = false;
    this.itemIdFormCtrl.reset();
    this.exceptionFormCtrl.reset();

    this.itemIdFormCtrl = this.createExceptionFormGroup(this.itemIdFormattedResForm)

    let exceptionActionsFormOptions:string
    if(selectedTable === ExceptionTable.ITEMSCORE_STUDENT){
      this.exceptionElementListFinal = [...this.studentExceptionFormAddon, ...this.baseExceptionForm]
      exceptionActionsFormOptions = 'add'
    }else if(selectedTable === ExceptionTable.ITEMSCORE_GLOBAL){
      this.exceptionElementListFinal = [...this.baseExceptionForm, ...this.globalExceptionFormAddon];
      exceptionActionsFormOptions = 'remove'
    }
    this.exceptionFormCtrl = this.createExceptionFormGroup(this.exceptionElementListFinal)

    this.modifyExistingFormTypeOptions(this.exceptionElementListFinal, 'exceptionAction', this.studentExceptionActionAddon, exceptionActionsFormOptions)
  }

  /**
   * Modify existing form field options 
   * @param {FormFieldConfig} targetForm form that needs to be changed
   * @param {string} fieldName form field name
   * @param {string[]} options form field options
   * @param {string} action "add" or "remove" options items, change type to "text" or "select"
   */
  modifyExistingFormTypeOptions(targetForm:FormFieldConfig[], fieldName:string, options: string[], action:string){
    const targetFromField = targetForm.find(field => field.name === fieldName)
    switch (action){
      case 'add':
        if( !targetFromField.options.includes(options[0]) ){
          targetFromField.options = targetFromField.options.concat(options);
        }
        break;
      case 'remove':
        targetFromField.options = targetFromField.options.filter(targetFormAction => {
          return options.every(option => {
            return targetFormAction !== option
          })
        })
        break;
      case 'text':
        targetFromField.type = 'text'
        delete targetFromField.options
        break;
      case 'select':
        targetFromField.type = 'select'
        targetFromField.options = options
        break;
      default:
        return;
    }
  }

  /**
   * Initialize angular FormControl with FormBuilder
   * @param fields 
   * @returns 
   */
  createExceptionFormGroup(fields: FormFieldConfig[]): FormGroup {
    const group = this.formBuilder.group({});
    fields.forEach(field => {
      const control = this.formBuilder.control(
        field.value || null, 
        field.validators || [] // Apply the validators if specified
      );
      group.addControl(field.name, control);
    });
    return group;
  }

  /**
   * 1. Get all formatted responses by item ID and 
   * 2. Get test question item label
   */
  async getFormattedResponsesByItemId() {
    const {item_id, lang} = this.itemIdFormCtrl.value

    try {
      const response = await this.auth.apiGet('public/test-ctrl/schools/global-item-exceptions', item_id, {
        query: { lang }
      })
      this.isItemIdChecked = true

      if(response.length){
        // Update exceptionFormCtrl values
        this.exceptionFormCtrl.controls.item_id.setValue(item_id)

        const itemIdExpectedResLangs = [...new Set(response.map(res => res.lang))];

        if(itemIdExpectedResLangs.length === 1){
          this.exceptionFormCtrl.controls.lang.setValue(response[0].lang)
        }

        this.globalExceptionQuestionLabel = `[${response[0].question_label}]`

        // Change match_response_value type to "select" and add formatted responses to its options
        const tqExpectedResFormattedRes = response.map(item => item.formatted_response)
        this.modifyExistingFormTypeOptions(this.exceptionElementListFinal, 'match_response_value', tqExpectedResFormattedRes, 'select')
      } else {
        this.modifyExistingFormTypeOptions(this.exceptionElementListFinal, 'match_response_value', [], 'text')
      }
    } catch(err) {
      const errMsg = this.generateErrorMessage(err.message)
      this.loginGuard.quickPopup(errMsg)
    }
  }

  /**
   * Check if the exceptionAction.value is "is_score_override"
   * @returns {boolean}
   */
  isScoreOverride():boolean{
    return this.exceptionFormCtrl.controls['exceptionAction'].value === 'is_score_override'
  }

  /**
   * If the form field has Validators.required, return "*"
   * @param {Validators} validators
   * @returns {string}
   */
  isFieldRequired(validators:Validators[]):string{
    if(validators?.includes(Validators.required)){
      return '*'
    } else {
      return ''
    }
  }

  /**
   * Create student item exception
   * @param test_window_id 
   */
  async createStudentItemException(test_window_id:number){
    const payload = {
      test_window_id,
      ...this.exceptionFormCtrl.value
    }
    const exceptionAction = this.exceptionFormCtrl.controls['exceptionAction'].value
    payload[exceptionAction] = 1
    if(!this.isScoreOverride()) {
      payload.new_score = null
    }

    try {
      await this.auth.apiCreate('public/test-ctrl/schools/student-item-exceptions', payload, {
        query: {
          isFromExceptionsTab: true
        }
      })
      this.select(ExceptionTable.ITEMSCORE_STUDENT)
    } catch(err) {
      const errMsg = this.generateErrorMessage(err.message)
      this.loginGuard.quickPopup(errMsg)
    }
  }

  /**
   * Create global item exception
   * @param test_window_id 
   */
  async createGlobalItemException(test_window_id:number){
    const payload = this.exceptionFormCtrl.value;
    if(!this.isScoreOverride()) {
      payload.new_score = null
    }

    try {
      const response = await this.auth.apiCreate('public/test-ctrl/schools/global-item-exceptions', payload, {
        query: {
          test_window_id
        }
      })
      this.dataItemscoreGlobal = response
      setTimeout(() => {
        this.applyDefaultRevokedFilter(this.currentSelection)
      }, 500)
    } catch (err) {
      const errMsg = this.generateErrorMessage(err.message)
      this.loginGuard.quickPopup(errMsg)
    }
  }

  /**
   * Revoke the selected exception item (single)
   */
  async revokeExceptionItem(){
    let apiRemoveEndpoint:string
    if(this.currentSelection === ExceptionTable.ITEMSCORE_STUDENT){
      apiRemoveEndpoint = 'public/test-ctrl/schools/student-item-exceptions'
    }else if (this.currentSelection === ExceptionTable.ITEMSCORE_GLOBAL){
      apiRemoveEndpoint = 'public/test-ctrl/schools/global-item-exceptions'
    }

    this.loginGuard.confirmationReqActivate({
      caption: 'Are you sure you want to revoke the selected item exception?',
      confirm: async () => {
        try {
          await this.auth.apiRemove(apiRemoveEndpoint, this.gridSelectedExceptionItem.id)
          this.gridSelectedExceptionItem = null
          this.select(this.currentSelection)
          this.loginGuard.quickPopup('Item exception revoked.')
        } catch (err){
          const errMsg = this.generateErrorMessage(err.message)
          this.loginGuard.quickPopup(errMsg)
        }
      }
    })
  }

  /**
   * Generate error message for rejected promise
   * @param {string} errMsg err.message
   * @returns {string} message
   */
  generateErrorMessage(errMsg:string):string{
    let message:string = "We are having trouble fulfilling your request, please try again.";
    switch(errMsg){
      case 'MISSING_PARAMS':
        message = "Cannot proceed with the request. Missing key info.";
        break;
      case 'NO_ITEM_FOUND_WITH_ID':
        message = "No test question found with the **item Id** provided.";
        break;
      case 'ITEM_ALREADY_EXIST':
        message = 'The record you are trying to add already exists.';
        break;
      case 'RIC_ID_MISMATCH':
        message = 'Reported issues common id does not match the item id.';
        break;
      case 'NO_NEW_SCORE_FOR_SCORE_OVERRIDE':
        message = 'You need to provide a **New Score** value for score override.';
        break;
      default:
        console.log(message)
    }
    return message
  }
}
