<table class="exception-menu">
    <tr>
        <th></th>
        <th>Student</th>
        <th>School</th>
        <th>Global</th>
    </tr>
    <tr>
        <th>Outcome</th>
        <td> <button 
                class="button is-small"
                [class.is-light]="!isSelected(ExceptionTable.OUTCOME_STUDENT)"
                [class.is-info]="isSelected(ExceptionTable.OUTCOME_STUDENT)"
                (click)="select(ExceptionTable.OUTCOME_STUDENT)"
        >Student Exceptions</button> </td>
        <td> <button 
                class="button is-small"
                [class.is-light]="!isSelected(ExceptionTable.OUTCOME_SCHOOL)"
                [class.is-info]="isSelected(ExceptionTable.OUTCOME_SCHOOL)"
                (click)="select(ExceptionTable.OUTCOME_SCHOOL)"
        >School Exceptions</button> </td>
        <td></td>
    </tr>
    <tr>
        <th>Item Score</th>
        <td> <button 
                class="button is-small"
                [class.is-light]="!isSelected(ExceptionTable.ITEMSCORE_STUDENT)"
                [class.is-info]="isSelected(ExceptionTable.ITEMSCORE_STUDENT)"
                (click)="select(ExceptionTable.ITEMSCORE_STUDENT)"
        >Student Item Exceptions</button> </td>
        <td></td>
        <td> <button 
                class="button is-small "
                [class.is-light]="!isSelected(ExceptionTable.ITEMSCORE_GLOBAL)"
                [class.is-info]="isSelected(ExceptionTable.ITEMSCORE_GLOBAL)"
                (click)="select(ExceptionTable.ITEMSCORE_GLOBAL)"
        >Item Exceptions</button> </td>
    </tr>
</table>

<div class="exceptions-option-buttons">
    <mat-slide-toggle 
        [(ngModel)]="isRevokedShown" 
        (change)="applyDefaultRevokedFilter(currentSelection)"
    >
        Include Revoked Exceptions?
    </mat-slide-toggle>
    <div>
        <button 
            *ngIf="currentSelection === 'ITEMSCORE_STUDENT' || currentSelection === 'ITEMSCORE_GLOBAL'" 
            class="button is-success" 
            (click)="showCreatingNewItemExceptionModal(currentSelection)"
        >
            Add Item Exception
        </button>
        <button
            *ngIf="gridSelectedExceptionItem?.is_revoked === 0"
            class="button is-danger"
            (click)="revokeExceptionItem()"
        >
            Revoke Selected Item
        </button> 
    </div>

</div>

<div *ngIf="currentSelection" style="margin-top:2em;">
    <ng-container [ngSwitch]="currentSelection">
        <ng-container *ngSwitchCase="ExceptionTable.OUTCOME_SCHOOL">No records found</ng-container>
        <ng-container *ngSwitchCase="ExceptionTable.OUTCOME_STUDENT">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="dataOutcomeStudent"
                [gridOptions]="gridOptionsOutcomeStudent"
                [enableCellTextSelection]="true"
            ></ag-grid-angular>
            <div style="margin: 0.5em 0em;">
                <button class="button is-light" (click)="exportCsvOutcomeStudent()">
                    Export Student Exceptions
                </button>
            </div>
        </ng-container>
        <ng-container *ngSwitchCase="ExceptionTable.ITEMSCORE_STUDENT">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="dataItemscoreStudent"
                [gridOptions]="gridOptionsItemscoreStudent"
                (rowSelected)="onRowClick($event)"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReady($event)"
            ></ag-grid-angular>
            <div style="margin: 0.5em 0em;">
                <button class="button is-light" (click)="exportCsvItemscoreStudent()">
                    Export Student Exceptions
                </button>
            </div>
        </ng-container>
        <ng-container *ngSwitchCase="ExceptionTable.ITEMSCORE_GLOBAL">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                [rowData]="dataItemscoreGlobal"
                [gridOptions]="gridOptionsItemscoreGlobal"
                (rowSelected)="onRowClick($event)"
                [enableCellTextSelection]="true"
                (gridReady)="onGridReady($event)"
            ></ag-grid-angular>
            <div style="margin: 0.5em 0em;">
                <button class="button is-light" (click)="exportCsvItemscoreGlobal()">
                    Export Student Exceptions
                </button>
            </div>
        </ng-container>
    </ng-container>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
            <div *ngSwitchCase="IssueReviewModal.CREATE_EXCEPTION" class="exception-form">
                <h1>Test Window {{ cmc().test_window_id }}: Create a <b>{{ cmc().item_exception_type | titlecase }}</b> Item Exception</h1>
                <!-- Item Id check form -->
                <form [formGroup]="itemIdFormCtrl" *ngIf="cmc().item_exception_type === 'GLOBAL' && !isItemIdChecked">
                    <ng-container *ngFor="let field of itemIdFormattedResForm">
                        <ng-container *ngIf="!field.condition" [ngSwitch]="field.type"> 
                            <mat-form-field *ngSwitchCase="'select'">
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <mat-select [formControlName]="field.name">
                                    <mat-option *ngFor="let option of field.options" [value]="option">{{ option | uppercase }}</mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field *ngSwitchDefault>
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <input [type]="field.type" matInput [formControlName]="field.name" placeholder="">
                            </mat-form-field>
                        </ng-container>
                    </ng-container>
                    <button 
                        class="button is-success" 
                        (click)="getFormattedResponsesByItemId()" 
                        [disabled]="itemIdFormCtrl.invalid"
                    >
                        Search Item Id
                    </button>
                </form>
                <h2 *ngIf="globalExceptionQuestionLabel">Question Label: {{ globalExceptionQuestionLabel }}</h2>
                <!-- Create item exception form -->
                <form *ngIf="currentSelection === ExceptionTable.ITEMSCORE_STUDENT || (currentSelection === ExceptionTable.ITEMSCORE_GLOBAL && isItemIdChecked)" [formGroup]="exceptionFormCtrl" >
                    <ng-container *ngFor="let field of exceptionElementListFinal">
                        <ng-container *ngIf="!field.condition" [ngSwitch]="field.type">
                            <div *ngSwitchCase="'radio'">
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <mat-radio-group [formControlName]="field.name" [required]="field.validators?.includes('required')">
                                    <mat-radio-button *ngFor="let option of field.options" [value]="option">{{ option }}</mat-radio-button>
                                </mat-radio-group>
                            </div>
                            <mat-form-field *ngSwitchCase="'select'">
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <select matNativeControl [formControlName]="field.name" [required]="field.validators?.includes('required')">
                                    <option *ngFor="let option of field.options" [value]="option">{{ option | uppercase }}</option>
                                </select>
                            </mat-form-field>
                            <mat-form-field *ngSwitchDefault>
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <input [type]="field.type" matInput [formControlName]="field.name" placeholder="">
                            </mat-form-field>
                        </ng-container>
                        <ng-container *ngIf="field.condition === 'is_score_override' && isScoreOverride()">
                            <mat-form-field>
                                <mat-label>{{ field.label }} {{ isFieldRequired(field.validators) }}</mat-label>
                                <input [type]="field.type" matInput [formControlName]="field.name" placeholder="">
                            </mat-form-field>
                        </ng-container>
                    </ng-container>
                </form>
            </div>

            <modal-footer [pageModal]="pageModal" [isEditDisable]="exceptionFormCtrl.invalid" [isConfirmAlert]="true" ></modal-footer>
        </div>
    </div>
</div>