<h3><b>Psychometric Pipeline Configuration</b></h3>

<div *ngIf="isLoadingConfig">Loading...</div>

<div *ngIf="!isLoadingConfig">
  <table>
    <tr *ngIf="mode === PsychConfigModal.VIEW">
      <td>
        config_id
      </td>
      <td>
        <input class="input" type="number" [(ngModel)]="config.id" [disabled]="true">
      </td>
    </tr>
    <tr>
      <td>
        assessment_type
      </td>
      <td>
        <div class="select">
          <select [(ngModel)]="config.assessment_type" (ngModelChange)="onAssessmentTypeChange($event)" [disabled]="mode === PsychConfigModal.VIEW">
            <option *ngFor="let option of assessmentTypes" [ngValue]="option">
              {{option}}
            </option>
          </select>
        </div>
      </td>
    </tr>
    <tr>
      <td>
        test_window_id
      </td>
      <td>
        <input class="input" type="number" [(ngModel)]="config.test_window_id" [disabled]="mode === PsychConfigModal.VIEW">
      </td>
    </tr>
    <tr>
      <td>
        reporting_subject
      </td>
      <td>
        <div class="select">
          <select [(ngModel)]="config.reporting_subject" [disabled]="mode === PsychConfigModal.VIEW">
            <option *ngFor="let option of [0, 1, 2, 3]" [ngValue]="option">
              {{option}}
            </option>
          </select>
        </div>
      </td>
    </tr>
  </table>

  <menu-bar [menuTabs]="psychConfigViews" [tabIdInit]="selectedPsychConfigView" (change)="selectPsychConfigView($event)"></menu-bar>

  <div [ngSwitch]="selectedPsychConfigView">
    <div *ngSwitchCase="PsychConfigView.CUT_POINTS">
      <h2>Cut Points</h2>

      <div *ngIf="config.assessment_type === PsychConfigAssessmentType.OSSLT">
        <div>
          <h3><b>EN</b></h3>
          <table>
            <tr>
              <td>
                Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not yet Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Not yet Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>

        <div>
          <hr>
          <h3><b>FR</b></h3>
          <table>
            <tr>
              <td>
                Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not yet Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Not yet Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>
      </div>

      <div *ngIf="config.assessment_type !== PsychConfigAssessmentType.OSSLT">
        <div>
          <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
          <table>
            <tr>
              <td>
                Level 4
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Level 4']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 3
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Level 3']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 2
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Level 2']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not enough evidence for Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.EN]['Not enough evidence for Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>

        <div *ngIf="isLangAssessment(config.assessment_type)">
          <hr>
          <h3><b>FR</b></h3>
          <table>
            <tr>
              <td>
                Level 4
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 4']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 3
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 3']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 2
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 2']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not enough evidence for Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Not enough evidence for Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.CUT_POINTS_ALT">
      <h2>Cut Points Alt</h2>

      <div *ngIf="config.assessment_type === PsychConfigAssessmentType.OSSLT">
        <div>
          <h3><b>EN</b></h3>
          <table>
            <tr>
              <td>
                Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not yet Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Not yet Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>

        <div>
          <hr>
          <h3><b>FR</b></h3>
          <table>
            <tr>
              <td>
                Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.FR]['Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not yet Successful
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.FR]['Not yet Successful']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>
      </div>

      <div *ngIf="config.assessment_type !== PsychConfigAssessmentType.OSSLT">
        <div>
          <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
          <table>
            <tr>
              <td>
                Level 4
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Level 4']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 3
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Level 3']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 2
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Level 2']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not enough evidence for Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points_alt[PsychConfigLang.EN]['Not enough evidence for Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>

        <div *ngIf="isLangAssessment(config.assessment_type)">
          <hr>
          <h3><b>FR</b></h3>
          <table>
            <tr>
              <td>
                Level 4
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 4']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 3
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 3']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 2
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 2']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
            <tr>
              <td>
                Not enough evidence for Level 1
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="config.cut_points[PsychConfigLang.FR]['Not enough evidence for Level 1']" [disabled]="mode === PsychConfigModal.VIEW">
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DOT_SCORES">
      <h2>Dot Scores</h2>

      <div>
        <div class="space-between">
          <h3><b *ngIf="isLangAssessment(config.assessment_type)">EN</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingDotScoreEN && !isAddingDotScoreEN">
            <button class="button is-danger" [disabled]="!isDotScoreENSelected()" (click)="deleteDotScoreEN()">Delete</button>
            <button class="button" [disabled]="!isDotScoreENSelected()" (click)="openDotScoreENConfig('edit')">Edit</button>
            <button class="button" (click)="openDotScoreENConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingDotScoreEN || isAddingDotScoreEN">
          <table>
            <tr>
              <td>
                dot
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreENInput.dot">
              </td>
            </tr>
            <tr>
              <td>
                value
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreENInput.value">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingDotScoreEN" (click)="saveDotScoreEN()">Update</button>
            <button class="button is-info" *ngIf="isAddingDotScoreEN" (click)="saveDotScoreEN()">Add</button>
            <button class="button" (click)="closeDotScoreENConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.dot_scores[PsychConfigLang.EN]"
            [gridOptions]="dotScoreENGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <div class="space-between">
          <h3><b>FR</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingDotScoreFR && !isAddingDotScoreFR">
            <button class="button is-danger" [disabled]="!isDotScoreFRSelected()" (click)="deleteDotScoreFR()">Delete</button>
            <button class="button" [disabled]="!isDotScoreFRSelected()" (click)="openDotScoreFRConfig('edit')">Edit</button>
            <button class="button" (click)="openDotScoreFRConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingDotScoreFR || isAddingDotScoreFR">
          <table>
            <tr>
              <td>
                dot
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreFRInput.dot">
              </td>
            </tr>
            <tr>
              <td>
                value
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreFRInput.value">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingDotScoreFR" (click)="saveDotScoreFR()">Update</button>
            <button class="button is-info" *ngIf="isAddingDotScoreFR" (click)="saveDotScoreFR()">Add</button>
            <button class="button" (click)="closeDotScoreFRConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.dot_scores[PsychConfigLang.FR]"
            [gridOptions]="dotScoreFRGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DOT_SCORES_ALT">
      <h2>Dot Scores Alt</h2>

      <div>
        <div class="space-between">
          <h3><b *ngIf="isLangAssessment(config.assessment_type)">EN</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingDotScoreAltEN && !isAddingDotScoreAltEN">
            <button class="button is-danger" [disabled]="!isDotScoreAltENSelected()" (click)="deleteDotScoreAltEN()">Delete</button>
            <button class="button" [disabled]="!isDotScoreAltENSelected()" (click)="openDotScoreAltENConfig('edit')">Edit</button>
            <button class="button" (click)="openDotScoreAltENConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingDotScoreAltEN || isAddingDotScoreAltEN">
          <table>
            <tr>
              <td>
                dot
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreAltENInput.dot">
              </td>
            </tr>
            <tr>
              <td>
                value
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreAltENInput.value">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingDotScoreAltEN" (click)="saveDotScoreAltEN()">Update</button>
            <button class="button is-info" *ngIf="isAddingDotScoreAltEN" (click)="saveDotScoreAltEN()">Add</button>
            <button class="button" (click)="closeDotScoreAltENConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.dot_scores_alt[PsychConfigLang.EN]"
            [gridOptions]="dotScoreAltENGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <div class="space-between">
          <h3><b>FR</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingDotScoreAltFR && !isAddingDotScoreAltFR">
            <button class="button is-danger" [disabled]="!isDotScoreAltFRSelected()" (click)="deleteDotScoreAltFR()">Delete</button>
            <button class="button" [disabled]="!isDotScoreAltFRSelected()" (click)="openDotScoreAltFRConfig('edit')">Edit</button>
            <button class="button" (click)="openDotScoreAltFRConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingDotScoreAltFR || isAddingDotScoreAltFR">
          <table>
            <tr>
              <td>
                dot
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreAltFRInput.dot">
              </td>
            </tr>
            <tr>
              <td>
                value
              </td>
              <td>
                <input class="input" type="number" [(ngModel)]="dotScoreAltFRInput.value">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingDotScoreAltFR" (click)="saveDotScoreAltFR()">Update</button>
            <button class="button is-info" *ngIf="isAddingDotScoreAltFR" (click)="saveDotScoreAltFR()">Add</button>
            <button class="button" (click)="closeDotScoreAltFRConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.dot_scores_alt[PsychConfigLang.FR]"
            [gridOptions]="dotScoreAltFRGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.CTT_FLAGS">
      <h2>CTT Flags</h2>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <table>
          <tr>
            <td>
              tooEasy
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.EN].tooEasy" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              tooDifficult
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.EN].tooDifficult" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              lowPB
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.EN].lowPB" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              lowCPB
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.EN].lowCPB" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
        </table>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <table>
          <tr>
            <td>
              tooEasy
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.FR].tooEasy" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              tooDifficult
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.FR].tooDifficult" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              lowPB
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.FR].lowPB" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              lowCPB
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.ctt_flags[PsychConfigLang.FR].lowCPB" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
        </table>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.REMOVE_FROM_SCORING">
      <h2>Remove From Scoring</h2>

      <div class="notification is-small" *ngIf="mode !== PsychConfigModal.VIEW">
        Please enter each item on a new line without commas.
      </div>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.remove_from_scoring[PsychConfigLang.EN]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
      
      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.remove_from_scoring[PsychConfigLang.FR]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.REMOVE_FROM_EQUATING">
      <h2>Remove From Equating</h2>

      <div class="notification is-small" *ngIf="mode !== PsychConfigModal.VIEW">
        Please enter each item on a new line without commas.
      </div>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.remove_from_equating[PsychConfigLang.EN]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
      
      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.remove_from_equating[PsychConfigLang.FR]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DUMMY_RESPONSES_ZERO">
      <h2>Dummy Responses Zero</h2>

      <div class="notification is-small" *ngIf="mode !== PsychConfigModal.VIEW">
        Please enter each item on a new line without commas.
      </div>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_responses_zero[PsychConfigLang.EN]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
      
      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_responses_zero[PsychConfigLang.FR]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DUMMY_RESPONSES_SECOND">
      <h2>Dummy Responses Second</h2>

      <div class="notification is-small" *ngIf="mode !== PsychConfigModal.VIEW">
        Please enter each item on a new line without commas.
      </div>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_responses_second[PsychConfigLang.EN]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
      
      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_responses_second[PsychConfigLang.FR]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DUMMY_N">
      <h2>Dummy N</h2>

      <div class="notification is-small" *ngIf="mode !== PsychConfigModal.VIEW">
        Please enter the number of responses for dummy responses zero and dummy responses second on two separate lines.
      </div>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_n[PsychConfigLang.EN]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
      
      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <textarea class="config-textarea" type="text" [(ngModel)]="config.dummy_n[PsychConfigLang.FR]" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.DIF_MODIFICATIONS">
      <h2>DIF Modifications</h2>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <table>
          <tr>
            <td>
              gender
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.EN].gender.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              esl
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.EN].esl.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              language
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.EN].language.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
        </table>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <table>
          <tr>
            <td>
              gender
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.FR].gender.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              esl
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.FR].esl.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              language
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.dif_modifications[PsychConfigLang.FR].language.sample_size" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
        </table>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.IRT_MODIFICATIONS">
      <h2>IRT Modifications</h2>

      <div>
        <h3 *ngIf="isLangAssessment(config.assessment_type)"><b>EN</b></h3>
        <table>
          <tr>
            <td>
              phase
            </td>
            <td>
              <div class="select">
                <select [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].phase" [disabled]="mode === PsychConfigModal.VIEW">
                  <option *ngFor="let option of ['UAT', 'ADMIN', 'FT']" [ngValue]="option">
                    {{option}}
                  </option>
                </select>
              </div>
            </td>
          </tr>
          <tr>
            <td>
              use_saved_params
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.EN].use_saved_params" (change)="config.irt_run_modifications[PsychConfigLang.EN].use_saved_params = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              a_parameter_lw_adjusted
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].a_parameter_lw_adjusted" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              change_scale_req_equating
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].change_scale_req_equating" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_dif_run
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.EN].is_dif_run" (change)="config.irt_run_modifications[PsychConfigLang.EN].is_dif_run = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_lw_mult
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.EN].is_lw_mult" (change)="config.irt_run_modifications[PsychConfigLang.EN].is_lw_mult = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_lin_use_op_score
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.EN].is_lin_use_op_score" (change)="config.irt_run_modifications[PsychConfigLang.EN].is_lin_use_op_score = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              irt_num_cycles
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].irt_num_cycles" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              irt_critical_value
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].irt_critical_value" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              guessing_param_override_03
            </td>
            <td>
              <div class="notification is-small" style="">
                Please enter each item on a new line without commas.
              </div>  
              <textarea class="config-textarea" type="text" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].guessing_param_override_03" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
            </td>
          </tr>
          <tr>
            <td>
              guessing_param_override_01
            </td>
            <td>
              <div class="notification is-small" style="">
                Please enter each item on a new line without commas.
              </div> 
              <textarea class="config-textarea" type="text" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.EN].guessing_param_override_01" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
            </td>
          </tr>
        </table>

        <div>
          <div class="space-between">
            <div>guessing_param_overrides</div>
            <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingGuessParamEN && !isAddingGuessParamEN">
              <button class="button is-danger" [disabled]="!isGuessParamENSelected()" (click)="deleteGuessParamEN()">Delete</button>
              <button class="button" [disabled]="!isGuessParamENSelected()" (click)="openGuessParamENConfig('edit')">Edit</button>
              <button class="button" (click)="openGuessParamENConfig('add')">Add</button>
            </div>
          </div>
          <div class="config-container" *ngIf="isEditingGuessParamEN || isAddingGuessParamEN">
            <table>
              <tr>
                <td>
                  question_id
                </td>
                <td>
                  <input class="input" type="text" [(ngModel)]="guessParamENInput.question_id">
                </td>
              </tr>
              <tr>
                <td>
                  value
                </td>
                <td>
                  <input class="input" type="number" [(ngModel)]="guessParamENInput.value">
                </td>
              </tr>
            </table>
            <div>
              <button class="button is-info" *ngIf="isEditingGuessParamEN" (click)="saveGuessParamEN()">Update</button>
              <button class="button is-info" *ngIf="isAddingGuessParamEN" (click)="saveGuessParamEN()">Add</button>
              <button class="button" (click)="closeGuessParamENConfig()">Cancel</button>
            </div>
          </div>
          <div class="grid-container">
            <ag-grid-angular
              class="ag-theme-alpine ag-grid-fullpage"
              [rowData]="config.irt_run_modifications[PsychConfigLang.EN].guessing_param_overrides"
              [gridOptions]="guessParamENGridOptions"
              [enableCellTextSelection]="true"
            ></ag-grid-angular>
          </div>
        </div>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <h3><b>FR</b></h3>
        <table>
          <tr>
            <td>
              phase
            </td>
            <td>
              <div class="select">
                <select [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].phase" [disabled]="mode === PsychConfigModal.VIEW">
                  <option *ngFor="let option of ['UAT', 'ADMIN', 'FT']" [ngValue]="option">
                    {{option}}
                  </option>
                </select>
              </div>
            </td>
          </tr>
          <tr>
            <td>
              use_saved_params
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.FR].use_saved_params" (change)="config.irt_run_modifications[PsychConfigLang.FR].use_saved_params = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              a_parameter_lw_adjusted
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].a_parameter_lw_adjusted" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              change_scale_req_equating
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].change_scale_req_equating" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_dif_run
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.FR].is_dif_run" (change)="config.irt_run_modifications[PsychConfigLang.FR].is_dif_run = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_lw_mult
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.FR].is_lw_mult" (change)="config.irt_run_modifications[PsychConfigLang.FR].is_lw_mult = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              is_lin_use_op_score
            </td>
            <td>
              <input type="checkbox" [ngModel]="config.irt_run_modifications[PsychConfigLang.FR].is_lin_use_op_score" (change)="config.irt_run_modifications[PsychConfigLang.FR].is_lin_use_op_score = $event.target.checked ? 1 : 0" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              irt_num_cycles
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].irt_num_cycles" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              irt_critical_value
            </td>
            <td>
              <input class="input" type="number" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].irt_critical_value" [disabled]="mode === PsychConfigModal.VIEW">
            </td>
          </tr>
          <tr>
            <td>
              guessing_param_override_03
            </td>
            <td>
              <div class="notification is-small" style="">
                Please enter each item on a new line without commas.
              </div> 
              <textarea class="config-textarea" type="text" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].guessing_param_override_03" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
            </td>
          </tr>
          <tr>
            <td>
              guessing_param_override_01
            </td>
            <td>
              <div class="notification is-small" style="">
                Please enter each item on a new line without commas.
              </div> 
              <textarea class="config-textarea" type="text" [(ngModel)]="config.irt_run_modifications[PsychConfigLang.FR].guessing_param_override_01" [disabled]="mode === PsychConfigModal.VIEW"></textarea>
            </td>
          </tr>
        </table>

        <div>
          <div class="space-between">
            <div>guessing_param_overrides</div>
            <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingGuessParamFR && !isAddingGuessParamFR">
              <button class="button is-danger" [disabled]="!isGuessParamFRSelected()" (click)="deleteGuessParamFR()">Delete</button>
              <button class="button" [disabled]="!isGuessParamFRSelected()" (click)="openGuessParamFRConfig('edit')">Edit</button>
              <button class="button" (click)="openGuessParamFRConfig('add')">Add</button>
            </div>
          </div>
          <div class="config-container" *ngIf="isEditingGuessParamFR || isAddingGuessParamFR">
            <table>
              <tr>
                <td>
                  question_id
                </td>
                <td>
                  <input class="input" type="text" [(ngModel)]="guessParamFRInput.question_id">
                </td>
              </tr>
              <tr>
                <td>
                  value
                </td>
                <td>
                  <input class="input" type="number" [(ngModel)]="guessParamFRInput.value">
                </td>
              </tr>
            </table>
            <div>
              <button class="button is-info" *ngIf="isEditingGuessParamFR" (click)="saveGuessParamFR()">Update</button>
              <button class="button is-info" *ngIf="isAddingGuessParamFR" (click)="saveGuessParamFR()">Add</button>
              <button class="button" (click)="closeGuessParamFRConfig()">Cancel</button>
            </div>
          </div>
          <div class="grid-container">
            <ag-grid-angular
              class="ag-theme-alpine ag-grid-fullpage"
              [rowData]="config.irt_run_modifications[PsychConfigLang.FR].guessing_param_overrides"
              [gridOptions]="guessParamFRGridOptions"
              [enableCellTextSelection]="true"
            ></ag-grid-angular>
          </div>
        </div>
      </div>
    </div>

    <div *ngSwitchCase="PsychConfigView.ADAPT_DATA">
      <h2>Adapt Data</h2>

      <div>
        <div class="space-between">
          <h3><b *ngIf="isLangAssessment(config.assessment_type)">EN</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingAdaptDataEN && !isAddingAdaptDataEN">
            <button class="button is-danger" [disabled]="!isAdaptDataENSelected()" (click)="deleteAdaptDataEN()">Delete</button>
            <button class="button" [disabled]="!isAdaptDataENSelected()" (click)="openAdaptDataENConfig('edit')">Edit</button>
            <button class="button" (click)="openAdaptDataENConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingAdaptDataEN || isAddingAdaptDataEN">
          <table>
            <tr>
              <td>
                question_id
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataENInput.question_id">
              </td>
            </tr>
            <tr>
              <td>
                raw_score
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataENInput.raw_score">
              </td>
            </tr>
            <tr>
              <td>
                adapted_score
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataENInput.adapted_score">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingAdaptDataEN" (click)="saveAdaptDataEN()">Update</button>
            <button class="button is-info" *ngIf="isAddingAdaptDataEN" (click)="saveAdaptDataEN()">Add</button>
            <button class="button" (click)="closeAdaptDataENConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.adapt_data[PsychConfigLang.EN]"
            [gridOptions]="adaptDataENGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>

      <div *ngIf="isLangAssessment(config.assessment_type)">
        <hr>
        <div class="space-between">
          <h3><b>FR</b></h3>
          <div class="action-buttons-container" *ngIf="mode !== PsychConfigModal.VIEW && !isEditingAdaptDataFR && !isAddingAdaptDataFR">
            <button class="button is-danger" [disabled]="!isAdaptDataFRSelected()" (click)="deleteAdaptDataFR()">Delete</button>
            <button class="button" [disabled]="!isAdaptDataFRSelected()" (click)="openAdaptDataFRConfig('edit')">Edit</button>
            <button class="button" (click)="openAdaptDataFRConfig('add')">Add</button>
          </div>
        </div>
        <div class="config-container" *ngIf="isEditingAdaptDataFR || isAddingAdaptDataFR">
          <table>
            <tr>
              <td>
                question_id
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataFRInput.question_id">
              </td>
            </tr>
            <tr>
              <td>
                raw_score
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataFRInput.raw_score">
              </td>
            </tr>
            <tr>
              <td>
                adapted_score
              </td>
              <td>
                <input class="input" type="text" [(ngModel)]="adaptDataFRInput.adapted_score">
              </td>
            </tr>
          </table>
          <div>
            <button class="button is-info" *ngIf="isEditingAdaptDataFR" (click)="saveAdaptDataFR()">Update</button>
            <button class="button is-info" *ngIf="isAddingAdaptDataFR" (click)="saveAdaptDataFR()">Add</button>
            <button class="button" (click)="closeAdaptDataFRConfig()">Cancel</button>
          </div>
        </div>
        <div class="grid-container">
          <ag-grid-angular
            class="ag-theme-alpine ag-grid-fullpage"
            [rowData]="config.adapt_data[PsychConfigLang.FR]"
            [gridOptions]="adaptDataFRGridOptions"
            [enableCellTextSelection]="true"
          ></ag-grid-angular>
        </div>
      </div>
    </div>
  </div>

  <div style="display:flex; flex-direction:row; margin-top:1.25em; margin-bottom:1.5em; justify-content: right;">
    <button class="button is-info" (click)="save()" [class.is-loading]="isSavingConfig" *ngIf="mode !== PsychConfigModal.VIEW">Save</button>
    <button class="button" (click)="cancel()">Cancel</button>
  </div>
</div>
