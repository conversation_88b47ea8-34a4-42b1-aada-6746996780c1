<h1 class="title">
    Multi-Factor Authentication (MFA) Requirements
</h1>
<p>
    You can configure MFA requirements for each role in the system. 
    If you enable M<PERSON> for a role, users with that role will be required to use MFA to log in unless they are exempted.
</p>
<p *ngIf="IS_MFA_DISABLED">
    <strong>MFA is currently disabled for the entire system.</strong>
</p>
<hr>
<div class="block" *ngFor="let role of u_role_types">
    <h2 class="subtitle">
        <i class="fas fa-user"></i>&nbsp;
        {{role.name}}
    </h2>
    <p>
        {{role.description}}
    </p>
    <button class="button is-small" [class.is-info]="role.requires_mfa" (click)="toggleMfaRequired(role)">
        {{role.requires_mfa ? 'Disable' : 'Enable'}} MFA Requirement
    </button>
</div>
<div class="buttons">
    <button class="button is-info" (click)="saveMfaRequirements()" [disabled]="changedRoles.length === 0">
        Save
    </button>
    <button class="button" (click)="closeModal()">
        Cancel
    </button>
</div>