import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TcModalMfaRequirementsComponent } from './tc-modal-mfa-requirements.component';

describe('TcModalMfaRequirementsComponent', () => {
  let component: TcModalMfaRequirementsComponent;
  let fixture: ComponentFixture<TcModalMfaRequirementsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ TcModalMfaRequirementsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TcModalMfaRequirementsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
