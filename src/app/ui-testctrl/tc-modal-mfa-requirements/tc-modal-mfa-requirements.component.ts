import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { PageModalController } from 'src/app/ui-partial/page-modal.service';

interface I_UserRoleTypes {
  id: number,
  name: string,
  description: string
  role_type: string,
  requires_mfa: number,
  is_shown: number,
  is_changed?: boolean
}

@Component({
  selector: 'tc-modal-mfa-requirements',
  templateUrl: './tc-modal-mfa-requirements.component.html',
  styleUrls: ['./tc-modal-mfa-requirements.component.scss']
})
export class TcModalMfaRequirementsComponent implements OnInit {
  @Input() pageModal: PageModalController;

  public IS_MFA_DISABLED: number;
  public u_role_types: I_UserRoleTypes[] = [];

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    this.initRoleTypes()
  }

  private async initRoleTypes() {
    const {u_role_types, IS_MFA_DISABLED} = await this.auth.apiFind(this.routes.TEST_CTRL_U_ROLE_TYPES)
    this.u_role_types = u_role_types
    this.IS_MFA_DISABLED = IS_MFA_DISABLED
  }

  /** Any model change on roles should trigger is_changed */
  public toggleMfaRequired(role: I_UserRoleTypes) {
    role.requires_mfa = role.requires_mfa === 1 ? 0 : 1
    role.is_changed = true
  }

  /**
   * Patches the roles that have been changed, currently only for requires_mfa
   */
  public async saveMfaRequirements() {
    if(this.changedRoles.length == 0) {
      this.loginGuard.quickPopup('No changes to save')
      return this.closeModal()
    }

    for(let role of this.changedRoles) {
      await this.auth.apiPatch(this.routes.TEST_CTRL_U_ROLE_TYPES, role.role_type, { 
        requires_mfa: role.requires_mfa 
      })
    }

    this.loginGuard.quickPopup('Saved successfully')
    this.closeModal()
  }

  public closeModal() {
    this.pageModal.closeModal()
  }

  public get changedRoles() {
    return this.u_role_types.filter(role => role.is_changed)
  }
}
