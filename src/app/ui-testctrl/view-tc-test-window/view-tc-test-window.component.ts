import * as moment from 'moment-timezone';
import * as _ from 'lodash';
import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { MyCtrlOrgService } from '../my-ctrl-org.service';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { ScrollService } from '../../core/scroll.service';
import { LangService } from '../../core/lang.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';
import { ITCDashboardSummaryRes } from '../types/api-response';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { CalendarOptions, FullCalendarComponent } from '@fullcalendar/angular';
import dayGridPlugin from '@fullcalendar/daygrid';
import enLocale from '@fullcalendar/core/locales/en-gb';
import frLocale from '@fullcalendar/core/locales/fr-ca';
import { IEditSaveCtrl, EditSaveFormlet, IMatrixDef, generateMatrixFormControls, renderMatrixFCKey, applyPropPatches } from '../../ui-partial/edit-save/edit-save.component';
import { FormControl } from '@angular/forms';
import { dateToTimeInputStr, dateAndTimeToDbDate, UTCTimetoESTTime, timeToDbTime } from '../../ui-partial/input-time/input-time.component';
import { dateStrToDateInputStr } from '../../ui-partial/input-date/input-date.component';
import { arrToMap } from '../../api/data.service';
import { UserRoles } from 'src/app/api/models/roles';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import {TEST_WINDOW_TYPE} from './../panel-student-lookup/panel-student-lookup.component'

interface ITestWindow {
  id: number;
  title: string;
  is_active: number;
  date_start: string;
  date_end: string;
  test_design_id: number;
  notes: string;
  created_on: string;
  create_by_uid: number;
  last_activated_on: string;
  last_activated_by_uid: number;
  is_allow_new_ts: number;
  is_allow_new_bookings: number;
  is_allow_results_tt: number;
  is_multi_attempt: number;
  num_attempts: number;
  attempts_intvl: string;
  is_invig_unsubmit: number;
  is_invig_taketest: number;
  num_sessions_created?: any;
  num_sessions_administered?: any;
  is_allow_test_centre: number;
  is_allow_classroom: number;
  is_allow_remote: number;
  is_allow_mobile_tether: number;
  is_allow_video_conference: number;
  is_require_stu_validate: number;
  max_stu_validate_attempts: number;
  ap_price_per_student: number;
  ap_description: string;
  ap_created_on: string;
  ap_created_by_uid : number;
  reg_lock_on : string;
  AutoApproveDates?: string;
  auto_close_start_on: string;
  auto_close_end_on: string;
  auto_close_grace_period_m: number
  auto_close_default_extension_m: number;
  auto_approve_unsubmit_time_limit_h: number,
  is_end_window_isr: number
}

export enum TWAttr {
  title = 'title',
  is_active = 'is_active',
  date_start = 'date_start',
  date_end = 'date_end',
  test_design_id = 'test_design_id',
  notes = 'notes',
  created_on = 'created_on',
  create_by_uid = 'create_by_uid',
  last_activated_on = 'last_activated_on',
  last_activated_by_uid = 'last_activated_by_uid',
  is_allow_new_ts = 'is_allow_new_ts',
  is_allow_new_bookings = 'is_allow_new_bookings',
  is_allow_results_tt = 'is_allow_results_tt',
  is_multi_attempt = 'is_multi_attempt',
  num_attempts = 'num_attempts',
  attempts_intvl = 'attempts_intvl',
  is_invig_unsubmit = 'is_invig_unsubmit',
  is_invig_taketest = 'is_invig_taketest',
  is_archived = 'is_archived',
  num_sessions_created = 'num_sessions_created',
  num_sessions_administered = 'num_sessions_administered',
  duration_m = 'duration_m',
  is_allow_test_centre = 'is_allow_test_centre',
  is_allow_classroom = 'is_allow_classroom',
  is_allow_remote = 'is_allow_remote',
  is_allow_mobile_tether = 'is_allow_mobile_tether',
  is_allow_video_conference = 'is_allow_video_conference',
  is_require_stu_validate = 'is_require_stu_validate',
  max_stu_validate_attempts = 'max_stu_validate_attempts',
  type_slug = 'type_slug',
  window_code = 'window_code',
  is_req_isr_appro = 'is_req_isr_appro',
  is_qa = 'is_qa',
  is_bg = 'is_bg',
  irt_ready = 'irt_ready',
  is_closed = 'is_closed',
  show_inactive_tw_report = 'show_inactive_tw_report',
  auto_close_start_on = 'auto_close_start_on',
  auto_close_end_on = 'auto_close_end_on',
  auto_close_grace_period_m = 'auto_close_grace_period_m',
  auto_close_default_extension_m = 'auto_close_default_extension_m',
  academic_year = 'academic_year',
  is_allow_appeals = 'is_allow_appeals',
  show_appeal_result = 'show_appeal_result',
  is_alt_file_access = 'is_alt_file_access',
  show_report_to_Board = 'show_report_to_Board',
  auto_approve_unsubmit_time_limit_h = 'auto_approve_unsubmit_time_limit_h',
  is_allow_session_schedule = 'is_allow_session_schedule',
  is_allow_multiple_module_check = 'is_allow_multiple_module_check',
  is_allow_check_ctrl_summaries = 'is_allow_check_ctrl_summaries',
  show_isr_num_q = 'show_isr_num_q',
  unsubmit_request_opt2_link_to_acc = 'unsubmit_request_opt2_link_to_acc',
  isr_version = 'isr_version',
  is_end_window_isr = 'is_end_window_isr'
}

export enum APAttr {
  ap_price_per_student = 'ap_price_per_student',
  ap_description = 'ap_description',
  ap_created_on = 'ap_created_on',
  ap_created_by_uid = 'ap_created_by_uid'
}



export enum TWMainMenuTab {
  CONFIGURE = 'CONFIGURE',
  MONITOR = 'MONITOR',
  SCORING_WINDOWS = 'SCORING_WINDOWS',
}

export enum TWDateFormProps {
  START_DATE = 'START_DATE',
  START_TIME = 'START_TIME',
  END_DATE = 'END_DATE',
  END_TIME = 'END_TIME',
  REG_LOCK_ON_DATE = 'REG_LOCK_ON_DATE',
  REG_LOCK_ON_TIME = 'REG_LOCK_ON_TIME',
  AUTO_SUBMISSION_START_DATE = 'AUTO_SUBMISSION_START_DATE',
  AUTO_SUBMISSION_END_DATE = 'AUTO_SUBMISSION_END_DATE',
  AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_H = 'AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_H',
  TIME_RANGE_START = 'TIME_RANGE_START',
  TIME_RANGE_END = 'TIME_RANGE_END',

}

export enum TWTestSessionDueOn {
  DUE_ON = 'DUE_ON',
}

enum TestDesignProps {
  id = 'id',
  created_on = 'created_on',
  created_by_uid = 'created_by_uid',
  last_updated_on = 'last_updated_on',
  last_updated_by_uid = 'last_updated_by_uid',
  name = 'name',
}
export enum TDInfoProps {
  name = 'name',
  created_on = 'created_on',
  created_by = 'created_by',
  last_updated_on = 'last_updated_on',
  last_updated_by = 'last_updated_by_uid',
}

export enum AllowInterval {
  DAY = 'DAY',
  WEEK = 'WEEK',
  WINDOW = 'WINDOW',
}

const NAME_FORM_COLS = [
  TWAttr.title,
  TWAttr.notes
];

const PRICE_FORM_COLS = [
  APAttr.ap_price_per_student,
  APAttr.ap_description,
];

const AUTO_SUBMISSION_FORM_COLS = [
  TWAttr.auto_close_grace_period_m,
  TWAttr.auto_close_default_extension_m,
];

const AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_FORM_COLS = [
  TWAttr.auto_approve_unsubmit_time_limit_h,
];

export enum Modal {
  COPY_TWTDAR_SETTING = "COPY_TWTDAR_SETTING",
  COPY_ASSESSMENT_TWTD_COMPONENTS = "COPY_ASSESSMENT_TWTD_COMPONENTS",
  COPY_SCHOOL_SEMESTERS = "COPY_SCHOOL_SEMESTERS",
  REGENERATE_CSV_REPORT_BY_BOARD = "REGENERATE_CSV_REPORT_BY_BOARD",
}

@Component({
  selector: 'view-tc-test-window',
  templateUrl: './view-tc-test-window.component.html',
  styleUrls: ['./view-tc-test-window.component.scss']
})
export class ViewTcTestWindowComponent implements OnInit, OnDestroy {

  // services
  constructor(
    private loginGuard: LoginGuardService, //
    private router: Router,
    private route: ActivatedRoute,
    private breadcrumbsService: BreadcrumbsService,
    private scrollService: ScrollService,
    private lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    public myCtrlOrg: MyCtrlOrgService,
    private pageModalService: PageModalService,
  ) { }

  @ViewChild('calendar', { static: false }) calendarComponent: FullCalendarComponent;
  calendarOptions: CalendarOptions = {
    initialView: 'dayGridMonth',
    events: [],
    eventTimeFormat: { // like '14:30:00'
      hour: '2-digit',
      minute: '2-digit',
      meridiem: true,
    },
    locale: this.getCalendarLocale()
  };
  // enum transfers
  TWMainMenuTab = TWMainMenuTab;
  TWAttr = TWAttr;
  TDInfoProps = TDInfoProps;
  APAttr = APAttr;

  // api vars
  testWindowSummary: Partial<ITestWindow>;

  // rendered vars
  public breadcrumb: IBreadcrumbRoute[];
  public isInited: boolean;
  public testWindowId: number;
  public isNewTestWindow = false;
  public isLoaded: boolean;
  public currentMainMenuTab = TWMainMenuTab.CONFIGURE;
  public mainMenuTabs: IMenuTabConfig<TWMainMenuTab>[] = [
    {id: TWMainMenuTab.CONFIGURE, caption: 'Configure'},
    {id: TWMainMenuTab.MONITOR, caption: 'Monitor'},
  ];
  allowedIntervals = [
    {id: AllowInterval.DAY, caption: 'day' },
    {id: AllowInterval.WEEK, caption: 'week' },
    {id: AllowInterval.WINDOW, caption: 'window' },
  ];
  nameFormTableDef: IMatrixDef = {
    rows: NAME_FORM_COLS, // cols as rows... this is because they are columns in the database table, but they are rows for the UI dsiplay
    cols: this.lang.getSupportedLanguages(),
  };
  priceFormTableDef: IMatrixDef = {
    rows: PRICE_FORM_COLS, // cols as rows... this is because they are columns in the database table, but they are rows for the UI dsiplay
    cols: [''],
  };
  autoSubmissionTableDef: IMatrixDef = {
    rows: AUTO_SUBMISSION_FORM_COLS,
    cols:[null]
  }
  autoApproveUnsubmitTimeLimitTableDef: IMatrixDef = {
    rows: AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_FORM_COLS,
    cols:[null]
  }
  testDesignRef = new Map();

  //Auto Submission toggle "Turn ON Auto Submission"
  setAutoSubmissionOn: boolean = false;
  //Auto Approve Unsubmit Time Limit toggle "Set/Unset auto approve unsubmit time limit"
  setAutoApproveUnsubmitTimeLimit = false

  setMissingCsvBoardOnly = true
  showCsvGenerationRecords = false
  bulkCsvReportGenerationRecords = []
  csvSingleBoardMident:number = null
  regenerateCSVbrdMidents:number[] = []

  // formlets
  editName: EditSaveFormlet;
  editPrice: EditSaveFormlet;
  editTestDesign: EditSaveFormlet;
  editAllowedAttempts: EditSaveFormlet;
  editDates: EditSaveFormlet;
  editISRVersion: EditSaveFormlet;
  editDuration: EditSaveFormlet;
  editAutoSubmission: EditSaveFormlet;
  editMaxStuValideAttempts: EditSaveFormlet;
  editAutoApproveUnsubmit:EditSaveFormlet;
  editPreCreateTestSessionDueOn:EditSaveFormlet;
  editRtAuditCronJob: EditSaveFormlet;

  pageModal: PageModalController;
  Modal = Modal;

  // calendar
  eventTimeFormat: { // like '14:30:00'
    hour: '2-digit',
    minute: '2-digit',
    meridiem: true
  };

  // logical vars
  private routeSub: Subscription;
  private myCtrlOrgSub: Subscription;

  public UserRoles = UserRoles;

  testDesignsAvail;

  assessmentComponentRecord;
  testWindowTdComponentRecord;
  componentReportingCategoryRecord;
  testWindowPreCreatedTestSessions;
  schoolSemesterRecord;
  markingWindowsRecord;

  // init / destroy
  ngOnInit() {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.scrollService.scrollToTop();
    this.loginGuard.activate([AccountType.TEST_CTRL]);
    this.routeSub = this.route.params.subscribe(routeParams => {
      const testWindowId = routeParams['testWindowId'];
      const test_ctrl_group_id = routeParams['test_ctrl_group_id'];
      if (testWindowId === null || testWindowId === undefined) {
        this.isNewTestWindow = true;
      } else {
        this.testWindowId = 1 * testWindowId;
        this.isNewTestWindow = false;
      }

      // register the new test window focus for the more generalized functions
      this.myCtrlOrg.selectedWindow = {
        id: testWindowId,
        test_ctrl_group_id
      };

      this.breadcrumb = [
        this.breadcrumbsService.TESTCTRL_DASHBOARD(),
        this.breadcrumbsService._CURRENT('Test Window', this.router.url),
      ];
      this.initRouteView();
    });
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    if (this.myCtrlOrgSub) {
      this.myCtrlOrgSub.unsubscribe();
    }
  }

  initRouteView() {
    this.initForms();
    this.isInited = false;
    this.myCtrlOrgSub = this.myCtrlOrg.sub().subscribe(orgInfo => {
      if (!this.isInited) {
        this.isInited = true;
        if (this.isNewTestWindow) {
          // maybe load some defaults here...
          this.testWindowSummary = {};
          this.isLoaded = true;
        } else {
          this.loadSummaryData();
          this.loadTestDesignData();
          this.loadMonitoringData();
          this.getCsvBulkReportGenerationProgress();
        }
      }
    });
  }

  initForms() {

    this.editName = new EditSaveFormlet(
      generateMatrixFormControls(this.nameFormTableDef),
      this.saveNameFormData
    );

    this.editPrice = new EditSaveFormlet(
      generateMatrixFormControls(this.priceFormTableDef),
      this.savePriceFormData
    );

    this.editTestDesign = new EditSaveFormlet(
      {
        [TWAttr.test_design_id]: new FormControl()
      },
      this.saveTestDesignFormData
    );

    this.editAllowedAttempts = new EditSaveFormlet(
      {
        [TWAttr.num_attempts]: new FormControl(),
        [TWAttr.attempts_intvl]: new FormControl(),
      },
      this.saveAllowedAttempts
    );

    this.editMaxStuValideAttempts = new EditSaveFormlet(
      {
        [TWAttr.max_stu_validate_attempts]: new FormControl(),
      },
      this.saveMaxStuValideAttempts
    );
    this.editISRVersion = new EditSaveFormlet(
      {
        [TWAttr.isr_version]: new FormControl(),
      },
      this.saveISRVersion
    );
    this.editDuration = new EditSaveFormlet(
      {
        [TWAttr.duration_m]: new FormControl(),
      },
      this.saveDuration
    );
    this.editDates = new EditSaveFormlet(
      {
        [TWDateFormProps.START_DATE]: new FormControl(),
        [TWDateFormProps.START_TIME]: new FormControl(),
        [TWDateFormProps.END_DATE]: new FormControl(),
        [TWDateFormProps.END_TIME]: new FormControl(),
        [TWDateFormProps.REG_LOCK_ON_DATE]: new FormControl(),
        [TWDateFormProps.REG_LOCK_ON_TIME]: new FormControl(),
      },
      this.saveDates
      );
    const autoSubmissionMatrixControls = generateMatrixFormControls(this.autoSubmissionTableDef);
    this.editAutoSubmission = new EditSaveFormlet(
      {
        [TWDateFormProps.AUTO_SUBMISSION_START_DATE]: new FormControl(),
        [TWDateFormProps.AUTO_SUBMISSION_END_DATE]: new FormControl(),
        ...autoSubmissionMatrixControls
      },
      this.saveAutoSubmission
    );
    const AutoApproveUnsubmitMatrixControls = generateMatrixFormControls(this.autoApproveUnsubmitTimeLimitTableDef);
    this.editAutoApproveUnsubmit = new EditSaveFormlet(
      {
        [TWDateFormProps.AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_H]: new FormControl(),
        ...AutoApproveUnsubmitMatrixControls,
      },
      this.saveAutoApproveUnsubmitTimeLimit
    );
    this.editAutoApproveUnsubmit.editCancel = this.cancelAutoApproveUnsubmitTimeLimit

    this.editPreCreateTestSessionDueOn = new EditSaveFormlet(
      {
        [TWTestSessionDueOn.DUE_ON]: new FormControl(),
      },
      this.saveTSDueOn
    );
    this.editRtAuditCronJob = new EditSaveFormlet(
      {
        [TWDateFormProps.START_DATE]: new FormControl(),
        [TWDateFormProps.START_TIME]: new FormControl(),
        [TWDateFormProps.END_DATE]: new FormControl(),
        [TWDateFormProps.END_TIME]: new FormControl(),
        [TWDateFormProps.TIME_RANGE_START]: new FormControl(),
        [TWDateFormProps.TIME_RANGE_END]: new FormControl(),

      },
      this.saveRtAuditConfigData
    )
  }

  // API utility
  loadSummaryData() {
    return this.auth
      .apiGet(
        this.routes.TEST_CTRL_TEST_WINDOW_SUMMARY,
        this.testWindowId
      )
      .then( (res: any) => {
        this.testWindowSummary = res.returnValue;
        this.assessmentComponentRecord = res?.assessmentComponentRecord;
        this.componentReportingCategoryRecord = res?.componentReportingCategoryRecord;
        this.testWindowTdComponentRecord = res?.testWindowTdComponentRecord;
        this.testWindowPreCreatedTestSessions = res?.testWindowPreCreatedTestSessions;
        this.schoolSemesterRecord = res?.schoolSemesterRecord;
        this.markingWindowsRecord = res?.markingWindowsRecord;
        this.loadNameFormData();
        this.loadPriceFormData();
        // this.loadAutoSubmissionFormData();
        this.loadDateFormData();
        this.loadAutoSubmissionDateFormData();
        this.loadAutoApproveUnsubmitDateFormData();
        this.loadReattemptData();
        this.updateCalendarDisplay();
        this.handleScoringWindowTab();
        this.loadPreCreatedTestSessions();
        this.loadRtAuditConfigData()
        this.editTestDesign.getFC(TWAttr.test_design_id).setValue(this.testWindowSummary[TWAttr.test_design_id]);
        this.editISRVersion.getFC(TWAttr.isr_version).setValue(this.testWindowSummary[TWAttr.isr_version]);
        this.editDuration.getFC(TWAttr.duration_m).setValue(this.testWindowSummary[TWAttr.duration_m]);
        this.editMaxStuValideAttempts.getFC(TWAttr.max_stu_validate_attempts).setValue(this.testWindowSummary[TWAttr.max_stu_validate_attempts]);

        // check if it is already started
        const m_start = moment.tz(this.testWindowSummary[TWAttr.date_start], moment.tz.guess());
        const m_now = moment.tz();
        const isStarted = m_start.isBefore(m_now);
        if (isStarted) {
          this.currentMainMenuTab = TWMainMenuTab.MONITOR;
        } else {
          this.currentMainMenuTab = TWMainMenuTab.CONFIGURE;
        }
        this.isLoaded = true;
      });
  }

  loadMonitoringData() {

  }
  loadTestDesignData() {
    return this.auth
      .apiFind(
        this.routes.TEST_CTRL_TEST_WINDOW_TEST_DESIGN,
      )
      .then( (res: any) => {
        const {testDesigns, users} = res;
        const userRef = arrToMap(users.data, 'id');
        const getUserName = (user) => {
          if (user) {
            return user.first_name + ' ' + user.last_name;
          }
          return '';
        };
        testDesigns.data.forEach(testDesign => {
          const createdByUser = userRef.get(testDesign[TestDesignProps.created_by_uid]);
          const updatedByUser = userRef.get(testDesign[TestDesignProps.last_updated_by_uid]);
          const testDesignInfo = {
            [TDInfoProps.name]: testDesign[TestDesignProps.name],
            [TDInfoProps.created_on]: this.renderDateFromStr(testDesign[TestDesignProps.created_on]),
            [TDInfoProps.last_updated_on]: this.renderDateFromStr(testDesign[TestDesignProps.last_updated_on]),
            [TDInfoProps.created_by]: getUserName(createdByUser),
            [TDInfoProps.last_updated_by]: getUserName(createdByUser),
          };
          this.testDesignRef.set(+testDesign[TestDesignProps.id], testDesignInfo);
        });
        this.testDesignsAvail = _.sortBy( testDesigns.data, `-${TestDesignProps.last_updated_on}` );
      });
  }

  loadNameFormData() {
    this.nameFormTableDef.rows.forEach(tableProp => {
      const data = JSON.parse(this.testWindowSummary[tableProp] || '{}');
      this.nameFormTableDef.cols.forEach(langCode => {
        const fc = this.getNameFormFC(tableProp, langCode);
        fc.setValue(data[langCode]);
      });
    });
    console.log("-----", this.testWindowSummary)
  }

  loadPriceFormData() {
    this.priceFormTableDef.rows.forEach(tableProp => {
      const data = this.testWindowSummary[tableProp] || '';
      this.priceFormTableDef.cols.forEach(col => {
        const fc = this.getPriceFormFC(tableProp, col);
        fc.setValue(data);
      });
    });
  }

  loadDateFormData() {
    const start = this.testWindowSummary.date_start;
    const end = this.testWindowSummary.date_end;
    const reg_lock = this.testWindowSummary.reg_lock_on;
    this.getStartDateFc().setValue(dateStrToDateInputStr(start));
    this.getStartTimeFc().setValue(dateToTimeInputStr(start));
    this.getEndDateFc().setValue(dateStrToDateInputStr(end));
    this.getEndTimeFc().setValue(dateToTimeInputStr(end));
    this.getRegLockDateFc().setValue(dateStrToDateInputStr(reg_lock));
    this.getRegLockTimeFc().setValue(dateToTimeInputStr(reg_lock));
  }
  loadReattemptData() {
    const props = [
      TWAttr.num_attempts,
      TWAttr.attempts_intvl,
    ];
    props.forEach(prop => {
       this.editAllowedAttempts.getFC(prop).setValue(this.testWindowSummary[prop]);
    });
  }

  loadRtAuditConfigData() {
    this.auth
    .apiGet(
      this.routes.TEST_CTRL_TEST_WINDOW_AUDIT_CONFIG,
      this.testWindowId,
    ).then((res) =>{
      if(!res) return;
      const {start_date, end_date} = res;
      
      this.editRtAuditCronJob.getFC(TWDateFormProps.START_DATE).setValue(dateStrToDateInputStr(start_date));
      this.editRtAuditCronJob.getFC(TWDateFormProps.START_TIME).setValue(dateToTimeInputStr(start_date));

      this.editRtAuditCronJob.getFC(TWDateFormProps.END_DATE).setValue(dateStrToDateInputStr(end_date));
      this.editRtAuditCronJob.getFC(TWDateFormProps.END_TIME).setValue(dateToTimeInputStr(end_date));
      this.updateCalendarDisplay();
    })
  }

  isOvernightAudit = () => {
    const startTime = moment(this.getRtAuditStartTimeFc().value, 'HH:mm A');
    const endTime = moment(this.getRtAuditEndTimeFc().value, 'HH:mm A');

    return endTime.isBefore(startTime);
  }

  saveRtAuditConfigData = () => {
    const startDate = dateAndTimeToDbDate(this.getRtAuditStartDateFc().value, this.getRtAuditStartTimeFc().value);
    const endDate = dateAndTimeToDbDate(this.getRtAuditEndDateFc().value, this.getRtAuditEndTimeFc().value);
    if(startDate <= this.testWindowSummary.date_start){
      this.loginGuard.quickPopup('Ensure start date is after session start date');
      throw new Error("INVALID_INPUT");
    }
    if(startDate >= endDate){
      this.loginGuard.quickPopup('Ensure start date is before end date');
      throw new Error("INVALID_INPUT");
    }
    const data = {
      startDate, 
      endDate
    }
    return this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_AUDIT_CONFIG, data, {query: {test_window_id: this.testWindowId}}).then(() => {
      this.updateCalendarDisplay();
    });
  }

  saveNameFormData = () => {
    const patch: any = {};
    this.nameFormTableDef.rows.forEach(tableProp => {
      const propData: any = {};
      this.nameFormTableDef.cols.forEach(langCode => {
        const fc = this.getNameFormFC(tableProp, langCode);
        propData[langCode] = fc.value;
      });
      patch[tableProp] = JSON.stringify(propData);
    });
    return this.applyConfigPatch(patch);
  }

  savePriceFormData = () => {
    const patch: any = {};
    this.priceFormTableDef.rows.forEach(tableProp => {
      let propData: any;
      this.priceFormTableDef.cols.forEach(col => {
        const fc = this.getPriceFormFC(tableProp, col);
        propData =  fc.value;
      });
      patch[tableProp] = propData;
    });
     
    return this.applyPriceConfigPatch(patch);
  }

  saveTestDesignFormData = () => {
    const patch = {
      test_design_id: this.getTestDesignId()
    };
    return this.applyConfigPatch(patch);
  }

  saveAllowedAttempts = () => {
    const props = [
      TWAttr.num_attempts,
      TWAttr.attempts_intvl,
    ];
    const patch: any = {};
    props.forEach(prop => {
      patch[prop] = this.fcv(this.editAllowedAttempts.getFC(prop));
    });
    return this.applyConfigPatch(patch);
  }

  saveMaxStuValideAttempts = () => {
    const patch = {
      [TWAttr.max_stu_validate_attempts]: this.editMaxStuValideAttempts.getFC(TWAttr.max_stu_validate_attempts).value
    };
    return this.applyConfigPatch(patch)
  }

  saveISRVersion = () => {
    const patch = {
      [TWAttr.isr_version]: this.editISRVersion.getFC(TWAttr.isr_version).value
    };
    return this.applyConfigPatch(patch);
  }

  saveDuration = () => {
    const patch = {
      [TWAttr.duration_m]: this.editDuration.getFC(TWAttr.duration_m).value
    };
    return this.applyConfigPatch(patch);
  }

  // applyConfigPatch(patch:any)
  saveDates = () => {
    const getFcValue = (prop: string) => this.editDates.getFC(prop).value;
    const date_start = dateAndTimeToDbDate(getFcValue(TWDateFormProps.START_DATE), getFcValue(TWDateFormProps.START_TIME));
    const date_end = dateAndTimeToDbDate(getFcValue(TWDateFormProps.END_DATE), getFcValue(TWDateFormProps.END_TIME));
    const reg_lock_on = dateAndTimeToDbDate(getFcValue(TWDateFormProps.REG_LOCK_ON_DATE), getFcValue(TWDateFormProps.REG_LOCK_ON_TIME));
    const patch = {
      date_start,
      date_end,
      reg_lock_on
    };
    return this.applyConfigPatch(patch)
    .then(res => {
      this.updateCalendarDisplay();
    });
  }

  /**
   * handles the structuring of auto submission config data
   * @return: function call that will make api call to auto submission PATCH endpoint
   */
  saveAutoSubmission = () => {
    let patch = {}
    if(this.setAutoSubmissionOn){
      const getFcValue = (prop: string) => this.editAutoSubmission.getFC(prop).value;
      const auto_close_start_on = timeToDbTime(getFcValue(TWDateFormProps.AUTO_SUBMISSION_START_DATE));
      const auto_close_end_on = timeToDbTime(getFcValue(TWDateFormProps.AUTO_SUBMISSION_END_DATE));
      patch = {
        auto_close_start_on,
        auto_close_end_on,
      };
      this.autoSubmissionTableDef.rows.forEach(tableProp => {
        let propData: any;
        this.autoSubmissionTableDef.cols.forEach(col => {
          const fc = this.getAutoSubmissionFormFC(tableProp, col);
          propData =  fc.value;
        });
        patch[tableProp] = propData;
      });
    }
    return this.applyAutoSubmissionConfigPatch(patch);
  }

  /**
   * Save the change of auto_approve_unsubmit_time_limit_h value.
   */
  saveAutoApproveUnsubmitTimeLimit = () => {
    const patch = {};
    this.autoApproveUnsubmitTimeLimitTableDef.rows.forEach(tableProp => {
     let propData: any;
     this.autoApproveUnsubmitTimeLimitTableDef.cols.forEach(col => {
       const fc = this.getAutoApproveUnsubmitTimeLimitFromFc(tableProp, col);
       propData =  fc.value;
     });
     patch[tableProp] = propData;
   });
    return this.applyAutoApproveUnsubmitTimeLimitConfigPatch(patch);
 }

  /**
   * Cancel the change of auto_approve_unsubmit_time_limit_h value.
   * Reset UI back to original setting
   */
 cancelAutoApproveUnsubmitTimeLimit = () =>{
  this.loadAutoApproveUnsubmitDateFormData()
 }

  saveTogglePropNumberic(prop: string, val: number) {
    const patch = { [prop]: val, };
    return this.applyConfigPatch(patch);
  }

  onGoingSaves = new Map();
  isPropSaving(prop: string){
    return this.onGoingSaves.get(prop)
  }

  validateInput(input: string): boolean {
    const pattern: RegExp = /^\d{4}-\d{4}$/; // Regular expression pattern for the format yyyy-yyyy
    return pattern.test(input);
  }

  async patchPropValPrompt(prop: string) {
    let valPrompted = prompt(`Change "${prop}" from "${this.getPropString(prop)}" to :`);
    if (!valPrompted){
      if (confirm('Are you sure you want to blank this value?')){
        valPrompted = '';
      }
      else {
        return;
      }
    }
    const val = (''+valPrompted).trim();
    if(prop === 'academic_year' && valPrompted !== '') {
      if(!this.validateInput(val)) {
        this.loginGuard.quickPopup('Please insert correct format for academic year.');
        return;
      }
    }
    const patch = { [prop]: val, };
    this.onGoingSaves.set(prop, true);
    try {
      await this.applyConfigPatch(patch);
    }
    catch(e){
      alert('Could not save')
    }
    this.onGoingSaves.set(prop, false)
  }

  applyConfigPatch(patch: {[key: string]: any}) {
    return this.auth.apiPatch(
      this.routes.TEST_CTRL_TEST_WINDOW_CONFIG,
      this.testWindowId,
      patch
    )
    .then(res => {
      applyPropPatches(patch, this.testWindowSummary);
    });
  }

  applyPriceConfigPatch(patch: {[key: string]: any}) {
    //validate price foramt
    if(patch.ap_price_per_student && !/^\d{0,10}(\.\d{1,2})?$/.test(patch.ap_price_per_student)){
      alert(this.lang.tra("Assessment Price format should be like 123.45"));
      return Promise.resolve(null)
    }

    //return Promise.resolve(null)
     return this.auth.apiPatch(
       this.routes.TEST_CTRL_TEST_WINDOW_ASSESSMENT_PRICE_CONFIG,
       this.testWindowId,
       patch
     )
     .then(res => {
       applyPropPatches(patch, this.testWindowSummary);
     });
  }

  /**
   * handles auto submission api request to update auto submission info for specific test window id
   * @param patch config data
   */
  applyAutoSubmissionConfigPatch(patch: {[key: string]: any}) {
    return this.auth
    .apiPatch(this.routes.TEST_CTRL_TEST_WINDOW_AUTO_SUBMISSION_CONFIG, this.testWindowId, patch, {
      query: { auto_submission_ON: this.setAutoSubmissionOn }
    })
    .then(res => {
      applyPropPatches(patch, this.testWindowSummary);
    })
    .catch(err => {
      switch (err.message) {
        case 'MISSING_VALUE':
          this.loginGuard.quickPopup(this.lang.tra('test_ctrl_auto_submission_missing_values'));
          break
        case 'INVALID_VALUE':
          this.loginGuard.quickPopup(this.lang.tra('test_ctrl_auto_submission_invalid_value'));
          break
        case 'INVALID_GRACE_PERIOD_AND_EXTENSION_PERIOD':
          this.loginGuard.quickPopup(this.lang.tra('test_ctrl_invalid_grace_and_extension_period'));
          break
      }
      throw err;
    });
  }

  /**
   * When user toggle "Set/Unset auto approve unsubmit time limit", 
   * auto_approve_unsubmit_time_limit_h value need to update accordinary
   * and the UI need to update as well.
   */
  toggleAutoApproveUnsubmitTimeLimit() { 
    if(this.setAutoApproveUnsubmitTimeLimit){
      const fc = this.getAutoApproveUnsubmitTimeLimitFromFc(TWAttr.auto_approve_unsubmit_time_limit_h, null);
      fc.setValue(1);
      //if(!this.testWindowSummary.auto_approve_unsubmit_time_limit_h){
        //this.testWindowSummary.auto_approve_unsubmit_time_limit_h = 1
      //}
    }else{
      const fc = this.getAutoApproveUnsubmitTimeLimitFromFc(TWAttr.auto_approve_unsubmit_time_limit_h, null);
      fc.setValue(null);
    }
    //this.loadAutoApproveUnsubmitDateFormData()
  }

  /**
   *Call the API to save the change of auto_approve_unsubmit_time_limit_h value.
   */
  applyAutoApproveUnsubmitTimeLimitConfigPatch(patch: {[key: string]: any}) {
    return this.auth
    .apiPatch(this.routes.TEST_CTRL_TEST_WINDOW_AUTO_APPROVE_UNSUB_TIME_LIMIT_CONFIG, this.testWindowId, patch)
    .then(res => {
      applyPropPatches(patch, this.testWindowSummary);
      this.loadAutoApproveUnsubmitDateFormData()
    })
    .catch(err => {
      switch (err.message) {
        default:
          this.loginGuard.quickPopup(err.message);
          break  
      }
      throw err;
    });
  }  

  // Form Utility
  initNameForm() {
    return ;
  }

  // DOM utility
  getTestDesignInfoProp(testDesignId: number, prop: string) {
    const testDesignInfo = this.testDesignRef.get(+testDesignId);
    if (testDesignInfo) {
      return testDesignInfo[prop];
    }
  }

  updateCalendarDisplay() {
    const calendarEventDateFmt = 'YYYY-MM-DD';
    // Add test window dates
    const dateStart = this.renderDateFromStr(this.testWindowSummary.date_start, calendarEventDateFmt);
    const dateEnd = this.renderDateFromStr(this.testWindowSummary.date_end, calendarEventDateFmt);
    const calendarEvents = [
      { title: 'Test Window', start: dateStart, end: dateEnd, backgroundColor: '#7BB658' },
      { title: 'End', date: dateEnd, backgroundColor: '#5C5C5C'},
    ];
    //Add any associated scoring window dates
    this.markingWindowsRecord?.forEach(mw => {
      const dateStart = this.renderDateFromStr(mw.start_on, calendarEventDateFmt);
      const dateEnd = this.renderDateFromStr(mw.end_on, calendarEventDateFmt);
      const mwTitle = `Scoring window #${mw.id} (${mw.lang}) - ${mw.name}`
      calendarEvents.push(
        //@ts-ignore
        {title: mwTitle, start: dateStart, end: dateEnd, textColor: 'black', backgroundColor: '#bfe8a7'})
    })

    if(this.getRtAuditEndDateFc().value && this.getRtAuditStartDateFc().value){
      calendarEvents.push(
        this.getRtAuditCalendarInfo()
      );
    }

    this.calendarOptions.events = calendarEvents;
  }

  fcv(formControl: FormControl) {
    return formControl?.value;
  }
  getLangName(langCode: string) {
    return this.lang.getKnownLangName(langCode);
  }
  getNameFormFC(rowId: string, colId: string) {
    const fcKey = renderMatrixFCKey(rowId, colId);
    return this.editName.getFC(fcKey);
  }
  getPriceFormFC(rowId: string, colId: string) {
    const fcKey = renderMatrixFCKey(rowId, colId);
    return this.editPrice.getFC(fcKey);
  }

  /**
   * Strucutres auto submission keys into form control columns 
   */
  getAutoSubmissionFormFC(rowId: string, colId: string) {
    const fcKey = renderMatrixFCKey(rowId, colId);
    return this.editAutoSubmission.getFC(fcKey);
  }

  getAutoApproveUnsubmitTimeLimitFromFc(rowId: string, colId: string) {
    const fcKey = renderMatrixFCKey(rowId, colId);
    return this.editAutoApproveUnsubmit.getFC(fcKey);
  }

  getStartDateFc = () => this.editDates.getFC(TWDateFormProps.START_DATE);
  getStartTimeFc = () => this.editDates.getFC(TWDateFormProps.START_TIME);
  getEndDateFc = () => this.editDates.getFC(TWDateFormProps.END_DATE);
  getEndTimeFc = () => this.editDates.getFC(TWDateFormProps.END_TIME);
  getRegLockDateFc = () => this.editDates.getFC(TWDateFormProps.REG_LOCK_ON_DATE);
  getRegLockTimeFc = () => this.editDates.getFC(TWDateFormProps.REG_LOCK_ON_TIME);
  getAutoSubmissionStartTimeFc = () => this.editAutoSubmission.getFC(TWDateFormProps.AUTO_SUBMISSION_START_DATE);
  getAutoSubmissionEndTimeFc = () => this.editAutoSubmission.getFC(TWDateFormProps.AUTO_SUBMISSION_END_DATE);
  getTestDesignId = () => this.fcv(this.editTestDesign.getFC(TWAttr.test_design_id));
  getAutoApproveUnsubmitTimeLimitFc = () => this.editAutoApproveUnsubmit.getFC(TWDateFormProps.AUTO_APPROVE_UNSUBMIT_TIME_LIMIT_H);
  getPreCreateTestSessionFc = () => this.editPreCreateTestSessionDueOn.getFC(TWTestSessionDueOn.DUE_ON);
  getRtAuditStartDateFc = () => this.editRtAuditCronJob.getFC(TWDateFormProps.START_DATE);
  getRtAuditStartTimeFc = () => this.editRtAuditCronJob.getFC(TWDateFormProps.START_TIME);
  getRtAuditEndDateFc = () => this.editRtAuditCronJob.getFC(TWDateFormProps.END_DATE);
  getRtAuditEndTimeFc = () => this.editRtAuditCronJob.getFC(TWDateFormProps.END_TIME); 

  getTestWindowTitle() {
    return this.fcv(
      this.getNameFormFC(
        TWAttr.title,
        this.lang.c()
      )
    );
  }

  getTestWindowDateStart() {
    return this.renderDateFromStr(this.testWindowSummary.date_start);
  }

  getTestWindowDateEnd() {
    return this.renderDateFromStr(this.testWindowSummary.date_end);
  }

  getTestWindowRegLockOn() {
    return this.renderDateFromStr(this.testWindowSummary.reg_lock_on);
  }

  isTestDesignDefined() {
    const testDesignId = this.getCurrentTestDesignId();
    if (testDesignId) {
      return true;
    } else {
      return false;
    }
  }

  getCurrentTestDesignId() {
    return this.testWindowSummary.test_design_id;
  }

  getCurrentTestDesignName() {
    const testDesignId = this.getCurrentTestDesignId();
    const name = this.getTestDesignInfoProp(testDesignId, TDInfoProps.name);
    return name;
  }

  renderDateFromStr(dateString: string, format?: string) {
    if (!format) {
      format = this.lang.tra('datefmt_dashboard_long');
    }
    return moment.tz(dateString, moment.tz.guess()).format(format);
  }

  selectMainMenuTab(tabId: TWMainMenuTab) {
    if (this.currentMainMenuTab !== tabId) {
      if (this.currentMainMenuTab === TWMainMenuTab.CONFIGURE) {
        // warn
      }

      this.currentMainMenuTab = tabId;
    }
  }

  isPropNumericChecked(prop: string) {
    return (+this.testWindowSummary[prop]) === 1;
  }

  getPropString(prop: string) {
    return this.testWindowSummary?.[prop]
  }

  togglePropCheckedNumeric(prop: string) {
    this.loginGuard.confirmationReqActivate({
      caption: `Are you sure you want to update test window's "${prop}" setting? \n\n Change from "${this.testWindowSummary[prop]?'On':'Off'}" to "${!this.testWindowSummary[prop]?'On':'Off'}"`,
      confirm: () => {
        let val = +this.testWindowSummary[prop];
        if (val === 1) {
          val = 0;
        } else {
          val = 1;
        }
        this.saveTogglePropNumberic(prop, val).then(res => {
          // don't need to manually update the prop in "testWindowSummary" as it is done by the save method
        })
        .catch(err => this.handleTogglePropError(err.message));
      }
    });
    
  }

  /**
   * Handle errors returned for toggling test window properties
   * @param errMsg Message of the error returned from API
   */
  handleTogglePropError(errMsg){
    switch(errMsg){
      case 'TW_TYPE_SLUG_INVALID':
        return this.loginGuard.quickPopup('Error: Invalid Assessment Code in this test window.')
      case 'TW_TYPE_SLUG_NOT_FOUND':
        return this.loginGuard.quickPopup('Error: Missing Assessment Code in this test window.')
      default:
        return this.loginGuard.quickPopup(errMsg)
    }
  }

  selectRemoteDelivery(prop: string) {
    let val = +this.testWindowSummary[prop];
    let patch = {
      // [TWAttr.is_allow_mobile_tether]: 0,
      // [TWAttr.is_allow_video_conference]: 0,
    };
    if ((prop === TWAttr.is_allow_mobile_tether) && !val) {
      patch = {
        [TWAttr.is_allow_mobile_tether]: 1,
        [TWAttr.is_allow_video_conference]: 0,
      };
    } else if (prop === TWAttr.is_allow_video_conference && !val) {
      patch = {
        [TWAttr.is_allow_mobile_tether]: 0,
        [TWAttr.is_allow_video_conference]: 1,
      };
    } else {
      return;
    }
    return this.applyConfigPatch(patch);
  }

  archiveTestWindow() {
    const confirmationCode = 'ARCHIVE';
    const confirmation = prompt('Are you sure you would like to archive this test window? If so, please enter the command ARCHIVE followed by the test window id.');
    const confirmationTest = confirmationCode + this.testWindowId;
    if (confirmation === confirmationTest) {
      this.isLoaded = false;
      this.saveTogglePropNumberic(TWAttr.is_archived, 1).then(res => {
        this.router.navigate([
          this.lang.c(),
          AccountType.TEST_CTRL,
          'dashboard'
        ]);
      });
    } else if (confirmation) {
      alert('Action cancelled.');
    }
  }

  getIntervalCaption(id: AllowInterval) {
    let caption;
    this.allowedIntervals.forEach(interval => {
      if (interval.id === id) {
        caption = interval.caption;
      }
    });
    return caption;
  }


  getCalendarLocale() {
    if (this.lang.c() === 'fr') { return frLocale; } else { return enLocale; }
  }

  getDataDashboard() {
    return `/${this.lang.c()}/${AccountType.TEST_CTRLD}/dashboard/${this.testWindowId}`;
  }

  /**",
   * Use a warning guard before user change the attribue of the test window.
   * Currently only is_closed, show_appeal_result, and is_alt_file_access need a warning before changing the state.
   * @param twAttrName test window attribute name
   */
  async popUpWarningMessage(twAttrName) {

    // Find current value of the target attribute
    const attrCurrValue = this.isPropNumericChecked(TWAttr[twAttrName])

    // For setting is_alt_file_access false -> true, first do a check if unfinalized alt materials exist for assessment type
    let isAltMatUnfinalized;
    if (twAttrName == TWAttr.is_alt_file_access && !attrCurrValue){
      isAltMatUnfinalized = await this.checkAltMatUnfinalized()
    }

    // For setting is_alt_file_access true -> false, message depends on if tw is QA
    const isTwQa = this.isPropNumericChecked(TWAttr['is_qa']);

    // Map attribute changes to confirmation messages
    const msgByAttr = {
      [TWAttr.show_appeal_result]: {
        setFalse: "twattr_conf_false_show_appeal_result",
        setTrue: "twattr_conf_true_show_appeal_result"
      },
      [TWAttr.is_closed]: {
        setFalse: "twattr_conf_false_is_closed",
        setTrue: "twattr_conf_true_is_closed"
      },
      [TWAttr.is_alt_file_access]: {
        setFalse: isTwQa ? "twattr_conf_false_is_alt_file_access_is_qa" : "twattr_conf_false_is_alt_file_access",
        setTrue: isAltMatUnfinalized ? "twattr_conf_true_is_alt_file_access_warning" : "twattr_conf_true_is_alt_file_access"
      }
    }

    const confMsg = this.lang.tra(attrCurrValue ? msgByAttr[twAttrName].setFalse : msgByAttr[twAttrName].setTrue)

    // Proceed after confirmation
    this.loginGuard.confirmationReqActivate({
      caption: confMsg,
      confirm: () => {
        // setTimeout due to second confirmation modal
        setTimeout(() => {
          this.togglePropCheckedNumeric(twAttrName);
        }, 0);
      }
    });

  }

  /* Return if all alt version files and templates have been finalized for the assessment type of this test window */
 async checkAltMatUnfinalized():Promise<boolean>{
    const query = {is_finalized: 0}
    const res = await this.auth.apiGet(this.routes.TEST_CTRL_TEST_WINDOW_ALT_MATERIAL_ACCESS, this.testWindowId, {query})
    .catch(err => {
      this.handleTogglePropError(err.message)
      throw Error(err.msg)
    })
    const {fileRecords, templateRecords} = res;
    if (fileRecords.length || templateRecords.length) return true;
    return false;
  }

  cModal() { 
    return this.pageModal.getCurrentModal(); 
  }
  cmc() { return this.cModal().config; }

  copyTwtdarSettingModalStart(view: string) {
    const config = {};
    this.pageModal.newModal({
      type: view === Modal.COPY_TWTDAR_SETTING ? view : null,
      config,
      finish: this.copyTWConfigModalFinish
    });
  }

  copyTWConfigModalFinish = () => {
    this.pageModal.closeModal();
  }

  copyAssessmentTwtdComponentModalStart(view: string) {
    const config = {};
    this.pageModal.newModal({
      type: view === Modal.COPY_ASSESSMENT_TWTD_COMPONENTS ? view : null,
      config,
      finish: this.copyTWConfigModalFinish
    });
  }

  copySchoolSemesterModalStart(view: string) {
    const config = {};
    this.pageModal.newModal({
      type: view === Modal.COPY_SCHOOL_SEMESTERS ? view : null,
      config,
      finish: this.copyTWConfigModalFinish
    });
  }

  reloadTwtdar:boolean = false;

  copySourceTWConfigToDestinationTW(testWindowId: number) {
    const sourceTestWindowId = testWindowId;
    const destinationTestWindowId = this.testWindowId;
    let apiPath = '/public/test-ctrl/test-window/twtdar-replication';

    this.auth.apiCreate(apiPath, {sourceTestWindowId, destinationTestWindowId}, this.myCtrlOrg.constructPermissionsParams())
    .then(res => {
      this.reloadTwtdar = !!res?.currentTwTwtdarRecord[0];
      this.pageModal.closeModal()
    })
    .catch(err => {
      this.handleCopyTwConfigErrorMsg(err.message);
      this.pageModal.closeModal()
    });
  }

  copySourceSemesterTWConfigToDestinationTW(testWindowId: number) {
    const sourceTestWindowId = testWindowId;
    const destinationTestWindowId = this.testWindowId;
    let apiPath = '/public/test-ctrl/test-window/school-semester-replication';
    this.auth.apiCreate(apiPath, {sourceTestWindowId, destinationTestWindowId}, this.myCtrlOrg.constructPermissionsParams())
    .then(res => {
      this.schoolSemesterRecord = res.schoolSemesterRecord
      this.loginGuard.quickPopup("The semester records for current test window are created successfully.")
      this.pageModal.closeModal()
    })
    .catch(err => {
      this.handleCopyTwConfigErrorMsg(err.message);
      this.pageModal.closeModal()
    });
  }

  copySourceAssessmentTwtdComponentToDestinationTW(testWindowId: number) {
    const sourceTestWindowId = testWindowId;
    const destinationTestWindowId = this.testWindowId;

    this.auth.apiCreate(
      '/public/test-ctrl/test-window/assessment-twtd-component-replication',
      {sourceTestWindowId, destinationTestWindowId},
      this.myCtrlOrg.constructPermissionsParams()
    )
    .then(res => {
      this.assessmentComponentRecord = res.assessmentComponentRecord
      this.testWindowTdComponentRecord = res.testWindowTdComponentRecord
      this.componentReportingCategoryRecord = res.componentReportingCategoryRecord
      this.loginGuard.quickPopup("The assessment_components, test_window_td_components and component_reporting_category records for current test window are created successfully.")
      this.pageModal.closeModal()
    })
    .catch(err => {
      this.handleCopyTwConfigErrorMsg(err.message);
      this.pageModal.closeModal()
    });
  }

  handleCopyTwConfigErrorMsg(msg:string) {
    if (msg === "TEST_WINDOW_NOT_EXIST") {
      this.loginGuard.quickPopup("Please insert the correct source test window id");
    } else if (msg === "RECORD_EXISTED_FOR_TEST_WINDOW"){
      this.loginGuard.quickPopup("Records already set up for this test window.")
    } else if (msg === "TEST_WINDOW_TYPE_SLUG_NULL") {
      this.loginGuard.quickPopup("The source and current test window type slug must not be empty");
    } else if (msg === "TEST_WINDOW_TYPE_SLUG_NOT_MATCH") {
      this.loginGuard.quickPopup("The source and current test window type slug don't match.");
    } else if (msg === "DEST_TW_TWTDAR_EXIST") {
      this.loginGuard.quickPopup("Current test window already has twtdar.");
    } else if (msg === "TEST_WINDOW_TYPE_SLUG_NOT_AVAILABLE") {
      this.loginGuard.quickPopup("Currently only Primary and Junior need to be set up for assessment_component and test_window_td_components");
    } else if (msg === "TEST_WINDOW_NO_ACADEMIC_YEAR") {
      this.loginGuard.quickPopup("Currently the test window doesn't have an academic year, please fill it out first");
    }
  }

  pjTypeSlugs = ['EQAO_G3P', 'EQAO_G6J'];

  isTwPj(){
    const typeSlug = this.getPropString(TWAttr.type_slug)
    return this.pjTypeSlugs.indexOf(typeSlug) !== -1;
  }
  isCopyAvailable() {
    return this.isTwPj();
  }
  
  /** Add menu tab for scoring windows if it's test window where it applies */
  handleScoringWindowTab(){
    console.log(this.testWindowSummary)
    const twTypesWithScoring = [TEST_WINDOW_TYPE.EQAO_G10L, TEST_WINDOW_TYPE.EQAO_G3P, TEST_WINDOW_TYPE.EQAO_G6J];
    const typeSlug = this.getPropString(TWAttr.type_slug)
    if (!typeSlug) return;
    const isTwHasScoring = twTypesWithScoring.includes(typeSlug)
    if (isTwHasScoring){
      this.mainMenuTabs.push(
        {id: TWMainMenuTab.SCORING_WINDOWS, caption: 'Scoring Windows'},
      )
    }
  }
  
  /**",
   * Auto approve all the school admin sign off for the student data in this test window.
   * It will also take a snapshot for all the student current data.
   */
  autoApproveAdminSignOff(){
    this.loginGuard.confirmationReqActivate({
      caption:"Are you sure auto approve all student's data?",
      confirm: () => {
        const data = {
          test_window_id: this.testWindowId
        }
        const params = { query: {} }
        this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_AUTO_APPROVE_STU_DATA, data, params)
          .then(res => {
            this.loginGuard.quickPopup(`Total ${res.auto_approve_school_counts} schools auto approved by this action`)
            this.testWindowSummary["AutoApproveDates"] = res.AutoApproveDates
          })
          .catch(error => {
            this.loginGuard.quickPopup(`${error.message}`)
          });
      },
      close: () => {
        //DO NOTHING
      }
    })   
  }

  /**
   * Auto request the ISRs for fully participate students in this test window.
   */
  autoRequestISR(){
    this.loginGuard.confirmationReqActivate({
      caption:"Are you sure you want to request the ISRs for all registered students?",
      confirm: () => {
        const data = {
          test_window_id: this.testWindowId
        }
        const params = { query: {} }
        this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_AUTO_REQUEST_STU_ISR, data, params)
          .then(res => {
            const firstMsg = "Students ISR are being generated"
            const lastMsg = "This process will take some time. You can check the export to see if a new ISR request has been generated."
            if (res === 'END_WINDOW_FLAG_OFF') {
              this.loginGuard.quickPopup(firstMsg + " for <b>fully participated students only</b>. If you want to request ISR for all students, please activate <b>End Of Window ISR Generation</b> flag.<br /><br />" + lastMsg)
            }else{
              this.loginGuard.quickPopup(`${firstMsg}. ${lastMsg}`)
            }
          })
          .catch(error => {
            this.loginGuard.quickPopup(`${error.message}`)
          });
      },
      close: () => {
        //DO NOTHING
      }
    })   
  }

  /**
   * Trigger bulk CSV report generation process for this test window
   */
  bulkGenerateCsvReport(){
    const m_reg_lock_on = moment.tz(this.testWindowSummary.reg_lock_on, moment.tz.guess());
    const m_now = moment.tz();
    const isPastTTRILockDown = m_reg_lock_on.isBefore(m_now);
    if (!isPastTTRILockDown) {
      alert('Cannot generate CSV report before TTRI Lockdown!')
      return
    }

    this.loginGuard.confirmationReqActivate({
      caption:`Are you sure you want to trigger bulk CSV report generation process for ${this.setMissingCsvBoardOnly ? "**missing report**" : "**all**"} boards?`,
      confirm: () => {
        const data = {
          test_window_id: this.testWindowId,
          missing_report_sd_only: this.setMissingCsvBoardOnly
        }
        const params = { query: {} }
        this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_BULK_GENERATE_REPORT_CSV, data, params)
          .then(res => {
            const firstMsg = `Board CSV report are being generated for ${res.csv_require_schl_dist_grp_ids.length} board(s).`
            const lastMsg = 'This process will take some time. You can click **Refresh** button to view the progress.'
            this.loginGuard.quickPopup(`${firstMsg}\n\n${lastMsg}`)
            this.bulkCsvReportGenerationRecords = res.bulkCsvReportGenerationRecords
            this.showCsvGenerationRecords = true
          })
          .catch(error => {
            this.loginGuard.quickPopup(`${error.message}`)
          });
      },
      close: () => {
        //DO NOTHING
      }
    })
  }

  regenerateCSVbrdMidentsModalStart() {
    this.pageModal.newModal({
      type: Modal.REGENERATE_CSV_REPORT_BY_BOARD, 
      config: {}, 
      finish: config => this.regenerateCSVbrdMidentsModalFinish()
    });
  }

  addToRegenerateCSVbrdMidents(board_mident){
    const parsedMident = Number(board_mident);
    if (!board_mident || Number.isNaN(parsedMident)) {
      alert('Please enter a valid Board Mident.');
      return;
    }

    if (this.regenerateCSVbrdMidents.includes(parsedMident)) {
      alert('This mident is already added.');
      return;
    }

    this.regenerateCSVbrdMidents.push(parsedMident)
    this.csvSingleBoardMident = null
  }

  /**
   * Regenerate CSV report by board mident(s)
   */
  async regenerateCSVbrdMidentsModalFinish() {
    const data = {
      test_window_id: this.testWindowId,
      board_midents: this.regenerateCSVbrdMidents
    }
    const params = { query: {} }
    try {
      const res:{
        brd_mident:number,
        message:string // also log expected generation failure
      }[] = await this.auth.apiCreate(this.routes.DIST_ADMIN_G9_REPORT, data, params)
      let message = ""
      res.forEach(board => {
        message += `Mident ${board.brd_mident} - ${board.message}\n\n`
      })
      this.loginGuard.quickPopup(message)
    } catch(err) {
      console.error(err)
      this.loginGuard.quickPopup(err.message) // For unexpected endpoint error
    }
    this.regenerateCSVbrdMidents = []
    this.csvSingleBoardMident = null
  }

  /**
   * Get all bulk CSV report generation records by test window
   */
  getCsvBulkReportGenerationProgress(){
    this.auth.apiGet(this.routes.TEST_CTRL_TEST_WINDOW_BULK_GENERATE_REPORT_CSV, this.testWindowId)
      .then(res => {
        this.bulkCsvReportGenerationRecords = res
      }).catch(error => {
        this.loginGuard.quickPopup(`${error.message}`)
      })
  }

  /**
   * Stop and revoke ongoing bulk report generation process
   */
  stopCsvBulkReportGenerationProcess(){
    this.auth.apiRemove(this.routes.TEST_CTRL_TEST_WINDOW_BULK_GENERATE_REPORT_CSV, this.testWindowId)
      .then(res => {
        this.bulkCsvReportGenerationRecords = res
        alert('Bulk CSV report generation process interrupted!')
      }).catch(error => {
        this.loginGuard.quickPopup(`${error.message}`)
      })
  }

  /**
   * Reset School Admin ISR Access Records for this test window
   */
  resetSchoolAdminISRAccessRecords(){
    this.loginGuard.confirmationReqActivate({
      caption:"Are you sure you want to reset School Admin ISR Access Records for this test windows?",
      confirm: () => {
        const data = {
          test_window_id: this.testWindowId
        }
        const params = { query: {} }
        this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_RESET_ISR_ACCESS_RECORDS, data, params)
          .then(res => {
            this.loginGuard.quickPopup(`${res.length} School Admin ISR Access Records Reset. Please check next scheduled sessions result to confirm`)
          })
          .catch(error => {
            this.loginGuard.quickPopup(`${error.message}`)
          });
      },
      close: () => {
        //DO NOTHING
      }
    })   
  }

  /**
   * handles loading of auto submission form control values 
   */
  loadAutoSubmissionDateFormData(){
    const start = this.testWindowSummary.auto_close_start_on;
    const end = this.testWindowSummary.auto_close_end_on;
    if(start && end){ // turn off auto submission will set start and end dates to null
      this.setAutoSubmissionOn = true
    }
    this.getAutoSubmissionStartTimeFc().setValue(UTCTimetoESTTime(start));
    this.getAutoSubmissionEndTimeFc().setValue(UTCTimetoESTTime(end));
    this.autoSubmissionTableDef.rows.forEach(tableProp => {
      const data = this.testWindowSummary[tableProp] !== null ? this.testWindowSummary[tableProp] : null; // In case of 0
      this.autoSubmissionTableDef.cols.forEach(col => {
        const fc = this.getAutoSubmissionFormFC(tableProp, col);
        fc.setValue(data);
      });
    });
  }

  /**
   * Load the auto_approve_unsubmit_time_limit_h value from testWindowSummary to the UI table
   */
  loadAutoApproveUnsubmitDateFormData(){
    this.setAutoApproveUnsubmitTimeLimit = !!this.testWindowSummary['auto_approve_unsubmit_time_limit_h']
    this.autoApproveUnsubmitTimeLimitTableDef.rows.forEach(tableProp => {
      const data = this.testWindowSummary[tableProp] || '';
      this.autoApproveUnsubmitTimeLimitTableDef.cols.forEach(col => {
        const fc = this.getAutoApproveUnsubmitTimeLimitFromFc(tableProp, col);
        fc.setValue(data);
      });
    });
  }

  /**
   * Check if current window is allowed pre create test sessions
   * @returns boolean 
   */
  isAllowedPreCreateTestSessionWindowType(){
    const allowedPreCreateTestSessionWindowType: string[] = [
      'EQAO_PQTQ'
    ]

    return allowedPreCreateTestSessionWindowType.includes(this.getPropString(TWAttr.type_slug))
  }

  //Set the Pre Created Test session Due on Time
  loadPreCreatedTestSessions() {
    if(this.isAllowedPreCreateTestSessionWindowType() && this.testWindowPreCreatedTestSessions.length){
      this.getPreCreateTestSessionFc().setValue(this.formatDueOnDate(this.testWindowPreCreatedTestSessions[0].due_on));
    }  
  }

  /** Create Pre Created Test session for test window */
  createPreCreateTestSessionForWindow(){
    const data = {
      test_window_id: this.testWindowId
    };
    const params = {}
    this.auth.apiCreate(this.routes.TEST_CTRL_TEST_WINDOW_TEST_SESSION_CONFIG, data, params)
      .then(res => {
        this.testWindowPreCreatedTestSessions = res
        this.loadPreCreatedTestSessions()
      })
      .catch(error => {
        this.loginGuard.quickPopup(`${error.message}`)
      });
  }

  /**
   * Check if Test window already have pre created Test sessions
   */
  havePreCreateTestSession(){
    return this.testWindowPreCreatedTestSessions?.length > 0
  }

  /**
   * Get the test window pre created test sessions
   * @returns 
   */
  getTestWindowPreCreatedTestSessions(){
    return this.testWindowPreCreatedTestSessions
  }

  /**
   * Strucutres auto submission keys into form control columns 
   */
  getPrCreateTestSessionFormFC(fcKey) {
    return this.editPreCreateTestSessionDueOn.getFC(fcKey);
  }

  /**
   * Reformat Due On String
   */
  formatDueOnDate(DueOnString:string){
    const date = new Date(DueOnString); // This will interpret the string as UTC
    const year = date.getFullYear();
    const month = ('0' + (date.getMonth() + 1)).slice(-2);  // Add leading zero
    const day = ('0' + date.getDate()).slice(-2);           // Add leading zero
    const hours = ('0' + date.getHours()).slice(-2);        // Add leading zero for local hours
    const minutes = ('0' + date.getMinutes()).slice(-2);    // Add leading zero
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  //Save the new Due On Date
  saveTSDueOn = () => {
    const id = this.testWindowId
    const data = {
      due_on: this.localToUTC('' + this.editPreCreateTestSessionDueOn.getFC(TWTestSessionDueOn.DUE_ON).value),
      test_window_id: this.testWindowId
    };
    const params = {}
    this.auth.apiUpdate(this.routes.TEST_CTRL_TEST_WINDOW_QUESTIONNAIRE_TS_CONFIG, id, data, params)
      .then(res => {
        this.testWindowPreCreatedTestSessions = res
        this.loadPreCreatedTestSessions()
      })
      .catch(error => {
        this.loginGuard.quickPopup(`${error.message}`)
      });
    return Promise.resolve();
  }

  //convert local time to UTC time
  localToUTC(inputDate){
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return moment.tz(inputDate, userTimezone).utc().format('YYYY-MM-DD HH:mm:ss');
  }

  /**
   * 
   * @returns A calendar event for schedules RT audit
   */
  getRtAuditCalendarInfo(){
    const startDate = moment(this.getRtAuditStartDateFc().value, 'YYYY-MM-DD').toDate();
    const endDate = moment(this.getRtAuditEndDateFc().value, 'YYYY-MM-DD')
      .add(1, 'day') // Add a day as the calendar doesn't include the last date
      .toDate();
    
    const rtAuditStartTime = this.getRtAuditStartTimeFc().value;
    const rtAuditEndTime = this.getRtAuditEndTimeFc().value;
    const timeRange = `${rtAuditStartTime} - ${rtAuditEndTime}`;
    
    return {
      title: `Running RT Audits (${timeRange})`,
      start: startDate,
      end: endDate,
      backgroundColor: '#eba000', // Set the background color
      allDay: true
    };
  }
    
}
