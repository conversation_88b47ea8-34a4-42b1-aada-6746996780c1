import * as DBT from "../db-types";

export interface ITestWindow {
    id?                   : DBT.ID,
    is_active?            : DBT.ID,
    title?                : DBT.VARCHAR,
    date_start?           : DBT.DATETIME,
    date_end?             : DBT.DATETIME,
    test_design_id?       : DBT.ID,
    notes?                : DBT.VARCHAR,
    created_on?           : DBT.DATETIME,
    created_by_uid?       : DBT.ID,
    last_activated_on?    : DBT.DATETIME,
    last_activated_by_uid?: DBT.ID,
    is_allow_test_centre? : DBT.BOOL_INT;
    is_allow_classroom?   : DBT.BOOL_INT;
    is_allow_remote?      : DBT.BOOL_INT;
    is_allow_mobile_tether?: DBT.BOOL_INT;
    is_allow_video_conference?: DBT.BOOL_INT;
    auto_close_default_extension_m?: DBT.BOOL_INT;
    auto_close_grace_period_m?: DBT.BOOL_INT;
}

