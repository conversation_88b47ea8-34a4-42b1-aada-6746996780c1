import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { BreadcrumbsService, IBreadcrumbRoute } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';

@Component({
  selector: 'view-s-reset-test-attempt',
  templateUrl: './view-s-reset-test-attempt.component.html',
  styleUrls: ['./view-s-reset-test-attempt.component.scss']
})
export class ViewSResetTestAttemptComponent implements OnInit {

  constructor(
    private auth:AuthService,
    private breadcrumbsService: BreadcrumbsService,
    private lang: LangService,
    private router: Router,
    private routes: RoutesService,
  ) { }
  
  public studentTestAttemptSearchForm = new FormGroup({
    oen: new FormControl(),
  });
  public breadcrumb: IBreadcrumbRoute[];
  public testAttempts;
  public oen:string = '';

  ngOnInit(): void {
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(this.lang.tra('title_dashboard'), `/${this.lang.c()}/support/dashboard`),
      this.breadcrumbsService._CURRENT('Schools', this.router.url),
    ];
  }

  searchTestAttempts(){
    this.oen = this.studentTestAttemptSearchForm.controls.oen.value;
    const query = {
      oen: this.oen,
    }
    this.auth
      .apiFind(this.routes.SUPPORT_TEST_ATTEMPT_RESET, {query})
      .then(testAttempts => {
        if(testAttempts.length == 0){
          this.testAttempts = null
        } else {
          this.testAttempts = testAttempts;
        }
      })
  }   
  
  resetTestAttempt(testAttempt){
    let invalidReason = prompt('Please specify the reason you want to invalid this attempt:')
    const testAttemptId = testAttempt.ta_id;
    const config = {
      oen: this.oen,
      uid: testAttempt.uid, 
      testWindowId: testAttempt.test_window_id,
      testSessionId: testAttempt.test_session_id, 
      reason: invalidReason.trim() ? invalidReason : null
    }
    this.auth
      .apiPatch(this.routes.SUPPORT_TEST_ATTEMPT_RESET, testAttemptId, config)
      .then(res => {
        const testAttempt = this.testAttempts.find(ta => ta.ta_id == testAttemptId)
        this.testAttempts.splice(this.testAttempts.indexOf(testAttempt),1)
        window.alert("Please login as teacher to re-generate a new test attempt")
      })
      .catch(error =>{
        window.alert(error.message);
      })
  }
}
