import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { ScrollService } from '../../core/scroll.service';
import { LangService } from '../../core/lang.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { BreadcrumbsService, IBreadcrumbRoute } from '../../core/breadcrumbs.service';

@Component({
  selector: 'view-s-dashboard',
  templateUrl: './view-s-dashboard.component.html',
  styleUrls: ['./view-s-dashboard.component.scss']
})
export class ViewSDashboardComponent implements OnInit, OnD<PERSON>roy {

  // services
  constructor(
    private loginGuard: LoginGuardService, //
    private router: Router,
    private route: ActivatedRoute,
    private breadcrumbsService: BreadcrumbsService,
    private scrollService: ScrollService,
    private lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,

  ) { }

  // rendered vars
  public breadcrumb: IBreadcrumbRoute[];
  public isInited: boolean;
  public isLoaded: boolean;
  public isLoadFailed: boolean;

  private routeSub: Subscription;

  loginAsSub: Subscription;

  // init / destroy
  ngOnInit() {
    this.scrollService.scrollToTop();
    this.routeSub = this.route.params.subscribe(routeParams => {

      this.loginAsKey = routeParams['loginAs']
      if (this.loginAsKey){
        this.loginAs(this.loginAsKey);
      }
      else{
        this.loginGuard.activate([AccountType.SUPPORT]);
      }

      this.breadcrumb = [
        this.breadcrumbsService._CURRENT(this.lang.tra('title_dashboard'), this.router.url),
      ];
      this.initRouteView();
    });
  }

  loginAsKey:string;
  loginAs(loginAsKey:string){
    const pieces = loginAsKey.split('x');
    this.auth
      .loginAlias(pieces[0], pieces[1])
      .then(userInfo => {
        console.log('pre gotoUserDashboard', userInfo)
      })
      .catch(e => {
        alert('Invalid login alias')
      })
  }

  ngOnDestroy() {
    if (this.loginAsSub) {
      this.loginAsSub.unsubscribe();
    }
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }
  initRouteView() {
    this.loginAsSub = this.auth.isLoggingInAs.subscribe(isLoggingInAs => {
      const userInfo = this.auth.u();
      if (!this.isInited && userInfo) {
        if (isLoggingInAs){
          this.auth.resetLoginAs();
          this.loginGuard.gotoUserDashboard(userInfo);
        }
        else{
          this.isInited = true;
          this.isLoaded = true;
        }
      }
    });
  }

  // API utility
  loadSummaryData() { }

}
