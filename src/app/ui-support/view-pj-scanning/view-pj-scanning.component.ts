import { Component, OnInit } from '@angular/core';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { Router } from '@angular/router';
import { AuthService } from '../../api/auth.service';
import { AccountType } from 'src/app/constants/account-types';
import { RoutesService } from 'src/app/api/routes.service';
import { ColumnApi, GridApi, ColDef, GridOptions, IGetRowsParams } from 'ag-grid-community';
import { mtz } from '../../core/util/moment';
import { bulkStatuses } from './../../ui-teacher/view-upload-bulk-scan/view-upload-bulk-scan.component'
import { ClientFilters, dateTimeFiltercolumns } from './types'
import * as moment from 'moment-timezone';
import qs from "qs";
import { Observable } from 'rxjs';
import { BULK_SCAN_STATUS } from './../../ui-teacher/view-upload-bulk-scan/view-upload-bulk-scan.component'
import { renderYesNo } from './../../core/util/render'
@Component({
  selector: 'view-pj-scanning',
  templateUrl: './view-pj-scanning.component.html',
  styleUrls: ['./view-pj-scanning.component.scss']
})
export class ViewPjScanningComponent implements OnInit {

  constructor(
    public breadcrumbsService: BreadcrumbsService,
    public lang: LangService,
    private router: Router,
    private routes: RoutesService,
    private auth: AuthService,
    private loginGuard: LoginGuardService
  ) { }

  public breadcrumb: any[];
  maxAllowedRunning: number;
  bulkUploadRecords: any[] = [];
  isShowQaFailedScans:boolean = false;

  ngOnInit(): void {
    this.breadcrumb = [
      this.breadcrumbsService.SUPPORT_DASHBOARD(),
      this.breadcrumbsService._CURRENT('PJ Scanning', this.router.url),
    ];
    this.loginGuard.activate([AccountType.SUPPORT]);

    this.loadBulkScanData();
  }

  async loadBulkScanData(){
    const res = await this.auth.apiFind(this.routes.SUPPORT_PJ_SCANNING)
    this.bulkUploadRecords = res.bulkUploadRecords
    this.maxAllowedRunning = res.maxAllowedRunning
  }

  refreshFailedBulkScans(){
     // Manually set the grid pagination to the first page
     this.failedUploadGridApi.paginationGoToFirstPage();
     // Trigger a refresh of the grid data
     this.failedUploadGridApi.purgeInfiniteCache();
  }

  /** Return the failed upload records (and total count) according to the pagination and client's filters and sort */
  loadFailedBulkScans(offset:number, limit:number): Observable<{ uploads: any[], count: number }>{
    this.failedUploadGridApi.deselectAll();
    return new Observable(observer => {
      const filters = this.prepFilters();
      const sortRules = this.failedUploadGridApi.getSortModel();
      const query = {
        //Filters and sort objects are encoded into url params
        clientFilters: qs.stringify(filters),
        //Only the latest sorting is applied
        clientSort: sortRules.length? qs.stringify(sortRules[0]) : undefined,
        is_exclude_qa: this.isShowQaFailedScans ? 0 : 1,
        offset,
        limit
      }
      this.auth.apiGet(this.routes.SUPPORT_PJ_SCANNING, 1, {query})
      .then((res) => {
        const {uploads, count} = res;
        observer.next({uploads, count});
        observer.complete();
      })
    });
  }

  COLDEF_CHECKBOX: ColDef = {width:50, checkboxSelection:true }
  COLDEF_ID: ColDef =  { headerName:'Upload ID', field:'id', width: 150 }
  COLDEF_STATUS: ColDef = { headerName: 'Status', field: 'status', valueGetter: (params) => this.lang.tra(bulkStatuses[params?.data?.status]?.slug), width: 150}
  COLDEF_REPLACE_ALL: ColDef = { headerName: 'Replace All Scans', field: 'is_override_flagged_only', valueGetter: (params) => renderYesNo(this.lang, !params?.data?.is_override_flagged_only), width: 150}
  COLDEF_TW_ID: ColDef = { headerName:'Test Window ID', field:'test_window_id', width: 150}
  COLDEF_SCHL_NAME: ColDef = { headerName:'School Name', field:'schl_name' }
  COLDEF_CLASS_NAME: ColDef = { headerName:'Class Name', field:'class_name' }
  COLDEF_UPLOADER: ColDef = { headerName:'Uploaded By',  field:'uploader' }
  COLDEF_CREATED_ON: ColDef = { headerName:'Uploaded On', field:'created_on', filter: 'agDateColumnFilter', valueGetter: params => this.renderDate(params?.data?.created_on) }
  COLDEF_INPUT_FILE: ColDef = { 
    headerName:'Input File', 
    field:'bulk_file_url',
    sortable: false, filter: false,
    cellRenderer: params => {
      if (!params?.data?.bulk_file_url) return undefined;
      return `<a href=${params.data.bulk_file_url} target=_blank>${this.lang.tra('btn_download')}</a>`
    }
  }
  COLDEF_STARTED_ON: ColDef = { headerName:'Started On', field:'started_on', filter: 'agDateColumnFilter', valueGetter: params => this.renderDate(params?.data?.started_on) }
  COLDEF_LAST_CHECK_ON: ColDef = { 
    headerName:'Client Last Checked Page On (for queue only)', 
    field:'client_queue_last_check_on',
    valueGetter: params => this.renderDate(params?.data?.client_queue_last_check_on),
    filter: false
  }

  bulkUploadColDefs: ColDef[] = [
    this.COLDEF_CHECKBOX,
    this.COLDEF_ID,
    this.COLDEF_STATUS,
    this.COLDEF_TW_ID,
    this.COLDEF_SCHL_NAME,
    this.COLDEF_CLASS_NAME,
    this.COLDEF_UPLOADER,
    {...this.COLDEF_CREATED_ON, filter: false},
    this.COLDEF_INPUT_FILE,
    this.COLDEF_REPLACE_ALL,
    {...this.COLDEF_STARTED_ON, filter: false},
    this.COLDEF_LAST_CHECK_ON
  ]

  failedUploadColDefs: ColDef[] = [
    this.COLDEF_ID,
    this.COLDEF_TW_ID,
    this.COLDEF_SCHL_NAME,
    this.COLDEF_CLASS_NAME,
    this.COLDEF_UPLOADER,
    this.COLDEF_CREATED_ON,
    this.COLDEF_INPUT_FILE,
    this.COLDEF_REPLACE_ALL,
    this.COLDEF_STARTED_ON
  ]

  defaultColDef: ColDef = {
    filter: true,
    sortable: true,
    resizable: true,
    width: 200
  }
  gridOptions:GridOptions = {
    pagination: true,
    paginationPageSize: 20,
    suppressPaginationPanel: false,
    columnDefs: this.bulkUploadColDefs,
    defaultColDef: this.defaultColDef,
  };

  failedUploadGridOptions:GridOptions = {
    pagination: true,
    suppressPaginationPanel: false,
    columnDefs: this.failedUploadColDefs,
    defaultColDef: this.defaultColDef,
    rowSelection: 'single',
    enableCellTextSelection: true,
    cacheBlockSize: 20,
    paginationPageSize: 100,
    rowModelType: 'infinite',
  };

  failedUploadGridColumnApi: ColumnApi;
  failedUploadGridApi: GridApi;
  onGridReadyFailedUploads (params){
    this.failedUploadGridColumnApi = params.columnApi;
    this.failedUploadGridApi = params.api;
    // Set up data source for API-side pagination
    const datasource = {
      getRows: (params: IGetRowsParams) => {
        // Pass the offset and limit based on the first row of the new page, and the page size
        this.loadFailedBulkScans(params.startRow, this.failedUploadGridOptions.paginationPageSize)
        .subscribe(data => { 
          params.successCallback(data.uploads, data.count) 
        });
      }
    }
    this.failedUploadGridApi.setDatasource(datasource);
  }

  /** Enable the "Set to Error" button only if currently selected an "In progress" upload record */
  isSetErrorEnabled():boolean {
    const selectedRecords = this.gridOptions?.api?.getSelectedRows();
    if (selectedRecords?.length !== 1) return false;
    const selectedRecord = selectedRecords[0];
    return selectedRecord.status == BULK_SCAN_STATUS.IN_PROGRESS;
  }

  /** Set the status of the selected bulk upload record to "Error" after confirmation */
  setUploadToError(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('lbl_confirm_pj_scan_set_error'),
      btnProceedConfig: {caption: this.lang.tra('lbl_yes')},
      btnCancelConfig: {caption: this.lang.tra('lbl_no')},
      confirm: () => {
        const selectedRecords = this.gridOptions?.api?.getSelectedRows();
        if (selectedRecords?.length !== 1) return;
        const selectedRecord = selectedRecords[0];
        this.auth.apiPatch(this.routes.SUPPORT_PJ_SCANNING, selectedRecord.id, {})
        .then(() => {
          this.loginGuard.quickPopup("btn_success")
          this.loadBulkScanData();
        })
        .catch(() => {
          this.loginGuard.quickPopup("lbl_error")
        })
      }
    })
  }


  /** Display a date in correct format */
  renderDate(inputDate){
    if (!inputDate) return null;
    return mtz(inputDate).format(this.lang.tra('datefmt_timestamp'))
  }


  initQueueClearingAttempt(){
    this.auth.apiCreate(this.routes.SUPPORT_PJ_SCANNING, {})
    .then(res => {
      const {newRunningIds} = res;
      const msg = newRunningIds.length ? `Started running bulk processes with IDs: ${newRunningIds.join(", ")}.` : `No new bulk processes started.`
      this.loginGuard.quickPopup(msg)
      this.loadBulkScanData();
    })
  }


    /**
   * Prepare filters for API-side filtering: adjust ag-grid filters, manually add more filters based on selected toggles
   * @returns Object with request properties as keys and filter objects as values
   */
  prepFilters(): ClientFilters {

    /**
     * Remove extra characters from the year only
     * e.g. Given "202333-09-01 00:00:00" return "2023-09-01 00:00:00"
     * Given "2023-09-01 00:00:00" return without changes
     */
    const dateFormatEnforceYYYY = (input:string) => {
      return input.substring(0, 4) + input.substring(input.indexOf('-'))
    }

    const localToUTC = (inputDate) => {
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return moment.tz(inputDate, userTimezone).utc().format('YYYY-MM-DD HH:mm:ss');
    }

    const filters = this.failedUploadGridApi.getFilterModel();    

    //Modify filters chosen on ag-grid
    for (const [prop, filter] of Object.entries(filters)) {

      // Somehow while the filter has yyyy-mm-dd placeholder it lets you type up to 6 numbers into the year e.g. 202333-09-01
      // But a valid YYYY year is needed in the API query
      // If the year exceeds four characters, trim it and reset the filters on the UI - on the next pass it will not have to be corrected
      if (filter.dateFrom) {
        const ogDateFrom = filter.dateFrom
        filter.dateFrom = dateFormatEnforceYYYY(ogDateFrom)
        if (ogDateFrom !== filter.dateFrom) this.failedUploadGridApi.setFilterModel(filters)
      }
      if (filter.dateTo) {
        const ogDateTo = filter.dateTo
        filter.dateTo = dateFormatEnforceYYYY(ogDateTo)
        if (ogDateTo !== filter.dateTo) this.failedUploadGridApi.setFilterModel(filters)
      }
      // In datetime columns, convert back to UTC from client's timezone
      if(dateTimeFiltercolumns.has(prop)) {
        filter.dateFrom = localToUTC(filter.dateFrom);
        filter.dateTo = localToUTC(filter.dateTo);
      }
    }

    return filters;
  }

  exportFailedCsv() {
    const todayDate = moment().format('YYYYMMDD')
    const fileName = `FailedBulkUploads-${todayDate}.csv`
    this.failedUploadGridApi.exportDataAsCsv({
      fileName
    });
  }
}
