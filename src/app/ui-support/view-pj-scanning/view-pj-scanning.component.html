<div class="page-body">
  <div>
    <header [breadcrumbPath]="breadcrumb"></header>
    <div class="page-content is-fullpage">
      <div style="margin-top: 1rem;">
        <h1>PJ Scanning</h1>

        <div>
          <h2>Settings</h2>
          <div *ngIf="maxAllowedRunning">
            <p>A maximum of <code>{{maxAllowedRunning}}</code> bulk processes can run at a time, any others remain in the waitlist queue.</p>
            <p>This value can be changed in <i>System Flags</i>.</p>
          </div>
        </div>

        <div>
          <h2>Clear waitlist queue</h2>
          <p>
            If there is capacity for X more waitlisted upload processes to start, this will start up to previously queued X processes, prioritizing the oldest first.
          </p>
          <div style="margin: 1em 0">
            <button class="button" (click)="initQueueClearingAttempt()">Go</button>
          </div>
        </div>


        <div>
          <div>
            <div class="columns">
              <div class="column"><h2>Bulk uploads in progress or in waitlist queue</h2></div>
              <div class="column is-narrow">
                <button *ngIf="isSetErrorEnabled()" class="button is-small is-danger is-light" (click)="setUploadToError()">Set to "Error"</button>
              </div>
              <div class="column is-narrow">
                <button class="button is-small" (click)="loadBulkScanData()">Refresh</button>
              </div>
            </div>
            
  
          </div>
          <div class="grid-container" style="flex-grow: 1;">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                style="border: none;"
                [rowData]="bulkUploadRecords"
                [gridOptions]="gridOptions"
            ></ag-grid-angular>
          </div>
        </div>

        <div style="margin-top: 2em">
          <div>
            <h2>Failed bulk uploads</h2>
            <div class="columns" style="margin-bottom: 1em">
              <div class="column">
                <p>Uploads that did not complete due to a technical error.</p>
              </div>
              <div class="column is-narrow">
                <mat-slide-toggle [(ngModel)]="isShowQaFailedScans" (change)="refreshFailedBulkScans()">Include QA</mat-slide-toggle>
              </div>
              <div class="column is-narrow">
                <button class="button is-small" (click)="refreshFailedBulkScans()">Refresh</button>
                <button class="button is-small" (click)="exportFailedCsv()">Export</button>
              </div>
            </div>
            
  
          </div>
          <div class="grid-container" style="flex-grow: 1;">
            <ag-grid-angular
                class="ag-theme-alpine ag-grid-fullpage"
                style="border: none;"
                [gridOptions]="failedUploadGridOptions"
                (gridReady)="onGridReadyFailedUploads($event)"
            ></ag-grid-angular>
          </div>
        </div>

        

      </div>
    </div>
  </div>
</div>
