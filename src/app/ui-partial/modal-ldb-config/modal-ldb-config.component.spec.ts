import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalLdbConfigComponent } from './modal-ldb-config.component';

describe('ModalLdbConfigComponent', () => {
  let component: ModalLdbConfigComponent;
  let fixture: ComponentFixture<ModalLdbConfigComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalLdbConfigComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalLdbConfigComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
