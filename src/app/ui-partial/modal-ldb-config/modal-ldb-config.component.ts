import { Component, Input, OnInit } from '@angular/core';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { PageModalController, PageModalService } from '../page-modal.service';
import { AuthService } from 'src/app/api/auth.service';
import { LangService } from 'src/app/core/lang.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { IS_RESPONDUS_BETA_MODE } from './../../ui-student/lockdown.service'
export enum LDBModal {
  LDB_CONFIG_MODAL = 'LDB_CONFIG_MODAL'
};

interface ILDBModalConfig {
  currentLevel: LDBConfigLevel,
  query: {
    school_class_id?: number,
    school_id?: number,
    board_id?: number
  }
}

export enum LDBConfigLevel {
  SYSTEM = 'SYSTEM',
  BOARD = 'BOARD',
  SCHOOL = 'SCHOOL',
  CLASS = 'CLASS'
}

export enum LdbConfigTypes {
  PROGRAM = 'PROGRAM',
  DOMAIN = 'DOMAIN'
}

const MAX_CHARACTER_LIMIT = 155;

interface ILDBConfig {
  order: number,
  header: string,
  description: string,
  whitelist: string[],
  blacklist: string[],
  domain_whitelist: string[],
  shown?: boolean
}

export const LevelConfig: { [key in LDBConfigLevel]: ILDBConfig } = {
  [LDBConfigLevel.SYSTEM]: {
    order: 0,
    header: 'lbl_ldb_config_lvl_sys',
    description: 'lbl_ldb_config_default_des',
    whitelist: [],
    blacklist: [],
    shown: false,
    domain_whitelist: []
  },
  [LDBConfigLevel.BOARD]: {
    order: 1,
    header: 'lbl_ldb_config_lvl_brd',
    description: LDBConfigLevel.BOARD,
    whitelist: [],
    blacklist: [],
    domain_whitelist: []
  },
  [LDBConfigLevel.SCHOOL]: {
    order: 2,
    header: 'lbl_ldb_config_lvl_schl',
    description: LDBConfigLevel.SCHOOL,
    whitelist: [],
    blacklist: [],
    domain_whitelist: []
  },
  [LDBConfigLevel.CLASS]: {
    order: 3,
    header: 'lbl_ldb_config_lvl_class',
    description: LDBConfigLevel.CLASS,
    whitelist: [],
    blacklist: [],
    domain_whitelist: []
  },
}

@Component({
  selector: 'modal-ldb-config',
  templateUrl: './modal-ldb-config.component.html',
  styleUrls: ['./modal-ldb-config.component.scss']
})
export class ModalLdbConfigComponent implements OnInit {
  @Input() config: ILDBModalConfig;
  @Input() pageModal: PageModalController;

  currentEntry = {
    [LdbConfigTypes.PROGRAM]: '',
    [LdbConfigTypes.DOMAIN]: '',
  };
  LevelConfig = LevelConfig;
  MAX_CHARACTER_LIMIT = MAX_CHARACTER_LIMIT;
  IS_RESPONDUS_BETA_MODE = IS_RESPONDUS_BETA_MODE;
  ldbConfigTypes = [
    {
      type: LdbConfigTypes.PROGRAM,
      whitelistType: 'whitelist', 
      blacklistType: 'blacklist', 
      description: 'caption_tech_readi_respondus_programs', 
      addPlaceholder: 'lbl_ldb_config_add_program'
    },
    {
      type: LdbConfigTypes.DOMAIN, 
      whitelistType: 'domain_whitelist', 
      description: 'caption_tech_readi_respondus_domains', 
      addPlaceholder: 'lbl_ldb_config_add_domain'
    },
  ]

  constructor(
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    public lang: LangService,
    private whitelabel: WhitelabelService
  ) { }

  ngOnInit(): void {
    this.initConfiguration();
  }

  validateEntryUnique() {
    
  }

  public async initConfiguration() {
    // Reset all tags
    for(const level in LevelConfig) {
      LevelConfig[level].whitelist = [];
      LevelConfig[level].blacklist = [];
      LevelConfig[level].domain_whitelist = [];
    }

    const res = await this.auth.apiFind('public/anon/ldb-config', { query: this.config.query} )
      .catch(() => {
        this.loginGuard.quickPopup('lbl_ldb_fetch_config_err');
        this.closeModal();
      })

    for(const level of res) {
      const config = LevelConfig[level.level];
      console.log('level = ', level)
      console.log('config = ', config)
      config.whitelist = level.whitelist;
      config.blacklist = level.blacklist;
      config.domain_whitelist = level.domain_whitelist || []
    }
  }

  public get currentConfig() {
    return LevelConfig[this.config.currentLevel]
  }

  public isEditable(config) {
    return config.order === this.currentConfig.order
  }

  public isShown(config) {
    return config.order <= this.currentConfig.order && config.shown !== false;
  }

  /** Renders only the levels that are shown */
  public get renderLevels() {
    return Object.values(LevelConfig)
      .filter(config => this.isShown(config)) 
      .sort((a, b) => a.order - b.order);
  }

  public removeTag(tag: string, type = 'whitelist', config = this.currentConfig, ) {
    config[type] = config[type].filter(t => t !== tag)
  }

  public async addTag(tag: string, type = 'whitelist', configType: LdbConfigTypes = LdbConfigTypes.PROGRAM, config = this.currentConfig) {
    if (!config[type].includes(tag)) {
      if (this.getTotalListSize(tag) > MAX_CHARACTER_LIMIT){
        return this.loginGuard.quickPopup(this.getMaxCharLimitText());
      }
      config[type].push(tag)
      this.currentEntry[configType] = '';
    } else {
      this.loginGuard.quickPopup('lbl_ldb_already_exists');
    }
  }


  getTotalListSize(newItem?: string){

    const getDomainFromString = (inputDomain: string) => {
      const match = inputDomain.match(/(?:https?:\/\/)?(?:www\.)?([^\/\s]+)/);
      const domain = match ? match[1] : '';
      return domain;
    }

    let currSize = 0;
    for (const level of Object.values(LevelConfig)){
      const whiteListTags = level.whitelist.join('');
      currSize += whiteListTags.length;
      const blacklistTags = level.blacklist.join('');
      currSize += blacklistTags.length;
      const domainWhiteListTags = level.domain_whitelist.map(d => getDomainFromString(d)).join('');
      currSize += domainWhiteListTags.length;
    }
    if (newItem){
      currSize += newItem.length;
    }
    return currSize;
  }

  public async saveChanges() {
    const res = await this.auth.apiCreate('public/anon/ldb-config', {
      whitelist: this.currentConfig.whitelist,
      blacklist: this.currentConfig.blacklist,
      domain_whitelist: this.currentConfig.domain_whitelist,
      level: this.config.currentLevel
    }, { query: this.config.query})
      .catch((err) => {
        if (err.message == 'MAX_LIST_CHARACTER_LIMIT_EXCEEDED'){
          this.loginGuard.quickPopup(this.getMaxCharLimitText());
        } else {
          this.loginGuard.quickPopup('lbl_ldb_config_saving_err');
        }
        this.closeModal();
        throw new Error('');
      });

    this.loginGuard.quickPopup('lbl_ldb_config_saving_suc');
    this.closeModal();
  }

  public closeModal() {
    this.pageModal.closeModal()
  }

  /**
   * Get maximum character limit reached error message
   * @returns {string}
   */
  getMaxCharLimitText():string {
    return this.lang.tra('lbl_ldb_char_limit_allow', undefined, { MAX_CHARACTER_LIMIT: MAX_CHARACTER_LIMIT })
  }

  /**
   * Get config level description text by role based on the whitelabel context
   * @param description can be a translation slug or a LDBConfigLevel level
   * @returns {string} Translated description or role-based text
   */
  getLevelDescriptionText(description:LDBConfigLevel):string {
    const roleLabels = {
      eqao: {
        [LDBConfigLevel.BOARD]: 'lbl_ldb_config_lvl_brd_role_eqao',
        [LDBConfigLevel.SCHOOL]: 'lbl_ldb_config_lvl_schl_role_eqao',
        [LDBConfigLevel.CLASS]: 'lbl_ldb_config_lvl_class_role_eqao',
      },
      abed: {
        [LDBConfigLevel.BOARD]: 'lbl_ldb_config_lvl_brd_role_abed',
        [LDBConfigLevel.SCHOOL]: 'lbl_ldb_config_lvl_schl_role_abed',
        [LDBConfigLevel.CLASS]: 'lbl_ldb_config_lvl_class_role_abed',
      },
    };

    // Check if the description is a valid LDBConfigLevel value
    const isValidConfigLevel = Object.values(LDBConfigLevel).includes(description as LDBConfigLevel);
    if (!isValidConfigLevel) { // If not valid, assume it's a translation slug
      return this.lang.tra(description)
    }

    const currentLabelSet = this.whitelabel.isEQAO ? roleLabels.eqao : 
                            this.whitelabel.isABED ? roleLabels.abed : {};

    const roleKey = currentLabelSet[description as LDBConfigLevel];
    const userRole = this.lang.tra(roleKey)

    // Return the translated text with the user role
    return this.lang.tra('lbl_ldb_configured_by', undefined, { USER_ROLE: userRole });
  }
}
