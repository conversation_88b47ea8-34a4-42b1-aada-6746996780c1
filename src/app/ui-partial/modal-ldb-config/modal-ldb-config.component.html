<h2 class="title"><tra [slug]="IS_RESPONDUS_BETA_MODE ? 'lbl_ldb_allowlist_setup_beta' : 'lbl_ldb_allowlist_setup'"></tra></h2>
<div class="columns">
  <div *ngFor="let configType of ldbConfigTypes" class="column">
    <p><tra-md [slug]="configType.description"></tra-md></p>
  </div>
</div>
<div class="columns">
  <div *ngFor="let configType of ldbConfigTypes" class="column">
    <div class="field has-addons is-fullwidth">
      <div class="control is-expanded has-icons-right has-icons-left">
        <input class="input is-small" type="text" [placeholder]="lang.tra(configType.addPlaceholder)" [(ngModel)]="currentEntry[configType.type]" />
        <span class="icon is-small is-left">
          <i class="fas fa-cog"></i>
        </span>
        <span class="icon is-small is-right">
          <i class="fas fa-plus"></i>
        </span>
      </div>
      <div class="control">
        <button class="button is-info is-small" (click)="addTag(currentEntry[configType.type], configType.whitelistType, configType.type)" [disabled]="getTotalListSize() >= MAX_CHARACTER_LIMIT">
          <tra slug="btn_add"></tra>
        </button>
      </div>
    </div>
    <div *ngIf="getTotalListSize() >= MAX_CHARACTER_LIMIT" class="notification is-danger is-light">
      {{getMaxCharLimitText()}}
    </div>
    
    
    <ng-container *ngFor="let level of renderLevels">
      <div style="margin-bottom: 1.25rem">
        <h3 class="subtitle"><tra [slug]="level.header"></tra></h3>
        <p><tra [slug]="getLevelDescriptionText(level.description)"></tra></p>
        <p *ngIf="level[configType.whitelistType].length === 0" class="has-text-grey"><tra slug="lbl_ldb_list_empty"></tra>.</p>
    
        <div class="field is-grouped is-grouped-multiline">
          <div class="control" *ngFor="let tag of level[configType.whitelistType]">
            <div class="tags has-addons">
              <span class="tag is-success">
                <i class="fas fa-check"></i>
              </span>
              <span class="tag is-dark">{{tag}}</span>
              <a class="tag is-delete" *ngIf="isEditable(level)" (click)="removeTag(tag, configType.whitelistType)"></a>
            </div>
          </div>
    
          <div class="control" *ngFor="let tag of level[configType.blacklistType]">
            <div class="tags has-addons">
              <span class="tag is-danger">
                <i class="fas fa-ban"></i>
              </span>
              <span class="tag is-dark">{{tag}}</span>
              <a class="tag is-delete" *ngIf="isEditable(level)" (click)="removeTag(tag, configType.blacklistType)"></a>
            </div>
          </div>
        </div>
      </div>
    </ng-container>


  </div>
</div>

<div class="button-container">
  <button class="button" (click)="closeModal()">
    <tra slug="ie_discard"></tra>
  </button>
  <button class="button is-info" (click)="saveChanges()">
      <tra slug="btn_save"></tra>
  </button>
</div>