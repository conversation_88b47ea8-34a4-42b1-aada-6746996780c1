import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
import { WhitelabelService } from '../domain/whitelabel.service';

@Directive({
  selector: '[wlCtx], [wlCtxNot]'
})
export class WlCtxDirective {

  private isShowing = false;

  constructor(
    private whitelabel: WhitelabelService,
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef
  ) { }

  @Input() set wlCtx(wlFlag: string) {
    const shouldHide = (wlFlag && !this.whitelabel.getSiteFlag(wlFlag));
    if (this.isShowing && shouldHide){
      this.viewContainer.clear()
    }
    if (!this.isShowing && !shouldHide){
      this.viewContainer.createEmbeddedView(this.templateRef);
    }
    console.log('wlCtx', wlFlag, shouldHide)
    this.isShowing = !shouldHide;
  }

  @Input() set wlCtxNot(wlFlag: string) {
    const shouldHide = (wlFlag && this.whitelabel.getSiteFlag(wlFlag));
    if (this.isShowing && shouldHide) {
      this.viewContainer.clear();
    }
    if (!this.isShowing && !shouldHide) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    }
    console.log('wlCtxNot', wlFlag, shouldHide)
    this.isShowing = !shouldHide;
  }
}
