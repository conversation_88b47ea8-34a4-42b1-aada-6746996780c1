<div 
[class.is-panel-visible]="isSidePanelVisible()" 
[class.is-panel-mobile-visible]="isMobileLeftPanel()" 
[class.is-panel-expanded]="isSidePanelExpanded()" 
style="height:100%;"
id="top-level"
>
  <div class="mobile-overhead">
    <div class="mobile-overhead-bar">
      <div class="btn-menu" (click)=openMobileLeftPanel()>
        <i class="fa fa-bars" aria-hidden="true"></i>
      </div>
    </div>
  </div>
  <div style="height:100%;">
    <div class="page-content content" >
      <router-outlet></router-outlet>
    </div>
    <div class="confirmation-modal"></div>
    <div class="log-back-in modal-screen-cover" [class.is-hidden]="!(loginGuard.getLoginReq() | async)">
      <div class="modal-screen-cover-content is-boxed">
        <div class="modal-scrollpane">
          <login-form></login-form>
        </div>
        <div class="delete-container">
          <button (click)="gotoHomeScreen()" class="delete is-large" [attr.aria-label]="lang.tra('btn_close')"></button>
        </div>
      </div>
    </div>
    <div class="connection-display tags">
      <warning-tag *ngIf="(loginGuard.getDevNetFail() | async)" slug="error_device_net" background="is-danger">
      </warning-tag>
      <warning-tag *ngIf="(loginGuard.getApiNetFail() | async)" slug="error_server_net">
      </warning-tag>
      <warning-tag *ngIf="(loginGuard.getAutoLogout() | async)" slug="alert_logged_out">
      </warning-tag>
      <warning-tag *ngIf="(loginGuard.getAutoLogoutWarning() | async)" slug="alert_logging_out" dismissType="none">
        <br/>
        <a (click)="loginGuard.refreshLogin()"> <tra slug="link_logging_out"></tra> </a>
      </warning-tag>
      <warning-tag *ngIf="!imageLoad.allImageLoaded()">
        Some images are still loading
      </warning-tag>
      <warning-tag *ngIf="loginGuard.isIOS16_4_1" slug="ios_version_update_warning_msg">
      </warning-tag>
      <warning-tag *ngIf="(loginGuard.fontCheckFail | async)" slug="error_fonts_unloaded">
      </warning-tag>
      <warning-tag *ngIf="(loginGuard.styleProfileCheck | async)" slug="error_style_profile_unloaded">
      </warning-tag>
    </div>
    <div class="device-net-fail-overlay modal-screen-cover" [class.is-hidden]="!isShowingDevNetFailOverlay">
      <div class="modal-screen-cover-content  is-boxed">
        <div class="modal-scrollpane">
          <div>
            <tra slug="offline_warning"></tra>
          </div>
          <div>
            <button (click)="isShowingDevNetFailOverlay = false" class="button is-danger">
              <tra slug="btn_dismiss_notif"></tra>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="api-net-fail-overlay modal-screen-cover" [class.is-hidden]="true"> <!-- !isShowingApiNetFailOverlay -->
      <div class="modal-screen-cover-content  is-boxed">
        <div class="modal-scrollpane">
          <div>
            <tra slug="error_server_net_long"></tra>
          </div>
          <hr/>
          <strong>Service Health</strong>
          <table class="table is-outlined is-bordered" style="margin-top: 1em;">
            <tr>
              <th>Current Status</th>
              <th>Current Details</th>
            </tr>
            <tr>
              <td>✅ Registration</td>
              <td>Service is operating normally</td>
            </tr>
            <tr>
              <td>✅ Login</td>
              <td>Service is operating normally</td>
            </tr>
            <tr>
              <td>✅ Invigilation</td>
              <td>Service is operating normally</td>
            </tr>
          </table>
          <hr/>
          <div>
            You may be receiving an error due to unusual traffic coming from your location. If you continue to experience a localized issue, please send <a target="_blank" href="https://www.google.com/search?q=what+is+my+ip&oq=what+is+my+ip">your IP address</a> to the technical support team (<a href="mailto:<EMAIL>"><EMAIL></a>) so that the issue can be investigated.
          </div>
          <hr/>
          <div>
            <button (click)="isShowingApiNetFailOverlay = false" class="button is-warning"><tra slug="btn_dismiss_notif"></tra></button>
          </div>
        </div>
      </div>
    </div>
    <div class="support-overlay modal-screen-cover" [class.is-hidden]="!(loginGuard.getSupportReq() | async) ">
      <div class="modal-screen-cover-content  is-boxed is-wide">
        <div class="modal-scrollpane">
          <info-center-details [isAuthActive]="isAuthActive()" [supportCenterInfo]="loginGuard.getSupportReq() | async" (dismiss)="dismissSupportPopup()"></info-center-details>
        </div>
      </div>
    </div>
    <div class="confirmation-overlay modal-screen-cover" [class.is-hidden]="!currentConfirmation" style="z-index:5000; line-height: 1.3em" [style.font-size.em]="currentConfirmation?.fontSize">
      <div class="modal-screen-cover-content  is-boxed" [style.max-width]="setCustomWidth()">
        <div class="modal-scrollpane">
          <div class="content">
            <tra-md [slug]="getCurrentConfirmationCaption()" [props]="currentConfirmation?.props"></tra-md>
          </div>
          <div class="content" *ngIf="haveDescription()" style="width: 75%; margin: auto; text-align: center;">
            <tra-md [slug]="getCurrentConfirmationDescription()" [props]="currentConfirmation?.descriptionProps"></tra-md>
          </div>
          <div class="content" *ngIf="showDropDownCategories()" style="width: 75%; margin: auto">
            <label><tra [slug]="getCurrentConfirmationCategoriesLabel()"></tra></label>
            <select [(ngModel)]="categorySelection" (ngModelChange)="categorySelectionChange($event)">
              <option *ngFor="let catagory of currentConfirmation.catagoriesList" [value]="catagory">{{getCatagoryText(catagory)}}</option>
            </select>
          </div>
          <div class="content" *ngIf="showExtraText()" style="width: 75%; margin: auto">
            <br>
            <textarea [(ngModel)]="extraTextInput" (ngModelChange)="extraTextAreaChange($event)"></textarea>
          </div>
          <br>
          <div class="content" *ngIf="showCurrentConfirmationSubCaption()" style="width: 75%; margin: auto">
            <tra-md [slug]="getCurrentConfirmationSubCaption()"></tra-md>
          </div>            
          <div *ngIf="showCurrentConfirmationInputBox()" class="modal-buttons">
            <input class="input" type="text" [(ngModel)]="confirmationInput">
          </div>
          <div *ngIf="showCurrentConfirmationCheckBox()" style="display:flex; margin-top:1em;">
            <div style="padding-top:0.3em;">
              <input type="checkbox" [(ngModel)]="confirmationCheckbox" id="confirmationCheckbox">
            </div>
            <div style="margin-left:1em;" class="text-checkbox"><label for="confirmationCheckbox"><tra-md [slug]="getCurrentConfirmationCheckBox()"></tra-md></label></div>
          </div>
          <div class="modal-buttons">
            <button class="button" style="font-size: 1.0em" [ngStyle]="getConfirmationBtnStyle(currentConfirmation?.btnCancelConfig)" (click)="closeModal()" *ngIf="!hideCancel()">
              <ng-container *ngFor="let caption of confirmationCancelCaptions; let i = index">
                <tra audio-slug [slug]="caption"></tra>
                <span *ngIf="i !== confirmationCancelCaptions.length - 1">&nbsp;</span>
              </ng-container>              
            </button>
            <button class="button is-info" style="font-size: 1.0em" [ngStyle]="getConfirmationBtnStyle(currentConfirmation?.btnProceedConfig)" (click)="confirmModal()" [disabled]="blockConfirm()" *ngIf="!hideConfirm()">
              <ng-container *ngFor="let caption of confirmationProceedCaptions; let i = index">
                <tra audio-slug [slug]="caption"></tra>
                <span *ngIf="i !== confirmationProceedCaptions.length - 1">&nbsp;</span>
              </ng-container>               
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="confirmation-overlay modal-screen-cover" [class.is-hidden]="!isShowingInfoOverlay" style="z-index:5000;">
      <div class="modal-screen-cover-content is-boxed" style="background-color: #dcf3ff;padding-top:0em;">
        <div (click)="closeInfoOverlay()" class="close-button">
          <img src=" https://eqao.vretta.com/authoring/user_uploads/96360/authoring/Close/1621026645213/Close.svg">
        </div>
        <div class="info-component-header">
          <div class="info-button">
            <img src="https://eqao.vretta.com/authoring/user_uploads/96360/authoring/Info/1621008934819/Info.svg">
          </div>
          <div class="heading-text">
            &nbsp;
            Instructions
          </div>
        </div>
        <div class="modal-scrollpane">
          <div class="info-component">
            <img [src]="getBackPicUrl()">
             <span style="margin-left: 1em;">{{lang.tra("instruction_back")}}</span>
          </div>
          <div class="info-component">
            <img [src]="getNextPicUrl()">
            <span style="margin-left: 1em;">{{lang.tra("instruction_forward")}}</span>
          </div>
          <div class="info-component">
            <img [src]="getTextPicUrl()">
            <span style="margin-left: 1em;">{{lang.tra("instruction_open_text_info")}}</span>
          </div>
          <div class="info-component" style="margin-bottom:0em;">
            <img style="width:3em" src="https://eqao.vretta.com/authoring/user_uploads/96360/authoring/Question/1621021878125/Question.svg">
            <span style="margin-left: 1em;">{{lang.tra("instruction_open_page")}}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="support-overlay modal-screen-cover" [class.is-hidden]="!(loginGuard.getHealthCheck() | async) "  style="z-index:5000;">
      <div class="modal-screen-cover-content  is-boxed is-wide" style="padding:2em; max-width:50em; max-height: 80vh; overflow:auto;">
        <tra-md [slug]="loginGuard.getHealthCheck().value.message"></tra-md>
      </div>
    </div>
  </div>
  <div class="content-shadow" (click)="closeMobileLeftPanel()"></div>
  <main-nav class="left-panel-container hide-on-paper" [class.is-hidden]="!isSidePanelVisible()"></main-nav>
</div>
