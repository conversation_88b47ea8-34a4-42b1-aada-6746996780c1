import { Component, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { objectToNumMap } from '../student-util';
import { EStyleProfile, StyleprofileService } from '../../core/styleprofile.service';
import { Router } from '@angular/router';

const LOCAL_STORAGE_KEY = 'eqao-sample';
const OBF_CONST = 'h928f92d9gd2';
const obf = (o) => {
  return OBF_CONST + btoa(JSON.stringify(o));
};
@Component({
  selector: 'diagnostics-page-switcher',
  templateUrl: './diagnostics-page-switcher.component.html',
  styleUrls: ['./diagnostics-page-switcher.component.scss']
})
export class DiagnosticsPageSwitcherComponent implements OnInit {

  constructor(
    private loginGuard: LoginGuardService,
    public lang:LangService,
    private auth: AuthService,
    private routes: RoutesService,
    private styleProfile: StyleprofileService,
    private router: Router,
  ) { }

  questionTitleMap:any;
  testTakerName: string;
  isAnonymous: boolean = false;
  asmtFmrk:any;
  questions:any;
  testForm:any;
  currentSession = 0
  testRunnerIndex = 0;
  isQuestionLoaded
  itemSetId =  486 // diagnostics temp question set id
  testState = {
    currentSectionIndex: 0,
    currentQuestionIndex: 0,
    itemSetId: null,
  };
  showFPS= true

  ngOnInit(): void {
    this.loginGuard.deactivate();
    this.testTakerName = this.lang.tra('osslt-name-surname');
    //this.loadDiagnosticsQuestions();
    this.styleProfile.setStyleProfile(EStyleProfile.OSSLT);
    this.styleProfile.styleProfileSub.subscribe(loaded => {
      console.log("loaded", loaded)
      if(!this.isQuestionLoaded){
        this.isQuestionLoaded = true
        this.loadDiagnosticsQuestions();
      }
    })
  }

  isEnglish(){
    return (this.lang.c() === 'en')
  }

  logAsmtStart(){
    this.auth.apiCreate(this.routes.LOG, {
      slug: 'OSSLT_PRACTICE_TEST_START',
      data: { 
        asmtLabel:'osslt', 
        lang: this.lang.c(), 
        isAnonymous: this.isAnonymous,
        uid: this.auth.getUid(),
        testSessionId: null, 
      }
    });
  }

  loadDiagnosticsQuestions() {
    this.isAnonymous = true;
    this.logAsmtStart();

    this.auth
      .apiGet(this.routes.ANON_SAMPLE_TEST_DESIGN_FORM, this.itemSetId, {query: {lang: this.lang.c()}})
      .then ( async (res:{testFormData:any, framework:string}) => {

        //change the vedio content in the form so its rlated to Respondus and not EQAO
        if (res.testFormData?.questionDb?.["19368"]?.content?.["1"]?.url) {
          res.testFormData.questionDb["19368"].content["1"].url = "https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515512/authoring/2024-08-26 09-28-50/1724686226794/2024-08-26 09-28-50.mp4";
        }
        if (res.testFormData?.questionDb?.["19368"]?.content?.["1"]?.urls && res.testFormData?.questionDb?.["19368"]?.content?.["1"]?.urls.length > 0 ) {
          res.testFormData.questionDb["19368"].content["1"].urls = [
            "https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515512/authoring/2024-08-26 09-28-50/1724686226794/2024-08-26 09-28-50.mp4",
            "https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515512/authoring/2024-08-26 09-28-50/1724686226794/2024-08-26 09-28-50.mp4"
          ];
        }
        this.asmtFmrk = JSON.parse(res.framework);
        await this.styleProfile.applyStyleProfileOrFallback(this.asmtFmrk.styleProfile, null, 'diagnostic-page-switcher', 'loadDiagnosticsQuestions')
        this.questions = objectToNumMap(res.testFormData.questionDb),
        this.testForm = {
          currentTestDesign: {
              ... res.testFormData,
              questionDb: null,
          },
          questionStates: {},
          testLang: res.testFormData.lang,
          questionSrcDb: this.questions,
        };
      })
      .catch(e => {
        console.error(e);
      })
  }

  saveQuestionResponse = (data) => {
    try {
      this.updateLocalStorage(data)
    }
    catch (e){
      try {
        this.updateLocalStorage({
          ... data,
          response_raw: null
        })
      }
      catch(e){}
    }
    return Promise.all([]);
  }

  updateLocalStorage(data) {
    const save = {
      itemSetId: this.itemSetId,
      testState: this.testState,
      testForm: {
        ... this.testForm,
        questionSrcDb: this.objectToNumMap(this.testForm)
      }
    }
    window.localStorage.setItem(LOCAL_STORAGE_KEY, obf(save));
  }

  private objectToNumMap( obj:{[key:number]: any}){
    const map = new Map();
    Object.keys(obj).forEach(key => {
      map.set(+key, obj[key]);
    })
    return map;
  } 

  submitTest = (skipPost?: boolean) => {
    const langCode = this.lang.getCurrentLanguage();
    window.location.href = `${window.location.origin}/?rldbxb=1`;
    //this.router.navigate([langCode, 'login-student'], { queryParams: {rldbxb: 1}}); // exist LDB after student submit
    return Promise.all([]);
  }

}
