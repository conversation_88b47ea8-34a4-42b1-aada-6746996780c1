@import '../../../styles/partials/_media.scss';
.FadeInMessage {
    --message-in-dur: 0.3s;
    animation-duration: var(--message-in-dur);
    animation-timing-function: ease;
    animation-name: fadeInMessage;
}

@keyframes fadeInMessage {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

.osslt-chat-window-container {
    height: 100%;
    width: 100%;
    border-radius: 20px;
    box-sizing: border-box;
    .logged-in {
        color: black;
        padding-left: 2.5%;
        box-sizing: border-box;
        padding-top: 5%;
        width:100%;
        background: linear-gradient(180deg, #139ED5 4.07%, rgba(36, 168, 214, 0) 78.85%);
        height:20%;
        .user-name {
            font-weight: 700;
        }
        .gear-icon {
            position:relative;
            top: 0.9em;
        }
    }
    .osslt-speech-toggle {
        color: black;
        font-size: 1em;
        .content{
            position: relative;
            &:hover{
                color: #2394c0;
            }
        }
        .mat-slide-toggle {
            margin-right: 7px; // excluded from CLOSER_LOOK_20210807
        }
    }
    .logged-in {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .osslt-speech-toggle {
        margin-right: 0.5em;
    }
    .osslt-chat-window-inner-container {
        background-color: rgba(255, 255, 255, 0.2); 
        width: 100%;
        min-height: 70%;
        box-sizing: border-box;
        padding: 15px 15px 15px 15px;
        border-radius: 7px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
    

        .header {
            font-weight: 900;
            font-size: 32px;
            width: 100%;
            text-align: center;
        }

        .header-underline {
            border-top: 2px solid #272727;
            opacity: 0.3;
            width: 30%;
            margin: 0 auto;
            height: 3em;
        }

        .text-box {
            width: 90%;
            min-height: 20%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #434343;
            border-radius: 5px;
            margin: 0 auto;
            margin-top: 1em;
            box-sizing: border-box;
            padding: 1em;
        }
        .text-box-info{
            width: 75%;
            font-size: 1.1em;
        }

        .text-box-image {
            width: 20%;
            object-fit: contain;
            max-height: 100%;
        }
    }
}


.chat-instructions {
    background-color: rgba(255, 255, 255, 0.498); 
    margin-left: 15px;
    padding: 10px;
    border-radius: 5px;
    margin-bottom:auto !important;
    font-weight: bold;
    position: relative;
}

.chat-window-container {
    // min-height: 100vh;
    background-color: #d1dded;
    height: 100%;
    width: 100%;    
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
    box-sizing: border-box;
    padding: 5% 2.5% 2.5% 2.5%;

    .logout-link{
        color: #285394;
    }
    .logout-link:hover{
        color: #0d2549;
    }
    .logged-in {
        color: black;
        margin-bottom: 5%;
        display: flex;
        // align-items: flex-end;
        justify-content: space-between;
        align-items: center;

        
        .user-name {
            font-weight: 700;
        }
        .checkbox {
            margin-left: auto;
            
        }
    }
    .chat-window-inner-container {
        background-color: rgb(255, 255, 255); 
        height: 85%;
        width: 100%;
        box-sizing: border-box;
        padding: 15px 15px 15px 15px;
        border-radius: 7px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        .audio-overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 10;
        }
    }

    .chat-question-container {
        display: flex;
        flex-direction: column-reverse;
        height: auto;
        overflow: auto;
        width: 100%;
        margin-top: 10px;
        .wrapper {
            padding-top: 2px;
        }
    }

    .chat-question {
        width: 66%;
        height: auto;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        border-radius: 10px;
        padding: 2%;
        margin-bottom: 2%;
        position: relative;
        margin-left: 15px;
        .audio-overlay:focus {
            box-shadow: 0 0 0 0.125em rgb(0, 0, 0);
            border-color: transparent;
            border-radius: 10px;
            outline: none;
        }
    }

    .outgoing {
        background-color: #90b4df;
        color: black;
        margin-left: auto;
        margin-right: 15px;
    }

    .incoming {
        background-color: #d0e0f6;
        margin-left: 15px;  
    }

    .chat-question.outgoing:after {
        content: '';
        position: absolute;
        left: 100%;
        top: 50%;
        width: 0;
        height: 0;
        border: 15px solid transparent;
        border-left-color: #90b4df;
        border-right: 0;
        border-bottom: 0;
        margin-top: -10px;
        margin-right: -15px;
    }

    .chat-question.incoming:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        width: 0;
        height: 0;
        border: 15px solid transparent;
        border-right-color: #d0e0f6;
        border-left: 0;
        border-bottom: 0;
        margin-top: -10px;
        margin-left: -15px;
    }

    .user-input-container {
        width: 100%;
        height: 80px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 1%;

        .user-input {
            width: 47%;
            height: 6vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
            background-color: rgba(255, 255, 255, 0.2);
            border-width: 2px;
            border-color: rgba(255,255,255, 0.098);
            cursor: pointer;
            border-radius: 10px;
        }

        .user-input:hover {
            
        }

        .button1 {
            background-color: #aad3e2;
            color: black;
        }

        .button2 {
            border: 2px solid #adb9e2;
            background-color: white;
            color: #234e83;
        }

        .single-button {
            background-color: #aad3e2;
            color: black;
            width: 97%;
            margin-left: 1.5%;
            white-space: initial;
            font-size: 1.5vw;
            @media only screen and (max-width: 900px) {
                font-size: 2.5vw;
             }
        }   

    }
    
    .gear-icon {
        position:relative;
        top: 0.5em;
    }

    .button {
        /*&.is-main, &.is-info, &.has-icon, &.is-light, &.is-success, &.is-danger, &.is-dark, &.is-small, &.focused {*/
            &:not(:active):focus {
                box-shadow: 0 0 0 0.125em rgb(0, 0, 0);
                border-color: transparent;
            }
        /*}*/
    }
}