import { Component, OnInit, Input, HostListener } from '@angular/core'
import { ChatService, ChatNodeType, IResponse, IChatMessage } from '../chat.service';
import { AuthService } from '../../api/auth.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { ArrowTabNavigationService } from '../../core/arrow-tab-navigation.service';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { TextToSpeechService } from 'src/app/ui-testrunner/text-to-speech.service';
import { StudentDashboardService } from '../student-dashboard.service';

@Component({
    selector: 'sa-chat-window',
    templateUrl: './sa-chat-window.component.html',
    styleUrls: ['./sa-chat-window.component.scss']
})

export class SaChatWindowComponent implements OnInit {

    @Input() isOsslt
  
    constructor(
        private chat: ChatService,
        private auth:AuthService, 
        public lang:LangService,
        private router: Router,
        private routes: RoutesService,
        private arrowTabNav: ArrowTabNavigationService,
        public textToSpeech: TextToSpeechService,
        private StudentDashboardService: StudentDashboardService
    ) {}

    displayName:string;
    private messageInSoundDelay: number = 260;

    private queuedMessage: IChatMessage;
    private queuedMessageTimeout;
    private notificationSound = new Audio();

    ngOnInit() {
        this.notificationSound.src = "../../../assets/sounds/pop.wav";
        this.notificationSound.load();
        this.notificationSound.volume = 0.2;
        
        if(this.chat.getChatMessages().length === 0) {
            this.showMessages();
        }
        this.auth.user().subscribe((userInfo:any) => {
            if (userInfo){
                // console.log(userInfo)
                this.displayName = [userInfo.first_name, userInfo.last_name].join(' ')
            }
        })             
    }

    @HostListener('document:keydown', ['$event'])
    keyDown(event) {
        this.arrowTabNav.keyDown(event)                
    }

    playNotificationSound(){
        // setTimeout(() => {
        //     this.notificationSound.currentTime=0;
        //     this.notificationSound.play();
        // }, this.messageInSoundDelay);
    }

    showQueuedMessage() {
        if(this.queuedMessage) {
            this.chat.chatMessages.push(this.queuedMessage); 
            const qm = this.queuedMessage;
            setTimeout(()=>{qm.fadedIn = true;}, 500);
            this.queuedMessage = undefined;

            this.playNotificationSound();
            this.showMessages();
        }
    }

    showMessages() {
        this.chat.buttons = [];
        const newMessage = this.chat.next();
        if(typeof newMessage === "string") {
            this.queuedMessage = {incoming: true, message: newMessage};
            this.queuedMessageTimeout = setTimeout(() => {this.showQueuedMessage()}, 1500);
        } else {
            switch(newMessage.type) {
                case ChatNodeType.RESPONSE:
                    const response = <IResponse>newMessage;
                    this.chat.buttons = response.buttons;
                    if(response.open_answer) {
                        this.chat.showOpenAnswer = true;
                    }
                    break;
                case ChatNodeType.CHECKLIST_MESSAGE:
                    break;
            }
        }
    }

    logChatAction(text: string) {
        let prev_message;
        if(this.chat.chatMessages.length > 0) {
            prev_message = this.lang.tra(this.chat.chatMessages[this.chat.chatMessages.length - 1].message);
        }

        this.auth.apiCreate(this.routes.LOG, {slug: "STUDENT_CHAT_BTN_PRESS", data: {
            uid: this.auth.getUid(),
            button_text: this.lang.tra(text),
            prev_message
        }});
    }

    clickButton(event: any, text: string, skip?: number, reset?: number) {
        event.stopPropagation();

        this.logChatAction(text);

        let message = text;

        this.chat.chatMessages.push( {incoming: false, message});
        
        if(reset) {
            this.chat.reset(reset);
        } else {
            this.chat.skip(skip ? skip : 0);
        }

        this.showMessages();
    }

    skipDelay() {
        clearTimeout(this.queuedMessageTimeout);
        this.showQueuedMessage();
    }

    async logout(){
        await this.auth.logout();
        this.router.navigateByUrl(`/${this.lang.c()}/login-student?rldbxb=1`);
    }

    submitOpenAnswer() {
        this.chat.chatMessages.push( {incoming: false, message: "Open Answer"});
        this.showMessages();
    }

    getChatMessages() {
        return this.chat.getChatMessages();
    }

    getButtons() {
        return this.chat.getButtons();
    }

    showOpenAnswer() {
        return this.chat.showOpenAnswer;
    }

    logAction(status: boolean): void {
        this.StudentDashboardService.logAction('TTS_TOGGLE', {
            info: {isTTSActive: status}
        });
    }
}