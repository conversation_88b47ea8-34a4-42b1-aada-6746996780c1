<div [ngSwitch]="currentView">
  <div class = "osslt-speech-toggle">
    <mat-slide-toggle 
      *ngIf="showByView()"
      [class.is-right-side]="showTtsToggleOnRight()"
      [(ngModel)]="textToSpeech.isActive"
      (ngModelChange)="logAction($event)"
      (keydown.enter)="toggleAudioButton()"
      role="button"
      aria-label="{{lang.tra('lbl_text_to_speech_toggle')}}"
    >
    </mat-slide-toggle>
    <div *ngIf="showByView()">
      <tra-md audio-slug [class.landing]="applyLandingStyle()" [class.minds-on] = "applyMindsONStyle()" class="content" [isCondensed]="true" slug="text_to_speech_disabled" *ngIf="!textToSpeech.isActive"></tra-md>
      <tra-md audio-slug [class.landing]="applyLandingStyle()" [class.minds-on] = "applyMindsONStyle()" class="content" [isCondensed]="true" slug="text_to_speech_enabled" *ngIf="textToSpeech.isActive"></tra-md>
    </div>
  </div>

    <ng-container *ngSwitchCase="View.WELCOME">
      <div *ngIf="!isLoaded && !isShowFormLoadError()">
        Loading...
      </div>
      <!-- handles case where test data doesnt exist or error message exists. Will replace assessment welcome -->
      <div *ngIf="isShowFormLoadError()" class="warning-panel">
        <div>
          <tra-md [slug]="errorMessage"></tra-md>
          <div style="margin-top:0.5em;">
            <a [routerLink]="getDashboardRouterLink()" class="button"><tra slug="test_back_to_resources"></tra></a>
          </div>
        </div>
      </div>
      <osslt-welcome
        *ngIf="isLoaded && !isShowFormLoadError()"
        [testTakerName]="testTakerName"
        [isOperational]="isOperational"
        (continueMindsOn)="moveToMindsOn()"
      ></osslt-welcome>
    </ng-container>

    <div *ngSwitchCase="View.TEST_RUNNER">
        <test-runner
            *ngIf="testForm && !isShowingResults"
            (backToMap)='moveToMap()'
            (backToDashboard)="goToDashboard()"
            (endSection)="completeModule($event)"
            (questionTitles)="questionTitleMap = $event"
            (studentPosition)="setStudentPosition($event)"
            [asmtFmrk]="asmtFmrk"
            [currentSession] ="currentSession"
            [currentTestDesign]="testForm.currentTestDesign"
            [documentItems]="asmtFmrk.referenceDocumentPages"
            [frameWorkTags]="asmtFmrk.tags"
            [helpPageItem]="asmtFmrk.helpPageId"
            [isOsslt]="isOsslt && !isQuestionnaire"
            [sectionIndexInit]="testRunnerIndex"
            [questionIndexInit]="0"
            [isSectionControlsEnabled]="false"
            [isTimeEnabled]="!asmtFmrk.isTimerDisabled"
            [questionSrcDb]="testForm.questionSrcDb"
            [questionStates]="testForm.questionStates"
            [regularTimeRemaining]="999"
            [saveQuestion]="saveQuestionResponse"
            [sessions] = 'sessions'
            [studentG9Connection]="studentG9Connection"
            [customConfirmTestDialogData]="customConfirmTestDialogData"
            [submitTest]="submitTest"
            [testFormId]="-1"
            [testFormType]="asmtFmrk.testFormType"
            [testLang]="testForm.testLang"
            [testSessionId]="testSessionId"
            [testTakerName]="testTakerName"
            [ossltAsmtModules]="modules"
            [asmtSlug]="asmtSlug"
            [currentSubSession]="currentSubSession"
            [isPj]="false" 
            [isStudent]="true"
        ></test-runner>

      </div>

    <osslt-minds-on
        *ngSwitchCase="View.MINDS_ON"
        [testTakerName]="testTakerName"
        [testSessionId]="testSessionId"
        [itemSetId]="itemSetId"
        [isOperational]="isOperational"
        (moveToMap)="moveToMap()"
    ></osslt-minds-on>

    <ng-container *ngSwitchCase="View.MAP">
      <osslt-assessment 
        [modules]="modules"
        [testTakerName]="testTakerName"
        (moveToNext)="moveToModule($event)" 
        [session]="currentSession"
        [isOperational]="isOperational"
        [currentModulesDone]="currentModulesDone"
        (finishSession)="moveToSessionReview()"
      ></osslt-assessment>
    </ng-container>

    <osslt-session-review
        *ngSwitchCase="View.SESSION_REVIEW"
        (moveToSessionFinished)="moveToNextSession()"
        (goBackToMap)="moveToMap()"
        [testTakerName]="testTakerName"
        [session]="currentSession"
        [modules]="modules"
    ></osslt-session-review>

    <osslt-session-complete
        *ngSwitchCase="View.SESSION_COMPLETE"
        [testTakerName]="testTakerName"
        [isOperational]="isOperational"
        [session]="currentSession"
        (moveToMapFromSessionComplete)="moveToMapFromSessionComplete()"
        (moveToQuestionnaire)="moveToQuestionnaire()"
    ></osslt-session-complete>

    <osslt-congratulations   
        *ngSwitchCase="View.CONGRATULATIONS" 
        [moduleInfo]="modules[currentModuleIndex]"
        [testTakerName]="testTakerName"
        (moveToNext)="moveToMap()"
        (goBackToModule)="goBackToModule()"
        [modules]="modules"
    ></osslt-congratulations>

    <external-results *ngSwitchCase="View.RESULTS" [isOsslt]="true" [asmtFmrk]="asmtFmrk" [testForm]="testForm" [questionTitleMap]="questionTitleMap" [questionScores]="questionScores" [showReadingSelections]="true" [moveLogoDown]="true" [exitButtonCaption]="showExitButtonCaption()" (exitClick)="goToDashboard()"></external-results>

</div>