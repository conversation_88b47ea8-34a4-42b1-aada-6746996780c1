


<div *ngIf="!isLoaded">
  Loading student profile...
</div>
<ng-container *ngIf="isLoaded" [ngSwitch]="isShowingWelcomeScreen()">
  <ng-container *ngSwitchCase="true">
    <div class="welcome-screen">
      <div class="logo">
        <img alt="EQAO welcome logo"
          [src]="lang.tra(whiteLabelService.getSiteText('sample_test_welcome_logo'))" />
      </div>
      <div class="welcome-wrapper" [class.ldb-notification-present]="!lockdown.isRespondus">
        <div class="ldb-container">
          <s-modal-ldb-warning></s-modal-ldb-warning>
        </div>
        <div class="welcome-content">
          <span><tra slug="eqao_welcome_to"></tra></span>
          <div class="online-grade">
            <tra-md slug="eqao_onlineg9_mathematics"></tra-md>
          </div>
          <div>
            <button class="button welcome-button" (click)="processWelcome()"><span style="margin-right: 2em;"><tra slug="G9_Onboarding_Enter_Button"></tra></span><i class="fas fa-caret-right"></i></button>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngSwitchCase="false">
    <div class="stu-dashboard">
      <div *ngIf="!isOsslt && !displayForBC" class="stu-dashboard__identity">
        <sa-chat-window class="chat-window"></sa-chat-window>
        <!-- <div class="stu-dashboard__identity--description">Paragraph</div>
            <div class="stu-dashboard__identity--buttons">
                <button style="height:50px; width:130px; border:none; border-radius:3px;">click me</button>
            </div> -->
      </div>
      <div *ngIf="displayForBC" class="stu-dashboard__menu-bc">
        <div class="stu-dashboard__menu--description">
          Welcome to <b>{{getTestWindowTitle()}}</b>!
        </div>
        <div class="stu-dashboard__menu--description">
          You {{hasAttempted ? 'have completed' : 'will take'}} the assessment: <b>{{componentName}}</b>.
        </div>
        <div *ngIf="!hasAttempted" class="stu-dashboard__menu--buttons">
          <img src="../../../assets/student-g9/target_colour.png" alt="" class="menu-icon">
          <button audio-slug (click)="goToAssessment()" class="button menu-button">Start Assessment
          </button>
        </div>
        <div *ngIf="hasAttempted" class="stu-dashboard__menu--buttons">
          <button audio-slug (click)="logout()" class="button menu-button" style="padding-left: 1em;">Logout</button>
        </div>
      </div>
      <div *ngIf="isOsslt" class='osslt-dashboard'>
        <sa-chat-window class="chat-window" [isOsslt]="isOsslt"></sa-chat-window>
      </div>
      <ng-container *ngIf="!isOsslt && !displayForBC" [ngSwitch]="viewType">
        <div *ngSwitchCase="ViewType.MAIN" class="stu-dashboard__menu">
          <div class="stu-dashboard__menu--description">
            <!-- <img class="stu-dashboard-banner" src="https://bubo.vretta.com/vea/platform/vea-web-client/uploads/3673c58021db6063167346d41fae88d2/2_G9OnlineAssessment_Banner_0721.png"/> -->
            <!-- <div><tra slug="pop_up_appearning_auto"></tra></div> -->
            <div>
              <tra audio-slug slug="pop_up_appearning_auto"></tra>
              <br>
              <tra audio-slug slug="in_the_meantime"></tra>
            </div>
          </div>
          <div class="stu-dashboard__menu--buttons">
            <img src="../../../assets/student-g9/backpack_business.png" alt="" class="menu-icon" aria-hidden="true">
            <button audio-slug class="button menu-button" (click)="goToResources()">
              <tra slug="lbl_resources"></tra>
            </button>
          </div>
          <div class="stu-dashboard__menu--buttons">
            <img src="../../../assets/student-g9/assessment_icon.png" alt="" class="menu-icon" aria-hidden="true">
            <button audio-slug (click)="processAccessToAssessment()" class="button menu-button">
              <tra slug="lbl_assessments"></tra>
            </button>
          </div>
        </div>
        <student-g9-resources-view *ngSwitchCase="ViewType.RESOURCES" class="resources-view" [isOsslt]="isOsslt">
        </student-g9-resources-view>
        <reminder-to-skip *ngSwitchCase="ViewType.REMINDER_TO_SKIP" class="resources-view"></reminder-to-skip>
        <exercise-selection *ngSwitchCase="ViewType.EXERCISE_SELECTION" class="resources-view"></exercise-selection>
        <student-g9-checklist *ngSwitchCase="ViewType.CHECKLIST" class="resources-view"></student-g9-checklist>
      </ng-container>
    
      <ng-container *ngIf="isOsslt" [ngSwitch]="viewType">
        <div class="main-background">
          <div *ngSwitchCase="ViewType.MAIN" class="stu-dashboard__menu-osslt">
            <s-modal-ldb-warning style="width: 80%"></s-modal-ldb-warning>
            <div class="stu-dashboard__menu-osslt--buttons">
              <img src="../../../assets/student-g9/backpack-osslt-trans.png" alt="" class="menu-icon-osslt" aria-hidden="true">
              <button audio-slug class="button menu-Osslt-button" (click)="goToResources()">
                <tra slug="lbl_resources"></tra>
              </button>
            </div>
            <div class="stu-dashboard__menu-osslt--buttons">
              <img src="../../../assets/student-g9/computer-trans.png" alt="" class="menu-icon-osslt" aria-hidden="true">
              <button audio-slug (click)="processAccessToAssessment()" class="button menu-Osslt-button">
                <tra slug="lbl_tests"></tra>
              </button>
            </div>
          </div>
          <student-g9-resources-view *ngSwitchCase="ViewType.RESOURCES" class="resources-view" [isOsslt]="isOsslt">
          </student-g9-resources-view>
          <!-- <student-osslt-minds-on *ngSwitchCase="ViewType.MINDS_ON" class="resources-view" [isOsslt]="isOsslt">
          </student-osslt-minds-on> -->
          <student-osslt-tests-view *ngSwitchCase="ViewType.TESTS" class="resources-view" [isOsslt]="isOsslt"
            (liveTest)="goToAssessment($event)"></student-osslt-tests-view>
          <osslt-gen-instr *ngSwitchCase="ViewType.GEN_INSTR" [isOsslt]="isOsslt"></osslt-gen-instr>
          <exercise-selection *ngSwitchCase="ViewType.EXERCISE_SELECTION" class="resources-view" [isOsslt]="isOsslt">
          </exercise-selection>
          <student-g9-checklist *ngSwitchCase="ViewType.CHECKLIST" class="resources-view" [isOsslt]="isOsslt">
          </student-g9-checklist>
        </div>
      </ng-container>
    </div>
  </ng-container>
  <div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
      <div [ngSwitch]="currentModalType">
        <div *ngSwitchCase="StudentLandingModalType.UpcomingAssessments">
          <h3><b><tra slug="title_upcoming_assessments"></tra></b></h3>
          <table class="table is-bordered">
            <tr>
              <td><b><tra-md slug="lbl_assessment_type"></tra-md></b></td>
              <td><tra-md slug="lbl_start_date_time"></tra-md></td>
            </tr>
            <tr *ngFor="let session of scheduledSessions">
              <td>{{renderCaption(session.slug)}}</td>
              <td>{{renderDateTime(session.date_time_start)}}</td>
            </tr>
          </table>
        </div>
        <div *ngSwitchCase="StudentLandingModalType.VerifyStudentinfo">
          <div>
            <s-modal-verify-student 
              [student]="student"               
              (closeVerifyStudentModal)="verifyStudentModalFinish()" 
            ></s-modal-verify-student>
          </div>
        </div>
        <div *ngSwitchCase="StudentLandingModalType.AssessmentInfo">
            <s-modal-assessment-info 
              [assessmentSlugInfo]="session.slug"
              [isQuestionnaire]="session.is_questionnaire"
              (closeAssessmentInfoModal)="assessmentInfoModalFinish()" 
              (continueNavigitionToAssessment)="navigateToAssessment()"
            ></s-modal-assessment-info>
        </div>
      </div>
      <modal-footer *ngIf="currentModalType !== StudentLandingModalType.VerifyStudentinfo && currentModalType !== StudentLandingModalType.AssessmentInfo" [pageModal]="pageModal"></modal-footer>
    </div>
  </div>
  <s-ldb-iframe></s-ldb-iframe>
</ng-container>

<div class="custom-modal" *ngIf="studentDidNotPayModal">
  <div class="modal-contents notificationModal">
    <span>
      <tra slug="eqao_student_not_paid"></tra>
    </span>
    <br>
    <button class="button" (click)="closeNotification()"><tra slug="ie_close_modal"></tra></button>
  </div>
</div>

<div class="custom-modal" *ngIf="noClassDetection">
  <div class="modal-contents">
    <span>
      <!-- <tra slug="eqao_student_not_paid"></tra> -->
      No Session Detected, please contact invigilator.
    </span>
    <br>

    <div>
      <button 
        (click)="logout()" 
        class="button">
        <tra slug="lbl_logout"></tra>
      </button>
      <button 
        class="button"
        (click)="pageRefresh()"
      >
        Refresh Page
      </button>
    </div>
  </div>
</div>
