@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/registration-or-login-form.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/partials/_modal.scss';


.custom-modal { @extend %custom-modal; }
.page-body {
    @extend %page-body;
    .page-content {
        background-color: #F5F5F5;
        // @extend %page-form-content;
    }
}


.stu-dashboard{
    width: 100%;
    min-height:100vh;
    display:flex;
    @media only screen and (max-width: 860px) {
       flex-direction: column;
       min-height:100vh;
    }

    .osslt-dashboard {
        background: white;
        width: 50%;
        border-color:#61a7ab; 
        display:flex;
        flex-direction:column;
        justify-content: center;
        align-items:center;
        @media only screen and (max-width: 770px) {
            width:100%;
        }
         
    }
    .main-background{
        width:50%;
        // height:100%;
        background: linear-gradient(225deg, #109DD5 10%, #68d3e1be 90%), url("../../../assets/student-g9/gradient_image.png");
        background-size:cover;
        @media only screen and (max-width: 770px) {
            width:100%;
         }
    }

    &__menu-bc {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .stu-dashboard__menu--buttons {
            width: 19rem;
        }
    }
    &__menu-osslt{
        height:100%;
        display:flex;
        flex-direction:column;
        justify-content: center;
        align-items:center;
         &--buttons{
             position:relative;
             height:5em;
             width:13em;
             margin-bottom:3em;
             margin-top:2em;
             .menu-icon-osslt{
                position:absolute;
                left: -30%;
                bottom: -33%;
                z-index: 1;
             }
         }
    }

    &__identity{
        width:50%;
        border-radius:10px;
        // border: 1.35px solid;
        // border-color:white; 
        // background: linear-gradient(179.72deg, rgb(209,221,237) -0.66%, rgb(209,221,237) 4.25%, rgb(209,221,237) 15.06%);
        display:flex;
        flex-direction:column;
        justify-content: center;
        align-items:center;
        @media only screen and (max-width: 860px) {
            width:100%;
         }
    }
    &__menu{
        width:50%;
        // background: linear-gradient(to bottom, rgb(255, 255, 255) 0%, rgb(255, 255, 255) 100%);
        display:flex;
        flex-direction:column;
        justify-content: center;
        align-items:center;
        @media only screen and (max-width: 860px) {
            width:100%;
         }
         &--description{
             color:black;
             line-height: 1.3em;
             font-size: 1.4em;
             max-width:80%;
             margin-bottom:10em;
             @media only screen and (max-width: 860px) {
                margin-top:4em;
                margin-bottom:7em;
             }
             @media only screen and (max-height: 550px) {
                margin-bottom:1em;
             }
             font-weight: 100;
         }
         &--buttons{
             position:relative;
             height:5em;
             width:17em;
             margin-bottom:3em;
             //background:yellow;
           .button {
             &:focus {
               box-shadow: 0 0 0 0.125em #adadad;
               border-color: transparent;
               outline: none;
             }
           }
         }
    }
}
.menu-icon{
    position:relative; 
    left:-5%; 
    bottom:30%;
    width: 6em;
    // z-index:1; 
    
}
.menu-button{
    height:40px; 
    width:130px; 
    border:none; 
    position:absolute; 
    text-align: center;
    background:rgb(69, 88, 150); 
    color: white;
    width:100%; 
    border-radius:10px; 
    font-size:1.3em; 
    font-weight: 600;
}
.menu-Osslt-button{
    height:50px; 
    width:130px; 
    border:none; 
    position:absolute; 
    border-radius:3px; 
    text-align: center;
    padding-left:2.5em;
    left:0;
    bottom:0; 
    background: #CFEAF3; 
    width:100%; 
    border: 1px solid rgba(33, 42, 43, 0.897); 
    color:rgb(0, 0, 0); 
    border-radius:5px;
    font-size:1.3em;
     &:hover {
         background: rgba(207, 234, 243, 0.8)
     }
}

.ldb-container {
    width: 35em;
    margin-bottom: 1em;
}

.chat-window {
    height: 100%;
    width: 100%;
}

.resources-view {
    width: 50%;
    height: 100%;
    @media only screen and (max-width: 860px) {
        width:100%;
     }
}
.stu-dashboard__menu{
    background: url('https://eqao.vretta.com/authoring/user_uploads/515714/authoring/2_G9OnlineAssessment_Banner_0721/1666112769451/2_G9OnlineAssessment_Banner_0721.png');
    background-repeat: no-repeat;
    background-size: 100%;
}
.stu-dashboard__menu--description {
    margin-top: 10em;
    margin-bottom: 3em;
    font-weight: 600;
    font-size: 1.5em;
    min-width: 20em;
}

.welcome-screen{
    color: black;
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100vh;
    background-repeat: no-repeat, repeat;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    background-image: url('https://eqao.vretta.com/authoring/user_uploads/515714/authoring/1_G9OnlineAssessment_WelcomePage_0721-_1_/1666112786613/1_G9OnlineAssessment_WelcomePage_0721-_1_.png');
    .logo{
        width: 12em;
        margin-left: 7em;
        margin-top: 3em;
    }


    
    .welcome-wrapper {
        margin-top: 15em;
        margin-left: 25em;

        @media only screen and (max-width: 1020px) {
            margin-left: 5em;
         }
         @media only screen and (max-width: 730px) {
            margin-left: 7.5em;
         }

        &.ldb-notification-present {
            margin-top: 0em;
        }
    }

    .welcome-content{
        line-height: 1em;
        font-size: 4.3em; 

        @media only screen and (max-width: 1020px) {
            font-size: 3.5em;
         }
         @media only screen and (max-width: 730px) {
            font-size: 3em;
         }
        .online-grade{
            padding-top: .2em;
            font-size: 1em;
            line-height: 1.1em;
            font-weight: bolder;
            color: #253e92;
            margin-bottom: 1em;
        }
        .welcome-button{
            font-size: .4em;
            color: white;
            background-color: #41849c;
            border-radius: .5em;
            font-weight: 600;
            padding-left: 3em;
            height: 2em;
        }
    }
    
}
