import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StudentDashboardService } from '../student-dashboard.service';
import { AuthService, IUserInfo } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import * as moment from 'moment-timezone';
import { RoutesService } from '../../api/routes.service';
import { StudentG9ConnectionService } from '../student-g9-connection.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AssessmentType, BcAssessmentsService, TestWindow } from 'src/app/bc-assessments/bc-assessments.service';
import { EStyleProfile, StyleprofileService } from '../../core/styleprofile.service';
import { Subscription } from 'rxjs';
import { LDBLoginStrategy, LockdownService } from '../lockdown.service';

export enum StudentDashboardView {
  MAIN = "main",
  RESOURCES = "resources",
  TESTS = "tests",
  GEN_INSTR = "gen_Instructions",
  MINDS_ON = "minds_on",
  REMINDER_TO_SKIP = "reminder_to_skip",
  EXERCISE_SELECTION = "exercise_selection",
  CHECKLIST = "checklist"
}
import { IAsmtCoverPage } from './model/types';
import { DEFAULT_COVER_PAGE_INFO } from './model/default';
import { sanitizeCoverPagePayload } from './model/util';

export {IAsmtCoverPage}

let schlClassGroupType = new Set();

export enum StudentLandingModalType {
  UpcomingAssessments = 'UpcomingAssessments',
  VerifyStudentinfo = 'VerifyStudentinfo',
  AssessmentTypeInfo = 'AssessmentTypeInfo',
  AssessmentInfo = 'AssessmentInfo'
}
export const SEEN_WELCOME = 'SEEN_G9_WELCOME';

@Component({
  selector: 'view-student-g9-dashboard',
  templateUrl: './view-student-g9-dashboard.component.html',
  styleUrls: ['./view-student-g9-dashboard.component.scss']
})
export class ViewStudentG9DashboardComponent implements OnInit,OnDestroy{
  ViewType = StudentDashboardView;
  scheduledSessions;
  pageModal: PageModalController;
  isAssistiveTech: boolean = false;
  lockSubscription: any;
  testSubmitted: boolean;
  testSubmittedSub: Subscription = null;
  loggedInUserSub: Subscription = null;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    public lang: LangService,
    private pageModalService: PageModalService,
    private auth: AuthService,
    public loginGuard: LoginGuardService,
    private studentG9Connection: StudentG9ConnectionService,
    private dash: StudentDashboardService,
    private routes: RoutesService,
    private styleProfile: StyleprofileService,
    private bcAssessments: BcAssessmentsService,
    public whiteLabelService: WhitelabelService,
    public lockdown: LockdownService
  ) {
  }
  

  StudentLandingModalType = StudentLandingModalType;
  public viewType: StudentDashboardView = StudentDashboardView.MAIN;
  isOsslt = false; // this.route.snapshot.data['isOsslt']
  isLoaded = false;
  public session;

  public displayForBC: boolean = false;
  private testWindow: TestWindow;
  public testSessionSlug: string;
  public componentName: string; // @bced
  public hasAttempted: boolean = false;
  isWelcoming = false;
  styleProfileSub;
  displayName = '';
  sch_class_id;
  currentModalType: StudentLandingModalType;
  student:any
  assessmentType:string = null;
  noClassDetection:boolean = false
  SCH_CLASS_GROUP_TYPES: Set<any> = new Set();

  coverPageInfo:Partial<IAsmtCoverPage> = DEFAULT_COVER_PAGE_INFO()
  coverPageLang:string

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.displayForBC = this.isBC();

    this.route.params.subscribe(params => {
      if (params['viewType']) {
        this.viewType = params['viewType'];
      }
    });
    this.loggedInUserSub = this.auth.user().subscribe(async (userInfo: IUserInfo) => {
        if (userInfo) {
          //this.lockSubscription = this.studentG9Connection.sub().subscribe(this.setStudentSoftLock)
          this.student = {
            schClassGroupId: userInfo.sch_class_group_id,
            firstName: userInfo.first_name.toUpperCase(),
            lastName: userInfo.last_name.toUpperCase(),
            oenOrSasn: userInfo.studentOenOrSasn,
            isSasnLogin: userInfo.isSasnLogin,
          }
          this.sch_class_id = userInfo.sch_class_id;
          if(!this.getIsStudentVerified()){
            this.verifyStudentModalStart();
          }      
          this.determineStudentDashboard(userInfo)
          this.testSubmittedSub = this.studentG9Connection.testSubmitted.subscribe(testSubmitted => {
            this.testSubmitted = testSubmitted
            if(testSubmitted){
              this.studentG9Connection.showAssessmentPopup(true,'Q');
              this.goToAssessment()
            } 
          })

          if (this.displayForBC && userInfo.test_window_id) {
            // console.log(userInfo)
            this.bcAssessments.getTestWindowById(userInfo.test_window_id, AssessmentType.GRAD).then(testWindow => {
              this.testWindow = testWindow;
              this.testSessionSlug = userInfo.assessmentSlug.split('_')[1];
              if (userInfo.assessmentSlug.split('_')[0] === 'FSA') {
                const code = this.testSessionSlug.split('-')[0];
                const component = code.startsWith('LT') ? 'Literacy' : 'Numeracy';
                const grade = code.includes('4') ? 4 : 7;
                this.componentName = `Grade ${grade} ${component}`;
              } else {
                this.componentName = this.testSessionSlug;
              }

              this.dash.loadAssessments().then(res => {
                const sessions = res[0].classes_sessions.find(s => s.slug.toLowerCase().endsWith(this.testSessionSlug.toLowerCase()));
                if (!sessions || sessions.length === 0) {
                  this.hasAttempted = true;
                }
              })
            });
          }
        }
        
        if(userInfo.sch_class_id) { // If class id is not present, dashboard will not load and LDB will not be initialized
          this.lockdown.getLDBEncrptedLaunchLink({
            strategy: LDBLoginStrategy.AUTH_TOKEN,
            authToken: userInfo.accessToken,
            passwordSeed: userInfo.sch_class_id
          })
        }

        //this.lockSubscription = this.studentG9Connection.sub().subscribe(this.setStudentSoftLock)
        this.student = {
          schClassGroupId: userInfo.sch_class_group_id,
          firstName: userInfo.first_name.toUpperCase(),
          lastName: userInfo.last_name.toUpperCase(),
          oenOrSasn: userInfo.studentOenOrSasn,
          isSasnLogin: userInfo.isSasnLogin,
        }
        this.assessmentType = userInfo.assessmentType;
        if(!this.getIsStudentVerified()){
          this.verifyStudentModalStart();
        }      
        this.determineStudentDashboard(userInfo) // todo: what is this really doing?
        this.displayName = [userInfo.first_name, userInfo.middle_name, userInfo.last_name].join(' ')
        this.testSubmittedSub = this.studentG9Connection.testSubmitted.subscribe(testSubmitted => {
          this.testSubmitted = testSubmitted
          if(testSubmitted){
            this.studentG9Connection.showAssessmentPopup(true,'Q');
            this.goToAssessment()
          } 
        })

        if(this.whiteLabelService.isABED()) {
          // this.domainValidation.studentDomainCheck();
        }

        if(this.whiteLabelService.isABED()) {
          await this.initCoverPage(userInfo.sch_class_id, userInfo.uid);
        } else {
          await this.lockdown.initSecurityConfiguration(userInfo.sch_class_id) //
        }
    });
    
    window.onbeforeunload = () => this.studentG9Connection.disconnect();
  }

  pageRefresh(){
    window.location.reload()
  }

  ngOnDestroy(){
    if(this.lockSubscription){
      this.lockSubscription.unsubscribe()
    }

    if(this.testSubmittedSub) {
      this.testSubmittedSub.unsubscribe();
    }

    if(this.loggedInUserSub) {
      this.loggedInUserSub.unsubscribe();
    }

    if(this.styleProfileSub) {
      this.styleProfileSub.unsubscribe()
    }
  }

  async determineStudentDashboard(userInfo: IUserInfo){
    if(!userInfo || !['EQAO_G9', 'EQAO_G10'].includes(userInfo.sch_class_group_type)) {
      return;
    }

    if(userInfo.assistive_tech) {
      this.isAssistiveTech = true;
    }
    if (userInfo.sch_class_group_type) {
      this.determineStudentFilter(userInfo.sch_class_group_type == "EQAO_G10");
      this.isLoaded = true;
    }
    else {
      const res = await this.auth.apiGet(this.routes.STUDENT_ASMT_FILTER, userInfo.uid, userInfo.uid);
      
      if(res["eqao_sdc.IS_G10"] != "1" && res["eqao_sdc.IS_G9"] != "1") {
        return;
      }
      this.determineStudentFilter(res["eqao_sdc.IS_G10"] == "1")
      this.isLoaded = true;
    }

    if(localStorage.getItem(SEEN_WELCOME) != 'true' && !this.isOsslt) {
      this.isWelcoming = true;
    }
    this.dash.init(); //initialize g9 connection
  }

  determineStudentFilter(isOSSLT) {
    if (isOSSLT) {
      this.isOsslt = true;
      //OSSLT style profile set in the osslt-page-switcher component
    } 
    else{
      this.isOsslt = false;
      if(!this.styleProfileSub) {
        //wait for default style profile to load before setting an override
        this.styleProfileSub = this.styleProfile.styleProfileSub.subscribe(loaded => {
          if(loaded) {
            this.styleProfile.setStyleProfile(EStyleProfile.GR9, true);
          }
        })
      }
    }
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  goToResources() {
    this.dash.goToDashboardView(StudentDashboardView.RESOURCES, this.isOsslt);
  }
  goToTests() {
    this.goToAssessment()
    // this.dash.goToDashboardView(StudentDashboardView.TESTS, this.isOsslt);
  }

  setStudentSoftLock = (res) => {

    console.log(res)
    if (res.isReady) {

      console.log(res, 'send soft lock')
      // setTimeout(function(){ alert("Hello"); }, 3000);
      if (this.isAssistiveTech) {
        this.studentG9Connection
          .updateStudentPosition({
            stageIndex: null,
            questionCaption: null,
            softLock: 1
          });
        this.studentG9Connection
          .updateStudentPosition({
            stageIndex: null,
            questionCaption: null,
            softLock: 1
          });
      }

    }
  }

  configureQueryParams() {
    if (this.auth.u()) {
      return {
        query: {
          schl_class_group_id: this.auth.u().sch_class_group_id,
          payCheck: true
        }
      }
    }
    return null;
  }

  studentDidNotPayModal: boolean = false;
  closeNotification(){
    this.studentDidNotPayModal = false;
  }

  async goToAssessment(event?) {
    await this.dash.checkRedisCacheInitialized();
    const res = await this.dash.loadAssessments();
    const sessions = res[0].classes_sessions;

    if(!sessions || !sessions.length) {
      const scheduled_sessions = res[0].scheduled_sessions;
      if (scheduled_sessions && scheduled_sessions.length > 0) {
        this.scheduledSessions = scheduled_sessions
        console.log('scheduledSessions', this.scheduledSessions)
        return this.newScheduledModalStart();
      } else {
        return this.loginGuard.disabledPopup('txt_student_no_asmt');
      }
    }

    if(this.testSessionSlug == null) {
      this.session = sessions[0];
    } else {
      this.session = sessions.find(s => s.slug.toLowerCase().endsWith(this.testSessionSlug.toLowerCase())) || sessions[0];
    }

    if (!this.session) {
      return this.loginGuard.disabledPopup('txt_student_no_asmt');
    } 
    if(!this.session.isPaid) {
      return this.loginGuard.disabledPopup(this.lang.tra('eqao_student_not_paid'))
    } 
    if(this.session.isAltVersionRequestsPending) {
      return this.loginGuard.disabledPopup(this.lang.tra('eqao_student_alt_request_pending'))
    } 
    if(this.session.slug.includes("OPERATIONAL") && this.session.isAltVersionRequestsApproved && !this.session.isAltVersionRequestsOperationalSent) {
      return this.loginGuard.disabledPopup(this.lang.tra('eqao_student_alt_request_not_op_sent'))
    }
    if(this.lockdown.isInsecure) {
      return this.loginGuard.quickPopup('txt_ldb_warning_popup')
    }

    if(this.session.slug) { // pop-up with asmt info
      return this.assessmentInfoModalStart()
    } else {
      return this.navigateToAssessment();
    }   
  }

  /**
   * Navigates student to their corresponding page switcher
   */
  navigateToAssessment() {
    if (this.isOsslt) {
      this.router.navigate(
        [`${this.lang.c()}/student/osslt-assessment/${this.session.test_session_id}`]
      );
    } else {
      this.router.navigate(
        [`${this.lang.c()}/student/assessment/${this.session.test_session_id}`]
      );
    }  
  }

  newScheduledModalStart() {
    this.currentModalType = StudentLandingModalType.UpcomingAssessments;
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    const config: any = {};
    this.pageModal.newModal({
      type: '',
      config,
      finish: config => this.newScheduledModalFinish()
    });
  }

  newScheduledModalFinish() {
    this.currentModalType = null;
    this.pageModal.closeModal();
  }

  renderCaption(slug) {
    let traSlug:string
    switch (slug) {
      case "G9_OPERATIONAL": 
        traSlug = "g9_assess_math";
        break
      case "G9_SAMPLE": 
        traSlug = "lbl_sample_g9";
        break
      case "OSSLT_OPERATIONAL": 
        traSlug = "lbl_osslt_test";
        break
      case "OSSLT_SAMPLE": 
        traSlug = "lbl_osslt_practice_test"
        break
      default:
        traSlug = slug
    }
    return this.lang.tra(traSlug)
  }

  verifyStudentModalStart(){
    this.currentModalType = StudentLandingModalType.VerifyStudentinfo;
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    const config: any = {};
    this.pageModal.newModal({
      type: '',
      config,
      finish: config => this.verifyStudentModalFinish()
    });
  }

  verifyStudentModalFinish(){
    this.currentModalType = null
    this.pageModal.closeModal();
  }

  getIsStudentVerified(){
    const isStudentVerified = this.auth.getCookie("isStudentLastNameVerified");
    if(isStudentVerified) return true;
    return false;
  }

  renderDateTime(dateTime) {
    return `${this.renderDay(dateTime)} at ${this.renderTime(dateTime)}`
  }

  renderDay(dateTime) {
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format(this.lang.tra('datefmt_day_month'));
  }

  renderTime(dateTime) {
    const m = moment.tz(dateTime, moment.tz.guess());
    return m.format('h:mm A')
  }

  isBC(): boolean {
    return this.whiteLabelService.getSiteFlag('IS_BCED') === true;
  }

  getTestWindowTitle(): string {
    if (!this.testWindow) return '...';
    return this.bcAssessments.getTestWindowTitle(this.testWindow);
  }

  logout(): void {
    if(this.lockdown.isRespondus) {
      return this.lockdown.ldbExitPrompt('ldb_exit_caption');
    }

    this.auth
      .logout()
      .then(r => {
        if (this.whiteLabelService.getSiteFlag('IS_BCED')){
          this.router.navigate(['/en/bced-landing/admin']);
        }
        else{
          this.router.navigate(['/en/login-router-st']);
        }
        setTimeout(() => {
          window.location.reload();
        }, 300);
      });
  }

  isShowingWelcomeScreen() {
    return this.isWelcoming;
  }

  processWelcome() {
    this.dash.init(); // @outdated: connects to the websockets, allows pop-ups to appear - register student as online.
    this.isWelcoming = false;
    localStorage.setItem(SEEN_WELCOME, 'true');
  }

  /**
   * Starts a pop-up modal with "You are starting the X assessment"  
   * Navigates to the page switcher upon modal confirmation
   */
  assessmentInfoModalStart() {
    this.currentModalType = StudentLandingModalType.AssessmentInfo;
    if(!this.pageModal) {
      this.pageModal = this.pageModalService.defineNewPageModal();
    }
    const config: any = {};
    this.pageModal.newModal({
      type: '',
      config,
      finish: config => this.assessmentInfoModalFinish()
    });
  }

  assessmentInfoModalFinish() {
    this.currentModalType = null
    this.pageModal.closeModal();
  }

  async processAccessToAssessment() {
    if(this.isOsslt) {
      this.goToTests();
    } else {
      this.goToAssessment();
    }
  }
  
  getLang() {
    return this.lang.getCurrentLanguage();
  }

  isLangActive(langCode: string) {
    return (this.getLang() === langCode);
  }

  setLang(langCode: string) {
    this.lang.setCurrentLanguage(langCode);
  }

  async initCoverPage(school_class_id:number, uid:number){
    let coverPage:Partial<IAsmtCoverPage>
    if (school_class_id){
      coverPage = await this.auth.apiGet(this.routes.TEST_COVER_PAGE_INFO, 1, {
        query: {
          school_class_id,
          uid
        }
      });
    }
    else {
      coverPage = {};
    }
    let isCoverPage = !!coverPage.type_slug
    if (!isCoverPage){
      this.noClassDetection = true;
      this.coverPageLang = this.coverPageInfo.lang
    }
    else {
      const {coverPageInfo, coverPageLang} = sanitizeCoverPagePayload(coverPage)
      this.coverPageInfo = coverPageInfo
      this.setLang(coverPageLang);
      this.coverPageLang = this.getLang()
      await this.lockdown.initSecurityConfiguration(school_class_id, coverPage.test_session_id)
    }
  }
}
