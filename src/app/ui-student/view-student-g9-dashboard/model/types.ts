export interface IAsmtCoverPage {
  test_window_id: number,
  test_session_id: number,
  lang: string,
  type_slug: string,
  course_code: string,
  window_date_human: string,
  title_persistent: string,
  course_name_full: string,
  part_code: string,
  part_description_short: string,
  cover_color: string,
  resource_td_id: number
  resource_item_set_id: number
  is_simple_cover?: number
  cover_security_msg?: number
  disable_province_logo: boolean,
  disable_add_time_msg: boolean,
  swap_title_persistent_and_course_name_full: boolean,
  additional_note_question_source?: string,
  disable_security_message?: boolean,
  course_name_full_override?: string
  cover_page_configs?:any, // raw api response
  cover_page_configs_overrides?:any, // raw api response
}

// Comes from the cover_page_configs column in the twtt table
export interface IAsmtCoverPageConfigs {
  disable_add_time_msg?: boolean,
  disable_province_logo?: boolean,
  swap_title_persistent_and_course_name_full?: boolean,
  additional_note_question_source?: string,
  disable_security_message?: boolean
}

// Comes from the cover_page_configs column in the twtdar table
export interface IAsmtCoverPageConfigsOverrides {
  title_override?: string
}