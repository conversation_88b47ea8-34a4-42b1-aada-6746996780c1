import { renderLangJson } from "src/app/core/util/lang-json";
import { IAsmtCoverPage, IAsmtCoverPageConfigs, IAsmtCoverPageConfigsOverrides } from "./types";
import { DEFAULT_COVER_PAGE_COLOR } from "./default";

export const sanitizeCoverPagePayload = (coverPage: Partial<IAsmtCoverPage>) => {
    let isCoverPage = !!coverPage.type_slug // checking if a non empty record was received
    if (isCoverPage){
      let converpageConfig: IAsmtCoverPageConfigs = {
        disable_add_time_msg: false,
        disable_province_logo: false
      }
      if (coverPage.cover_page_configs){
        converpageConfig = JSON.parse(coverPage.cover_page_configs);
      }

      let converpageConfigOverrides: IAsmtCoverPageConfigsOverrides = {}
      if (coverPage.cover_page_configs_overrides){
        converpageConfigOverrides = JSON.parse(coverPage.cover_page_configs_overrides)
      }
      
      const coverPageInfo = {
        window_date_human:      renderLangJson(coverPage.window_date_human, coverPage.lang),
        title_persistent:       renderLangJson(coverPage.title_persistent, coverPage.lang),
        course_name_full:       converpageConfigOverrides.title_override ? converpageConfigOverrides.title_override : coverPage.course_name_full,
        part_code:              coverPage.part_code,
        part_description_short: coverPage.part_description_short,
        cover_color:            coverPage.cover_color || DEFAULT_COVER_PAGE_COLOR,
        resource_td_id:         coverPage.resource_td_id,
        resource_item_set_id:   coverPage.resource_item_set_id,
        is_simple_cover:        coverPage.is_simple_cover,
        cover_security_msg:     coverPage.cover_security_msg,
        // Overrides from the TWTT table
        disable_add_time_msg:   converpageConfig.disable_add_time_msg,
        disable_province_logo:  converpageConfig.disable_province_logo,
        swap_title_persistent_and_course_name_full: converpageConfig.swap_title_persistent_and_course_name_full,
        additional_note_question_source: converpageConfig.additional_note_question_source,
        disable_security_message:  converpageConfig.disable_security_message,
      }
      
      return {
          coverPageInfo,
          coverPageLang: coverPage.lang,
      }
    } 
}