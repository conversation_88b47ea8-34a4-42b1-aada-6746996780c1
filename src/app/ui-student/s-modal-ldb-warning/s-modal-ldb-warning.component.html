<div class="lockdown-container is-centered">
    <div class="box lockdown-box" *ngIf="lockdown.isRenderLockdownPopup">
      <div class="box-title">
        <h2 class="title">
          <i class="fas fa-exclamation-triangle"></i>&nbsp;&nbsp;
          <tra [slug]="'lbl_ldb_required'"></tra>
        </h2>
      </div>
      <div class="box-content">
        <p>
          {{renderLockdownPopup}}
        </p>
        <ng-container *ngIf="lockdown.isChromeBook && !lockdown.isChromeBookLDBExtension">
          <br>
          <p>
            {{lockdown.renderChromebookWarning}}
          </p>
        </ng-container>
        <br>
        <a (click)="lockdown.installInstructionsPopup()">
          <tra [slug]="'lbl_ldb_install_instructions'"></tra>
        </a>
        <hr>
        <div *ngIf="!lockdown.isChromeBook">
          <a [attr.href]="lockdown.ldbLaunchURL" 
              class="button menu-button is-info is-fullwidth" 
              [class.ABED-button]="whiteLabelService.isABED()"
              [class.is-loading]="!lockdown.ldbLaunchURL" 
              (click)="lockdown.onRespondusLaunchBtn($event)"
              >
            <span><i class="fa fa-globe"></i></span>&nbsp;&nbsp;
            <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'lbl_ldb_launch_btn_beta' : 'lbl_ldb_launch_btn'"></tra>
          </a>
        </div>
        <div *ngIf="lockdown.isChromeBook">
          <a href="#" 
             class="button menu-button is-info is-fullwidth" 
             [class.ABED-button]="whiteLabelService.isABED()"
             [class.is-loading]="!lockdown.ldbLaunchURL" 
             (click)="lockdown.onRespondusLaunchBtn($event)"
          >
            <span><i class="fa fa-globe"></i></span>&nbsp;&nbsp;
            <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'lbl_ldb_launch_btn_beta' : 'lbl_ldb_launch_btn'"></tra>
          </a>
        </div>
      </div>
    </div>
</div>