import { Component, OnInit } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { LockdownService, IS_RESPONDUS_BETA_MODE } from '../lockdown.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

@Component({
  selector: 's-modal-ldb-warning',
  templateUrl: './s-modal-ldb-warning.component.html',
  styleUrls: ['./s-modal-ldb-warning.component.scss']
})
export class SModalLdbWarningComponent implements OnInit {

  constructor(
    public langService: LangService,
    public lockdown: LockdownService,
    public whiteLabelService: WhitelabelService,
  ) { }

  ngOnInit(): void {
  }

  IS_RESPONDUS_BETA_MODE = IS_RESPONDUS_BETA_MODE;

  public get renderLockdownPopup() {
    // For non-Respondus secure environments, show a recommendation to use it
    if(!this.lockdown.isInsecure && !this.lockdown.isRespondus) {
      return this.langService.tra('txt_ldb_cover_page_secure')
    }

    return this.langService.tra('txt_ldb_cover_page_warning', undefined, {EXAM_SUPERVISOR_TEMPLATE: this.langService.tra('lbl_invigilator')})
  }
}
