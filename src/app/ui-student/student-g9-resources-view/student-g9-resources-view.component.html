<div *ngIf="!isOsslt" class="resources-view">
    <div class="row-div">
        <div class="view-div">
            <img class="view-icon" src="../../../assets/student-g9/backpack_business.png" alt="" aria-hidden="true">
            <p class="view-text"><tra audio-slug slug="lbl_resources"></tra></p>
        </div>
        <button name="back button" class="back-button button" (click)="goBack()" audio-slug slug="btn_back" [attr.aria-label]="lang.tra('btn_back')" [tooltip]="lang.tra('btn_back')">
            <img class="back-arrow" src="../../../assets/student-g9/return_arrow.png" alt="" aria-hidden="true">
        </button>
    </div>
    
    <div class="center-resources-menu">
        <div class="resources-menu">
            <student-g9-menu-button class="full-width" *ngFor="let button of buttons" [text]="button.text" [click]="button.click"></student-g9-menu-button>     
        </div>
    </div>
</div>

<div *ngIf="isOsslt" class="resources-view">
    <button class="row-div-osslt button resource-Osslt-button" (click)="goBack()" audio-slug slug="btn_back" [tooltip]="lang.tra('btn_back')" [attr.aria-label]="lang.tra('btn_back')">
        <img class="view-icon" src="../../../assets/student-g9/backpack-osslt-trans.png" alt="" aria-hidden="true">
        <p class="view-text-osslt"><tra audio-slug slug="lbl_resources"></tra></p>
        <img class="back-arrow " src="../../../assets/student-g9/return_arrow.png" alt="" (click)="goBack()" aria-hidden="true">
    </button>
    
    <div class="center-resources-menu">
        <div class="resources-menu">
            <student-g9-menu-button class="full-width" *ngFor="let button of buttons" [isOsslt]="isOsslt" [text]="button.text" [click]="button.click"></student-g9-menu-button>     
        </div>
    </div>
</div>