import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ViewStudentG9DashboardComponent } from './view-student-g9-dashboard/view-student-g9-dashboard.component';
import { ViewStudentG9ExerciseComponent } from './view-student-g9-exercise/view-student-g9-exercise.component';
import { UiStudentRoutingModule } from './ui-student-routing.module';
import { SaChatWindowComponent} from '../ui-student/sa-chat-window/sa-chat-window.component';
import { StudentG9ResourcesViewComponent } from './student-g9-resources-view/student-g9-resources-view.component';
import { ReminderToSkipComponent } from './reminder-to-skip/reminder-to-skip.component';
import { ExerciseSelectionComponent } from './exercise-selection/exercise-selection.component';
import { StudentG9HeaderComponent } from './student-g9-header/student-g9-header.component';
import { StudentG9MenuButtonComponent } from './student-g9-menu-button/student-g9-menu-button.component';
import { StudentG9ChecklistComponent } from './student-g9-checklist/student-g9-checklist.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { ViewFieldTestAssessComponent } from './view-field-test-assess/view-field-test-assess.component';
import { ViewStudentLiveAssessComponent } from './view-student-live-assess/view-student-live-assess.component';
import { OssltAssessmentComponent } from './osslt-assessment/osslt-assessment.component'
import { OssltWelcomeComponent } from './osslt-welcome/osslt-welcome.component'
import { OssltMindsOnComponent } from './osslt-minds-on/osslt-minds-on.component'
import { OssltCongratulationsComponent } from './osslt-congratulations/osslt-congratulations.component'
import {OssltTestRunnerComponent} from './osslt-test-runner/osslt-test-runner.component'
import {OssltPageSwitcherComponent } from './osslt-page-switcher/osslt-page-switcher.component'
import {OssltGenInstrComponent} from './osslt-gen-instr/osslt-gen-instr.component'
import {OssltSessionCompleteComponent} from './osslt-session-complete/osslt-session-complete.component'
import {OssltToolExplorationComponent} from './osslt-tool-exploration/osslt-tool-exploration.component'
import {OssltSessionReviewComponent} from './osslt-session-review/osslt-session-review.component'
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { StudentOssltTestsViewComponent } from './student-osslt-tests-view/student-osslt-tests-view.component';
import { StudentOssltMindsOnComponent } from './student-osslt-minds-on/student-osslt-minds-on.component';
import { ExternalResultsComponent } from './external-results/external-results.component';
import { ViewStudentG9ProportionalIsrComponent } from './view-student-g9-proportional-isr/view-student-g9-proportional-isr.component';
import { pjWelcomeComponent } from './pj-welcome/pj-welcome.component';
import { pjHomeComponent } from './pj-home/pj-home.component';
import { PjPageSwitcherComponent } from './pj-page-switcher/pj-page-switcher.component';
import { PjMapComponent } from './pj-map/pj-map.component';
import { pjCompletionComponent } from './pj-completion/pj-completion.component';
import { MapElementComponent } from './map-element/map-element.component';
import { pjExerciseComponent } from './pj-exercise/pj-exercise.component';
import { G9TtsToggleComponent } from './g9-tts-toggle/g9-tts-toggle.component';
import { PjMapButtonComponent } from './pj-map-button/pj-map-button.component';
import { SModalVerifyStudentComponent } from './s-modal-verify-student/s-modal-verify-student.component';
import { DiagnosticsPageSwitcherComponent } from './diagnostics-page-switcher/diagnostics-page-switcher.component';
import { SModalAssessmentInfoComponent } from './s-modal-assessment-info/s-modal-assessment-info.component';
import { TooltipModule } from 'ng2-tooltip-directive';
import { SModalLdbWarningComponent } from './s-modal-ldb-warning/s-modal-ldb-warning.component';
import { SLdbIframeComponent } from './s-ldb-iframe/s-ldb-iframe.component';
@NgModule({
  declarations: [
    ViewStudentG9DashboardComponent,
    ViewStudentG9ExerciseComponent,
    SaChatWindowComponent,
    ViewFieldTestAssessComponent,
    StudentG9ResourcesViewComponent,
    ReminderToSkipComponent,
    ExerciseSelectionComponent,
    StudentG9HeaderComponent,
    StudentG9MenuButtonComponent,
    StudentG9ChecklistComponent,
    ViewStudentLiveAssessComponent,
    OssltAssessmentComponent,
    OssltWelcomeComponent,
    OssltMindsOnComponent,
    OssltCongratulationsComponent,
    OssltPageSwitcherComponent,
    OssltTestRunnerComponent,
    OssltGenInstrComponent,
    OssltSessionCompleteComponent,
    OssltToolExplorationComponent,
    OssltSessionReviewComponent,
    StudentOssltTestsViewComponent,
    StudentOssltMindsOnComponent,
    ExternalResultsComponent,
    ViewStudentG9ProportionalIsrComponent,
    pjWelcomeComponent,
    pjHomeComponent,
    PjPageSwitcherComponent,
    PjMapComponent,
    pjCompletionComponent,
    pjExerciseComponent,
    MapElementComponent,
    G9TtsToggleComponent,
    PjMapButtonComponent,
    SModalVerifyStudentComponent,
    DiagnosticsPageSwitcherComponent,
    SModalAssessmentInfoComponent,
    SModalLdbWarningComponent,
    SLdbIframeComponent,
  ],
  imports: [
    CommonModule,
    UiStudentRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    MatSlideToggleModule,
    UiPartialModule,
    UiTestrunnerModule,
    TooltipModule
  ],
  exports: [
    ExternalResultsComponent
  ]
})
export class UiStudentModule { }
