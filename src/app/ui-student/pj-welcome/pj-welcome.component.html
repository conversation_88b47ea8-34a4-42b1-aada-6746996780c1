<div class="welcome-container" [class.is-g6]="isG6" [class.is-g3]="!isG6">
    <div class="user-name-logout-btn">
        <span  *ngIf="displayName" class='user-name'><tra audio-slug [slug]="displayName"></tra></span>
        <button class="welcome-logout-button" (click)="logout()">
            <span *ngIf="!isPublic"><a class='logout-link logout-btn' (click)="logout()"><tra slug="btn_logout"></tra></a></span>
        </button>
    </div>

    <div class="welcome-dashboard">
        <div class="welcome-txt-container">
            <div class="logo">
                <img audio-slug slug="pj_eqao_logo" [alt]="lang.tra('pj_eqao_logo')"
                  [src]="lang.tra(whitelabel.getSiteText('sample_test_welcome_logo'))" />
            </div>
            <tra-md audio-slug [isCondensed]="true" class="main-welcome-txt" [slug]="getWelcomeSlug()"></tra-md>
            <tra-md *ngIf="session.isAltVersionRequestsApproved && linear === 1" audio-slug class="main-welcome-sub-sub-text" slug="pj_alternative_version_audio"></tra-md>
            <tra-md *ngIf="session.isAltVersionRequestsApproved && linear === 2" audio-slug class="main-welcome-sub-sub-text" slug="pj_alternative_version"></tra-md>
            <tra-md audio-slug [isCondensed]="true" class="main-welcome-sub-text" slug="pj_welcome_sub"></tra-md>
            <!-- <div role="button" (click)="goHome.emit($event)" class="enter-button welcome-next-btn"><tra audio-slug slug="pj_g6_welcome_enter_btn"></tra></div> -->
            <button class="enter-button-test welcome-next-btn" (click)="goHome.emit($event)">
                <tra audio-slug slug="pj_g6_welcome_enter_btn"></tra>
            </button>
        </div>
    
        <div class="welcome-ldb-container">
            <s-modal-ldb-warning></s-modal-ldb-warning>
        </div>
    </div>
</div>