.welcome-container {
    display: flex;
    flex-direction: column;

    background-position: center;
    min-height: 100vh;
    width: 100%;
    background-repeat: no-repeat, repeat;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;
    color: black;
    font-weight: bolder;

    .welcome-dashboard {
        display: flex;
        gap: 2em;
        flex-direction: row;
        flex-wrap: wrap-reverse; // Last elements shown first on mobile
        align-items: start;

        margin-left: 5em;
        margin-top: 1em;
        margin-bottom: 1em;
        margin-right: 2em;
    }

    &.is-g6{
        background-image: url("https://eqao.vretta.com/authoring/user_uploads/515714/authoring/v1Asset 107_space_welcome/1638475295031/v1Asset 107_space_welcome.svg");        
        .main-welcome-txt{
            font-size: 6em;
        }

        .main-welcome-sub-text {
            font-size: 4em;
        }

        .welcome-next-btn{
            font-size: 5em;
        }
        
        .welcome-txt-container {
            padding: 1em;
            // padding-top: 5em;
            // padding-left: 7em;/
            // padding-right: 48%;
            .enter-button {
                border-width: 10px;
            }
        }

        @media screen and (max-width:1470px) and (min-height:780px){
            .main-welcome-txt{
                font-size: 5em;
            }
            .main-welcome-sub-text{
                font-size: 3em;
            }
            .main-welcome-sub-sub-text {
                font-size: 1.5em;
            }
            .welcome-next-btn{
                font-size: 5em;
            }
        }
        @media screen and (max-width:1056px) {
            .main-welcome-txt{
                font-size: 3.5em;
            }
            .main-welcome-sub-text{
                font-size: 2.5em;
                line-height: 1em;
                margin: .1em;
            }
            .main-welcome-sub-sub-text {
                font-size: 1.25em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                // margin-top: .5em;
            }
        }
        @media screen and (max-width:700px) {
            display: flex;
            justify-content: space-around;
            align-items: center;
            .welcome-txt-container{
                padding: 1em;
                background-color: rgba(255,255,255,0.8);
                border-radius: 2em;
            }
            
            .main-welcome-txt{
                font-size: 3.5em;
            }
            .main-welcome-sub-text{
                font-size: 2.5em;
                line-height: 1em;
                margin: .1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1.25em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:500px){
            .logo{
                margin-bottom: 0.5em;
            }
            .main-welcome-txt{
                font-size: 3em;
            }
            .main-welcome-sub-text{
                font-size: 2em;
                line-height: 0.5em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 2em;
                border: solid 3px black;
                border-width: 7px
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:600px) and (min-height:500px){
            .logo{
                margin-bottom: 1em;
            }
            .main-welcome-txt{
                font-size: 3em;
            }
            .main-welcome-sub-text{
                font-size: 2em;
                line-height: 1em;
                margin: .1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                border-width: 10px
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:750px) and (min-height:600px){
            .logo{
                margin-bottom: 1em;
            }
            .main-welcome-txt{
                font-size: 4em;
            }
            .main-welcome-sub-text{
                font-size: 2.8em;
                line-height: 1em;
                margin: .1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1.4em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                border-width: 10px
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:420px){
            .welcome-txt-container {
                padding-top: 3em;
            }
        }

        @media screen and (max-height:370px){
            .welcome-txt-container {
                padding-top: 1em;
            }
        }

    }

    &.is-g3{
        display: flex;
        background-image: url("https://eqao.vretta.com/authoring/user_uploads/515714/authoring/v1Asset 53_field_welcome/1638475331821/v1Asset 53_field_welcome.svg");

        .main-welcome-txt{
            font-size: 4em;
        }

        .main-welcome-sub-text {
            font-size: 3em;
        }

        .welcome-next-btn{
            font-size: 4em;
        }    

        .welcome-txt-container {
            padding: 1em;
            width: 53em;
            height: fit-content;
            background-color: rgba(255,255,255,0.5);
            border-radius: 2em;
            .enter-button {
                border-width: 5px;
            }
        }

        .welcome-dashboard {

        }

        @media screen and (max-width:617px) {
            display: flex;
            justify-content: center;
            .main-welcome-txt{
                font-size: 3em;
                line-height: 1.2em;
            }
            .main-welcome-sub-text{
                font-size: 2em; 
                line-height: 1.2em;
            }
            .welcome-next-btn{
                font-size: 3em;
            }
            .welcome-txt-container{
                padding: 1em;
                background-color: rgba(255,255,255,0.5);
                border-radius: 2em;
            }
        }

        @media screen and (max-height:500px){
            .logo{
                margin-bottom: 0.5em;
            }
            .main-welcome-txt{
                font-size: 3em;
            }
            .main-welcome-sub-text{
                font-size: 2em;
                line-height: 1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 2em;
                border: solid 3px black;
                border-width: 7px
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:600px) and (min-height:500px){
            .logo{
                margin-bottom: 1em;
            }
            .main-welcome-txt{
                font-size: 3em;
            }
            .main-welcome-sub-text{
                font-size: 2em;
                line-height: 1em;
                margin: .1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                border-width: 10px
                // margin-top: .5em;
            }
        }

        @media screen and (max-height:750px) and (min-height:600px){
            .logo{
                margin-bottom: 1em;
            }
            .main-welcome-txt{
                font-size: 4em;
            }
            .main-welcome-sub-text{
                font-size: 2.8em;
                line-height: 1em;
                margin: .1em;
            }
    
            .main-welcome-sub-sub-text {
                font-size: 1.4em;
                line-height: 1em;
            }
            .welcome-next-btn{
                font-size: 3.5em;
                border: solid 5px black;
                border-width: 10px
                // margin-top: .5em;
            }
        }
    }

    .welcome-ldb-container {
        // padding-top: 5em;
        flex: 1;
    }

    .main-welcome-txt{
        line-height: 1.2em;
    }

    .main-welcome-sub-text{
        line-height: 1.2em;
    }

    .main-welcome-sub-sub-text {
        font-size: 2em;
        line-height: 1em;
    }

    .welcome-txt-container{
        .enter-button {
            border-color: black;
            border-style: solid;
            width: fit-content;
            border-radius: .4em;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: .5em .8em;
            background-color: white;
            cursor: pointer;
            
            &:hover {
                border-color: white;
                color: white;
                background-color: black;
            }
        }
    }
}

.logo{
    width: 12em;
    margin-bottom: 3em;
}

.user-name-logout-btn{
    // position:fixed; 
    padding-block: 1rem; 
    padding-left: 0.8rem;
    color: #000;

    .logout-btn{
        // margin-left:1.5rem;
        color: #000;
    }
}

.enter-button-test {
    border-color: black;
    border-style: solid;
    width: fit-content;
    border-radius: .4em;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: .1em .6em;
    background-color: white;
    cursor: pointer;
    border-width: 10px;
    
    &:hover {
        border-width: 10px;
        border-color: white !important;
        color: white;
        background-color: black;
    }
}

// @media (max-height:700px){
//     .enter-button-test {
//         position:fixed;
//         left:6rem;
//         bottom:1rem; 
//     }
// }

.welcome-logout-button{
    background-color: transparent;
    margin-left:1.5rem;
    border: none;
}
