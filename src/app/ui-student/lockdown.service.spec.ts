import { TestBed } from '@angular/core/testing';
import { LockdownService } from './lockdown.service';
import { Router } from '@angular/router';
import { AuthService } from '../api/auth.service';

describe('LockdownService', () => {
  let service: LockdownService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockAuthService: jasmine.SpyObj<AuthService>;

  beforeEach(() => {
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockAuthService = jasmine.createSpyObj('AuthService', ['someMethod']); // Replace 'someMethod' with actual methods used

    TestBed.configureTestingModule({
      providers: [
        LockdownService,
        { provide: Router, useValue: mockRouter },
        { provide: AuthService, useValue: mockAuthService }
      ]
    });
    service = TestBed.inject(LockdownService);
  });

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [LockdownService]
    });
    service = TestBed.inject(LockdownService);
  });

  fdescribe('isChromeBook', () => {
    it('should return true when Chrome LDB Extension is present', () => {
      document.cookie = 'cbLDBex=1';
      expect(service.isChromeBook).toBe(false);
    });

    it('should return false when Chrome LDB Extension is not present', () => {
      document.cookie = '';
      expect(service.isChromeBook).toBe(false);
    });

    it('should return false when cookie is empty', () => {
      document.cookie = '';
      expect(service.isChromeBook).toBe(false);
    });

    it('should return false when cookie has other values', () => {
      document.cookie = 'otherCookie=value';
      expect(service.isChromeBook).toBe(false);
    });

    it('should return false for partial match', () => {
      document.cookie = 'cbLDBex=';
      expect(service.isChromeBook).toBe(false);
    });

    it('should be case sensitive', () => {
      document.cookie = 'CBLDBOX=1';
      expect(service.isChromeBook).toBe(false);
    });
  });

  describe('isLockdown', () => {
    it('should return true when Respondus Lockdown Browser is active', () => {
      document.cookie = 'rldbarv=somevalue';
      expect(service.isLockdown).toBe(true);
    });

    it('should return true when Safe Exam Browser is active', () => {
      (window as any).SafeExamBrowser = {};
      expect(service.isLockdown).toBe(true);
      delete (window as any).SafeExamBrowser;
    });

    it('should return false when neither Respondus nor Safe Exam Browser is active', () => {
      document.cookie = '';
      expect(service.isLockdown).toBe(false);
    });

    it('should return true when both Respondus and Safe Exam Browser are active', () => {
      document.cookie = 'rldbarv=somevalue';
      (window as any).SafeExamBrowser = {};
      expect(service.isLockdown).toBe(true);
      delete (window as any).SafeExamBrowser;
    });

    it('should return false for partial match of Respondus cookie', () => {
      document.cookie = 'rldbar=somevalue';
      expect(service.isLockdown).toBe(false);
    });

    it('should return false when SafeExamBrowser is defined but falsy', () => {
      (window as any).SafeExamBrowser = null;
      expect(service.isLockdown).toBe(false);
      delete (window as any).SafeExamBrowser;
    });
  });

  afterEach(() => {
    document.cookie = '';
    delete (window as any).SafeExamBrowser;
  });
});
