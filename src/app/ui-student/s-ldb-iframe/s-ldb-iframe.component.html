<!-- This iFrame is used for auto-launch of whitelisted links in Chromebook and iPad -->
<ng-container *ngFor="let link of lockdown.domainWhitelist">
    <iframe *ngIf="lockdown.launchDomainTriggered" height="1" [src]="sanitizer.bypassSecurityTrustResourceUrl(link)"></iframe>
</ng-container>

<!-- This iFrame is used for Chromebook LDB Extension -->
<iframe [src]="lockdown.ldbLaunchURL" *ngIf="lockdown.launchLinkTriggered" class="iframe-ldb"></iframe>
