import { Component, OnInit } from '@angular/core';
import { LockdownService } from '../lockdown.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 's-ldb-iframe',
  templateUrl: './s-ldb-iframe.component.html',
  styleUrls: ['./s-ldb-iframe.component.scss']
})
export class SLdbIframeComponent implements OnInit {

  constructor(
    public lockdown: LockdownService,
    public sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
  }

}
