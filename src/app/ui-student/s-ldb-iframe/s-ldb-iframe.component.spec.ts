import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SLdbIframeComponent } from './s-ldb-iframe.component';

describe('SLdbIframeComponent', () => {
  let component: SLdbIframeComponent;
  let fixture: ComponentFixture<SLdbIframeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ SLdbIframeComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SLdbIframeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
