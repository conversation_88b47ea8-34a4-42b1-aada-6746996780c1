import { Injectable } from '@angular/core';
import { AuthService, getFrontendDomain } from '../api/auth.service';
import { LangService } from '../core/lang.service';
import { RoutesService } from '../api/routes.service';
import { LoginGuardService } from '../api/login-guard.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Subscription } from 'rxjs';
import { WhitelabelService } from '../domain/whitelabel.service';

// If true, adds clarification that respondus is in Beta to relevant UI text
export const IS_RESPONDUS_BETA_MODE = true;

export interface ILDBLoginConfiguration {
  strategy: LDBLoginStrategy,
  authToken?: string,
  passwordSeed?: number,
  loginInfo?: {
    accessCode: string,
    studentNumber: string,
    isSasnLogin: string,
    dob: string
  }
}

export enum LDBLoginStrategy {
  AUTH_TOKEN = 'AUTH_TOKEN',
  ACCESS_CODE = 'ACCESS_CODE'
}

export const LDB_LINK_TEMPLATE = {
  'LDB_WINDOWS': 'ldb_download_windows',
  'LDB_WINDOWS_MSI': 'ldb_download_windows_msi',
  'LDB_MAC': 'ldb_download_mac',
  'LDB_IPAD': 'ldb_download_ipad',
  'LDB_CHROMEBOOK': 'ldb_download_chromebook',
}

interface SafeExamBrowser {
  version: string;
  security: {
      updateKeys(callback: () => void): void;
      browserExamKey: string;
      configKey: string;
  };
}
var SafeExamBrowser: SafeExamBrowser | undefined;

interface ISecurityConfig {
  isSecure: boolean;
  ldbConfiguration: ILDBSecurityConfiguration
}

interface ILDBSecurityConfiguration {
  security_level: string,
  whitelist: string[],
  blacklist: string[],
  domain_whitelist: string[]
}

/**
 * Lockdown Browser Service
 *  - Lockdown Browser functionality, such as Respondus, SEB
 *  - Respondus Browser Link Generation and Validation
 *  - Rendering lockdown-related text and pop-ups
 */
@Injectable({
  providedIn: 'root'
})
export class LockdownService {
  public launchLinkTriggered = false;
  public launchDomainTriggered = false;
  public ldbLaunchURL;
  public domainWhitelist:string[] = [];
  private launchTimeout: NodeJS.Timeout;
  private routeParamsSub: Subscription;
  private securityConfig: ISecurityConfig

  constructor(
    private auth: AuthService,
    private lang: LangService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService,
    private sanitizer: DomSanitizer,
    private whitelabel: WhitelabelService
  ) { }

  ngOnDestroy(){
    if(this.routeParamsSub){
      this.routeParamsSub.unsubscribe()
    }
  }

  /**
   * Get the LDB Starting Page Launch Link based on strategy:
   * - `LDBLoginStrategy.AUTH_TOKEN` - redirect link contains auth token which is used for re-logging in
   * - `LDBLoginStrategy.ACCESS_CODE` - redirect link contains student login information
   */
  public async getLDBEncrptedLaunchLink(LDBLoginConfiguration: ILDBLoginConfiguration){
    const res = await this.auth.apiCreate(this.routes.AUTH_LOCK_DOWN_BROWSER, {
        clientDomain: getFrontendDomain(),
        lang: this.lang.c(),
        cbLDBex: this.isChromeBookLDBExtension,
        LDBLoginConfiguration
      }).catch(error =>{
        switch(error.message){
          default:
            this.loginGuard.quickPopup(error.message)
            break
        }
      });

    this.ldbLaunchURL = this.sanitizer.bypassSecurityTrustResourceUrl(res)
  }

  public async initSecurityConfiguration(school_class_id?: number, test_session_id?: number) {
    const rldbarv = await this.auth.getCookie('rldbarv')
    const KIOSK_PASSWORD = this.auth.getKioskPassword()
    const securityConfig = await this.auth.apiCreate(this.routes.STUDENT_LOCKDOWN, {school_class_id, test_session_id}, {query: {rldbarv, KIOSK_PASSWORD}})

    this.securityConfig = securityConfig
    this.initDomains()
  }

  private async initDomains() {
    this.domainWhitelist = this.securityConfig.ldbConfiguration.domain_whitelist.filter((domain)=>{
      return this.isUrl(domain);
    });
    console.log('domainWhitelist', this.domainWhitelist)

    // Do not trigger the launch if the app is running on Respondus
    if(!this.isRespondus) {
      return;
    }

    // Disabled auto-launch for now - student can launch the links in the tool bar
    return 

    // Confirmation modal to trigger a user action - cannot open a new tab without user interaction
    this.loginGuard.confirmationReqActivate({
      caption: 'ldb_domain_launch_caption',
      confirm: () => {
        for(const url of this.domainWhitelist){
          window.open(url, '_blank')
        }
      },
      btnCancelConfig: {
        hide: true
      }
    })
  }

  isUrl(domain: string) {
    try {
      new URL(domain);
      return true;
    } catch (error) {
        return false;
    }
  }

  public get isKiosk() {
    if(!this.auth.getKioskPassword()){
      return false;
    }
    return this.auth.getKioskPassword().length > 0
  }

  /** Check if the app is running on Chrome LDB Extension */
  public get isChromeBookLDBExtension() {
    return document.cookie.includes("cbLDBex=1");
  }

  /** Check if the app is running on Chromebook */
  public get isChromeBook() {
    return /CrOS/.test(navigator.userAgent);
  }

  public get isIpad() {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  public get isLockdownClient() {
    const isSEB = typeof <any>SafeExamBrowser !== 'undefined'

    return this.isRespondus || isSEB;
  }

  public get isRespondus() {
    const isLDB = document.cookie.includes("rldbarv");
    return isLDB
  }

  /** 
   * Check if the app is running on Lockdown Browser - Respondus or SEB 
   */
  public get isLockdown() {
    return this.isLockdownClient
  }

  public get isInsecure() {
    if(this.securityConfig) {
      return !this.securityConfig.isSecure
    }

    // Mostly used during initialization, securityConfig should be normally defined
    return !this.isLockdown
  }

  /**
   * This function should and only be use for Chromebook to start the extension  
   * Will render the iFrame on a page, triggering an auto-launch on the link 
   */
  public oniFrameLaunchLockdownBrowser(event: Event): void {
    // Prevent the default anchor link behavior
    event.preventDefault();

    try {
      this.launchLinkTriggered = true;
      setTimeout(() => {
        this.launchLinkTriggered = false;
      }, 1000);
      console.log('LDB_LAUNCHED', this.ldbLaunchURL);
    } catch (error) {
      alert(`Error launching LockDown Browser: ${error}`);
    }
  }

  public onRespondusLaunchBtn(event: Event) {
    // If launched from Chromebook but the LDB extension is not installed, show a warning
    if(this.isChromeBook && !this.isChromeBookLDBExtension) {
      return this.loginGuard.quickPopup(this.renderChromebookWarning)
    }

    // If Chromebook LDB extension is installed and supported, launch the LDB through iFrame
    if(this.isChromeBookLDBExtension) {
      this.oniFrameLaunchLockdownBrowser(event);
    }

    // In all cases, show a delayed pop-up in case the LDB was not started
    this.launchDelayedPopup()
  }

  /** 
   * Launch pop-up in case the LDB was not started 
   * - has a delay of 5 seconds
   */
  public launchDelayedPopup() {
    if(this.launchTimeout) {
      clearTimeout(this.launchTimeout);
    }

    this.launchTimeout = setTimeout(() => {
      const popupText = this.lang.tra('txt_ldb_launch_popup', undefined, {EXAM_SUPERVISOR_TEMPLATE: this.lang.tra('lbl_invigilator')});
      this.loginGuard.quickPopup(popupText);
    }, 5000);
  }

  /** Exit the LDB by redirecting to the ?rldbxb=1 route */
  public exitBrowser() {
    window.location.href = `${window.location.origin}/?rldbxb=1`;
  }

  /** Show an error message and exit the LDB */
  public ldbExitPrompt(caption = "ldb_error_exit_caption") {
    this.loginGuard.confirmationReqActivate({
      caption,
      confirm: this.exitBrowser,
      btnCancelConfig: {
        hide: true
      }
    })
  }

  public get isRenderLockdownPopup() {
    if(this.whitelabel.isEQAO()) { // For EQAO, pop-up is not shown for exempt students as well as other lockdown browsers
      return this.isInsecure
    }
    
    return !this.isRespondus
  }

  public get renderInstructionsText() {
    for(let platform in LDB_LINK_TEMPLATE) {
      LDB_LINK_TEMPLATE[platform] = this.lang.tra(LDB_LINK_TEMPLATE[platform])
    }

    const renderedCaption = this.lang.tra('txt_ldb_install_instructions', undefined, LDB_LINK_TEMPLATE)
    return renderedCaption
  }

  public get renderChromebookWarning() {
    return this.lang.tra('txt_ldb_chromebook_extension_required', undefined, {EXAM_SUPERVISOR_TEMPLATE: this.lang.tra('lbl_invigilator')});
  }

  /** Show installation instructions pop-up */
  public installInstructionsPopup() {
    this.loginGuard.quickPopup(this.renderInstructionsText)
  }

  /**
   * Show the external tools pop-up to the students
   * - This pop-up is shown when the student is using the LDB
   * - The pop-up contains the list of external tools that are allowed to be used during the exam
   */
  public showDomainPopup() {
    const domainList = this.domainWhitelist.map(domain => {
      const domainName = new URL(domain).hostname
      // return `- [${domainName}](${domain})`
      // A href target blank:
      return `- <a href="${domain}" target="_blank">${domainName}</a>`
    }).join('\n')

    const popupText = this.lang.tra('txt_ldb_domain_popup', undefined, {DOMAINS: domainList});
    this.loginGuard.quickPopup(popupText)
  }
}
