import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ItemBankAuditor } from '../../item-set-editor/controllers/audits';
import { AuditConfigs, AuditMetaRecord, AuditQuestionScope, AuditTarget, AuditType, IAuditConfig } from '../data/audits';
import { AssetLibraryCtrl } from '../../item-set-editor/controllers/asset-library';
import { EditingDisabledService } from '../../editing-disabled.service';
import { AuditFilterService } from '../../services/audit-filter.service';
import { FormControl } from '@angular/forms';
import { Subscription } from 'rxjs';

export const auditTypeWithoutSummary = new Set(["PUBLISH"]);
@Component({
  selector: 'audit-table',
  templateUrl: './audit-table.component.html',
  styleUrls: ['./audit-table.component.scss'],
  providers: [AuditFilterService]
})
export class AuditTableComponent implements OnInit, OnD<PERSON>roy {
  
  @Input() auditCtrl:ItemBankAuditor;
  @Input() assetLibraryCtrl:AssetLibraryCtrl;
  @Input() auditType: AuditType;
  @Input() audits:IAuditConfig[];
  @Input() caption: string;
  @Input() twiddleState =  new FormControl(false);

  auditScopeDictionary: { [key in AuditQuestionScope]: string } = {
    [AuditQuestionScope.ITEMS_SCORED]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SCORED_MINUS_FT]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_HUMAN_SCORED]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SCORED_MINUS_HS]: 'audit_assessment',
    [AuditQuestionScope.ITEMS_SURVEY]: 'audit_assessment',
    [AuditQuestionScope.SCREENS]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_NONSCORED]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_PASSAGES]: 'audit_assessment',
    [AuditQuestionScope.SCREENS_NON_PASSAGES]: 'audit_assessment',
    [AuditQuestionScope.ITEM_BANK]: 'ie_audit_item_bank',
    [AuditQuestionScope.ITEM_BANK_SCORED]: 'ie_audit_item_bank',
    [AuditQuestionScope.SCREENS_INCLUDE_DISABLED]: 'audit_assessment_disabled',
    [AuditQuestionScope.PASSAGE_ITEMS]: 'audit_assessment',
    [AuditQuestionScope.ITEM_BANK_PASSAGES]: 'ie_audit_item_bank',
    [AuditQuestionScope.NONE]: 'audit_assessment'
  }; // slugs for translation
  auditCount = 0;
  autoFixSet = new Set();
  AuditTarget = AuditTarget;
  private subscription: Subscription = new Subscription();


  constructor(
    private editingDisabled: EditingDisabledService,
    public auditFilterService: AuditFilterService
  ) { }

  ngOnInit(): void {
    this.setAuditAutoFixSet();
    if(!auditTypeWithoutSummary.has(this.auditType)){
      this.initAuditFilterService();
      this.subscription.add(this.auditCtrl.auditRanSub.subscribe((auditSlug)=>{ // on completion of audit
        this.updateFilters(auditSlug);
      }));
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    
  }

  get filterControl() {
    return this.auditFilterService.filterControl
  }

  get hiddenAudits() {
    return this.auditFilterService.hiddenAudits
  }

  setAuditAutoFixSet(){
    this.audits.forEach(audit =>{
      audit.checks?.forEach(c =>{
        if(c.autoFix){
          this.autoFixSet.add(c.id)
        }
      })
    })
  }

  initAuditFilterService(){
    this.auditFilterService.auditCtrl = this.auditCtrl;
    this.auditFilterService.audits = this.audits;
    this.auditFilterService.updateFilters();
  }

  updateFilters(auditSlug?:string | boolean) {
    this.auditFilterService.updateFilters(auditSlug)
  }

  getNumberOfIssues(slug:string){
    return this.auditCtrl.getAuditLog(slug)?.num_issues ?? 0
  }
  
  activateAuditItems(auditSlug:string, resultId:string, auditTarget:AuditTarget){
    const currentItemMemId = this.auditCtrl.activeAuditItemMemId
    // Clear previous values
    this.clearPreviousListedValue();
    // if clicking on same item list clear and return
    const slug = this.renderAuditResultSlug(auditSlug, resultId);
    if(currentItemMemId === slug){
      // this is technically being done in clearPreviousListedValue but adding here to be explicit
      this.auditCtrl.activeAuditItemMemId = null;
      return
    }
    switch(auditTarget){
      case AuditTarget.MSCAT_PANELS:
      case AuditTarget.TLOFT_PANELS:
      case AuditTarget.LINEAR_SECTIONS:
        this.auditCtrl.activateAuditPanels(slug, auditTarget);
        break;
      case AuditTarget.QUESTIONS:
      default:
        this.auditCtrl.activateAuditQuestions(slug);
        break;

    }
  }

  clearPreviousListedValue(){
    this.auditCtrl.clearAuditQuestions();
    this.auditCtrl.clearAuditPanels();
    this.auditCtrl.auditItemListingMode = null;
  }

  checkActiveQuestionMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.checkActiveQuestionMem(slug)
  }
  renderAuditResultSlug(auditSlug:string, resultId:string){
    return auditSlug+'_'+resultId
  }
  canFixAuditQuestions(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)

  }

  getAuditQMem(auditSlug:string, resultId:string){
    const slug = this.renderAuditResultSlug(auditSlug, resultId)
    return this.auditCtrl.auditQuestionMem[slug]
  }
  canSelectAuditQuestions(auditSlug:string, resultId:string){
    const auditQMem = this.getAuditQMem(auditSlug, resultId)
    return (auditQMem && auditQMem.length > 0)
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);
  resetDiffLangs() {
    this.auditCtrl.auditsRan['isDiffEnglish'] = false;
    this.auditCtrl.auditsRan['isDiffFr'] = false;
  }
  
  toggleBothLangs() {
    this.resetDiffLangs();
  }
  toggleDiffEn() {
    this.resetDiffLangs();
    this.auditCtrl.auditsRan['isDiffEnglish'] = !this.auditCtrl.auditsRan['isDiffEnglish'];
  }
  toggleDiffFr() {
    this.resetDiffLangs();
    this.auditCtrl.auditsRan['isDiffFr'] = !this.auditCtrl.auditsRan['isDiffFr'];
  }

  get auditTypeSummary(){
    return auditTypeWithoutSummary.has(this.auditType) ? null : this.auditCtrl.frameworkAuditQuestionMem?.[this.auditType];
  }

  getAuditNumFlaggedItems(slug: string){
    return this.auditCtrl.auditQuestionMem[slug]?.items?.length ?? 0
  }

}
