import { IAuditResultSummary } from "../../item-set-editor/controllers/audits";
import { AuditMetaRecord } from "../data/audits";

export function aggregateAuditMetaRecords(auditMetaRecord: Partial<AuditMetaRecord>): IAuditResultSummary {
      const records = Object.values(auditMetaRecord ?? {}) ?? [];
      return records.reduce((summary: IAuditResultSummary, record: IAuditResultSummary) => {
        for (const key in record) {
          if(['firstRan', 'lastRan'].includes(key)){
            if(key === 'firstRan'){
              summary[key] = Math.min(summary[key] ?? Infinity, record[key] ?? Infinity);
            }
            if(key === 'lastRan'){
              summary[key] = Math.max(summary[key] ?? -Infinity, record[key] ?? -Infinity);
            }
          } else if (typeof record[key] === 'number') {
            const prev = typeof summary[key] === 'number' && !isNaN(summary[key]) ? summary[key] : 0;
            summary[key] = prev + record[key];
          } else {
            summary[key] = record[key];
          }
        }
        return summary;
      }, {} as IAuditResultSummary);
}
  