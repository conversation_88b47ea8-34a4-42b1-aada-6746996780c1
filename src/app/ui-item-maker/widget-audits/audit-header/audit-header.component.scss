.audit-row {
  width: 100%;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 2rem;
}

.audit-header {
  display: flex;
  cursor: pointer;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  h3 {
    margin: 0;
  }
}

.audit-header-actions {
  display: flex;
  align-items: center;
  gap: 0.2em;
  position: absolute;
  right: 2em;
  .audit-status-msg {
    padding-top: 0.1em;
  }

  .refresh-button {
    margin-right: 1em;
  }
}

::ng-deep .audit-summary {
  margin-left: 0.5em;
  width: fit-content;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin-top: 0.1em;

  div {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 0.5em;

    i {
      margin-right: 3px;
    }
  }
}
