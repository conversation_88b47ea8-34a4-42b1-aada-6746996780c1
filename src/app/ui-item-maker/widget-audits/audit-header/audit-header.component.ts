import { Component, Input } from '@angular/core';
import { IAuditResultSummary, ItemBankAuditor } from '../../item-set-editor/controllers/audits';
import { FormControl } from '@angular/forms';
import { AuditFilterOptions, AuditFilterService } from '../../services/audit-filter.service';

@Component({
  selector: 'audit-header',
  templateUrl: './audit-header.component.html',
  styleUrls: ['./audit-header.component.scss']
})
export class AuditHeaderComponent {
  @Input() auditCtrl!: ItemBankAuditor;
  @Input() isExpanded: FormControl;
  @Input() caption: string;
  @Input() showDate: boolean = true;
  @Input() auditStatus: IAuditResultSummary;

  constructor(
    public auditFilterService: AuditFilterService
  ){}

  @Input() refresh!: (event: Event) => void;

  onToggleExpanded(): void {
    this.isExpanded.setValue(!this.isExpanded.value);
  }

  onRefresh(event: Event): void {
    event.stopPropagation();
    if (this.refresh) {
      this.refresh(event);
    }
  }
  filterControl(filterOption: AuditFilterOptions){
    return this.auditFilterService?.filterControl.get(filterOption)?.value ?? true
  }
}
