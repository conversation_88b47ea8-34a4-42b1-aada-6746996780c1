import { Component, OnInit } from '@angular/core';
import { AuditFilterOptions, AuditFilterService } from '../../services/audit-filter.service';

@Component({
  selector: 'audit-filter',
  templateUrl: './audit-filter.component.html',
  styleUrls: ['./audit-filter.component.scss']
})
export class AuditFilterComponent implements OnInit {

  constructor(
    public auditFilterService: AuditFilterService
  ) { }

  ngOnInit(): void {
  }
  get filterOptionDict () {
    return this.auditFilterService.filterOptionDict
  }

  get filterControl() {
    return this.auditFilterService.filterControl
  }
  
  get filterOption() {
    return this.auditFilterService.filterOption
  }

  isFilterActive(option:AuditFilterOptions): boolean{
    return this.auditFilterService.filterControlbyOption(option) 
  }

}
