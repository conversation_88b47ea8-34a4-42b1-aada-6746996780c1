<div style="display: flex; gap: 0.2em" (click)="$event.stopPropagation()">
      <div class="filter-control" *ngFor="let option of filterOption;">
         <label [tooltip]="filterOptionDict[option].tooltip">
            <i [class]="filterOptionDict[option].icon" [style.color]="isFilterActive(option)? filterOptionDict[option].color : 'dimgrey'"></i>
            <input type="checkbox" [formControl]="filterControl.get(option)" />
         </label>
      </div>
</div>