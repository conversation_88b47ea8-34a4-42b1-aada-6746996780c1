<div class="page-body">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    ></header>
    <div class="page-content is-fullpage" >
      <h2><tra slug="test_auth_template_library_header"></tra></h2>
      <div style="display: flex; gap: 1.5em; flex-direction: column;">
        <div class="row">
          <div *ngIf="isTemplateValid()" class="card flex-column" style="">
            <h3><tra slug="Element Reference Lookup"></tra></h3>
            <div class="flex-column" style="gap: 1em; ">
              <input class="input" type="text" placeholder="Property..." [(ngModel)]="propertyFC" [disabled]="!isTemplateValid()">
              <input class="input" type="text" placeholder="Value..." [(ngModel)]="valueFC" [disabled]="!isTemplateValid()">
            </div>
            <button class="button" (click)="setElementRef(propertyFC, valueFC)" style="margin-top: 2em;" [disabled]="!isTemplateValid()"><tra slug="auth_content_search"></tra></button>
            <div *ngIf="elementRef && elementRef.length > 0" class="flex-column" style="margin-top: 1em;">
              <small style="font-weight: bold;"><tra slug="result"></tra>:</small>
              <small>{{elementRef}}</small>
            </div>
          </div>
          <div class="card flex-column" style="flex-grow: 2; justify-content: center; align-items: center;" *ngIf="!isTemplateValid()">
            <div class="flex-column" style="margin-bottom: 1em; justify-content: center; align-items: center;">
              <div><tra slug="test_auth_search_template"></tra></div>
              <button [disabled]="!isTemplatesLoaded()" (click)="openTemplateOptions()" class="button is-yellow"><tra slug="test_auth_select_template"></tra></button>
              <button class="button" style="margin-top: 1em;" (click)="showTemplateCreationCard()">
                Create New Template
              </button>
            </div>
          </div>
          <div *ngIf="isTemplateValid()" class="card" style="flex-grow: 2;">
            <div class="flex-column" style="justify-content: space-between; gap: 1em; height: 100%">
              <div class="flex-column" style="gap: 0.5em">
                <div class="flex-column">
                  <b>Title</b>
                  <input
                    type="text"
                    [(ngModel)]="templateDefName"
                    class="input"
                    (change)="onTemplateDefChange()"
                  />
                </div>
                <div class="flex-column">
                  <b>Description</b>
                  <textarea
                    type="text"
                    [(ngModel)]="templateDefDescription"
                    class="input"
                    class="textarea"
                    (ngModelChange)="onTemplateDefChange()"
                  ></textarea>
                </div>
                <div class="flex-column" *ngIf="templateDefMeta">
                  <b>Meta</b>
                  <label>
                    <input type="checkbox"  [(ngModel)]="templateDefMeta.is_math_compatible" (change)="onTemplateDefChange()">
                    Math compatible?
                  </label>
                </div>
                <div class="flex-column">
                  <b>Thumbnail</b>
                  <capture-image
                    [element]="templateDefImage"
                    urlProp="upload"
                    fileType="image"
                    [isNoScale]="true"
                    [isCondensed]="true"
                    [displayImageControls]="false"
                    [displayAltText]="false"
                    (onUrlChanged)="onTemplateDefThumbnailUpload($event)"
                  ></capture-image>
                  <div *ngIf="templateDefImage.upload" style="word-break: break-all; margin-top: 1em;">
                    {{templateDefImage.upload}}
                  </div>
                </div>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <button class="button is-success" [disabled]="!isTemplateDefChange" (click)="saveTemplateDefChanges(template.templateDef.id)">Save</button>
                <small *ngIf="isTemplateDefSaving"><tra slug="mrkg_saving"></tra>...</small>
              </div>
            </div>
          </div>
          <div *ngIf="isTemplateValid()" class="card" style="display: flex; gap: 1em;">
            <div class="flex-column">
              <b>{{template.templateDef.template_name}}</b>
              <div>Version {{template.templateDef.current_version_id}}</div>
              <div>
                <img 
                  class="display-image"
                  style="width: 17em;"
                  [src]="template.templateDef.icon_url"
                />
              </div>
              <div style="align-self: center; margin-top: 1em; width: 100%;">
                <button *ngIf="!template.templateDef.is_archived" class="button is-light-yellow is-fullwidth" (click)="archiveTemplate(template.templateDef.id, 1)">Archive Template</button>
                <button *ngIf="template.templateDef.is_archived" class="button is-light-yellow is-fullwidth" (click)="archiveTemplate(template.templateDef.id, 0)">Unarchive Template</button>
              </div>
              <div style="align-self: center; margin-top: 0.5em; width: 100%;">
                <button class="button is-red is-fullwidth" (click)="deleteTemplate(template.templateDef.id)">Delete Template</button>
              </div>
              <div style="align-self: center; margin-top: 0.5em; width: 100%;">
                <button (click)="openTemplateOptions()" class="button is-fullwidth"><tra slug="test_auth_select_template"></tra></button>
              </div>
              <div style="align-self: center; margin-top: 0.5em; width: 100%;">
                <button class="button is-fullwidth" (click)="showTemplateCreationCard()">
                  Create New Template
                </button>
              </div>
            </div>
          </div>
          <div class="card" *ngIf="isTemplateValid()">
            <h3>Group Access</h3>
            <div style="display: flex;flex-direction: column; gap: 1em;">
              <div class="is-over-flow" style="max-height: 20em; height: 20em;">
                <table class="table">
                  <tr>
                    <th>
                      ID
                    </th>
                    <th>
                      Name
                    </th>
                    <th>
                      Remove
                    </th>
                  </tr>
                  <tr *ngFor="let group of templateGroups">
                    <td>{{ group.group_id }}</td>
                    <td>{{ group.description }}</td> 
                    <td>
                      <button 
                        class="button is-red"
                        (click)="removeAuthoringGroup(group.group_id)"
                        [disabled]="isAuthGroupRemoving"
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                </table> 
              </div>
              <div style="display: flex; gap: 1em; align-items: center;">
                <div class="select is-fullwidth">
                  <select (change)="currentGroupToAdd = $event.target.value">
                    <option  *ngFor="let group of getAuthoringGroups(); let index = index" [selected]="group.group_id == currentGroupToAdd" [value]="group.group_id">
                        {{group.description}}
                    </option>
                  </select>
                </div>
                <button class="button is-success" [disabled]="!currentGroupToAdd || isAuthGroupAdding" (click)="addAuthoringGroup(currentGroupToAdd)">
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="card" *ngIf="showTemplateCreation">
          <h3>Template Creation</h3>
            <div class="input-container" style="width: 30em;">
              <b>Title</b>
              <input class="input" type="text" [(ngModel)]="inputTemplateName"/>
            </div>
            <div class="input-container" style="width: 15em;">
              <b>Reference Item (optional)</b>
              <input class="input" type="text" [(ngModel)]="inputTemplateReference"/>
            </div>
            <div class="input-container">
              <b>Content</b>
              <textarea class="textarea is-selected is-full" [(ngModel)]="inputTemplateContent"></textarea>
            </div>
            <div class="input-container">
              <b>Thumbnail</b>
              <capture-image 
                [element]="inputTemplateImage"
                urlProp="upload"
                fileType="image"
                [isNoScale]="true"
                [isCondensed]="true"
                [displayImageControls]="false"
                [displayAltText]="false"
              ></capture-image>
              <div *ngIf="inputTemplateImage.upload" style="word-break: break-all; margin-top: 1em;">
                {{inputTemplateImage.upload}}
              </div>
            </div>
            <div style="display: flex; justify-content: flex-end; margin-top: 1em; gap: 1em;">
              <div class="is-error" *ngIf="contentError && contentError.length">
                {{contentError}}
              </div>
              <button class="button" (click)="createTemplate()" [disabled]="hasInvalidInputs()">Create</button>
            </div>
        </div>
        <div style="display: flex; gap: 1.5em;">
          <div class="card" *ngIf="isTemplateValid()" style="flex-grow: 2;">
            <h3>Viewer</h3>
            <div>
              <div 
                *ngFor="let contentElement of template.templateContent"
                [ngStyle]="getElementStyle(contentElement)"
              >
                <element-render 
                  *ngIf="isTemplateValid()" 
                  [contentElement]="contentElement" 
                  [isLocked]="true" 
                  [questionState]="{}"
                  [questionPubSub]="questionPubSub"
                >
                </element-render>
              </div>
            </div>
          </div>
          <div class="card" *ngIf="isTemplateValid()">
            <h3>Versions</h3>
            <div class="is-over-flow">
              <table class="table">
                <tr>
                  <th>
                    ID
                  </th>
                  <th>
                    User
                  </th>
                  <th>
                    Date
                  </th>
                  <th>
                    Select
                  </th>
                </tr>
                <tr *ngFor="let version of template.templateVersions">
                  <td>{{ version.id }}</td>
                  <td>{{ version.created_by }}</td> 
                  <td>{{ version.created_on }}</td> 
                  <td>
                    <button class="button" 
                      [class.is-info]="template.templateVersion.tqtv_id == version.id"
                      (click)="loadTemplateVersion(version.id)"
                    >
                      Select
                    </button>
                  </td>
                </tr>
              </table>
              <button
                class="button"
                style="margin-top: 1em;"
                (click)="setCurrentVersion()"
                [disabled]="isCurrentVersion()"
              >
                Set as current version
              </button>
            </div>
          </div>
        </div>
        <div *ngIf="isTemplateValid()" style="display: flex; gap: 1.5em;">
          <div class="card" style="flex-grow: 2;">
            <div class="space-between">
              <h3><tra slug="test_auth_template_content"></tra></h3>
              <div style="display: flex; align-items: center; gap: 0.5em;">
                <a [class.deselected]="!isTreeView" (click)="toggleTreeView(false)">JSON</a>
                |
                <a [class.deselected]="isTreeView" (click)="toggleTreeView(true)">Tree</a>
              </div>
            </div>
            <textarea *ngIf="!isTreeView" [(ngModel)]="template.templateVersion.content_config" (change)="onContentChange()" class="textarea is-selected is-full">
            </textarea>
            <template-json-viewer *ngIf="isTreeView" [data]="treeContent"></template-json-viewer>
          </div>
          <div class="card" style="max-height: 55em; width: 26em;">
            <h3>Simplification Configurations</h3>
            <div 
              cdkDropList
              (cdkDropListDropped)="drop(template.templateConfigs, $event);"
              style="display: flex; flex-direction: column; gap: 1em; overflow: auto; max-height: 88%; padding: 1em;"
            >
              <div cdkDrag class="surface-card" *ngFor="let config of template.templateConfigs" style="display: flex; flex-direction: column; gap: 0.5em;">
                <div>
                  <div cdkDragHandle style="display: flex; justify-content: flex-end; cursor: move;">
                    <a class="button" (click)="removeConfig(template.templateConfigs, config)">
                      <i class="fas fa-trash"  aria-hidden="true"></i>
                    </a> 
                  </div>
                  <div class="config-header">
                    Configuration Type
                  </div>
                  <div class="select is-fullwidth">
                    <select [(ngModel)]="config.configType" (change)="onConfigChange()">
                      <option *ngFor="let type of TemplateConfigTypes" [value]="type">
                          {{type}}
                      </option>
                    </select>
                  </div>
                  <i *ngIf="getConfigTypeInfo(config.configType)" class="tip">{{getConfigTypeInfo(config.configType)}}</i>
                </div>
                <div>
                  <div class="config-header">
                    ID
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.id" (change)="onConfigChange()">
                </div>
                <div>
                  <div class="config-header">
                    Label
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.label" (change)="onConfigChange()">
                </div>
                <div>
                  <div class="config-header">
                    French Label
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.label_fr" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType != 'note'">
                  <div class="config-header">
                    Element Reference
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.elementRef" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType == 'mcq' || config.configType == 'mcq-table'">
                  <div class="config-header">
                    Option type
                  </div>
                  <div class="select is-fullwidth">
                    <select [(ngModel)]="config.optionElementType" (change)="onConfigChange()">
                      <option *ngFor="let type of TemplateOptionTypes" [value]="type">
                          {{type}}
                      </option>
                    </select>
                  </div>
                </div>
                <div *ngIf="config.configType == 'advanced-inline' || config.configType == 'keyboard-input-freeform' || config.configType == 'math-cloze'">
                  <div class="config-header">
                    Default weight
                  </div>
                  <input class="input" type="number" [(ngModel)]="config.defaultWeight" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType == 'validator'">
                  <div class="config-header">
                    Entries Reference
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.secondaryRef" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType == 'mcq'">
                  <div class="config-header">
                    MCQ Element Reference
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.secondaryRef" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType == 'translation'">
                  <div class="config-header">
                    English translation
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.translationEN" (change)="onConfigChange()">
                  <div class="config-header">
                    French translation
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.translationFR" (change)="onConfigChange()">
                </div>
                <div *ngIf="config.configType == 'selection'">
                  <div *ngIf="ensureSelectionData(config) as selectionData">
                    <div class="config-header">
                      Type
                    </div>
                    <div class="select is-fullwidth">
                      <select [(ngModel)]="selectionData.type" (change)="onConfigChange()">
                        <option *ngFor="let type of ['dropdown', 'button']" [value]="type">
                            {{type}}
                        </option>
                      </select>
                    </div>
                    <div class="config-header">
                      Options
                    </div>
                    <div class="selection-options-container">
                      <span>value</span><span>caption</span><span>icon</span><span></span>
                      <ng-container *ngFor="let option of selectionData.options">
                        <div>
                          <input type="text" [(ngModel)]="option.id" (change)="onConfigChange()">
                        </div>
                        <div>
                          <input type="text" [(ngModel)]="option.caption" (change)="onConfigChange()">
                        </div>
                        <div>
                          <input type="text" [(ngModel)]="option.icon" (change)="onConfigChange()">
                        </div>
                        <div>
                          <a style="font-size: 0.7em;" class="button" (click)="removeConfig(selectionData.options, option)">
                            <i class="fas fa-trash"  aria-hidden="true"></i>
                          </a> 
                        </div>
                      </ng-container>
                    </div>
                    <div style="display: flex">
                      <a class="button" (click)="addSelectionOption(selectionData)">
                        Add Option
                      </a> 
                      <dropdown-select 
                        [list]="templateSelectionOptionsPreset" 
                        (onSelect)="loadSelectionOptionsPreset(selectionData, $event)"
                        placeholder="Load Preset"
                      ></dropdown-select>
                    </div>
                  </div>
                </div>
                <div *ngIf="config.configType == 'math-cloze' && config.hiddenConfigs">
                  <div class="config-header">
                    Hide font size option
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.isHideFontSize"
                    (change)="onConfigChange()"
                  >
                </div>
                <div *ngIf="config.configType == 'interactive-diagram' && config.hiddenConfigs">
                  <div class="config-header">
                    Hide style profile options
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.hideStyleProfile"
                    (change)="onConfigChange()"
                  >
                  <div class="config-header">
                    Hide styling options
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.hideStyle"
                    (change)="onConfigChange()"
                  >
                  <div class="config-header" *ngIf="!config.hiddenConfigs.hideStyle">
                    Simplified Style Mode
                  </div>
                  <input 
                    *ngIf="!config.hiddenConfigs.hideStyle"
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.simpleStyleMode"
                  >
                  <div class="config-header">
                    Hide configuration options
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.hideConfig"
                    (change)="onConfigChange()"
                  >
                </div>
                <div *ngIf="config.configType == 'scientific-notation' && config.hiddenConfigs">
                  <ng-container *ngTemplateOutlet="scientificNotationHiddenConfigs; context: {hiddenConfigs: config.hiddenConfigs}"></ng-container>
                </div>
                <div *ngIf="config.configType == 'advanced-inline' && config.hiddenConfigs">
                  <div class="config-header">
                    Only allow non-interactive elements
                  </div>
                  <input 
                    type="checkbox"
                    (change)="onConfigChange()"
                    [(ngModel)]="config.hiddenConfigs.isShowOnlyStaticElements"
                  >
                  <div class="config-header">
                    Configure Content's Hidden Configurations
                  </div>
                  <input 
                    type="checkbox"
                    (change)="ensureAdvancedInlineHiddenConfig(config); onConfigChange()"
                    [(ngModel)]="config.hiddenConfigs.isConfigureContentHiddenConfigs"
                  >
                    
                  <ng-container *ngIf="
                    config.hiddenConfigs.isConfigureContentHiddenConfigs &&
                    config.hiddenConfigs.contentHiddenConfigs
                    ">
                    <details *ngIf="config.hiddenConfigs.contentHiddenConfigs['scientific-notation']">
                      <summary>Scientific Notation</summary>
                      <div>
                        <ng-container *ngTemplateOutlet="scientificNotationHiddenConfigs; context: {hiddenConfigs: config.hiddenConfigs.contentHiddenConfigs['scientific-notation']}"></ng-container>
                      </div>
                    </details>
                  </ng-container>
                </div>
                <div *ngIf="config.configType == 'stim' && config.hiddenConfigs">
                  <div class="config-header">
                    Always use full width vertical container
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.isVerticalContainerAlwaysFullWidth"
                    (change)="onConfigChange()"
                  >
                  <div class="config-header">
                    Only allow border on outer vertical container
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.isAllowBorderOnlyOnOuterVerticalContainer"
                    (change)="onConfigChange()"
                  >
                </div>
                <div *ngIf="config.configType == 'mcq' && config.hiddenConfigs">
                  <div class="config-header">
                    Only allow one correct option
                  </div>
                  <input 
                    type="checkbox"
                    [(ngModel)]="config.hiddenConfigs.isOnlyOneCorrectOption"
                    (change)="onConfigChange()"
                  >
                </div>
              </div>
            </div>
            <button style="width: 100%; margin-top: 1em;" class="button is-yellow" (click)="createDefaultConfig()">Add</button>
          </div>
          <div class="card" style="max-height: 55em; min-width: 26em;">
            <h3>Migration Options</h3>
            <div
              style="display: flex; flex-direction: column; gap: 1em; overflow: auto; max-height: 88%; padding: 1em;"
            >
              <div class="surface-card" *ngFor="let config of template.migrationOptions" style="display: flex; flex-direction: column; gap: 0.5em;">
                <div>
                  <div style="display: flex; justify-content: flex-end; cursor: move;">
                    <a class="button" (click)="removeConfig(template.migrationOptions, config)">
                      <i class="fas fa-trash"  aria-hidden="true"></i>
                    </a> 
                  </div>
                </div>
                <div>
                  <div class="config-header">
                    Parent ID
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.parent_id" (change)="onMigrationOptChange()">
                </div>
                <div *ngIf="!config.elementRefs">
                  <div class="config-header">
                    Element Reference
                  </div>
                  <input class="input" type="text" [(ngModel)]="config.elementRef" (change)="onMigrationOptChange()">
                </div>
                <div *ngIf="config.elementRefs">
                  <div class="config-header">
                    Element References
                  </div>
                  <div style="display: flex; flex-direction: column; gap: 0.5em;">
                    <div *ngFor="let elementRef of config.elementRefs; let i = index; trackBy: trackByIndex" style="display: flex; gap: 1em; align-items: center;">
                      <input class="input" type="text" [ngModel]="config.elementRefs[i]" (ngModelChange)="setValueByIndex(config.elementRefs, i, $event)" (change)="onMigrationOptChange()">
                      <a class="small-icon" style="color: #ff5656" [class.cursor-disabled]="config.elementRefs.length <= 1">
                        <i class="fas fa-trash"  aria-hidden="true" [class.disabled]="config.elementRefs.length <= 1" (click)="removeConfig(config.elementRefs, elementRef)"></i>
                      </a> 
                    </div>
                  </div>
                  <a (click)="createElementReference(config)">Add</a>
                </div>
                <mat-slide-toggle [(ngModel)]="config.isExcluded" (ngModelChange)="onMigrationOptChange()">Excluded option?</mat-slide-toggle>
              </div>
            </div>
            <button style="width: 100%; margin-top: 1em;" class="button is-yellow" (click)="createDefaultMigrationOption()">Add</button>
          </div>
        </div>
        <div *ngIf="isTemplateValid()" style="display: flex; justify-content: flex-end;">
          <div style="display: flex; gap: 1em; align-items: center;">
            <div *ngIf="isContentInvalid" class="is-error" style="margin-top: 1em;">
              The current JSON content is invalid.
            </div>
            <div class="is-error" *ngIf="!isContentInvalid && isConfigInvalid && invalidConfigLabel">
              A configuration's element reference is invalid: <br>
              &#8226; {{invalidConfigLabel}}
            </div>
            <div class="is-error" *ngIf="!isContentInvalid && isConfigInvalid && !invalidConfigLabel">
              A configuration is invalid.
            </div>
            <div class="is-error" *ngIf="!isContentInvalid && !isConfigInvalid && isMigrationOptInvalid">
              A migration option is invalid: <br>
              &#8226; Index {{invalidMigrationIndex}}
            </div>
            <div *ngIf="isSaveError">
              An error occurred saving your changes.
            </div>
            <button class="button is-success" [disabled]="!isTemplateValid() || !hasChanges() || hasInvalidInput()" (click)="saveChanges()">Save</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <footer [hasLinks]="true"></footer>
</div>
<template-library [hideArchived]="false" [authRestrictions]="authRestrictions"></template-library>

<ng-template #scientificNotationHiddenConfigs let-hiddenConfigs="hiddenConfigs">
  <!-- <div class="config-header">
    Hide Advanced Options Toggle
  </div>
  <input 
    type="checkbox"
    [(ngModel)]="hiddenConfigs.hideAdvancedOptions"
    (change)="onConfigChange()"
  > -->
  <div class="config-header">
    Hide Multiple Answers Options
  </div>
  <input 
    type="checkbox"
    [(ngModel)]="hiddenConfigs.hideAllowMultipleAnswers"
    (change)="onConfigChange()"
  >
  <div class="config-header">
    Hide Fixed Part Options
  </div>
  <input 
    type="checkbox"
    [(ngModel)]="hiddenConfigs.hideFixedOptions"
    (change)="onConfigChange()"
  >
</ng-template>