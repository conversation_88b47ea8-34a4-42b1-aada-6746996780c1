import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { ElementType, IContentElement, IQuestionScoringInfo, IScoringCodes } from 'src/app/ui-testrunner/models';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ITemplateDef, ITemplateVersionHistory, StyleprofileService, TemplateVersionConfig } from 'src/app/core/styleprofile.service';
import { Subscription } from 'rxjs';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { ScrollService } from 'src/app/core/scroll.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IAdvancedInlineHiddenConfigs, IContentElementTemplate, TemplateConfigDefRow, TemplateConfigTypes, TemplateMeta, TemplateMigrationOption, TemplateOptionTypes } from 'src/app/ui-testrunner/element-render-template/model';
import { IContentElementCanvas } from 'src/app/ui-testrunner/element-render-canvas/model';
import { IContentElementFrame } from 'src/app/ui-testrunner/element-render-frame/model';
import { QUESTION_RUNNER_WIDTH } from 'src/app/ui-testrunner/test-runner/test-runner.component';
import { indexOf } from '../services/util';
import { templateSelectionOptionsPreset } from 'src/app/ui-item-maker/element-config-template/constructions/template-selection/options-preset';
import { IAuthoringGroup, ItemMakerService } from '../item-maker.service';
import { getTemplateConfigById, getValueByJoinedPath, getValueByPath, joinElementRefs } from '../element-config-template/util';
import { QuestionPubSub } from 'src/app/ui-testrunner/question-runner/pubsub/question-pubsub';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Location } from '@angular/common';
import { IAuthRestrictions } from '../item-set-editor/item-set-editor.component';

export interface ITLTemplate {
  templateDef: ITemplateDef, 
  templateContent: IContentElement[], 
  templateVersion: TemplateVersionConfig, 
  templateConfigs: TemplateConfigDefRow[], 
  templateVersions: ITemplateVersionHistory[],
  migrationOptions: TemplateMigrationOption[],
  meta: TemplateMeta
}

@Component({
  selector: 'view-im-template-library',
  templateUrl: './view-im-template-library.component.html',
  styleUrls: ['./view-im-template-library.component.scss']
})
export class ViewImTemplateLibrary implements OnInit {

  @Input() itemBankCtrl:ItemBankCtrl
  @Input() frameworkCtrl: ItemSetFrameworkCtrl

  TemplateConfigTypes = TemplateConfigTypes;
  TemplateOptionTypes = TemplateOptionTypes;
  currentQuestionScoringInfo: IQuestionScoringInfo
  templateSelectionSub:Subscription
  templateSelectionCancelationSub:Subscription
  isAwaitingTemplateSelection:boolean;
  template: ITLTemplate = {
    templateDef: null,
    templateConfigs: null,
    templateContent: null,
    templateVersion: null,
    templateVersions: null,
    migrationOptions:  null,
    meta: null
  };
  propertyFC: string = '';
  valueFC: string = '';
  templateSelectionOptionsPreset = templateSelectionOptionsPreset;

  // Validation and change tracking

  isTemplateContentChange = false;

  isContentInvalid = false;

  isConfigInvalid = false;
  invalidConfigLabel = null;
  
  isMigrationOptInvalid = false;
  invalidMigrationIndex: number;

  isTreeView = false;

  public breadcrumb: any[] = [];
  private routeSub: Subscription;
  private querySub: Subscription;

  constructor( 
    private lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    private styleProfileService: StyleprofileService,
    private loginGuard: LoginGuardService,
    private scrollService: ScrollService,
    private breadcrumbsService: BreadcrumbsService,
    private route: ActivatedRoute,
    private router: Router,
    private location: Location,
    public itemMakerService: ItemMakerService
  ) { }
  authRestrictions: IAuthRestrictions = {}
  questionPubSub = new QuestionPubSub(false, null);
  ngOnInit() {
    this.styleProfileService.initializeTemplates()
    this.loginGuard.activate();
    this.scrollService.scrollToTop();
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
      this.breadcrumbsService._CURRENT('Template Library', this.router.url),
    ];
    this.routeSub = this.route.params.subscribe(routeParams => {
      if(this.querySub) {
        this.querySub.unsubscribe();
      }
    });

    if(!this.itemMakerService.getAuthoringGroups() || !this.itemMakerService.getAuthoringGroups().length) {
      this.itemMakerService.loadMyAuthoringGroups(true).then(() => {
        this.blockAccessIfUserNotTemplateAuthor();
      });
    } else {
      this.blockAccessIfUserNotTemplateAuthor();
    } 
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }

    if(this.querySub) {
      this.querySub.unsubscribe();
    }
  }

  blockAccessIfUserNotTemplateAuthor() {
    if (!this.isUserTemplateAuthor()) {
      alert('You do not have the Template Editor role.');
      this.location.back();
    }
  }

  openTemplateOptions(){
    this.templateSelectionSub = this.styleProfileService.templateSelection.subscribe(this.loadTemplate)
    this.styleProfileService.templateSelectionReq.next(true);
    this.templateSelectionCancelationSub = this.styleProfileService.templateSelectionReq.subscribe((isActivate) => {
      if (!isActivate){
        this.endTemplateListener()
      }
    })
  }

  endTemplateListener(){
    this.templateSelectionSub.unsubscribe()
    this.templateSelectionCancelationSub.unsubscribe()
    this.templateSelectionSub = null;
    this.templateSelectionCancelationSub = null;
  }

  loadTemplate = (templateDef:ITemplateDef, isEndTemplateListener = true) => {
    if(isEndTemplateListener) {
      this.endTemplateListener()
    }

    const templateConfig = this.styleProfileService.templateConfigRef.get(templateDef.id);
    const templateVersions = this.styleProfileService.templateVersionMap.get(templateDef.id);

    // Template dependent
    this.template.templateDef = templateDef
    this.template.templateVersions = templateVersions;

    if(templateDef.meta) {
      this.template.meta = JSON.parse(templateDef.meta);
    }

    // Version dependent
    this.template.templateVersion = templateConfig;
    this.template.templateContent = JSON.parse(templateConfig.content_config);
    this.template.templateConfigs = JSON.parse(templateConfig.template_config);
    this.template.migrationOptions = JSON.parse(templateConfig.migrationOptions);

    // For template def editor
    this.templateDefImage = {upload: this.template.templateDef.icon_url}
    this.templateDefName = this.template.templateDef.template_name;
    this.templateDefDescription = this.template.templateDef.description;
    this.templateDefMeta = this.template.meta;
    
    console.log(this.template, 'template');
    this.questionPubSub = new QuestionPubSub(false, null);
    this.resetErrorHandling();

    this.validateTemplate();

    this.initializeTemplateGroups(templateDef.id);
  }

  async loadTemplateVersion(versionId: number) {
    if(versionId == this.template.templateVersion.tqtv_id) {
      return;
    }

    const template = await this.auth.apiGet(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATE_VERSIONS, versionId, {query:{auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)}});
    this.template.templateVersion = template;
    this.template.templateContent = JSON.parse(template.content_config);
    this.template.templateConfigs = JSON.parse(template.template_config);
    this.template.migrationOptions = JSON.parse(template.migrationOptions);
    this.questionPubSub = new QuestionPubSub(false, null);
    this.resetErrorHandling();

    this.validateTemplate();
  }

  elementRef: string = null;

  parseInput(input: string) {
    // Check if the input is exactly 'true' or 'false' to convert to boolean
    if (input === "true") {
        return true;
    } else if (input === "false") {
        return false;
    }

    // Check if the input is a number
    const number = Number(input);
    if (!isNaN(number)) {
        return number;
    }

    // Return the input as a string by default
    return input;
}

  setElementRef(key, value) {
    try {
      const parsedValue = this.parseInput(value);
      const parsedContent = JSON.parse(this.processContent(this.template.templateVersion.content_config));
      this.elementRef = this.findElementRef(key, parsedValue, '', parsedContent) || 'No matches';
    } catch (err) {
      throw new Error(err.message);
    }
  }

  findElementRef = (key, value, currentPath = '', currentContent: IContentElement | IContentElement[] = this.template.templateContent, depth = 0) => {
    console.log('findElementRef called');
    // Depth check to avoid excessive recursion
    if (depth > 100) {
      console.warn('Maximum repeats reached');
      return null;
    }
  
    // Convert value to a string for comparison
    const valueStr = String(value);
  
    // Handle arrays in the content
    if (Array.isArray(currentContent)) {
      for (let i = 0; i < currentContent.length; i++) {
        const result = this.findElementRef(key, valueStr, `${currentPath}${i}.`, currentContent[i], depth + 1);
        if (result) return result;
      }
    } else if (typeof currentContent === 'object' && currentContent !== null) {
      // Handle objects in the content
      for (const [prop, propValue] of Object.entries(currentContent)) {
        const propValueStr = String(propValue);
        if (prop === key && propValueStr.includes(valueStr)) {
          return `${currentPath}${prop}`;
        }
        if (typeof propValue === 'object') {
          const result = this.findElementRef(key, valueStr, `${currentPath}${prop}.`, propValue, depth + 1);
          if (result) return result;
        }
      }
    }
  
    // Return null if no match is found
    return null;
  }

  computeFontScaleByBaseWidth(baseWidth:number){
    let fontScale = 1
    if (baseWidth && QUESTION_RUNNER_WIDTH < baseWidth){
      fontScale = QUESTION_RUNNER_WIDTH / baseWidth;
    }
    return fontScale;
  }

  getFontScale(element:IContentElement){
    let fontScale = 1;
    if (element.elementType === ElementType.CANVAS){
      const elementCanvas = <IContentElementCanvas> element;
      fontScale = this.computeFontScaleByBaseWidth(elementCanvas.width);
    }
    if (element.elementType === ElementType.FRAME){
      const elementFrame = <IContentElementFrame> element;
      fontScale = this.computeFontScaleByBaseWidth(elementFrame.width);
    }
    return fontScale;
  }

  getElementStyle(element:IContentElement){
    const fontScale = this.getFontScale(element);
    const style = {
      'font-size': fontScale+'em',
    }
    return style
  }

  hasTemplateConfig() {
    return this.template.templateConfigs.length > 0;
  }

  isTemplateValid() {
    return this.template && this.template.templateVersion && this.template.templateContent
  }

  onContentChange() {
    if(!this.isTemplateContentChange)
      this.isTemplateContentChange = true;

    this.isSaveError = false;
    this.validateContent();
  }

  /**
   * Helper function for performing all template version validations.
   */
  validateTemplate() {
    this.validateConfig();
    this.validateContent();
    this.validateMigrationOptions();
  }

  validateContent() {
    // Attempt parsing content config to ensure validity
    let isInvalid = false;
    try{
      const contentConfigParsed = JSON.parse(this.template.templateVersion.content_config);
      isInvalid = false;
    } catch {
      isInvalid = true;
    }

    this.isContentInvalid = isInvalid;

    return !isInvalid;

  }

  /**
   * Handles any processes that should run upon configuration change.
   */
  onConfigChange() {
    this.invalidConfigLabel = null;
    // Track if config is changed
    if(!this.isTemplateContentChange)
      this.isTemplateContentChange = true;

    this.isSaveError = false;

    this.validateConfig();
  }

  /**
   * Handles any processes that should run upon migration option change.
   */
  onMigrationOptChange() {
    this.invalidMigrationIndex = null;
    // Track if config is changed
    if(!this.isTemplateContentChange)
      this.isTemplateContentChange = true;

    this.isSaveError = false;

    this.validateMigrationOptions();
  }

  /**
   * Returns the value of the first element in the array where the key is matched, or undefined otherwise.
   * @param templateConfigs the array of configurations to search.
   */
  getTemplateConfigById(key:string){
    return getTemplateConfigById(this.template.templateConfigs, key);
  }

  /**
   * Returns true if migration options are valid, and false if not.
   * 
   * Goes through each option and checks whether any configurations have an empty parent ID, an invalid parent ID, or an incorrect element reference.
   * Stores error information in {@link isMigrationOptInvalid} and {@link invalidMigrationIndex}
   */
  validateMigrationOptions() {
    // Attempt to find each elementRef to ensure it exists
    let isInvalid = false;
    for(let i =0; i<this.template.migrationOptions.length; i++) {
      const config = this.template.migrationOptions[i];
      try{
        if(!config.parent_id || !config.parent_id.length) { // Check if null or empty parent ID.
          isInvalid = true;
          this.invalidMigrationIndex = i;
        } else {
          const parentConfig = this.getTemplateConfigById(config.parent_id);
          if(parentConfig == undefined) { // Check if the parent config ID exists
            isInvalid = true;
            this.invalidMigrationIndex = i;
          } else { // Validate the joined reference
            const content = JSON.parse(this.processContent(this.template.templateVersion.content_config));
            if(config.elementRefs && config.elementRefs.length) {
              for(let elementRef of config.elementRefs) {
                if(config.elementRefs.length > 1 && !elementRef.length) {
                  isInvalid = true;
                  this.invalidMigrationIndex = i;
                }

                const value = getValueByJoinedPath(parentConfig.elementRef, elementRef, content);
                if(value == undefined) {
                  isInvalid = true;
                  this.invalidMigrationIndex = i;
                }
              }
            } else {
              const value = getValueByJoinedPath(parentConfig.elementRef, config.elementRef, content);
              if(value == undefined) {
                isInvalid = true;
                this.invalidMigrationIndex = i;
              }
            }
          }
        }
      } catch(err) {
        // If an error is encountered during parsing, set invalid
        console.error(err);
        isInvalid = true;
      }
    }

    this.isMigrationOptInvalid = isInvalid;

    return !isInvalid;
  }

  /**
   * Creates a default migration option element.
   */
  createDefaultMigrationOption() {
    const config: TemplateMigrationOption = {
      parent_id: '',
      elementRefs: [''],
      elementRef: '',
      isExcluded: false
    };
    this.template.migrationOptions.push(config);

    this.onMigrationOptChange();
  }

  createElementReference(config: TemplateMigrationOption) {
    if(!config.elementRefs) {
      config.elementRefs = [];
    }
    config.elementRefs.push('');

    this.onMigrationOptChange();
  }

  configTypesNoElementRef = ["note"]
  validateConfig() {
    // Attempt to find each elementRef to ensure it exists
    let isInvalid = false;
    for(let i =0; i<this.template.templateConfigs.length; i++) {
      const config = this.template.templateConfigs[i];
      try{
        if(!this.configTypesNoElementRef.includes(config.configType)) { // checking element ref validity
          if(!config.elementRef || !config.elementRef.length) {
            isInvalid = true;
            this.invalidConfigLabel = config.label;
          } else {
            const value = this.getValueByPath(config.elementRef);
            if(value == undefined) {
              // If undefined encountered, no element ref is found
              isInvalid = true;
              this.invalidConfigLabel = config.label;
            }
          }
        }
        if(config.configType == "validator") {
          if(!config.secondaryRef || !config.secondaryRef.length) {
            isInvalid = true;
            this.invalidConfigLabel = config.label;
          } else {
            const value = this.getValueByPath(config.secondaryRef);
            if(value == undefined) {
              // If undefined encountered, no element ref is found
              isInvalid = true;
              this.invalidConfigLabel = config.label;
            }
          }
        }
      } catch(err) {
        // If an error is encountered during parsing, set invalid
        console.error(err);
        isInvalid = true;
      }
    }

    this.isConfigInvalid = isInvalid;

    return !isInvalid;
  }

  resetErrorHandling() {
    this.isMigrationOptInvalid = false;
    this.isConfigInvalid = false;
    this.isContentInvalid = false;
    this.isTemplateContentChange = false;
    this.isSaveError = false;
    this.treeContent = [];
    this.isTreeView = false;
  }

  hasChanges() {
    return this.isTemplateContentChange;
  }

  hasInvalidInput() {
    return this.isConfigInvalid || this.isContentInvalid || this.isMigrationOptInvalid;
  }

  isSaveError = false;
  saveChanges() {
    if(!this.validateConfig()) {
      alert('Unable to save invalid content/configurations.');
      return;
    }

    const template_config = JSON.stringify(this.template.templateConfigs);

    const migrationOptions = JSON.stringify(this.template.migrationOptions);

    let content_config = '';
    try {
      content_config = this.processContent(this.template.templateVersion.content_config);
    } catch (err){
      return console.error(err);
    }

    const data = {
      tqt_id: this.template.templateDef.id,
      content_config,
      template_config,
      meta_adjusters: '[]',
      migrationOptions,
      auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)
    }
    this.auth.apiCreate(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATE_VERSIONS, data).then((newVersion) => {
      this.template.templateVersions.push({
        id: newVersion.id,
        tqt_id: newVersion.tqt_id,
        created_by: newVersion.created_by,
        created_on: newVersion.created_on
      })
      this.loadTemplateVersion(newVersion.id);
    }).catch((err) => {
      this.isSaveError = true;
    });
  }

  getValueByPath(elementRef: string) {
    const content = JSON.parse(this.processContent(this.template.templateVersion.content_config));
    return getValueByPath(elementRef, content);
  }

  createDefaultConfig() {
    const config: TemplateConfigDefRow = {
      configType: 'text',
      label: '',
      elementRef: '',
      hiddenConfigs: {}
    };
    this.template.templateConfigs.push(config);

    this.onConfigChange();
  }

  removeConfig(content:any[], element:any){
    if (window.confirm('Remove this part?')){
      let i = indexOf(content, element);
      if (i !== -1){
        content.splice(i, 1)
      }
    }

    this.onConfigChange();
    this.onMigrationOptChange();
  }

  setCurrentVersion() {
    const newVersionId = this.template.templateVersion.tqtv_id;
    const templateId = this.template.templateDef.id;

    this.auth.apiPatch(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATES, templateId, {
      current_version_id: newVersionId
    }, {
      query: {auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)}
    }).then((template) => {
      this.template.templateDef.current_version_id = newVersionId;
    })
  }

  isCurrentVersion() {
    return this.template.templateVersion.tqtv_id == this.template.templateDef.current_version_id;
  }

  inputTemplateImage = {upload:''};
  inputTemplateName:string = null;
  inputTemplateContent: string = null;
  inputTemplateReference: string = null;
  showTemplateCreation = false;

  showTemplateCreationCard() {
    this.showTemplateCreation = !this.showTemplateCreation;

    if(!this.showTemplateCreation) {
      return;
    }
  }

  createTemplate() {
    if(this.hasInvalidInputs()) {
      alert('Please fill out all required fields')
      return;
    }

    let content_config = '';
    try {
      content_config = this.processContent(this.inputTemplateContent);
    } catch (err){
      return console.error(err);
    }

    this.auth.apiCreate(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATES, {
      icon_url: this.inputTemplateImage.upload,
      name: this.inputTemplateName,
      reference_item: this.inputTemplateReference,
      content_config,
      auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)
    }).then(() => {
      this.styleProfileService.initializeTemplates();
      this.resetTemplateInputs();
      alert('Template successfully created.');
    }).catch((err) => {
      this.contentError = `An error occurred: ${err.message}`
    })
  }

  contentError: string = null;
  processContent(inputStr) {
    let input: any = null;
    try {
      input = JSON.parse(inputStr);
    } catch (err) {
      this.contentError = 'Error processing JSON input';
      throw new Error('Error processing JSON input: ' + err.message);
    }
    let contentArray = [];

    // Check if input is an object with a content property
    if (typeof input === 'object' && !Array.isArray(input) && input !== null) {
        const inputContainsContent = input.hasOwnProperty('content');
        const inputContentHasLengthOne = inputContainsContent && input.content.length === 1;
        const isTemplate = inputContentHasLengthOne && input.content[0].elementType === ElementType.TEMPLATE;
        const isTemplateTurnedFrame = inputContentHasLengthOne && 
                                      input.content[0].elementType === ElementType.FRAME &&
                                      (<IContentElementTemplate>input.content[0]).templateId;
        if (isTemplate || isTemplateTurnedFrame) {
            contentArray = (<IContentElementTemplate>input.content[0]).content;
        } else if (inputContainsContent && input.content.length > 0) {
            contentArray = input.content;
        } else if (input.hasOwnProperty('langLink') && input.langLink.hasOwnProperty('content') && input.langLink.content.length > 0) {
            contentArray = input.langLink.content;
        } else {
            this.contentError = "Input object must have a 'content' or 'langLink.content' property.";
            throw new Error("Input object must have a 'content' or 'langLink.content' property.");
        }
    } else if (Array.isArray(input)) {
        contentArray = input;
    } else {
        this.contentError = "Invalid input format. Input must be an object with a 'content' property or an array.";
        throw new Error("Invalid input format. Input must be an object with a 'content' property or an array.");
    }

    // Check if each element in the content array has an elementType property
    contentArray.forEach((element, index) => {
        if (!element.hasOwnProperty('elementType')) {
            this.contentError = `Element at index ${index} is missing the 'elementType' property.`;
            throw new Error(`Element at index ${index} is missing the 'elementType' property.`);
        }
    });

    this.contentError = null;

    const parsedContent = JSON.stringify(contentArray);
    return parsedContent;
  }

  hasInvalidInputs() {
    const validateInput = (input: string) => {
      if(!input || !input.length) {
        return false;
      }

      return true;
    }
    const validateJSON = (input: string) => {
      try {
        const content = JSON.parse(input);
        return true;
      } catch {
        return false;
      }
    }

    if(
      !validateInput(this.inputTemplateName) ||
      !validateInput(this.inputTemplateContent) ||
      !validateJSON(this.inputTemplateContent) ||
      !validateInput(this.inputTemplateImage.upload)
    ) {
      return true;
    }
    
    return false;
  }

  resetTemplateInputs() {
    this.inputTemplateImage = {upload:''};
    this.inputTemplateName = null;
    this.inputTemplateContent = null;
    this.inputTemplateReference = null;
    this.showTemplateCreation = false;
    this.contentError = null;
  }

  archiveTemplate(templateId: number, is_archived: number) {
      this.auth.apiRemove(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATES, templateId, {query: {is_archived,  auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)}}).then((template) => {
      this.template.templateDef.is_archived = is_archived;
      this.styleProfileService.initializeTemplates();
    }).catch((err) => {
      alert(`Error: ${err.message}`);
    });
  }


  deleteTemplate(templateId: number) {
    this.auth.apiRemove(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATES, templateId, {query: {is_revoked: 1, auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)}}).then((template) => {
      this.template = {
        templateDef: null,
        templateConfigs: null,
        templateContent: null,
        templateVersion: null,
        templateVersions: null,
        migrationOptions: null,
        meta: null
      }
      this.styleProfileService.initializeTemplates();
    }).catch((err) => {
      alert(`Error: ${err.message}`);
    });
  }

  onTemplateDefChange() {
    this.isTemplateDefChange = true;
  }

  isTemplateDefChange = false;
  isTemplateDefSaving = false;
  templateDefImage = {upload: ''};
  templateDefName: string;
  templateDefDescription: string;
  templateDefMeta: TemplateMeta;
  saveTemplateDefChanges(templateId: number) {
    const name = this.templateDefName
    const description = this.templateDefDescription;
    const icon_url = this.templateDefImage.upload;
    let data: any = {
      name,
      description,
      icon_url,
    }
    this.isTemplateDefSaving = true;

    if(this.templateDefMeta) { // For backwards compatibility
      const meta = JSON.stringify(this.templateDefMeta);
      data.meta = meta;
    }

    this.auth.apiPatch(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATES, templateId, data, {query: {auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)}}).then((template) => {
      this.isTemplateDefChange = false;
      this.template.templateDef.template_name = template.name;
      this.template.templateDef.description = template.description;
      this.template.templateDef.icon_url = template.icon_url;
      if(template.meta && this.templateDefMeta) {
        this.template.templateDef.meta = template.meta;
      }
    }).catch((err) => {
      alert(`error saving changes: ${err.message}`)
    }).finally(() => {
      this.isTemplateDefSaving = false;
    })
  }

  onTemplateDefThumbnailUpload($event) {
    this.templateDefImage.upload = $event;
    this.onTemplateDefChange();
  }

  /**
   * Returns the authoring groups applied to a specific template.
   * @param templateId the ID of the template to look up. 
   * @returns an array of authoring groups that the template belongs to.
   */
  async getGroupFromTemplate(templateId: number) {
    if(!this.template || !this.template.templateDef) {
      return;
    }
    if(!this.itemMakerService.getAuthoringGroups() || !this.itemMakerService.getAuthoringGroups().length) {
      await this.itemMakerService.loadMyAuthoringGroups(true);
    }
    const groups = this.itemMakerService.getAuthoringGroups();

    this.template.templateDef.id
    const templates = this.styleProfileService.templates.filter((templateDef) => templateDef.id == templateId);
    const templateGroups = templates.map((templateDef) => {
      return groups.find((group) => group.group_id == templateDef.bank_group_id);
    }).filter((templateDef) => templateDef != undefined && templateDef != null);

    return templateGroups.filter((group, i) => templateGroups.findIndex((temp) => temp.group_id == group.group_id) == i); // Remove duplicates (for multi-group assigned templates)

  }

  currentGroupToAdd: number = null;
  templateGroups: IAuthoringGroup[] = [];
  async initializeTemplateGroups(templateId: number) {
    this.templateGroups = await this.getGroupFromTemplate(templateId);
  }

  getAuthoringGroups() {
    return this.itemMakerService.getAuthoringGroups(); 
  }

  /*
    Checks if the User has the template author role.
  */
  isUserTemplateAuthor() {
    const authGroups = this.getAuthoringGroups();

    if (!authGroups) {
      return false;
    }

    const groupsAsTempAuth = authGroups.filter(entry => entry.isTemplateAuthor);
    let hasAnyNonPersonalGroups = false;
    groupsAsTempAuth.forEach(group => {
      if (!group.isPersonal){
        hasAnyNonPersonalGroups = true;
      }
    })
    return hasAnyNonPersonalGroups;
  }
  
  isAuthGroupAdding = false;
  isAuthGroupRemoving = false;
  async addAuthoringGroup(currentGroupToAdd: number) {
    this.isAuthGroupAdding = true;
    const data = {
      tqt_id: this.template.templateDef.id,
      bank_group_id: currentGroupToAdd,
      auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)
    }
    return await this.auth.apiCreate(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATE_AUTH_GROUPS, data).then(async () => {
      await this.styleProfileService.initializeTemplates();
      await this.initializeTemplateGroups(this.template.templateDef.id);
    }).finally(() => {
      this.isAuthGroupAdding = false;
    })
  }
  async removeAuthoringGroup(group_id: number) {
    this.isAuthGroupRemoving = true;
    return await this.auth.apiRemove(this.routes.TEST_AUTH_TEST_QUESTION_TEMPLATE_AUTH_GROUPS, group_id, {query: {
      tqt_id: this.template.templateDef.id,
      auth_groups: this.itemMakerService.myGroups.map(group => group.group_id)
    }}).then(() => {
      const indexToRemove = this.templateGroups.findIndex((group) => group.group_id == group_id);
      this.templateGroups.splice(indexToRemove, 1)
      this.styleProfileService.initializeTemplates();
    }).finally(() => {
      this.isAuthGroupRemoving = false;
    })
  }
  
  ensureAdvancedInlineHiddenConfig(config: TemplateConfigDefRow) {
    const hiddenConfigs = config.hiddenConfigs as IAdvancedInlineHiddenConfigs;
    if (hiddenConfigs?.isConfigureContentHiddenConfigs) {
      hiddenConfigs.contentHiddenConfigs = {
        'scientific-notation': {}
      }
    }
  }
  
  ensureSelectionData(config: TemplateConfigDefRow) {
    if (config.selectionData == undefined || config.selectionData.options == undefined) {
      config.selectionData = {
        type: 'dropdown',
        options: []
      };
    }
    return config.selectionData;
  }
  addSelectionOption(selectionData: TemplateConfigDefRow['selectionData']) {
    selectionData.options.push({
      id: '',
      caption: '',
      icon: ''
    })
  }
  loadSelectionOptionsPreset(selectionData: TemplateConfigDefRow['selectionData'], id: string) {
    const confirm = window.confirm('Are you sure you wan to load this preset?');
    if (!confirm) return;
    
    const selectedPreset = templateSelectionOptionsPreset.find((preset) => preset.id == id);
    console.log(selectionData)
    console.log(templateSelectionOptionsPreset)
    console.log(id, selectedPreset)
    if (selectedPreset?.options != undefined) {
      selectionData.options = [...selectedPreset.options];
    }
  }

  /*
  * Handles the drop event for rearranging items in a list.
  * Updates the order of items in the specified array and triggers a configuration change notification.
  */
  drop(arr:any, event: CdkDragDrop<string[]>) {
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
    this.onConfigChange();
  }

  /**
   * Helper function that sets the value of an element in an array based on the index.
   * @param arr the array to mutate.
   * @param index the index of the element.
   * @param newValue the new value of the element.
   */
  setValueByIndex(arr: any[], index: number, newValue) {
    arr[index] = newValue;
  }

  /**
   * For *ngFor input bug
   */
  trackByIndex(index: number, item: any): any {
    return index;
  }

  isTemplatesLoaded() {
    return this.styleProfileService.isTemplateSuccessfullyLoaded;
  }

  treeContent: IContentElement[] = [];
  toggleTreeView(toggle: boolean) {
    this.isTreeView = toggle;

    if(toggle) {
      try {
        this.treeContent = JSON.parse(this.template.templateVersion.content_config);
      } catch (err) {
        this.treeContent = [];
        alert('Invalid JSON content');
      }
    }
  }

  configTypeInfo: {[key: string]: string} = {
    "el": "reference the entire element",
    "text": "reference the specific Text property",
    "note": "",
    "number": "reference the specific Number property",
    "math": "reference the entire Math element",
    "checkbox": "reference the specific true/false property",
    "selection": "",
    "advanced-inline": "reference the 'advancedList' property of the Advanced Inline element",
    "mcq": "reference the *'options'* property of the MCQ element for the *Element Reference*. Reference the entire MCQ element for the MCQ Element Reference.",
    "mcq-table": "reference the entire mcq table element",
    "mcq-freeform": "reference the 'options' property of the MCQ Freeform element",
    "image": "reference the entire image element",
    "table": "reference the entire table element",
    "select-table": "reference the entire selection table element",
    "validator": "for the 'Entries Reference', refer to any path you would like to scan for entry blocks. Reference the entire validator element",
    "translation": "reference the specific text property you would like to apply a translation to.",
    "interactive-diagram": "reference the entire Interactive Diagram element",
    "canvas": "reference the entire Canvas element",
    "keyboard-input-freeform": "reference the 'displayList' property of the Canvas element",
    "math-cloze": "reference the entire Math Cloze Canvas element",
    "select-paragraph": "",
    "select-text": "reference the entire Text Selection element",
    "scientific-notation": "reference the entire Scientific Notation element"
}

getConfigTypeInfo(key: string) {
  const configsNoTailSlug = [
    "translation",
    "mcq"
  ]
  const configInfo = this.configTypeInfo[key];
  if(configInfo == undefined || configInfo == null || !configInfo.length) {
    return undefined;
  }
  const hasTailSlug = !configsNoTailSlug.includes(key);
  
  return `Tip: ${configInfo} ${hasTailSlug ? 'you would like to expose to the author.' : ''}`
}

    
}
