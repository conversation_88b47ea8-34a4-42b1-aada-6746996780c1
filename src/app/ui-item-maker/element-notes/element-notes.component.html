<div *ngIf="isLoading">
  <tra slug="ie_loading"></tra>
</div>

<ng-container *ngIf="!isLoading">
  <!-- Regular top-level view -->
  <!-- Insivible but still rendered so that resolveAllTrigger can resolve all graphic request comments even in zoom view -->
  <div [ngStyle]="{'display': nestedZoomNote ? 'none' : 'block'}">
<div *ngIf="isLoading">
  <tra slug="ie_loading"></tra>
</div>

<ng-container *ngIf="!isLoading">
  <!-- Regular top-level view -->
  <!-- Insivible but still rendered so that resolveAllTrigger can resolve all graphic request comments even in zoom view -->
  <div [ngStyle]="{'display': nestedZoomNote ? 'none' : 'block'}">
    <div *ngIf="(!notes || !notes.length) && newCommentDisabled">
        <i><tra slug="ie_no_comments"></tra></i>
    </div>

    <div class="new-comment-buttons">
      <button
        *ngIf="!newCommentDisabled"
        class="button is-small"
        (click)="initNewComment()"
      >
        <tra slug="auth_new_comment"></tra>
      </button>
      <button 
        *ngIf="!newCommentDisabled && !newHighlightCommentDisabled && !HIGHLIGHT_COMMENT_TEMP_BLOCKED"
        class="button is-small" 
        [class.is-warning]="highlighter.isHighlightCommentButtonActive"
        (click)="clickHighlightcomment()"
      >
        <tra slug="auth_new_highlight_comment"></tra>
      </button>
      <button *ngIf="canImportExportComments" style="align-self:flex-start;" class="button is-small" (click)="openImportExportComments()">
          <span class="icon"><i class="fa fa-upload" aria-hidden="true"></i></span>
          <span>Import/Export Comments</span>
      </button>
    </div>

    <div *ngFor="let note of notes; let i = index">
        <comment-display 
            [groupId]="groupId"
            [singleGroupId]="singleGroupId"
            [delEnabled]="delEnabled"
            [showResolved]="showResolvedComments.value" 
            [isPinnedDisplay]="getPinned"
            [resolveAllTrigger]="resolveAllTrigger"
            [comment]="note" 
            [isGraphicRequestIsSignedOff]="isGraphicRequestIsSignedOff"
            [itemId]="itemId"
            [isExpanded]="isTopLevelCommentExpanded(note, i)"
            [nestingVisibleDepth]="0"
            (assignHandler)="assignHandlerPass($event)"
            (unassignHandler)="unassignHandlerPass(note)"
            (refreshPinned)="refreshPins()"
            (refreshCount)="refreshCountEmit()"
            (exportSingleComment)="openImportExportComments($event)"
            (graphicReqRemoveSignedOff)="graphicReqRemoveSignedOff.emit()"
            (nestedZoomTrigger)="setNestedZoom($event)"
        ></comment-display>
    </div>

  </div>

  <!-- Zoom view (start reading with some deeply nested comment as the top) -->
  <div *ngIf="nestedZoomNote">
    <div>
      <a (click)="setNestedZoom()"><tra slug="auth_comment_to_top"></tra></a>
    </div>
    <div *ngIf="isShowToParent()">
      <a (click)="setNestedZoom(nestedZoomNote.parent)"><tra slug="auth_comment_to_parent"></tra></a>
    </div>
    <comment-display 
    [groupId]="groupId"
    [singleGroupId]="singleGroupId"
    [delEnabled]="delEnabled"
    [showResolved]="showResolvedComments.value" 
    [isPinnedDisplay]="getPinned"
    [comment]="nestedZoomNote"
    [isZoomView]="true"
    [isGraphicRequestIsSignedOff]="isGraphicRequestIsSignedOff"
    [itemId]="itemId"
    [isExpanded]="true"
    [nestingVisibleDepth]="0"
    (assignHandler)="assignHandlerPass($event)"
    (unassignHandler)="unassignHandlerPass(nestedZoomNote)"
    (refreshPinned)="refreshPins()"
    (refreshCount)="refreshCountEmit()"
    (exportSingleComment)="openImportExportComments($event)"
    (graphicReqRemoveSignedOff)="graphicReqRemoveSignedOff.emit()"
    (nestedZoomTrigger)="setNestedZoom($event)"
    ></comment-display>
  </div>

  <!-- View resolved or not -->
  <label class="checkbox">
    <input type="checkbox" [formControl]="showResolvedComments" (change)="showResolvedChanged()">
    <tra slug="el_show_resolved_issues"></tra>
  </label>

</ng-container>

<div *ngIf="isImportExportOpen" style="margin-top:0.3em;">
    <textarea class="textarea" [formControl]="importExportStr" rows=3></textarea>
    <div class="buttons" style="margin-top:0.3em;">
        <div [class.no-pointer-events]="isReadOnly()" (click)="closeImportExportComments()" class="button is-danger">Close</div>
        <div [class.no-pointer-events]="isReadOnly()" (click)="addComments()" class="button">Add Comments</div>
    </div>
</div>

<!-- Modal to submit a comment -->
<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents" style="width: 30vw;">
    <h3><tra slug="auth_new_comment_instruction"></tra></h3>
    <div class="columns" *ngIf="cModal().type == CommentModalType.NEW_HIGHLIGHT_COMMENT">
      <div class="column is-narrow icon is-large is-flex is-justify-content-center">
        <i class="fas fa-quote-left has-text-grey"></i>
      </div>
      <div *ngIf="!cmc().imageUrl" class="column has-text-grey is-italic" [innerHTML]="cmc().highlightHtml"></div>
      <img *ngIf="cmc().imageUrl" class="comment-preview-img" [src]="cmc().imageUrl">
    </div>
    <textarea 
    class="textarea"
    [(ngModel)]="commentText"
    cdkTextareaAutosize
    [cdkTextareaAutosize]="true" 
    [cdkAutosizeMinRows]="2"
    ></textarea>

    <div style="margin-top: 1em">
      <comment-file-upload
      [uploads]="newCommentUploads"
      [isCurrentUserAuthor]="true"
      >
      </comment-file-upload>
      <button class="button is-dark" (click)="insertNewCommentUploads()" style="margin-top: 1em">
        <tra slug="ie_upload_files"></tra>
      </button>
    </div>

    <modal-footer [isEditDisable]="!commentText" [pageModal]="pageModal"></modal-footer>
  </div>
</div>
