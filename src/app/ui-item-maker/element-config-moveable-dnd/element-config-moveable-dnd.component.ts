import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { createDefaultElement, elementIconById, generateDefaultElementImage } from '../item-set-editor/models';
import { EditingDisabledService } from '../editing-disabled.service';
import { 
  //GradingType, 
  ElementType, 
  //ElementTypeDefs, 
  //elementTypes, 
  //IContentElement, 
  //IContentElementDnd, 
  //IContentElementDndDraggable, 
  //IContentElementDndSub, 
  //IContentElementLocAndDims, 
  //IContentElementMoveableDragDrop, 
  IElementTypeDef, 
} from '../../ui-testrunner/models';
import { indexOf } from '../../ui-testrunner/services/util';
import { moveItemInArray, CdkDragDrop } from '@angular/cdk/drag-drop';
import { TwiddleState } from '../../ui-partial/twiddle/twiddle.component';
import { ElementTypeDefs } from '../../ui-testrunner/models/ElementTypeDefs';
import { GradingType, IContentElementMoveableDragDrop } from 'src/app/ui-testrunner/element-render-moveable-dnd/model';
import { IContentElementDndSub } from 'src/app/ui-testrunner/element-render-dnd/model';
import { getVoiceChange } from 'src/app/io-audio/capture-voice/capture-voice.component';

@Component({
  selector: 'element-config-moveable-dnd',
  templateUrl: './element-config-moveable-dnd.component.html',
  styleUrls: ['./element-config-moveable-dnd.component.scss']
})
export class ElementConfigMoveableDndComponent implements OnInit {

  @Input() element:IContentElementMoveableDragDrop;

  constructor(private editingDisabled: EditingDisabledService) { }

  subElementTypes: IElementTypeDef[] = [
    ElementTypeDefs.TEXT,
    ElementTypeDefs.MATH,
    ElementTypeDefs.IMAGE,
    ElementTypeDefs.TABLE,
  ];

  gradingTypes = [
    {id: GradingType.NORMAL, caption: "Normal"},
    {id: GradingType.PLUS_MINUS, caption: "Plus Minus"},
    {id: GradingType.REDUCTION, caption: "Reduction"}
  ]

  twiddleDraggableElements = new TwiddleState(false);
  twiddleTargets = new TwiddleState(false);
  twiddleAnchorImage = new TwiddleState(false);
  twiddleOther = new TwiddleState(false);

  overFlowAmount:number = 0; // this is the height dnd including overflowed elements

  ngOnInit(): void {
    this.ensureConfigRules()
  }

    /**
   * Makes any necessary adjustments to `this.element` to follow specifications for the MOVEABLE_DND element type
   * In case e.g. the question was created before some updates to the block creation code
   */
  ensureConfigRules(){
    if (!this.element.draggableCounter) this.element.draggableCounter = 1;
    if (!this.element.draggables) this.element.draggables = [];
    if (!this.element.targets) this.element.targets = [];
    if (!this.element.backgroundImg) this.element.backgroundImg = generateDefaultElementImage(ElementType.IMAGE)
    if (!this.element.moveableImages) this.element.moveableImages = [];    
    this.element.draggables.forEach((drag)=>{
      if (!drag.voiceover) drag.voiceover = {}
      if (!drag.voiceover.url) drag.voiceover['url'] = ''
      if (!drag.voiceover.script) drag.voiceover['script'] = ''
    })
    this.element.targets.forEach(target => {
      if (!target.elementType) target.elementType = ElementType.DND_TARGET;
      if (target.voiceover) delete target.voiceover;
    })
    this.updateOverflowY();
  }

  getVoiceChange = getVoiceChange; 
  
  insertUneditableElement(elementType: ElementType) {
    const newEl = {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
      ...createDefaultElement(elementType)
    }
    this.element.moveableImages.push(newEl)
    this.updateChangeCounter();
  }

  insertDraggableElement(elementType: ElementType) {
    const newEl = {
      id: this.element.draggableCounter,
      element: createDefaultElement(elementType),
      voiceover: {script: '', url: ''},
      ... this.newDefaultDndElementConfig()
    };
    this.element.draggables.push(newEl);
    this.updateChangeCounter();
  }

  updateChangeCounter() {
      if (!this.element._changeCounter) this.element._changeCounter = 1;
      this.element._changeCounter++;
      this.updateOverflowY()
  }

  isGradingPlusMinus() {
    return this.element.gradingMode == GradingType.PLUS_MINUS
  }

  insertTarget() {
      const newEl = {
        voiceover: {},
        elementType: ElementType.DND_TARGET,
        ... this.newDefaultDndElementConfig()
      }
      this.element.targets.push(newEl);
      this.updateChangeCounter();
  }

  insertImage() {
    const newEl = generateDefaultElementImage(ElementType.IMAGE)
    this.element.moveableImages.push(newEl)
    this.updateChangeCounter();
  }

  newDefaultDndElementConfig (): IContentElementDndSub {
    return  {
      x: 0,
      y: 0,
    };
  }

  isReadOnly() {
      return false;
  }

  drop(element, $event) {
      
  }

  removeElement(arr:any[], element) {
    const index = indexOf(arr, element)
    if (index!=-1) {
        if (window.confirm('Remove this option?')) {
            arr.splice(index,1);
        }
    }
    this.updateChangeCounter();
  }

  currentOptionID: string;
  currentTargetID: string;
  insertCorrectID() {
    if (this.currentOptionID == undefined || this.currentTargetID==undefined) {
      return;
    }
    if (!this.element.pairMapping) {
      this.element.pairMapping = []
    }
    this.element.pairMapping.push({optionID: this.currentOptionID, targetID: this.currentTargetID})
  }

  deleteID(pair) {
    const index = this.element.pairMapping.indexOf(pair)
    if (index!=-1) {
      this.element.pairMapping.splice(pair, 1);
    }
  }

  insertCombinations(){
    if(!this.element.multipleCombinations){
      this.element.multipleCombinations = [];
    }
    let combObj = {}
    this.element.targets.forEach((target) => {
      if(target.id){ 
        combObj[target.id] = ""  
      }
    })
    this.element.multipleCombinations.push(combObj)
  }
  
  deleteCombination(combination){
    const index = this.element.multipleCombinations.indexOf(combination)
    if (index!=-1) {
      this.element.multipleCombinations.splice(index,1);
    }
  }

  /**
   * Function used to flag if new height is needed and what that height is
   */
  updateOverflowY() {
    const dndElement:HTMLElement = document.querySelector(`#element-render-moveable-dnd-${this.element.entryId}`);
    const htmlElements = Array.from(dndElement.querySelectorAll(".target-el, .moveable-obj, .render-image"));
    const dndBottom = dndElement.offsetTop + dndElement.offsetHeight;
    let lowestPoint = dndBottom;
  
    // Loop through each of the HTML elements to find the element with the lowest bottom position
    htmlElements.forEach((el:HTMLElement) => {
      const elBottom = el.offsetTop + el.offsetHeight;
      // If this element's bottom is lower than the current lowest, update lowestPoint
      if (elBottom > lowestPoint) {
        lowestPoint = elBottom;
      }
    });
    this.element.isOverflownY = lowestPoint > dndBottom;
    if(this.element.isOverflownY){
      const computedStyle = window.getComputedStyle(dndElement);
      const fontSize = parseFloat(computedStyle.fontSize);
      this.overFlowAmount = this.element.isOverflownY ? (lowestPoint - dndBottom)/fontSize : 0;
    }
    else {
      this.overFlowAmount = 0;
    }
  }

  autoFixOverFlowY() {
    this.element.height = this.overFlowAmount + (this.element.height || 0);
    this.element.isOverflownY = false;
  }
  
}
