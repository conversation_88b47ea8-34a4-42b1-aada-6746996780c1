import { Component, OnInit, Input } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';

import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { RangeValueAccessor } from '@angular/forms';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { trim } from 'jquery';
import moment from 'moment';
import { downloadUrl } from 'src/app/core/download-string';
import { IAssembledPanel } from '../item-set-editor/models/assessment-framework';

enum ViewMode {
  TESTLET,
  ITEM,
}
@Component({
  selector: 'widget-assembled-forms',
  templateUrl: './widget-assembled-forms.component.html',
  styleUrls: ['./widget-assembled-forms.component.scss']
})
export class WidgetAssembledFormsComponent implements OnInit {


  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() showTableOnly: boolean = false;
  @Input() activeIds: (number | string)[];

  public util = new ItemBankUtilCtrl();
  currentViewMode?:ViewMode;
  currentPanelEdit:any;
  public isEditPanelIds:boolean = false;
  viewLegend = false
  testletSectionBackgroundColor = [
    "#2563EB",  // Strong Blue
    "#B91C1C", // Dark Red
    "#047857", // Dark Green
    "#7C3AED", // Deep Purple
    "#D97706", // Deep Orange
    "#1E3A8A", // Dark Blue
    "#0F766E", // Teal
    "#9333EA", // Vivid Violet
    "#DC2626", // Bright Red
    "#059669", // Emerald Green
  ];
  panels: IAssembledPanel[];

  constructor(
    private editingDisabled: EditingDisabledService,
    private auth: AuthService,
    private lang: LangService,
  ) { }

  ngOnInit(): void {
    if (!this.frameworkCtrl.asmtFmrk.assembledPanels){
      this.frameworkCtrl.asmtFmrk.assembledPanels = [];
    }
    if(this.activeIds){
      this.panels = this.frameworkCtrl.asmtFmrk.assembledPanels?.filter(panel => this.activeIds.includes(panel.id))
    } else {
      this.panels = this.frameworkCtrl.asmtFmrk.assembledPanels;
    }
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true, true);

  clearSourceForms(){

  }

  async loadFromSourceForms(){
    const sourceTestFormsStr = prompt('Please provide comma-delimitted list of test forms');
    const source_test_forms = sourceTestFormsStr.split(',').map(idStr=> {
      return {id: +idStr.trim()}
    })
    const formsData = await this.auth.apiCreate('public/test-auth/panel-shells', { source_test_forms });
    formsData.panels.forEach(form => {
      this.frameworkCtrl.asmtFmrk.assembledPanels.push({
        id: form.source_tf_id,
        testletIds: form.testletIds,
        lang: form.lang,
        sections: form.sections,
      })
    })
  }

/**
 * Exports assembled panels as an Excel file by converting the panel data to CSV format.
 * The file is automatically downloaded after creation.
 * 
 * @returns {Promise<void>} A promise that resolves when the file download has started.
 */

  async exportAssembledPanels(){
    // Prepare the panel data by converting testletIds into a comma-separated string
    const panelData = this.frameworkCtrl.asmtFmrk.assembledPanels.map(panel => {
      return {
        ...panel, 
        testletIds: panel.testletIds.join(',')
      }
    });
    const fileName = `assembled-panels-${this.frameworkCtrl.asmtFmrk.caption}-${moment().format('YYYY-MM-DD[T]HH_mm_ss')}.csv`;
    const file:any = await this.auth.jsonToExcel(panelData, fileName);
    downloadUrl(file.url);
  }
  
  isItemExposureActive(){
    return this.currentViewMode === ViewMode.ITEM
  }

  itemStats = [];
  async toggleItemExposure(){
    if (this.isItemExposureActive()){
      this.currentViewMode = null;
      return;
    }
    const items = [];
    const itemStatRef = new Map();
    const testletToItems = new Map();
    let totalPanelWeight = 0;
    const panels = this.frameworkCtrl.asmtFmrk.assembledPanels || [];
    const includedPanels = panels.filter(panel => !panel.isExcluded)
    this.frameworkCtrl.asmtFmrk.testlets.forEach(testlet => {
      testletToItems.set(testlet.id, testlet.questions.map(q => q.id));
    })
    includedPanels.forEach(panel => {
      totalPanelWeight += +(panel.multiplier || 1);
    })
    includedPanels.forEach(panel => {
      panel.testletIds.forEach(testletId => {
        const questionIds = testletToItems.get(testletId);
        questionIds?.forEach(item_id => {
          let item = itemStatRef.get(item_id);
          if (!item){
            const question = this.frameworkCtrl.itemBankCtrl.getQuestionById(item_id)
            item = {
              item_id, 
              label: question.label,
              testletIds:[], 
              panelIds:[], 
              panelPresence: 0,
              estimatedExposure: 0,
            }
            itemStatRef.set(item_id, item);
            items.push(item)
          }
          item.panelPresence += +(panel.multiplier || 1)
          item.estimatedExposure = item.panelPresence / totalPanelWeight;
          if (item.testletIds.indexOf(testletId) === -1){
            item.testletIds.push(testletId);
          }
          item.panelIds.push(panel.id);
        })
      })
    });
    // items.forEach(item => {
    //   item.testletsFormatted = item.testletIds.map(tId => `[${tId}]`).join(',');
    // })
    this.itemStats = items
    this.currentViewMode = ViewMode.ITEM    
  }

  formatPerc(num:number){
    return ((num || 0)*100).toFixed(2)+'%'
  }

  confirmMultiplier(panel){
    panel.multiplier = Math.max(1, Math.round(panel.multiplier || 1));
    this.currentPanelEdit = null;
  }

  logItemStats(){
    console.log(this.itemStats)
  }

  newPanel(){
    const id = prompt('What ID would you like to assign to this new panel? It cant match any of the other panels currently being used in this test design.')
    if (id){
      const quadrantsReq = new Set()
      const quadrantsCovered = new Map()
      let isProblematic = false;

      let panels = this.frameworkCtrl.asmtFmrk.assembledPanels
      if (!panels){
        panels = this.frameworkCtrl.asmtFmrk.assembledPanels = []
      }
      const allQuadrants = this.frameworkCtrl.asmtFmrk.quadrants;
      const allSections = this.frameworkCtrl.asmtFmrk.partitions
        .map(section => section.id);
      const quadrantToSectionMap = new Map();
      for (let quadrant of allQuadrants){
        const section = allSections.findIndex(s => s+'' === quadrant.section+'')
        if(section !== -1){
          quadrantToSectionMap.set(+quadrant.id, section);
        }
        quadrantsReq.add(+quadrant.id)
      }
      const testletToQuadrant = new Map();
      this.frameworkCtrl.asmtFmrk.testlets.forEach(testlet => {
        testletToQuadrant.set(+testlet.id, +testlet.quadrant);
      })
      for (let panel of panels){
        if (panel.id == id){
          alert('ID is already being used');
          throw new Error()
        }
      }
      const lang = this.lang.c();
      const testletIdsStr = prompt('Enter the list of testlet ids in the order in which they should appear in the assessment (each ID should be separated by a comma).')
      const testletIds = testletIdsStr.split(',').map(str => +(str.trim()) )
      // validate
      for (let i=0; i<testletIds.length; i++){
        const testletId = testletIds[i]
        const quadrantId = testletToQuadrant.get(testletId);
        if (!quadrantsReq.has(+quadrantId)){
          alert(`Testlet ${testletId} using a quadrant (${quadrantId}) that is not in the current design. Aborting.`)
          throw new Error()
        }
        if (quadrantsCovered.has(+quadrantId)){
          alert(`Testlet ${testletId} and ${quadrantsCovered.get(+quadrantId)} are in the same quadrant (${quadrantId}). Need to be distinct. Aborting.`)
          throw new Error()
        }
        quadrantsCovered.set(+quadrantId, testletId)
      }
      if (quadrantsCovered.size !== quadrantsReq.size){
        alert(`Some quadrants are not covered (see console log for details). Remove panel unless this is just a test.`)
      }
      testletIds.sort((a,b) => { // Testlet Id's by section
        const quadrantA = testletToQuadrant.get(a);
        const quadrantB = testletToQuadrant.get(b);
        const sectionA = quadrantToSectionMap.get(quadrantA) ?? - 1;
        const sectionB = quadrantToSectionMap.get(quadrantB) ?? - 1;
        return sectionA - sectionB
      })
      const questionCount: {fieldTestCount: number, operationalCount: number} = {fieldTestCount: 0, operationalCount: 0};
      testletIds.forEach(id =>{
        const testlet = this.frameworkCtrl.asmtFmrk.testlets.find(testlet => +testlet.id === +id);
        if(testlet){
          const {questions} = this.frameworkCtrl.testletCtrl.getTestletQuestions(testlet, false);
          questions.forEach((q)=>{
            if(!q.isReadingSelectionPage){
              const isFieldTest = q.meta?.['FT'];
              if(isFieldTest){
                questionCount.fieldTestCount++;
              } else {
                questionCount.operationalCount++;
              }
            }
          });
        }
      })
      panels.push({
        id,
        lang,
        multiplier: 1,
        testletIds,
        isProblematic,
        numOperationalItems: questionCount.operationalCount,
        numFieldTestItems: questionCount.fieldTestCount
      })
    }
  }

  getTestletSectionColor(testletId:number){
    const testlet = this.frameworkCtrl.asmtFmrk.testlets?.find(testlet => testlet.id == testletId);
    const sectionColorIndex = this.frameworkCtrl.asmtFmrk.partitions?.findIndex(section => section.id == testlet?.section);
    let color;
    if(sectionColorIndex === -1 || !testlet) color = "#000000" // if testlet doesn't exist or we cant find it's section gray it out
    else color =  this.testletSectionBackgroundColor[sectionColorIndex]
    return `background-color: ${color}`
  }

  getTestletQuadrant(testletId:number){
    const testlet = this.frameworkCtrl.asmtFmrk.testlets?.find(testlet => testlet.id == testletId);
    return testlet?.quadrant
  }

  getSectionQuadrants(section:any){
    const sectionId = section.id;
    const quadrants = this.frameworkCtrl.asmtFmrk.quadrants
      .filter(quadrant => quadrant.section == sectionId)
      .map(quadrant => quadrant.id);
    return quadrants.join(',')
  }
  // toggleTestletExposure(){

  // }


}
