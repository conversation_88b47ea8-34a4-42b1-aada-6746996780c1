import { Component, OnInit, Input, Output } from '@angular/core';
import { QuestionView } from '../item-set-editor/models/types';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { IStyleProfile, StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';

import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { AuditResultType, IAuditResultSummary, ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { IAssessmentPartition, TestFormConstructionMethod } from '../item-set-editor/models/assessment-framework';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { IAuthRestrictions } from '../item-set-editor/item-set-editor.component';


export enum FrameworkModal {
  SECTION_EDIT      = 'SECTION_EDIT'
}

@Component({
  selector: 'widget-framework-main',
  templateUrl: './widget-framework-main.component.html',
  styleUrls: ['./widget-framework-main.component.scss']
})
export class WidgetFrameworkMainComponent implements OnInit{


  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Input() authRestrictions: IAuthRestrictions = {
    isCurrentUserRestricted: false,
    isCurrentUserTemplateManager: false
  };

  public pageModal: PageModalController;


  QuestionView = QuestionView;
  questionsViews: IMenuTabConfig<QuestionView>[] = [
    {id: QuestionView.QUESTION_BANK, caption: this.lang.tra('ie_item_bank'), count: () => this.frameworkCtrl.getNumQuestions(), showIf: () => this.frameworkCtrl.isTestformLOFT() || this.frameworkCtrl.isTestformMsCat() },
    // {id: QuestionView.MSCAT_MODULES, caption: 'Modules', count: () => this.getNumModules(), showIf: () => this.isTestformMsCat() },
    {id: QuestionView.MSCAT_PANELS, caption: 'Panels', count: () => this.panelCtrl.getNumPanels(this.frameworkCtrl.asmtFmrk), showIf: () => this.frameworkCtrl.isTestformMsCat() },
    {id: QuestionView.QUADRANTS, caption: 'Quadrants', count: () => this.frameworkCtrl.getNumQuadrants(), showIf: () => this.frameworkCtrl.isTestformLOFT() || this.frameworkCtrl.isTestformMsCat() },
    {id: QuestionView.TESTLETS, caption: 'Testlets', count: () => this.frameworkCtrl.getNumTestlets(), showIf: () => this.frameworkCtrl.isTestformLOFT() || this.frameworkCtrl.isTestformMsCat() },
    {id: QuestionView.FORM_CONSTRUCTION, caption: this.lang.tra('auth_form_construction'), showIf: () => this.frameworkCtrl.isTestformLinear() },
    {id: QuestionView.AUDITS, caption: this.lang.tra('auth_audits') },
    {id: QuestionView.ASSEMBLED_FORMS, caption: 'Assembled Panels', count: () => this.frameworkCtrl.getNumStoredPanels(), showIf: () => this.frameworkCtrl.isTestformLOFT() },
    {id: QuestionView.SAMPLE_FORMS, caption: 'Sample Test Forms', count: () => this.frameworkCtrl.getNumTestForms(), showIf: () => this.frameworkCtrl.isTestformLOFT() },
    {id: QuestionView.STYLE_PROFILE, caption: 'Style Profile', flagged: () => !this.frameworkCtrl.asmtFmrk.styleProfile},
  ];
  removedQuestions: { [key: string]: string[] } = {};
  collapsedSections: { [key: string]: boolean } = {};
  allCollapsed = true;
  frameworkCollapsed = true;
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private lang: LangService,
    public profile:StyleprofileService,
    private auth: AuthService,
    private routes: RoutesService,  
    private pageModalService: PageModalService,
    private authScopeSettings: AuthScopeSettingsService,
  ) { }

  FrameworkModal = FrameworkModal;

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.itemBankCtrl.styleProfileSelector.valueChanges.subscribe(styleprofile => this.frameworkCtrl.asmtFmrk.styleProfile = styleprofile);
    this.removedQuestions = this.frameworkCtrl.getRemovedQuestionInFramework();
    this.frameworkCtrl.alertRemovedQuestions(this.removedQuestions)
    this.initializeCollapsedSections();
    this.initAuditState();
  }


  async initAuditState(){
    await this.auditCtrl.getFrameworkLevelAudits();
    this.auditCtrl.getAuditLogs();
  }

  initializeCollapsedSections(): void {
    Object.keys(this.removedQuestions??{}).forEach(section => {
      this.collapsedSections[section] = true;
    });
  }

  getRemovedQuestionsSections(): string[] {
    return Object.keys(this.removedQuestions);
  }

  
  hasRemovedQuestions(): boolean {
    return Object.keys(this.removedQuestions).length > 0;
  }
  
  toggleIsSectionCollapsed(section: string): void {
    this.collapsedSections[section] = !this.collapsedSections[section];
  }
  
  toggleAll(expandAll: boolean): void {
    if (this.frameworkCollapsed) return;
    this.allCollapsed = expandAll;
    Object.keys(this.collapsedSections).forEach(section => {
      this.collapsedSections[section] = this.allCollapsed;
    });
  }
  
  toggleAllInFramework(): void {
    this.frameworkCollapsed = !this.frameworkCollapsed;
    // When framework is expanded, collapse all sections initially
    if (!this.frameworkCollapsed) {
      this.toggleAll(false);
    }
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true, true);
  
  getStyleProfileData = () => this.profile.getStyleProfile();
  
  setLang(lang: string) {
    this.previewCtrl.setLang(lang);
    this.auditCtrl.resetAudits();
    this.initAuditState();
    this.itemFilterCtrl.updateItemFilter();
  }

  updateStyleProfile = (data) => {
    let slug = this.profile.getSelectedStyleProfile();
    let id = this.profile.getStyleProfileId();
    if(!id) return;

    this.auth
      .apiPatch(this.routes.TEST_AUTH_STYLE_PROFILES, id, data, {
        query: {
          group_id: this.itemBankCtrl.groupId,
          slug
        }
      })
      .then(res => {
        // update the current style profile with new data
        this.profile.setStyleProfileFromConfig(data, slug, id);
      })
      .catch(e => console.error("STYLE_PROFILE_UPDATE_ERROR", e));
  } 

  openSectionEditModal(section: IAssessmentPartition) {
    const config = {
      section
    }
    
    this.pageModal.newModal(
      {
        type: FrameworkModal.SECTION_EDIT,
        config,
        isProceedOnly: true,
        finish: () => {}
      }
    );
  } 

  cModal() { return this.pageModal?.getCurrentModal(); }
  cmc() { return this.cModal()?.config; }

  isSaveBtnEnabled(){
    for (const dimension of this.frameworkCtrl.asmtFmrk.primaryDimensions) {
      if (this.authScopeSettings.isItemParamViewable(dimension)){
        return true
      }   
    }
    return false
  }
}
