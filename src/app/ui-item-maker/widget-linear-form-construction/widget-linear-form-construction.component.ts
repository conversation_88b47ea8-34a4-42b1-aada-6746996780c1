import { Component, OnInit, Input, Output } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';


import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { FormControl } from '@angular/forms';
import { IAssessmentPartition } from '../item-set-editor/models/assessment-framework';
import { partition } from 'cypress/types/lodash';
import { EventEmitter } from '@angular/core';


@Component({
  selector: 'widget-linear-form-construction',
  templateUrl: './widget-linear-form-construction.component.html',
  styleUrls: ['./widget-linear-form-construction.component.scss']
})
export class WidgetLinearFormConstructionComponent implements OnInit {
  
  
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Input() itemComponentEditCtrl: ItemComponentEditService;
  @Input() showTableOnly: boolean = false; // only display the table of sections, no header
  @Input() activeIds: number[] = [];
  @Output() openSectionEditModal = new EventEmitter();
  
  public util = new ItemBankUtilCtrl();
  partitions: IAssessmentPartition[] = [];
  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    public profile: StyleprofileService
  ) { }

    panelName: string;
    isEditing: boolean = false;

  ngOnInit(): void {
    this.panelName = this.frameworkCtrl.asmtFmrk.panelName;
    this.checkIdLabelMapping()
    this.setActivePanels();
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true, true);

  getItemSetId(){
    return this.itemBankCtrl.customTaskSetId
  }

  checkIdLabelMapping(){
    const changesMade = []
    const asmtFmrk = this.frameworkCtrl.asmtFmrk;
    for (let partition of asmtFmrk.partitions){
      const {questions} = this.frameworkCtrl.getSectionQuestionsLinear(asmtFmrk, partition);
      for (let question of questions){
        if (question.id){
          const questionRef = this.itemBankCtrl.getQuestionById(question.id)
          // console.log('checkIdLabelMapping', questionRef)
          if (questionRef && question.label !== questionRef.label){
            changesMade.push({from:question.label, to: questionRef.label})
            question.label = questionRef.label
          }
        }
      }
    }
    if (changesMade.length > 0){
      console.log('labelChangesToSave', changesMade)
      alert('It looks like some of the item labels for items that were assigned to the form have changed. These have been updated in the form and reflected in the framework the next time you save. List is available in the console');
    }
  }

  saveName() {
    this.frameworkCtrl.asmtFmrk.panelName = this.panelName;
    this.isEditing = false;
  }

  startEdit() {
    this.isEditing = true;
  }
  isNameEmpty() {
    if(this.frameworkCtrl.asmtFmrk.panelName == null || this.frameworkCtrl.asmtFmrk.panelName.trim().length == 0) {
      return true;
    }

    return false;
  }
  
  activateLinearFormQuestions(){
    this.toggleTestQuestionCount()
    this.itemBankCtrl.activateLinearFormQuestions();
  }

  showTestQuestionCount:boolean = false;
  questionCount:{fieldTestCount: number, operationalCount: number} = {fieldTestCount: 0, operationalCount: 0};
  toggleTestQuestionCount(){
    this.showTestQuestionCount = !this.showTestQuestionCount;
    if(this.showTestQuestionCount){
      this.refreshTestQuestion()
    }
  }

  refreshTestQuestion(){
    this.questionCount = {fieldTestCount: 0, operationalCount: 0};
    this.frameworkCtrl.asmtFmrk.partitions?.forEach((partition) =>{
      const questions = this.frameworkCtrl.getSectionQuestionsLinear(this.frameworkCtrl.asmtFmrk, partition).questions;
      questions.forEach((q)=>{
        const question = this.itemBankCtrl.getQuestionById(+q.id);
        if(question && !question.isReadingSelectionPage){
          const isFieldTest = question.meta?.['FT'];
          if(isFieldTest){
            this.questionCount.fieldTestCount++;
          } else {
            this.questionCount.operationalCount++;
          }
        }
      })
    })
  }

  removeQuestionFromTestForm(questionIdentifier: any, partition:IAssessmentPartition){
    this.frameworkCtrl.removeQuestionFromTestform(questionIdentifier, partition)
    if(this.showTestQuestionCount){
      this.refreshTestQuestion();
    }
  }

  setActivePanels(){
    if(this.activeIds?.length > 0){
      this.partitions = this.frameworkCtrl.asmtFmrk.partitions.filter(partition => {
        return this.activeIds.includes(partition.id);
      });
    } else {
      this.partitions = this.frameworkCtrl.asmtFmrk.partitions;
    }
  }

  updateActivePanels(activeId: number){
    this.activeIds.push(activeId);
    this.setActivePanels();
  }

}
