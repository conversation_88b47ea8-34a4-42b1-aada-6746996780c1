<div >
    <span *ngIf="!showTableOnly">    
      <a target="sample" routerLink="/{{lang.c()}}/test-auth/framework/1/{{getItemSetId()}}/preview">View as Test Taker (Shareable Link)</a>
      <!-- Todo: should only activate this link for test designs... -->

      <div class="pre-table-strip">
        <div>
          <button 
            class="button  has-icon" 
            (click)="previewCtrl.previewLinearTestForm();" 
          >
            <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
            <span><tra slug="ie_view_as_test_taker"></tra></span>
          </button>

        <button
            [attr.disabled]="isReadOnly() ? 'disabled' : null"
            class="button  has-icon" 
            (click)="printViewCtrl.gotoLinearPrintView();" 
          >
            <span class="icon"><i class="fa fa-print" aria-hidden="true"></i> </span>
            <span><tra slug="auth_print_view"></tra></span>
        </button>
        <button 
            class="button  has-icon" 
            [class.is-info]="itemFilterCtrl.activeTestFormFilter"
            (click)="activateLinearFormQuestions();" 
          >
            <span class="icon"><i class="fa fa-filter" aria-hidden="true"></i> </span>
            <span><tra slug="auth_filter_assessment_to_used_list"></tra></span>
        </button>
      </div>
        </div>
      </span>
    <div style="display:flex; flex-direction:row; ">
      <div style="max-height:90vh; overflow:auto">
        <div class="panel-name-container" *ngIf="!isEditing">
          <div *ngIf="isNameEmpty()">
            <i class="panelname">Panel name</i>
          </div>
          <div *ngIf="!isNameEmpty()">
            <h3 class="panelname">{{frameworkCtrl.asmtFmrk.panelName}}</h3>
          </div>
          <span [class.is-showing-item-tally]="showTestQuestionCount">
            <div *ngIf="showTestQuestionCount">
              Operational: {{questionCount.operationalCount}}&nbsp;Field Test: {{questionCount.fieldTestCount}}
            </div>
            <button class="button" (click)="startEdit()"><tra slug="edit_btn"></tra></button>
          </span>
        </div>
        <div class="panel-name-container" *ngIf="isEditing">
          <input type="text" [(ngModel)]="panelName" class="panelname-edit" placeholder="Enter a panel name">
          <button class="button" (click)="saveName()"><tra slug="btn_done"></tra></button>
        </div>
        <table class="table is-bordered" style="width:auto; margin-bottom:2em;" cdkDropListGroup>
          <tr>
            <th style="width: 18em; max-width:20em"><tra slug="ie_section"></tra></th>
            <th style="width: 22em;"><tra slug="ie_questions"></tra></th>
          </tr>
          <tr *ngFor="let partition of partitions; let index = index">
            <td>
              <div class="space-between">
                <div>
                  <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'description')"> <tra [slug]="partition.description || '(Description)'"></tra></a>
                  <span *ngIf="partition.isConditional">(Path {{+partition.conditionOnOption + 1}})</span>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: flex-end">
                  <button class="button is-small" (click)="openSectionEditModal.emit(partition)"><i class="fas fa-ellipsis-h"></i></button>
                  <code>
                    {{partition.id}}
                  </code>
                </div>
              </div>
              <twiddle caption="Options" (change)="partition.isConfigExpanded = $event"></twiddle>
              <div *ngIf="partition.isConfigExpanded">
                <button (click)="util.moveArrElUp(partition, partitions)"><tra slug="auth_move_up"></tra></button>
                <button (click)="util.moveArrElDown(partition, partitions)"><tra slug="auth_move_down"></tra></button>
                <div  class="section-settings-container">
                  <div class="section-setting"><span class="first-el"><tra slug="ie_preamble"></tra>:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'preambleQuestionLabel', true)"> {{partition.preambleQuestionLabel || lang.tra('ie_no_preamble')}} </a></div>
                  <div class="section-setting"><span class="first-el">Module Map Item Meta:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'mapMetaItemId', true)"> {{partition.mapMetaItemId || '(No Map Meta Surrogate)'}} </a></div>
                  <div class="section-setting"><span class="first-el">Info Caption:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'infoCaption', true)"> {{partition.infoCaption || '(No Caption)'}} </a></div>
                  <div class="section-setting"><span class="first-el">Submission Text: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'submissionText', true)"> <tra [slug]="partition.submissionText || 'btn_review_submit'"></tra> </a></span></div>
                  <div class="section-setting"><span class="first-el">Notepad Text: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'notepadText', true)"> {{partition.notepadText || '(None)'}} </a></span></div>
                  <div class="section-setting"><span class="first-el">Image:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'preambleThumbnail', true)"> {{partition.preambleThumbnail || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el">EN Thumbnail:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'sidebarThumbnailEn', true)"> {{partition.sidebarThumbnailEn || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el">FR Thumbnail:</span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'sidebarThumbnailFr', true)"> {{partition.sidebarThumbnailFr || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el"><tra slug="auth_image_text"></tra></span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'preambleThumbnailText', true)"> {{partition.preambleThumbnailText || '(No translation slug)'}} </a></div>
                  <div class="section-setting"><span class="first-el"><tra slug="auth_image_seleted"></tra></span> <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'preambleThumbnailSelected', true)"> {{partition.preambleThumbnailSelected || '(No url)'}} </a></div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isShuffled" (toggle)="partition.isShuffled = !partition.isShuffled"></check-toggle><span><tra slug="ie_shuffle_order_q"></tra></span>        </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isCalculatorEnabled" (toggle)="partition.isCalculatorEnabled = !partition.isCalculatorEnabled"></check-toggle> <span><tra slug="ie_enable_calculator_q"></tra></span>    </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isNotepadDisabled" (toggle)="partition.isNotepadDisabled = !partition.isNotepadDisabled"></check-toggle> <span>Disable Notepad?</span>    </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isFormulaSheetEnabled" (toggle)="partition.isFormulaSheetEnabled = !partition.isFormulaSheetEnabled"></check-toggle> <span><tra slug="ie_enable_formula_sheet_q"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isTimeLimit" (toggle)="partition.isTimeLimit = !partition.isTimeLimit"></check-toggle> <span><tra slug="ie_apply_time_limit_q"></tra></span> </div>
                  <div class="section-setting-sub" *ngIf="partition.isTimeLimit">
                    <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'timeLimitMinutes', true)"> {{partition.timeLimitMinutes || '--'}} </a> minutes
                  </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.isShuffled" (toggle)="partition.useQuestionLabel = !partition.useQuestionLabel"></check-toggle><span><tra slug="auth_use_item_label"></tra></span>        </div>
                  <div class="section-setting"><check-toggle [disabled]="isReadOnly()" class="first-el" [isChecked]="partition.isConditional" (toggle)="partition.isConditional = !partition.isConditional"></check-toggle> <span><tra slug="ie_activate_by_decision_q"></tra></span> </div>
                  <div class="section-setting-sub" *ngIf="partition.isConditional">
                    <tra slug="ie_item_id"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'conditionOnItem', true)"> {{partition.conditionOnItem || '--'}} </a>,
                    Option Index: <a [class.is-disabled]="isReadOnly()" (click)="util.replaceProp(partition, 'conditionOnOption', true)"> {{partition.conditionOnOption || '--'}} </a>
                  </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.disableScoring" (toggle)="partition.disableScoring = !partition.disableScoring"></check-toggle> <span><tra slug="auth_disable_scoring"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.disableFlagging" (toggle)="partition.disableFlagging = !partition.disableFlagging"></check-toggle> <span><tra slug="auth_disable_flagging"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.disableLeftBar" (toggle)="partition.disableLeftBar = !partition.disableLeftBar"></check-toggle> <span><tra slug="auth_disable_left_bar"></tra></span> </div>
                  <div class="section-setting">
                    <div>
                      <tra slug="auth_entry_warning"></tra>
                      <button *ngIf="!partition.msgReqFill" (click)="partition.msgReqFill='new message'"><tra slug="auth_override"></tra></button>
                      <div><textarea *ngIf="partition.msgReqFill" [(ngModel)]="partition.msgReqFill"></textarea></div>
                    </div> 
                  </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="partition.customSectionPopup" (toggle)="partition.customSectionPopup = !partition.customSectionPopup"></check-toggle> <span>Custom Section Popup</span> </div>
                  <div *ngIf="partition.customSectionPopup">
                    <div class="section-setting">
                        <div [class.no-pointer-events]="isReadOnly()" class="select">
                          <select [(ngModel)]="partition.customSectionPopupSlug">
                            <option [value]=undefined></option>
                            <option *ngFor="let option of profile.getSectionPopupSlugs()" [value]="option">
                              {{option}}
                            </option>
                          </select>
                        </div>
                    </div>          
                  </div>  
                </div>
                <div>
                  <button [disabled]="isReadOnly()" class="button is-small is-danger" (click)="frameworkCtrl.removeSectionFromTestForm(partition)"><tra slug="auth_discard_section"></tra> {{partition.id}}</button>
                </div>
              </div>
            </td>


            <td >
              <div 
                cdkDropList  
                [cdkDropListDisabled]="isReadOnly()"
                [cdkDropListData]="frameworkCtrl.getSectionQuestionsLinear(frameworkCtrl.asmtFmrk, partition).questions"
                (cdkDropListDropped)="util.dropBtwn($event)"
              >    
              <div 
              *ngFor="let questionIdentifier of frameworkCtrl.getSectionQuestionsLinear(frameworkCtrl.asmtFmrk, partition).questions" 
              cdkDrag 
              class="section-question-row"
              [class.is-question-nav-open]="itemBankCtrl.isCurrentQuestionNavigationOpen(questionIdentifier)"
              >
              <button [disabled]="isReadOnly()" [class.no-pointer-events]="isReadOnly()" class="stack-edit-element-header" cdkDragHandle>
                <div class="icon"><i class="fa fa-bars" aria-hidden="true"></i></div>
              </button>
              <div class="question-label">
                {{questionIdentifier.label}}
              </div>
              <button [disabled]="isReadOnly()" (click)="removeQuestionFromTestForm(questionIdentifier, partition)" [title]="lang.tra('ie_remove_item_from_tf')" class="button is-danger">
                <i class="fa fa-minus" aria-hidden="true"></i>
              </button>
              <button 
                (click)="itemBankCtrl.openQuestionNavigation(questionIdentifier)" 
                [title]="lang.tra('ie_view_question')" 
                class="button"
                [class.is-info]="itemBankCtrl.isQuestionSetForNavigation(questionIdentifier)"
              >
                <i class="fa fa-compass" aria-hidden="true"></i>
              </button>
              <button (click)="itemBankCtrl.selectSectionQuestion(questionIdentifier)" [title]="lang.tra('ie_view_question')" class="button " [class.is-info]="itemBankCtrl.isSectionQuestionSelected(questionIdentifier)">
                <i class="fa fa-eye" aria-hidden="true"></i>
              </button>
            </div>
          </div>
          <div style="margin:0.5em 0em;">
            <button [disabled]="isReadOnly()" class="button has-icon is-info is-small is-fullwidth is-outlined" (click)="itemBankCtrl.selectSection(partition)" *ngIf="!itemBankCtrl.isSelectedSection(partition)">
              <span class="icon"> <i class="fa fa-circle-thin" style="color:#ccc;" aria-hidden="true" ></i> </span>
              <span> <tra slug="ie_add_question_to_sec"></tra> </span>
            </button>
            <button [disabled]="isReadOnly()" class="button has-icon is-small is-fullwidth is-info " (click)="frameworkCtrl.scrollToQuestionListing()" *ngIf="itemBankCtrl.isSelectedSection(partition)">
              <span class="icon">
                <i class="fa fa-plus-circle" aria-hidden="true" ></i>
              </span>
              <span> <tra slug="ie_adding_question_to_sect"></tra> {{partition.id}} </span>
            </button>
          </div>
        </td>
      </tr>
    </table>
    </div>
    <div class="section-question-navigation" *ngIf="itemBankCtrl.currentQuestionNavigation">
      <h4>
        Dropdown Navigation
        <span>({{itemBankCtrl.currentQuestionNavigation.label}})</span>
      </h4>
      <div class="config-container">
        <div class="flex-row control center">
          <div class="flex-col">
            <span>Set as Anchor</span>
          </div>
          <div class="flex-col">
            <input 
              type="checkbox" 
              [(ngModel)]="itemBankCtrl.currentQuestionNavigation.isAnchor" 
              (change)="itemBankCtrl.setQuestionNavigationAsAnchor($event.target.checked, itemBankCtrl.currentQuestionNavigation)"
            >
          </div>
        </div>
        <div *ngIf="itemBankCtrl.currentQuestionNavigation.isAnchor">
          <div class="flex-row control center">
            <div class="flex-col">
              <span>Anchor Label</span>
            </div>
            <div class="flex-col">
              <input 
                class="input" 
                type="text" 
                style="width:15em; text-align:center" 
                (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                [formControl]="itemBankCtrl.currentQuestionNavigationLabel"
              >
            </div>
          </div>
          <div class="flex-row control center">
            <div class="flex-col">
              <span>Lock By</span>
            </div>
            <div class="flex-col">
              <div class="select is-small">
                <select 
                  [(ngModel)]="itemBankCtrl.currentQuestionLockBy" 
                  (change)="itemBankCtrl.cleanQuestionNavigationState()"
                >
                  <option [value]="null" selected>None</option>
                  <option value="section">Section Id</option>
                  <option value="item">Item Label</option>
                  <option value="choice">Choice Id</option>
                </select>
              </div>
            </div>
          </div>
          <div *ngIf="itemBankCtrl.currentQuestionLockBy" [ngSwitch]="itemBankCtrl.currentQuestionLockBy">
            <div class="flex-row control center" *ngSwitchCase="'section'">
              <div class="flex-col">
                <span>Section ID</span>
              </div>
              <div class="flex-col">
                <input 
                  class="input" 
                  type="text" 
                  (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                  [(ngModel)]="itemBankCtrl.currentQuestionLockBySectionId"
                >
              </div>
            </div>
            <div class="flex-col control center" *ngSwitchCase="'item'">
              <div class="flex-col" style="padding-bottom: .5em">
                <span>Item Labels</span>
              </div>
              <div class="flex-col">
                <div *ngFor="let item of itemBankCtrl.currentQuestionLockByItems; let index = index; trackBy: itemBankCtrl.trackByIndex;">
                  <div class="flex-row item-label" *ngIf="itemBankCtrl.currentQuestionLockByItems[index]">
                    <input 
                      class="input is-small" 
                      placeholder="Item Label" 
                      type="text" 
                      style="width:15em; text-align:center" 
                      [(ngModel)]="itemBankCtrl.currentQuestionLockByItems[index].label"
                      disabled
                    >
                    <button class="button is-small is-danger" (click)="itemBankCtrl.removeQuestionLockyByItemLabel(index, itemBankCtrl.currentQuestionNavigation)">
                      <tra slug="ie_delete_param"></tra>
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex-col">
                <div class="flex-row item-label">
                  <input 
                      class="input is-small" 
                      placeholder="Item Label" 
                      type="text" 
                      style="width:15em; text-align:center" 
                      [formControl]="itemBankCtrl.currentQuestionLockByNewItemLabel"
                  >
                  <button class="button is-small" (click)="itemBankCtrl.addQuestionLockByItemLabel(itemBankCtrl.currentQuestionLockByNewItemLabel, itemBankCtrl.currentQuestionNavigation)">
                    <tra slug="ie_add_item_bank"></tra>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex-col control center" *ngSwitchCase="'choice'">
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Item Label</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    [formControl]="itemBankCtrl.currentQuestionLockByNewItemLabel"
                    (change)="itemBankCtrl.addQuestionLockByItemLabelToChoiceLock(itemBankCtrl.currentQuestionLockByNewItemLabel, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByItem && itemBankCtrl.currentQuestionLockByItem.label"
                  >
                </div>
              </div>
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Entry ID</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByEntryId"
                  >
                </div>
              </div>
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Choice ID</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByChoiceId"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section-question-review" *ngIf="itemBankCtrl.isFormQuestionSelected()">
      <h4>
        <tra slug="ie_item_preview"></tra>
        <button class="button is-small" (click)="itemBankCtrl.switchToEditorView()"><tra slug="ie_edit"></tra></button>
      </h4>
      <question-runner 
        [currentQuestion]="itemBankCtrl.getCurrentQuestionContent()" 
        [questionState]="itemBankCtrl.activeQuestionState"
        [isSubmitted]="itemEditCtrl.isLocked" 
        [isPrintMode]="printViewCtrl.isResultsPrint"
      ></question-runner>
    </div>
    </div>
    <div style="margin-top:1em;">
      <button [disabled]="isReadOnly()" (click)="frameworkCtrl.createNewSection(updateActivePanels.bind(this))" class="button is-success"><tra slug="ie_create_new_section"></tra></button>
      <button [disabled]="isReadOnly()" class="button has-icon" [class.is-success]="saveLoadCtrl.isSaveChangedRequired" (click)="saveLoadCtrl.saveChanges()">
        <span class="icon"><i class="fa fa-floppy-o" aria-hidden="true"></i></span>
        <span><tra slug="ie_save_changes"></tra></span>
      </button> 
    </div>
    <hr/>

</div>