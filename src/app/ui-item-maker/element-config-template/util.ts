import { ITemplateDef, TemplateVersionConfig } from "src/app/core/styleprofile.service";
import { IContentElementTemplate, TemplateConfigDefRow, TemplateMigrationOption } from "src/app/ui-testrunner/element-render-template/model";
import { ElementType, IContentElement } from "src/app/ui-testrunner/models";
import { generateDefaultElementText } from "../item-set-editor/models";

export const MCQ_OPTION_MAP = [
  'A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z',
  'AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ',
  'BA','BB','BC','BD','BE','BF','BG','BH','BI','BJ','BK','BL','BM','BN','BO','BP','BQ','BR','BS','BT','BU','BV','BW','BX','BY','BZ',
  'CA','CB','CC','CD','CE','CF','CG','CH','CI','CJ','CK','CL','CM','CN','CO','CP','CQ','CR','CS','CT','CU','CV','CW','CX','CY','CZ',
  'DA','DB','DC','DD','DE','DF','DG','DH','DI','DJ','DK','DL','DM','DN','DO','DP','DQ','DR','DS','DT','DU','DV','DW','DX','DY','DZ',
  'EA','EB','EC','ED','EE','EF','EG','EH','EI','EJ','EK','EL','EM','EN','EO','EP','EQ','ER','ES','ET','EU','EV','EW','EX','EY','EZ',
  'FA','FB','FC','FD','FE','FF','FG','FH','FI','FJ','FK','FL','FM','FN','FO','FP','FQ','FR','FS','FT','FU','FV','FW','FX','FY','FZ',
];

/**
 * Helper function to get the value of a property based on elementRef
 * @param elementRef The element reference path for the required property.
 * @param newValue The new value for the property
 */
export const getValueByPath = (elementRef: string, content: IContentElement[]) => {
  // Split the path into an array of keys
  const keys = elementRef.split('.');
  // Reduce the keys to access the nested property
  const result = keys.reduce((currentObject, key) => {
      // Check if the key is a number and convert it to access array elements
      const index = isNaN(+key) ? key : parseInt(key, 10);
      // Return the next level in the path or undefined if not found
      return currentObject && currentObject[index] !== undefined ? currentObject[index] : undefined;
  }, content);

  return result;
}

/**
 * Helper function to set the value of objects based on elementRef
 * @param elementRef The element reference path for the required property.
 * @param newValue The new value for the property
 */
export const setValueByPath = (elementRef: string, newValue: any, content: IContentElement[]) => {
  const keys = elementRef.split('.');
  const lastKey = keys.pop();
  const lastObject = keys.reduce((currentObject, key) => {
      const index = isNaN(+key) ? key : parseInt(key, 10);
      if (!currentObject[index]) {
          throw new Error('Invalid path or object does not exist at specified path');
      }
      return currentObject[index];
  }, content);
  lastObject[lastKey] = newValue;
}

/**
 * Helper function for getting all nested templates within a content array.
 * @param elements content array to search
 * @returns an array of all nested templates.
 */
export const getNestedTemplates = (elements: IContentElement[]) => {
  const results: IContentElementTemplate[] = [];
  deepSearchTemplates(elements, results);
  return results;
}

/**
 * Performs a deep search for all template elements in a content array and stores it in the results array.
 * @param elements array of content elements
 * @param results array to store templates in
 */
export const deepSearchTemplates = (elements: any[], results: any[]) => {
  elements.forEach(element => {
    // Check if the current element has elementType 'results_print'
    if (element.elementType === ElementType.TEMPLATE && element.templateId) {
      results.push(element);
    }

    // If the element has nested content, recursively search it
    if (element.content && Array.isArray(element.content)) {
      deepSearchTemplates(element.content, results);
    }
  });
}

/**
* Helper function for syncing values from the previous content to the new version's content.
* @param templateDef the newly loaded template definition.
* @param templateConfig the parsed template configuration.
* @param element the element to upgrade
*/
export const upgradeTemplateContent = (templateDef:ITemplateDef, templateConfig: TemplateVersionConfig, element: IContentElementTemplate) => {
  const cachedOldElement: IContentElementTemplate = JSON.parse(JSON.stringify(element));
  // Set template defs
  element.templateName = templateDef.template_name;
  element.templateId = templateConfig.tqt_id;
  element.templateVersion = templateConfig.current_version_id;
  element.templateDescription = templateDef.description;

  try {
    // Get new template content
    const newContent: IContentElement[] = JSON.parse(templateConfig.content_config);

    // Get new template configs
    const newTemplateConfigs: TemplateConfigDefRow[] = JSON.parse(templateConfig.template_config);

    const migrationOptions: TemplateMigrationOption[] = JSON.parse(templateConfig.migrationOptions);

    // Caveat: If the ID stays the same, but the typing of the element reference has changed, it will cause issues.

    const oldContent: IContentElement[] = JSON.parse(JSON.stringify(element.content));

    // Load new content
    element.content = newContent;
    element.migrationOptions = migrationOptions;

    // If migration options exist, use clean migration.
    if(migrationOptions.length > 0) {
      cleanMigrateContent(templateConfig, element, oldContent);
    } else {
      console.log('dirty migrating');
      dirtyMigrateContent(templateConfig, element, oldContent);
    }

    // Set current configs to new config
    element.templateConfigs = newTemplateConfigs;

  } catch (err) {
    element.templateName = cachedOldElement.templateName;
    element.templateId = cachedOldElement.templateId;
    element.templateVersion = cachedOldElement.templateVersion;
    element.templateDescription = cachedOldElement.templateDescription;
    element.content = cachedOldElement.content;
    element.templateConfigs = cachedOldElement.templateConfigs;
    console.error(err);
    alert('An error occurred migrating the template version.')
  }
}

/**
 * Returns a correctly transformed value for specific migration cases.
 * 
 * @param oldValue the old value to transform.
 * @param newParentConfig the parent configuration of the new version.
 * @param oldParentConfig the parent configuration of the old version.
 */
export const getMigratedValue = (oldValue: any, newParentConfig: TemplateConfigDefRow, oldParentConfig: TemplateConfigDefRow) => {
  try {
    if(newParentConfig.configType == 'advanced-inline' && oldParentConfig.configType == 'text') { // Text to Advanced Inline
      const textElement = generateDefaultElementText(ElementType.TEXT);
      textElement.caption = oldValue;
  
      return [textElement];
    } else if(newParentConfig.configType == 'text' && oldParentConfig.configType == 'advanced-inline') { // Advanced Inline to Text
      const caption = oldValue[0]?.caption;
  
      return caption;
    }
  
    return oldValue;
  } catch (err) {
    console.error(err);
    return oldValue;
  }
}

/**
 * The new process for migrating old content during template upgrading. Utilizes migrationOption to bring over specific values.
 * @param templateConfig the template version object
 * @param element the element of the upgraded template
 * @param oldContent a deep copy of the element content before upgrading
 */
const cleanMigrateContent = (templateConfig: TemplateVersionConfig, element: IContentElementTemplate, oldContent: IContentElement[]) => {
  // Get new template content
  const newContent: IContentElement[] = JSON.parse(templateConfig.content_config);

  // Get new and old template configs
  const newTemplateConfigs: TemplateConfigDefRow[] = JSON.parse(templateConfig.template_config);
  const oldTemplateConfigs: TemplateConfigDefRow[] = element.templateConfigs ? JSON.parse(JSON.stringify(element.templateConfigs)) : []

  const migrationOptions: TemplateMigrationOption[] = JSON.parse(templateConfig.migrationOptions);

  // Loop through the included migration options, carry over all values
  const migrationOptionsIncluded = migrationOptions.filter((opt) => !opt.isExcluded);
  const migrationOptionsExcluded = migrationOptions.filter((opt) => opt.isExcluded);
  migrationOptionsIncluded.forEach((option) => {
    try {
      // Check if the parent ID exists in the old config
      const newParentConfig = getTemplateConfigById(newTemplateConfigs, option.parent_id);
      const oldParentConfig = getTemplateConfigById(oldTemplateConfigs, option.parent_id);

      // If old parent config doesn't exist or new parent config doesn't exist, return.
      if(oldParentConfig == undefined || newParentConfig == undefined) {
        return;
      }

      // new elementRefs property should override old elementRef
      if(option.elementRefs && option.elementRefs.length) {
        option.elementRefs.forEach((elementRef) => {
          migrateProperty(elementRef, element, oldParentConfig, newParentConfig, oldContent)
        })
      } else {
        migrateProperty(option.elementRef, element, oldParentConfig, newParentConfig, oldContent)
      }
    } catch (err) {
      console.error(err);
    }
  })

  // Loop through the excluded migration options, put new template version content back into properties.
  migrationOptionsExcluded.forEach((option) => {
    try {
      // Check if the parent ID exists in the old config
      const newParentConfig = getTemplateConfigById(newTemplateConfigs, option.parent_id);
      const oldParentConfig = getTemplateConfigById(oldTemplateConfigs, option.parent_id);

      // If old parent config doesn't exist or new parent config doesn't exist, return.
      if(oldParentConfig == undefined || newParentConfig == undefined) {
        return;
      }

      // new elementRefs property should override old elementRef
      if(option.elementRefs && option.elementRefs.length) {
        option.elementRefs.forEach((elementRef) => {
          migrateProperty(elementRef, element, newParentConfig, newParentConfig, newContent)
        })
      } else {
        migrateProperty(option.elementRef, element, newParentConfig, newParentConfig, newContent)
      }
    } catch (err) {
      console.error(err);
    }
  })
}

/**
 * Helper function that migrates the old content to the new version.
 * @param childRef the child element reference of the migration option.
 * @param element the template element to update.
 * @param newParentConfig the parent configuration of the new version.
 * @param oldParentConfig the parent configuration of the old version.
 * @param oldContent a deep copy of the element content before upgrading
 * @param newContent content the new template version.
 */
const migrateProperty = (childRef: string, element: IContentElementTemplate, oldParentConfig: TemplateConfigDefRow, newParentConfig: TemplateConfigDefRow, oldContent:IContentElement[]) => {
  // Migrate value from the old content
  let oldValue = getValueByJoinedPath(oldParentConfig.elementRef, childRef, oldContent);
  
  const parsedValue = getMigratedValue(oldValue, newParentConfig, oldParentConfig);

  // Bring old value over to the new element reference
  setValueByJoinedPath(parsedValue, newParentConfig.elementRef, childRef, element.content);
}

/**
 * The old process for migrating old content during template upgrading. Utilizes configuration element references to bring over values.
 * @param templateConfig the template version object
 * @param element the element of the upgraded template
 * @param oldContent a deep copy of the element content before upgrading
 */
const dirtyMigrateContent = (templateConfig: TemplateVersionConfig, element: IContentElementTemplate, oldContent: IContentElement[]) => {
  // Get new template configs
  const newTemplateConfigs: TemplateConfigDefRow[] = JSON.parse(templateConfig.template_config);
  const oldTemplateConfigs: TemplateConfigDefRow[] = element.templateConfigs ? JSON.parse(JSON.stringify(element.templateConfigs)) : []

  const excludeConfigTypes = ['translation']
  const oldTemplateSyncedMapping = new Map<string, boolean>();
  oldTemplateConfigs.forEach((oldConf) => {
    oldTemplateSyncedMapping.set(oldConf.id, false);
  })
  // Loop through new configs, if an old config is found, sync their data values.
  newTemplateConfigs.forEach((newConfig) => {
    if(!newConfig.id) {
      return;
    }
    
    // Find old config ID
    const oldConfig = oldTemplateConfigs.find((config) => config.id == newConfig.id);
    if(!oldConfig || excludeConfigTypes.includes(oldConfig.configType)) { // Ignoring exclusions
      return;
    }

    // Get value from old content
    const oldValue = getValueByPath(oldConfig.elementRef, oldContent);

    // Restore old value to new content
    setValueByPath(newConfig.elementRef, oldValue, element.content);

    oldTemplateSyncedMapping.set(oldConfig.id, true);
  })

  // Loop through all unsynced old configs, and attempt a dirty sync using element ref
  oldTemplateSyncedMapping.forEach((synced, value) => {
    if(synced) {
      return;
    }

    // Get the new content value using the old element reference.
    const oldConfig = oldTemplateConfigs.find((config) => config.id == value);
    if(excludeConfigTypes.includes(oldConfig.configType)) { // Ignoring exclusions
      return;
    }

    const newValue = getValueByPath(oldConfig.elementRef, element.content);
    const oldValue = getValueByPath(oldConfig.elementRef, oldContent);

    // If typing is a match, restore old value to new content.
    if(typeof newValue == typeof oldValue) {
      setValueByPath(oldConfig.elementRef, oldValue, element.content);
    }
  })
}

/**
 * Returns the value that the path routes to.
 * 
 * This method is similar to {@link getValueByPath()} with the exception that it combines two element references together.
 * 
 * @param elementRefA the parent element reference; left-hand side.
 * @param elementRefB the child element reference; right-hand side.
 * @param content the content that the path should search.
 */
export const getValueByJoinedPath = (elementRefA: string, elementRefB: string, content: IContentElement[]) => { 
  let ref = '';

  if(elementRefB && elementRefB.length) {
    ref = joinElementRefs(elementRefA, elementRefB);
  } else {
    ref = elementRefA
  }
  return getValueByPath(ref, content);
}

/**
 * This method is similar to {@link setValueByPath()} with the exception that it combines two element references together.
 * @param newValue the new value .
 * @param elementRefA the parent element reference; left-hand side.
 * @param elementRefB the child element reference; right-hand side.
 * @param content the content that the path should search.
 */
export const setValueByJoinedPath = (newValue: any, elementRefA: string, elementRefB: string, content: IContentElement[]) => { 
  let ref = '';

  if(elementRefB && elementRefB.length) {
    ref = joinElementRefs(elementRefA, elementRefB);
  } else {
    ref = elementRefA
  }
  return setValueByPath(ref, newValue, content);
}

/**
 * Returns the joint element reference.
 * @param elementRefA the parent element reference; left-hand side.
 * @param elementRefB the child element reference; right-hand side.
 */
export const joinElementRefs = (elementRefA: string, elementRefB: string) => {
  const temp = [elementRefA, elementRefB];
  return temp.join('.');
}

/**
 * Returns the value of the first element in the array where the key is matched, or undefined otherwise.
 * @param templateConfigs the array of configurations to search.
 * @param key the key to find.
 */
export const getTemplateConfigById = (templateConfigs: TemplateConfigDefRow[], key:string): TemplateConfigDefRow | undefined => {
  if(!templateConfigs || !templateConfigs.length) {
    return undefined;
  }
  
  for (let config of templateConfigs){
    if (config.id == key){
      return config;
    }
  }

  return undefined;
}