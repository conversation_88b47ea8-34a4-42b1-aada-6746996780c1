import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { IContentElementFrame } from 'src/app/ui-testrunner/element-render-frame/model';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { IContentElementMath } from 'src/app/ui-testrunner/element-render-math/model';
import { IContentElementSolution } from 'src/app/ui-testrunner/element-render-solution/model';
import { IContentElementTable } from 'src/app/ui-testrunner/element-render-table/model';
import { TemplateConfigDefRow } from 'src/app/ui-testrunner/element-render-template/model';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { ElementType, IContentElement } from 'src/app/ui-testrunner/models';
const FULL_WIDTH = 39;

@Component({
  selector: 'template-solution',
  templateUrl: './template-solution.component.html',
  styleUrls: ['./template-solution.component.scss']
})
export class TemplateSolutionComponent implements OnInit {
  @Input() config: TemplateConfigDefRow;
  @Input() elementRef: string;
  @Input() element: IContentElementSolution
  @Input() twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  @Output() onTwiddleToggle = new EventEmitter<any>();
  
  elementTypeForInsertion: string;
  
  elementOptions = [
    'text',
    'image',
    'table',
    'math',
    'text:advanced_inline',
    'text:bullet',
    'text:numbered',
  ]
  elementLabels = {
    'text': 'Text',
    'image': 'Image',
    'table': 'Table',
    'math': 'Math',
    'text:advanced_inline': 'Advanced Inline',
    'text:bullet': 'Bullet List',
    'text:numbered': 'Numbered List',
  }
  elementIcons = {
    'text': 'fa-font',
    'image': 'fa-image',
    'table': 'fa-table',
    'math': 'fa-pencil-alt',
    'text:advanced_inline': 'fa-font',
    'text:bullet': 'fa-list-ul',
    'text:numbered': 'fa-list-ol',
  }

  constructor(
    public lang: LangService,
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
  }
  
  addElement(elementType: string){
    switch (elementType) {
      case 'text': {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.alignment = 'left';
        this.element.content.push(textElement)
      } break;
      case 'image': {
        const imageElement = <IContentElementImage>createDefaultElement(ElementType.IMAGE);
        this.element.content.push(imageElement)
      } break;
      case 'table': {
        const tableElement = <IContentElementTable>createDefaultElement(ElementType.TABLE);
        this.element.content.push(tableElement)
      } break;
      case 'math': {
        const mathElement = <IContentElementMath>createDefaultElement(ElementType.MATH);
        this.element.content.push(mathElement)
      } break;
      case 'text:advanced_inline': {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
        textElement.alignment = 'left';
        this.element.content.push(textElement)
      } break;
      case 'text:bullet': {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.BULLET;
        textElement.alignment = 'left';
        this.element.content.push(textElement)
      } break;
      case 'text:numbered': {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.NUMBERED;
        textElement.alignment = 'left';
        this.element.content.push(textElement)
      } break;
      default: {
        alert("Not Implemented");
      }
    }
  }
  getElementType(element: IContentElement): string {
    if (element.elementType == 'text') {
      switch (element.paragraphStyle) {
        case TextParagraphStyle.ADVANCED_INLINE:
          return 'text:advanced_inline';
        case TextParagraphStyle.BULLET:
          return 'text:bullet';
        case TextParagraphStyle.NUMBERED:
          return 'text:numbered';
        case TextParagraphStyle.REGULAR:
        default:
          return element.elementType;
      }
    } else {
      return element.elementType;
    }
  }
  
  dropContent(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.element.content, event.previousIndex, event.currentIndex);
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  onImageUploadEmit() {
    this.onImageUploaded.next();
  }
  onTwiddleToggleEmit(elementRef: string, key: string, id: number, toggle: boolean) {
    this.onTwiddleToggle.next({elementRef, key, id, toggle});
  }  
  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }
  
  getTwiddleToggleInfo(elementRef: string, key: string, id: number, initialValue: boolean = false) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, initialValue);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, initialValue);
      this.twiddleToggleMap.get(elementRef).set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).get(key).has(id)) {
      this.twiddleToggleMap.get(elementRef).get(key).set(id, initialValue);
    }

    return this.twiddleToggleMap.get(elementRef).get(key).get(id);
  }
}
