import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { IContentElementFrame } from 'src/app/ui-testrunner/element-render-frame/model';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { IContentElementMath } from 'src/app/ui-testrunner/element-render-math/model';
import { IContentElementTable } from 'src/app/ui-testrunner/element-render-table/model';
import { TemplateConfigDefRow } from 'src/app/ui-testrunner/element-render-template/model';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { ElementType } from 'src/app/ui-testrunner/models';
const FULL_WIDTH = 39;

enum STIM_ELEMENT_TYPE {
  TEXT = 'text',
  IMAGE = 'image',
  TABLE = 'table',
  MATH = 'math',
  VIRTUAL_TOOLS = 'virtual_tools',
  TEXT_ADVANCED_INLINE = 'text:advanced_inline',
  TEXT_BULLET = 'text:bullet',
  TEXT_NUMBERED = 'text:numbered',
  HORIZONTAL_CONTAINER = 'horizontal_container',
  VERTICAL_CONTAINER = 'vertical_container',
}

@Component({
  selector: 'template-stim',
  templateUrl: './template-stim.component.html',
  styleUrls: ['./template-stim.component.scss']
})
export class TemplateStimComponent implements OnInit {
  @Input() config: TemplateConfigDefRow;
  @Input() elementRef: string;
  @Input() element: IContentElementFrame & {_isSimplifiedConfigView?: boolean};;
  @Input() isOuterFrame: boolean = false;
  @Input() isChildOfOuterFrame: boolean = false;
  @Input() isContainer: boolean = false;
  @Input() isShowAdvancedOptions: boolean = false; // only for inner frames
  @Input() isSimplifiedConfigView: boolean = false; // only for inner frames
  @Input() twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  @Input() parentElementType: string;
  
  @Input() isVerticalContainerAlwaysFullWidth: boolean = false;
  @Input() isAllowBorderOnlyOnOuterVerticalContainer: boolean = false;
  
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  @Output() onTwiddleToggle = new EventEmitter<any>();
  
  elementTypeForInsertion: string;
  
  stimElementOptions = [
    STIM_ELEMENT_TYPE.TEXT,
    STIM_ELEMENT_TYPE.IMAGE,
    STIM_ELEMENT_TYPE.TABLE,
    STIM_ELEMENT_TYPE.MATH,
    STIM_ELEMENT_TYPE.VIRTUAL_TOOLS,
    STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE,
    STIM_ELEMENT_TYPE.TEXT_BULLET,
    STIM_ELEMENT_TYPE.TEXT_NUMBERED,
    STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER,
    STIM_ELEMENT_TYPE.VERTICAL_CONTAINER,
  ]
  stimElementLabels = {
    [STIM_ELEMENT_TYPE.TEXT]: 'Text',
    [STIM_ELEMENT_TYPE.IMAGE]: 'Image',
    [STIM_ELEMENT_TYPE.TABLE]: 'Table',
    [STIM_ELEMENT_TYPE.MATH]: 'Math',
    [STIM_ELEMENT_TYPE.VIRTUAL_TOOLS]: 'Virtual Tools',
    [STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE]: 'Advanced Inline',
    [STIM_ELEMENT_TYPE.TEXT_BULLET]: 'Bullet List',
    [STIM_ELEMENT_TYPE.TEXT_NUMBERED]: 'Numbered List',
    [STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER]: 'Horizontal Container',
    [STIM_ELEMENT_TYPE.VERTICAL_CONTAINER]: 'Vertical Container',
  }
  stimElementIcons = {
    [STIM_ELEMENT_TYPE.TEXT]: 'fa-font',
    [STIM_ELEMENT_TYPE.IMAGE]: 'fa-image',
    [STIM_ELEMENT_TYPE.TABLE]: 'fa-table',
    [STIM_ELEMENT_TYPE.MATH]: 'fa-pencil-alt',
    [STIM_ELEMENT_TYPE.VIRTUAL_TOOLS]: 'fa-ruler',
    [STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE]: 'fa-font',
    [STIM_ELEMENT_TYPE.TEXT_BULLET]: 'fa-list-ul',
    [STIM_ELEMENT_TYPE.TEXT_NUMBERED]: 'fa-list-ol',
    [STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER]: 'fa-bars fa-rotate-90',
    [STIM_ELEMENT_TYPE.VERTICAL_CONTAINER]: 'fa-bars',
  }
  collapsableInSimplifiedViewTypes = [
      STIM_ELEMENT_TYPE.TABLE,
      STIM_ELEMENT_TYPE.VIRTUAL_TOOLS,
      STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE,
      STIM_ELEMENT_TYPE.TEXT_BULLET,
      STIM_ELEMENT_TYPE.TEXT_NUMBERED,
  ];
  public horizontalAlignments = [
    {id:'left', icon:'fa-align-left'},
    {id:'center', icon:'fa-align-center'},
    {id:'right', icon:'fa-align-right'},
  ]
  public verticalAlignments = [
    {id:'top', icon:'fa-align-left fa-rotate-90'},
    {id:'center', icon:'fa-align-center fa-rotate-90'},
    {id:'bottom', icon:'fa-align-right fa-rotate-90'},
  ]

  constructor(
    public lang: LangService,
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
    this.ensureStyleRaw(this.element);
    this.ensureOuterFrameStyle();
    this.ensureStyleUpdate();
  }
  
  dropContent(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.element.content, event.previousIndex, event.currentIndex);
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  getIsShowAdvancedOptions(): boolean {
    if (this.isOuterFrame) return this.element.isShowAdvancedOptions && !this.getIsSimplifiedConfigView();
    return this.isShowAdvancedOptions && !this.getIsSimplifiedConfigView();
  }
  getIsSimplifiedConfigView(): boolean {
    if (this.isOuterFrame) return this.element._isSimplifiedConfigView;
    return this.isSimplifiedConfigView;
  }
  onImageUploadEmit() {
    this.onImageUploaded.next();
  }
  onTwiddleToggleEmit(elementRef: string, key: string, id: number, toggle: boolean) {
    this.onTwiddleToggle.next({elementRef, key, id, toggle});
  }  
  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }
  
  ensureOuterFrameStyle() {
    if (this.isOuterFrame) {
      this.element.width = FULL_WIDTH;
      this.updateStyle(this.element, {
        'margin-bottom': '1em',
      });
    }
  }
  ensureStyleUpdate() {
    const type = this.getStimType(this.element);
    if (type == STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER){
      // allow content vertical alignment configuration
      this.element.isContentFullHeight = true;
      this.updateStyle(this.element, {'align-items': ''})
      this.element.content.forEach(el => {
        this.updateStyle(el as IContentElementFrame, {'height': '100%'})
      })
    }
  }
  
  toggleStyle(key: string, activeValue: string, defaultValue: string = '') {
    this.element.styleRaw[key] = this.element.styleRaw[key] != activeValue ? activeValue : defaultValue;
  }
  
  isContainerType(frameElement: IContentElementFrame): boolean {
    const type = this.getStimType(frameElement);
    return type == STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER || type == STIM_ELEMENT_TYPE.VERTICAL_CONTAINER;
  }
  isCollapsableInSimplifiedViewType(type: string): boolean {
    return this.collapsableInSimplifiedViewTypes.includes(type as STIM_ELEMENT_TYPE);
  }
  getStimType(frameElement: IContentElementFrame): string {
    if (frameElement == this.element && this.isOuterFrame) return STIM_ELEMENT_TYPE.VERTICAL_CONTAINER;
    if (frameElement.styleRaw?.['display'] == 'flex') {
      const flexDirection = frameElement.styleRaw?.['flex-direction'];
      if (flexDirection == 'row') {
        return STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER;
      } else if (flexDirection == 'column') {
        return STIM_ELEMENT_TYPE.VERTICAL_CONTAINER;
      }
    } 
    const innerContentType = this.getInnerContentType(frameElement);
    if (innerContentType == 'text') {
      switch (frameElement.content[0]?.paragraphStyle) {
        case TextParagraphStyle.ADVANCED_INLINE:
          return STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE;
        case TextParagraphStyle.BULLET:
          return STIM_ELEMENT_TYPE.TEXT_BULLET;
        case TextParagraphStyle.NUMBERED:
          return STIM_ELEMENT_TYPE.TEXT_NUMBERED;
        case TextParagraphStyle.REGULAR:
        default:
          return innerContentType;
      }
    } else {
      return innerContentType;
    }
  }
  getInnerContentType(frameElement: IContentElementFrame): string {
    return frameElement.content?.[0]?.elementType;
  }
  getInnerContentElement(frameElement: IContentElementFrame): any {
    return frameElement.content?.[0];
  }
  
  ensureStyleRaw(element: IContentElementFrame) {
    if (!element.styleRaw) element.styleRaw = {};
    if (!element.activatedStyles) element.activatedStyles = [];
  }
  
  updateStyle(frameElement: IContentElementFrame, style: {[key: string]: string}) {
    this.ensureStyleRaw(frameElement);
    frameElement.styleRaw = {...frameElement.styleRaw, ...style};
    frameElement.activatedStyles = Object.keys(frameElement.styleRaw);
  }
  
  addStimElement(elementType: string){
    const newFrame = <IContentElementFrame>createDefaultElement(ElementType.FRAME);
    this.updateStyle(newFrame, {
      'width': 'fit-content',
      'padding': '0.5em',
    });
    if (this.getStimType(this.element) == STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER) {
      this.updateStyle(newFrame, {'height': '100%'})
    }
    this.element.content.push(newFrame);
    switch (elementType) {
      case STIM_ELEMENT_TYPE.TEXT: {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.alignment = 'left';
        newFrame.content = [textElement];
      } break;
      case STIM_ELEMENT_TYPE.IMAGE: {
        const imageElement = <IContentElementImage>createDefaultElement(ElementType.IMAGE);
        newFrame.content = [imageElement];
      } break;
      case STIM_ELEMENT_TYPE.TABLE: {
        const tableElement = <IContentElementTable>createDefaultElement(ElementType.TABLE);
        newFrame.content = [tableElement];
      } break;
      case STIM_ELEMENT_TYPE.MATH: {
        const mathElement = <IContentElementMath>createDefaultElement(ElementType.MATH);
        newFrame.content = [mathElement];
      } break;
      case STIM_ELEMENT_TYPE.TEXT_ADVANCED_INLINE: {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
        textElement.alignment = 'left';
        newFrame.content = [textElement];
      } break;
      case STIM_ELEMENT_TYPE.TEXT_BULLET: {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.BULLET;
        textElement.alignment = 'left';
        textElement.isListNoMargin = true;
        newFrame.content = [textElement];
      } break;
      case STIM_ELEMENT_TYPE.TEXT_NUMBERED: {
        const textElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
        textElement.paragraphStyle = TextParagraphStyle.NUMBERED;
        textElement.alignment = 'left';
        textElement.isListNoMargin = true;
        newFrame.content = [textElement];
      } break;
      case STIM_ELEMENT_TYPE.HORIZONTAL_CONTAINER: {
        newFrame.isContentFullHeight = true;
        this.updateStyle(newFrame, {
          'display': 'flex',
          'flex-direction': 'row',
          'gap': '1em',
        });
      } break;
      case STIM_ELEMENT_TYPE.VERTICAL_CONTAINER: {
        this.updateStyle(newFrame, {
          'display': 'flex',
          'flex-direction': 'column',
          'gap': '0.5em',
        });
        if (this.isVerticalContainerAlwaysFullWidth) {
          this.updateStyle(newFrame, {'width': '100%'});
        }
      } break;
      default: {
        alert("Not Implemented");
      }
    }
  }
  
  isAllowShowBorder(frameElement: IContentElementFrame): boolean {
    if (this.isAllowBorderOnlyOnOuterVerticalContainer) {
      return this.isChildOfOuterFrame && this.getStimType(frameElement) == STIM_ELEMENT_TYPE.VERTICAL_CONTAINER;
    } else {
      return true;
    }
  }
  
  getTwiddleToggleInfo(elementRef: string, key: string, id: number, initialValue: boolean = false) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, initialValue);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, initialValue);
      this.twiddleToggleMap.get(elementRef).set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).get(key).has(id)) {
      this.twiddleToggleMap.get(elementRef).get(key).set(id, initialValue);
    }

    return this.twiddleToggleMap.get(elementRef).get(key).get(id);
  }
  
  toggleHorizontalAlignment(alignment: 'left' | 'center' | 'right') {
    switch (alignment) {
      case 'left':
        this.toggleStyle('margin', '0')
        break;
      case 'center': 
        this.toggleStyle('margin', '0 auto')
        break;
      case 'right':
        this.toggleStyle('margin', '0 0 0 auto')
        break;
    }
  }
  toggleVerticalAlignment(alignment: 'top' | 'center' | 'bottom') {
    switch (alignment) {
      case 'top':
        this.toggleStyle('align-content', 'start')
        break;
      case 'center': 
        this.toggleStyle('align-content', 'center')
        break;
      case 'bottom':
        this.toggleStyle('align-content', 'end')
        break;
    }
  }
}
