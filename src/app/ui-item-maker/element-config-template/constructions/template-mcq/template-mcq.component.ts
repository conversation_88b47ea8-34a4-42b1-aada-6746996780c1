import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { AuthScopeSettingsService } from 'src/app/ui-item-maker/auth-scope-settings.service';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { ItemBankCtrl } from 'src/app/ui-item-maker/item-set-editor/controllers/item-bank';
import { createDefaultElement, generateDefaultElementTextLink } from 'src/app/ui-item-maker/item-set-editor/models';
import { IContentElementMcqOption } from 'src/app/ui-testrunner/element-render-mcq/model';
import { TemplateConfigDefRow } from 'src/app/ui-testrunner/element-render-template/model';
import { TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { ElementType, IContentElement, IElementTypeDef } from 'src/app/ui-testrunner/models';

@Component({
  selector: 'template-mcq',
  templateUrl: './template-mcq.component.html',
  styleUrls: ['./template-mcq.component.scss']
})
export class TemplateMcqComponent implements OnInit {
  
  @Input() mcqElementOptions: IContentElementMcqOption[];
  @Input() elementRef: string;
  @Input() elementType: 'text' | 'math' | 'image' | 'advanced-inline' = 'text';
  @Input() secondaryElement: any;
  @Input() twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  @Input() hasValidator: boolean = false;
  @Input() mcqEntryId: number = undefined;
  @Input() isOnlyOneCorrectOption: boolean = false;
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onValidatorAlign = new EventEmitter<any>();
  @Output() onAssignEntryId = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  @Output() onTwiddleToggle = new EventEmitter<any>();

  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private styleProfileService: StyleprofileService,
    private lang: LangService,
    private itemBankCtrl: ItemBankCtrl,
  ) { }

  ngOnInit(): void {
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  toggleValue(obj: any, key: string) {
    obj[key] = !obj[key];
  }
  
  onImageUploadEmit() {
    this.onImageUploaded.next();
  }
  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }
  onValidatorAlignEmit() {
    this.onValidatorAlign.next();
  }
  onAssignEntryIdEmit() {
    this.onAssignEntryId.next();
  }
  onTwiddleToggleEmit(elementRef: string, key: string, id: number, toggle: boolean) {
    this.onTwiddleToggle.next({elementRef, key, id, toggle});
  }  
  
  insertMCQEntry(
    options:any[],
    elementType: ElementType | string,
    paragraphStyle: TextParagraphStyle | string = TextParagraphStyle.REGULAR) {
    let content:any = '';
    const optionElement:IContentElementMcqOption = {
      ... createDefaultElement(elementType),
      elementType,
      optionType: ElementType.MCQ_OPTION,
      content,
      isCorrect: false,
      optionId: -1,
      link: generateDefaultElementTextLink()
    };

    if (paragraphStyle === TextParagraphStyle.ADVANCED_INLINE) {
      optionElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    } else if (paragraphStyle === TextParagraphStyle.BULLET) {
      optionElement.paragraphStyle = TextParagraphStyle.BULLET;
    } else {
      delete optionElement.paragraphStyle;
    }

    options.push(optionElement)
  }
  
  getTwiddleToggleInfo(elementRef: string, key: string, id: number) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef)?.has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      this.twiddleToggleMap.get(elementRef)?.set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef)?.get(key)?.has(id)) {
      this.twiddleToggleMap.get(elementRef)?.get(key)?.set(id, false);
    }

    return this.twiddleToggleMap.get(elementRef)?.get(key)?.get(id);
  }
  
  getMCQOptionLabel(index: number) {
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if(index >= alphabet.length) {
      return 'Index out of bounds.';
    }

    return alphabet[index];
  }
  
  drop(arr:any, event: CdkDragDrop<string[]>) {
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
  }
  
  toggleCorrectOption(option: IContentElementMcqOption) {
    if (!this.isOnlyOneCorrectOption) {
      option.isCorrect = !option.isCorrect;
    } else {
      if (option.isCorrect) {
        option.isCorrect = !option.isCorrect;
      } else {
        this.mcqElementOptions.forEach(option => option.isCorrect = false);
        option.isCorrect = true;
      }
    }
  }
  

}
