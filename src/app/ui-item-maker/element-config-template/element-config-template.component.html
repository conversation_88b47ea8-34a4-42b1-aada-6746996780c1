<div *ngIf="!this.element.templateId">
    <button (click)="openTemplateOptions()" class="button is-fullwidth">Select Template</button>
</div>
<div *ngIf="this.element.templateId">
    <div class="template-header ">
        <div class="space-between">
            <span>
                <strong>{{this.element.templateName}}</strong><br/>
                <small>
                    Template ID: {{this.element.templateId}} 
                    (Version: {{this.element.templateVersion}})
                </small>
            </span>
            <div>
                <a [class.no-pointer-events]="isReadOnly()" [style.color]="isReadOnly()? 'grey' : ''" (click)="openTemplateOptions()">Change</a>
                <!-- Temporarily disabling template upgrades -->
                <span *ngIf="hasNewVersion() && authRestrictions.isCurrentUserTemplateManager">
                    |
                    <a [class.no-pointer-events]="isReadOnly()" [style.color]="getUpgradeColor()" (click)="upgradeVersion()">Upgrade</a>
                </span>
            </div>        </div>
        <div>
            <small>{{element.templateDescription && element.templateDescription.length ? element.templateDescription : ''}}</small>
        </div>
    </div>
    <div *ngIf="!hasTemplateConfigs()">
        <!-- <div cdkDropList [cdkDropListDisabled]="isReadOnly()" (cdkDropListDropped)="drop(element.content, $event);"> -->
            <div *ngFor="let contentElement of element.content" class="nested-element"> <!-- cdkDrag  -->
              <div [class.no-pointer-events]="isReadOnly()" class="nested-element-header" > <!-- cdkDragHandle -->
                <button (click)="contentElement._isCollapsed = !contentElement._isCollapsed">
                    <i class="fa" [ngClass]="getIconByElementTypeId(contentElement.elementType)" aria-hidden="true"></i>
                </button>
              </div>
              <div class="nested-element-content" *ngIf="!contentElement._isCollapsed">
                <element-config [contentElement]="contentElement" [contents]="element.content"></element-config>
              </div>
            </div>
        <!-- </div> -->
    </div>
    <div *ngIf="hasTemplateConfigs()" class="configuration-list" [class.separator]="!authRestrictions.isCurrentUserRestricted">
        <div *ngFor="let config of element.templateConfigs" class="configuration-list">
            <div *ngIf="isConfigTypeMatch('note', config)">
              <tra *ngIf="!isLabelFR(config)" [slug]="config.label"></tra>
              <tra *ngIf="isLabelFR(config)" [slug]="config.label_fr"></tra>
            </div>
            <div *ngIf="!isConfigTypeMatch('translation', config) && !isConfigTypeMatch('note', config)">
                <div class="configuration-header">
                    <tra *ngIf="!isLabelFR(config)" [slug]="config.label"></tra>
                    <tra *ngIf="isLabelFR(config)" [slug]="config.label_fr"></tra>
                </div>
                <div *ngIf="isConfigTypeMatch('math', config)">
                    <div class="capture-math-container">
                        <capture-math [obj]="getValueByPath(config.elementRef)" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                    </div>
                </div>
                <div *ngIf="isConfigTypeMatch('number', config)">
                    <input 
                        type="number" 
                        class="input text-number"
                        style="width:120px; text-align:center" 
                        [ngModel]="getValueByPath(config.elementRef)" 
                        (ngModelChange)="setValueByPath(config.elementRef, $event)"
                        min="0.1"
                        step="1"
                    >
                </div>
                <div *ngIf="isConfigTypeMatch('checkbox', config)">
                    <input 
                        type="checkbox"
                        [ngModel]="getValueByPath(config.elementRef)" 
                        (ngModelChange)="setValueByPath(config.elementRef, $event)"
                    >
                </div>
                <div *ngIf="isConfigTypeMatch('label', config)">
                    Not Implemented
                </div>
                <div *ngIf="isConfigTypeMatch('text', config)">
                    <textarea 
                        textInputTransform
                        [ngModel]="getValueByPath(config.elementRef)" 
                        (ngModelChange)="setValueByPath(config.elementRef, $event)"
                        cdkTextareaAutosize 
                        [cdkTextareaAutosize]="true" 
                        [cdkAutosizeMinRows]="2"
                        class="textarea"
                    ></textarea>
                </div>
                <div *ngIf="isConfigTypeMatch('selection', config)">
                  <template-selection
                    [value]="getValueByPath(config.elementRef)" 
                    [data] = "config.selectionData"
                    (onChange)="setValueByPath(config.elementRef, $event)"
                  ></template-selection>
                </div>
                <template-advanced-inline
                    *ngIf="isConfigTypeMatch('text-list', config) || isConfigTypeMatch('advanced-inline', config)"
                    [advancedList]="getValueByPath(config.elementRef)"
                    [config]="config"
                    [isTextList]="isConfigTypeMatch('text-list', config)"
                    [twiddleToggleMap]="twiddleToggleMap"
                    [hasValidator]="hasValidator()"
                    [isStaticElementOnly]="config.hiddenConfigs?.isShowOnlyStaticElements"
                    (onRemoveElement)="removeElement($event.content, $event.element)"
                    (onValidatorAlign)="alignValidators()"
                    (onAssignEntryId)="ensureResponseEntryIds()"
                    (onImageUploaded)="onImageUploaded()"
                    (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                >
                </template-advanced-inline>
                <div *ngIf="isConfigTypeMatch('mcq', config)" class="text-list gap">
                  <template-mcq
                    *ngIf="config.optionElementType !== 'table'"
                    [elementRef]="config.elementRef"
                    [elementType]="config.optionElementType"
                    [mcqElementOptions]="getValueByPath(config.elementRef)"
                    [isOnlyOneCorrectOption]="config.hiddenConfigs?.isOnlyOneCorrectOption"
                    [secondaryElement]="config.secondaryRef ? getValueByPath(config.secondaryRef) : undefined"
                    [twiddleToggleMap]="twiddleToggleMap"
                    [hasValidator]="hasValidator()"
                    (onRemoveElement)="removeElement($event.content, $event.element)"
                    (onValidatorAlign)="alignValidators()"
                    (onAssignEntryId)="ensureResponseEntryIds()"
                    (onImageUploaded)="onImageUploaded()"
                    (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                  >
                  </template-mcq>
                  <div *ngIf="config.optionElementType === 'table'" class="text-list">  
                    <div *ngFor="let option of getValueByPath(config.elementRef); let op_i = index">
                        <div>
                            <div style="display: flex; margin-top: 0.5em; margin-bottom: 0.5em;">
                                <div class="option-container-buttons" (mousedown)="$event.stopPropagation()" style="margin-right: 0.5em;">
                                    <a 
                                        class="button is-correct-toggle"
                                        [class.is-disabled]="isReadOnly()"
                                        [class.is-success] = "option.isCorrect"
                                        [class.is-danger]  = "!option.isCorrect"
                                        (click)="toggleValue(option,'isCorrect')"
                                    >
                                        <i 
                                        class="fa fa-check" 
                                        [class.fa-check] = "option.isCorrect"
                                        [class.fa-times] = "!option.isCorrect"
                                        style="width: 16px;"
                                        aria-hidden="true"
                                        ></i>
                                    </a>
                                </div>
                                <a class="button" (click)="removeElement(getValueByPath(config.elementRef), option);" (mousedown)="$event.stopPropagation()">
                                    <i class="fas fa-trash"  aria-hidden="true"></i>
                                </a>
                            </div>
                            <template-table
                                [config]="config"
                                [elementRef]="config.elementRef"
                                [tableElement]="option"
                                [hasValidator]="hasValidator()"
                                [twiddleToggleMap]="twiddleToggleMap"
                                [isNonInteractive]="true"
                                [mcqOptionNumber]="op_i"
                                (onRemoveElement)="removeElement($event.content, $event.element)"
                                (onValidatorAlign)="alignValidators()"
                                (onAssignEntryId)="ensureResponseEntryIds()"
                                (onImageUploaded)="onImageUploaded()"
                                (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                            ></template-table>
                        </div>
                        <div class="separator option-divider"></div>
                    </div>
                    <button *ngIf="config.optionElementType === 'table'" class="button is-primary" style="width: 100%" (click)="insertMCQEntry(getValueByPath(config.elementRef), 'table')">Add Option</button>
                  </div>
                </div>
                <div *ngIf="isConfigTypeMatch('mcq-freeform', config)" class="text-list" style="gap: 1em;">
                    <div cdkDropList (cdkDropListDropped)="drop(getValueByPath(config.elementRef), $event)" [class.no-pointer-events]="isReadOnly()">
                      <div *ngFor="let option of getValueByPath(config.elementRef); let optionIndex = index" class="separator draggable" style="display: flex; justify-content: space-between; gap: 1.25em; padding-top: 1em" cdkDrag>
                          <div class="text-list" style="width: 100%;">
                              <div class="space-between" style="gap: 1em;">
                                  <div>
                                      Content
                                  </div>
                                  <div *ngIf="option.elementType == 'math'" class="capture-math-container" (mousedown)="$event.stopPropagation()">
                                      <capture-math [obj]="option" prop="content" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                                  </div>
                                  <input
                                      textInputTransform
                                      *ngIf="option.elementType == 'text'"
                                      type="text" 
                                      class="input" 
                                      style="text-align:center;" 
                                      (mousedown)="$event.stopPropagation()"
                                      [(ngModel)]="option.content"
                                  >
                              </div>
                              <div class="space-between">
                                  <div>
                                    Position
                                  </div>
                                  <ng-container *ngIf="isMcqFreeformBeingMoved(config.elementRef, optionIndex)">
                                    <span>
                                      (Right click to cancel)
                                    </span>
                                  </ng-container>
                                  <ng-container *ngIf="!isMcqFreeformBeingMoved(config.elementRef, optionIndex)">
                                    <a 
                                      class="button" 
                                      style="margin-right: 0px;" 
                                      (click)="requestMoveMcqFreeformOption($event, config.elementRef, optionIndex)"
                                      (mousedown)="$event.stopPropagation()"
                                    >
                                        <i class="fa fa-map-marker-alt"  aria-hidden="true"></i>
                                        <span style="padding-left: 0.5em;">Move</span>
                                    </a>   
                                  </ng-container>
                              </div>
                              <ng-container *ngIf="getValueByPath(getParentPath(config.elementRef)).isShowAdvancedOptions">
                                <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em; margin-left: auto">
                                  <div>X: </div>
                                  <input 
                                      (mousedown)="$event.stopPropagation()"
                                      type="number" 
                                      class="input is-small" 
                                      style="width:5em; text-align:center" 
                                      [(ngModel)]="option.x"
                                      step="0.1"
                                  >
                                  <div>Y: </div>
                                  <input 
                                      (mousedown)="$event.stopPropagation()"
                                      type="number" 
                                      class="input is-small" 
                                      style="width:5em; text-align:center" 
                                      [(ngModel)]="option.y"
                                      step="0.1"
                                  >
                                </div>
                              </ng-container>
                              <div  *ngIf="getValueByPath(getParentPath(config.elementRef)).isShowAdvancedOptions" class="buttons is-no-wrap">
                                <button *ngFor="let align of alignmentsJustify" [disabled]="isReadOnly()" class="button is-small"  (click)="setAlignmentForMcqFreeform(option, align.id)">
                                  <i class="fa {{align.icon}}"></i>
                                </button>
                              </div>
                          </div>
                          <div class="option-container-buttons">
                              <a 
                                class="button is-correct-toggle"
                                [class.is-disabled]="isReadOnly()"
                                [class.is-success] = "option.isCorrect"
                                [class.is-danger]  = "!option.isCorrect"
                                (click)="toggleValue(option,'isCorrect')"
                                (mousedown)="$event.stopPropagation()"
                                style="margin-right: 0px;"
                              >
                                <i 
                                  class="fa fa-check" 
                                  [class.fa-check] = "option.isCorrect"
                                  [class.fa-times] = "!option.isCorrect"
                                  style="width: 16px;"
                                  aria-hidden="true"
                                ></i>
                              </a>
                              <a class="button" style="margin-right: 0px;" (click)="removeElement(getValueByPath(config.elementRef), option);" (mousedown)="$event.stopPropagation()">
                                  <i class="fas fa-trash"  aria-hidden="true"></i>
                              </a>   
                          </div>
                      </div>
                    </div>
                    <select-element
                        [elementTypes]="mcqElementTypeDefs"
                        (insert)="insertMCQEntry(getValueByPath(config.elementRef), $event)"
                    >
                    </select-element>
                    <label class="checkbox">
                      <input type="checkbox" [(ngModel)]="getValueByPath(getParentPath(config.elementRef)).isShowAdvancedOptions"/>
                      Show Advanced Options
                    </label>
                </div>
                <div *ngIf="isConfigTypeMatch('select-table', config)">
                    <div class="text-list">  
                        <div class="text-list">
                            <twiddle style="width: 100%;" caption="Column Headers" (change)="setTwiddleToggleInfo(config.elementRef, 'columns', 0, $event)"></twiddle>
                            <div class="text-list" *ngIf="getTwiddleToggleInfo(config.elementRef, 'columns', 0)">
                                <div style="display: flex; flex-flow: column wrap;">
                                    <td style="margin-left: 0.5em; margin-bottom: 1.5em;">
                                        <div style="margin-bottom: 0.7em;">
                                            Column 0 Header
                                        </div>
                                        <input
                                            textInputTransform
                                            [(ngModel)]="getValueByPath(config.elementRef).topLeftText.caption"
                                            type="text" 
                                            class="input" 
                                        >
                                        <div>
                                            <button 
                                                *ngFor="let align of alignments" 
                                                [disabled]="isReadOnly()" 
                                                class="button is-small"  
                                                (click)="selectTableSetColumnAlignment(getValueByPath(config.elementRef), align.id)" 
                                                style="margin-top: 0.7em; margin-right: 0.5em"
                                            >
                                              <i class="{{align.icon}}"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td *ngFor="let cell of getValueByPath(config.elementRef).topRow; let col_i = index;" style="margin-left: 0.5em; margin-bottom: 1.5em;">
                                        <div style="display: flex; flex-direction: row; justify-content: space-between; margin-bottom: 0.7em;">
                                            <div>
                                                Column {{ col_i + 1 }} Header
                                            </div>
                                            <a class="button" (click)="selectTableDeleteColumn(getValueByPath(config.elementRef), col_i)">
                                                <i class="fas fa-trash"  aria-hidden="true"></i>
                                            </a>
                                        </div>
                                        <div>
                                            <div class="select is-fullwidth" style="margin-bottom: 0.7em;">
                                                <select (change)="onSelectTableChange($event.target.value, cell.content)">
                                                    <option  *ngFor="let elementType of selectTableElements; let index = index" [selected]="elementType.id == getTableElement(cell.content)" [value]="elementType.id">
                                                        {{elementType.label}}
                                                    </option>
                                                </select>
                                            </div>
                                            <input
                                                textInputTransform
                                                *ngIf="cell.content.elementType == 'text' && cell.content.paragraphStyle != 'advanced-inline'"
                                                [(ngModel)]="cell.content.caption"
                                                type="text" 
                                                class="input" 
                                            >
                                            <div *ngIf="cell.content.elementType == 'math'" class="capture-math-container">
                                                <capture-math [obj]="cell.content" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                                            </div>
                                            <template-advanced-inline
                                                *ngIf="cell.content.elementType === 'text' && cell.content.paragraphStyle === 'advanced-inline'"
                                                [advancedList]="cell.content.advancedList"
                                                [config]="config"
                                                [isTextList]="false"
                                                [twiddleToggleMap]="twiddleToggleMap"
                                                [hasValidator]="hasValidator()"
                                                [inlineElements]="staticInlineElements"
                                                (onRemoveElement)="removeElement($event.content, $event.element)"
                                                (onValidatorAlign)="alignValidators()"
                                                (onAssignEntryId)="ensureResponseEntryIds()"
                                                (onImageUploaded)="onImageUploaded()"
                                                (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                                            >
                                            </template-advanced-inline>
                                            <div>
                                                <capture-image *ngIf="cell.content.elementType == 'image'" [element]="cell.content" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploaded()"></capture-image>
                                                <asset-library-link *ngIf="cell.content.elementType == 'image'" [element]="cell.content"></asset-library-link>
                                            </div>
                                            <div>
                                                <button 
                                                    *ngFor="let align of alignments" 
                                                    [disabled]="isReadOnly()" 
                                                    class="button is-small"  
                                                    (click)="selectTableSetColumnAlignment(getValueByPath(config.elementRef), align.id, col_i)" 
                                                    style="margin-top: 0.7em; margin-right: 0.5em"
                                                >
                                                  <i class="{{align.icon}}"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </td>
                                </div>
                                <button class="button is-primary" (click)="selectTableInsertAnswer(getValueByPath(config.elementRef))">
                                    Add column
                                </button>
                            </div>
                        </div>
                        <div class="text-list">
                            <twiddle style="width: 100%;" caption="Row Headers" (change)="setTwiddleToggleInfo(config.elementRef, 'rows', 0, $event)"></twiddle>
                            <div class="text-list gap" *ngIf="getTwiddleToggleInfo(config.elementRef, 'rows', 0)">
                                <div style="display: flex; flex-flow: column wrap;">
                                    <td *ngFor="let cell of getValueByPath(config.elementRef).leftCol; let row_i = index;" style="margin-left: 0.5em; margin-bottom: 1.5em;">
                                        <div style="margin-left: 0.5em; margin-bottom: 0.7em ;display: flex; flex-direction: row; justify-content: space-between;">
                                            <div>
                                                Row {{ row_i + 1 }} Header
                                            </div>
                                            <a class="button" (click)="selectTableDeleteRow(getValueByPath(config.elementRef), row_i)">
                                                <i class="fas fa-trash"  aria-hidden="true"></i>
                                            </a> 
                                        </div>
                                        <div>
                                            <div class="select is-fullwidth" style="margin-bottom: 0.7em;">
                                                <select (change)="onSelectTableChange($event.target.value, cell.content)">
                                                    <option  *ngFor="let elementType of selectTableElements; let index = index" [selected]="elementType.id == getTableElement(cell.content)" [value]="elementType.id">
                                                        {{elementType.label}}
                                                    </option>
                                                </select>
                                            </div>
                                            <div *ngIf="cell.content.elementType == 'math'" class="capture-math-container">
                                                <capture-math [obj]="cell.content" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                                            </div>
                                            <textarea 
                                                textInputTransform
                                                *ngIf="cell.content.elementType == 'text' && cell.content.paragraphStyle != 'advanced-inline'"
                                                [(ngModel)]="cell.content.caption" 
                                                cdkTextareaAutosize 
                                                [cdkTextareaAutosize]="true" 
                                                [cdkAutosizeMinRows]="2"
                                                class="textarea selection-table"
                                            ></textarea>
                                            <template-advanced-inline
                                                *ngIf="cell.content.elementType === 'text' && cell.content.paragraphStyle === 'advanced-inline'"
                                                [advancedList]="cell.content.advancedList"
                                                [config]="config"
                                                [isTextList]="false"
                                                [twiddleToggleMap]="twiddleToggleMap"
                                                [hasValidator]="hasValidator()"
                                                [inlineElements]="staticInlineElements"
                                                (onRemoveElement)="removeElement($event.content, $event.element)"
                                                (onValidatorAlign)="alignValidators()"
                                                (onAssignEntryId)="ensureResponseEntryIds()"
                                                (onImageUploaded)="onImageUploaded()"
                                                (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                                            >
                                            </template-advanced-inline>
                                            <div>
                                                <capture-image *ngIf="cell.content.elementType == 'image'" [element]="cell.content" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploaded()"></capture-image>
                                                <asset-library-link *ngIf="cell.content.elementType == 'image'" [element]="cell.content"></asset-library-link>
                                            </div>
                                        </div>
                                    </td>
                                    <button class="button is-primary" (click)="selectTableInsertQuestion(getValueByPath(config.elementRef))">
                                        Add row
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="text-list">
                            <div class="text-list">
                                <twiddle style="width: 100%;" caption="Answers" (change)="setTwiddleToggleInfo(config.elementRef, 'answers', 0, $event)"></twiddle>
                                <div class="text-list gap" *ngIf="getTwiddleToggleInfo(config.elementRef, 'answers', 0)">
                                    <div>
                                        <table class="select-table">
                                            <tr>
                                                <td class="wrap-content"> 
                                                    {{ getValueByPath(config.elementRef).topLeftText.caption }}
                                                </td>
                                                <td class="wrap-content" *ngFor="let cell of getValueByPath(config.elementRef).topRow">
                                                    <div class="wrap-content" *ngIf="cell.content.elementType == 'text' && cell.content.paragraphStyle != 'advanced-inline'">
                                                        {{ cell.content.caption }}
                                                    </div>
                                                    <div class="wrap-content" *ngIf="cell.content.elementType === 'math'">
                                                        <render-math class="wrap-content" [obj]="cell.content" [prop]="'latex'"></render-math>
                                                    </div>
                                                    <div class="wrap-content" *ngIf="cell.content.elementType == 'image'">
                                                        {{ getImageText(cell.content) }}
                                                    </div>
                                                    <div class="wrap-content">
                                                        <div class="advanced-inline-container inline-block" *ngIf="cell.content.elementType == 'text' && cell.content.paragraphStyle == 'advanced-inline'">
                                                            <div *ngFor="let contentElement of getSelectionTableAdvancedInlineElementsMinusImage(cell.content.advancedList)" class="inline-text-block">
                                                                <div [ngSwitch]="contentElement.elementType" class="inline-text-block">
                                                                    <div *ngSwitchCase="'math'" class="inline-block">
                                                                        <render-math [obj]="contentElement" [prop]="'latex'"></render-math>
                                                                    </div>
                                                                    <div *ngSwitchCase="'text'" class="inline-block"> 
                                                                        {{ contentElement.caption }}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr *ngFor="let row of getValueByPath(config.elementRef).checkBoxRows; let i = index">
                                                <td>
                                                    <div class="wrap-content" *ngIf="getValueByPath(config.elementRef).leftCol[i].content.elementType == 'text' && getValueByPath(config.elementRef).leftCol[i].content.paragraphStyle != 'advanced-inline'">
                                                        {{ getValueByPath(config.elementRef).leftCol[i].content.caption }}
                                                    </div>
                                                    <div class="wrap-content" *ngIf="getValueByPath(config.elementRef).leftCol[i].content.elementType === 'math'">
                                                        <render-math class="wrap-content" [obj]="getValueByPath(config.elementRef).leftCol[i].content" [prop]="'latex'"></render-math>
                                                    </div>
                                                    <div class="wrap-content" *ngIf="getValueByPath(config.elementRef).leftCol[i].content.elementType == 'image'">
                                                        {{ getImageText(getValueByPath(config.elementRef).leftCol[i].content) }}
                                                    </div>
                                                    <div class="wrap-content">
                                                        <div class="advanced-inline-container inline-block" *ngIf="getValueByPath(config.elementRef).leftCol[i].content.elementType == 'text' && getValueByPath(config.elementRef).leftCol[i].content.paragraphStyle == 'advanced-inline'">
                                                            <div *ngFor="let contentElement of getSelectionTableAdvancedInlineElementsMinusImage(getValueByPath(config.elementRef).leftCol[i].content.advancedList)" class="inline-text-block">
                                                                <div [ngSwitch]="contentElement.elementType" class="inline-text-block">
                                                                    <div *ngSwitchCase="'math'" class="inline-block">
                                                                        <render-math [obj]="contentElement" [prop]="'latex'"></render-math>
                                                                    </div>
                                                                    <div *ngSwitchCase="'text'" class="inline-block"> 
                                                                        {{ contentElement.caption }}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td *ngFor="let cell of row">
                                                    <div class="center">
                                                        <input type="checkbox" [(ngModel)]="cell.value">
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-list">
                            <twiddle style="width: 100%;" caption="Column Width" (change)="setTwiddleToggleInfo(config.elementRef, 'column_width', 0, $event)"></twiddle>
                            <div class="text-list" *ngIf="getTwiddleToggleInfo(config.elementRef, 'column_width', 0)">
                                <div style="margin-left: 0.5em; margin-bottom: 0.5em;">
                                    <div class="configuration-header">
                                        Max question column width
                                    </div>
                                    <input 
                                        type="number" 
                                        class="input text-number"
                                        style="width:120px; text-align:center" 
                                        [ngModel]="getValueByPath(config.elementRef).questionColumnWidth" 
                                        (ngModelChange)="setValueByPath(config.elementRef + '.questionColumnWidth', $event)"
                                        min="0.1"
                                        step="1"
                                    >
                                </div>
                                <div style="margin-left: 0.5em; margin-bottom: 0.5em;">
                                    <div class="configuration-header">
                                        Max checkbox column width
                                    </div>
                                    <input 
                                        type="number" 
                                        class="input text-number"
                                        style="width:120px; text-align:center" 
                                        [ngModel]="getValueByPath(config.elementRef).answerColumnWidth" 
                                        (ngModelChange)="setValueByPath(config.elementRef + '.answerColumnWidth', $event)"
                                        min="0.1"
                                        step="1"
                                    >
                                </div>
                                <div style="margin-left: 0.5em; margin-bottom: 0.5em;">
                                    <div class="configuration-header">
                                        Uniform width for checkbox columns
                                    </div>
                                    <input 
                                        type="checkbox"
                                        [ngModel]="getValueByPath(config.elementRef).isUniformOptionWidth" 
                                        (ngModelChange)="setValueByPath(config.elementRef + '.isUniformOptionWidth', $event)"
                                    >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="isConfigTypeMatch('select-paragraph', config)" class="text-list gap">
                    <div *ngFor="let paragraphList of getValueByPath(config.elementRef).paragraphs" class="separator" style="display: flex;flex-direction: column; gap: 1em;">
                        <div *ngFor="let option of paragraphList">
                          <div class="space-between gap" style="align-items: flex-start;">
                              <!-- <div *ngIf="config.optionElementType && config.optionElementType == 'math'" style="text-align:center; font-size:150%; width: 100%;">
                                  <capture-math [obj]="option" prop="content" [isManualKeyboard]="true"></capture-math>
                              </div> -->
                              <!-- *ngIf="!config.optionElementType || config.optionElementType == 'text'"  -->
                              <!-- <input
                                  type="text" 
                                  class="input" 
                                  style="text-align:center;" 
                                  [(ngModel)]="option.content"
                              > -->
                              <textarea 
                                  textInputTransform
                                  style="min-width: unset;"
                                  [(ngModel)]="option.content" 
                                  (input)="updateChangeCounterByRef(config.elementRef)"
                                  cdkTextareaAutosize 
                                  [cdkTextareaAutosize]="true" 
                                  [cdkAutosizeMinRows]="2"
                                  class="textarea"
                              ></textarea>
                              <div>
                                <div class="option-container-buttons" style="margin-bottom: 0.5em;">
                                    <a 
                                      class="button is-correct-toggle"
                                      [class.is-disabled]="isReadOnly()"
                                      [class.is-success] = "option.isCorrect"
                                      [class.is-danger]  = "!option.isCorrect"
                                      (click)="toggleValue(option,'isCorrect')"
                                    >
                                      <i 
                                        class="fa fa-check" 
                                        [class.fa-check] = "option.isCorrect"
                                        [class.fa-times] = "!option.isCorrect"
                                        style="width: 16px;"
                                        aria-hidden="true"
                                      ></i>
                                    </a>
                                </div>
                                <a class="button" (click)="removeElement(getValueByPath(config.elementRef).paragraphs, paragraphList); updateChangeCounterByRef(config.elementRef);">
                                    <i class="fas fa-trash"  aria-hidden="true"></i>
                                </a>   
                              </div>
                          </div>
                        </div>
                    </div>
                    <button style="width: 100%" (click)="insertSelectParagraphOption(config.elementRef)" class="button is-primary">Add</button>
                </div>
                <div *ngIf="isConfigTypeMatch('select-text', config)" class="text-list gap">
                  <ng-container *ngIf="getValueByPath(config.elementRef).texts as texts">
                    <div *ngFor="let option of texts" class="separator" style="display: flex;flex-direction: column; gap: 1em;">
                        <div class="space-between gap" style="align-items: flex-start;">
                            <textarea 
                                textInputTransform
                                style="min-width: unset; box-sizing: initial"
                                [(ngModel)]="option.content" 
                                (input)="updateChangeCounterByRef(config.elementRef)"
                                cdkTextareaAutosize 
                                [cdkTextareaAutosize]="true" 
                                [cdkAutosizeMinRows]="2"
                                class="textarea"
                            ></textarea>
                            <div class="text-select-button-container">
                              <div class="option-container-buttons" style="margin-bottom: 0.5em;" *ngIf="!option.isDisabled">
                                  <a 
                                    class="button is-correct-toggle"
                                    [class.is-disabled]="isReadOnly()"
                                    [class.is-success] = "option.isCorrect"
                                    [class.is-danger]  = "!option.isCorrect"
                                    (click)="toggleValue(option,'isCorrect')"
                                  >
                                    <i 
                                      class="fa fa-check" 
                                      [class.fa-check] = "option.isCorrect"
                                      [class.fa-times] = "!option.isCorrect"
                                      style="width: 16px;"
                                      aria-hidden="true"
                                    ></i>
                                  </a>
                              </div>
                              <div class="option-container-buttons" style="margin-bottom: 0.5em;">
                                <!-- <a 
                                  class="button is-correct-toggle"
                                  [class.is-dark] = "option.isDisabled"
                                  (click)="disableMCQOption(option); updateChangeCounterByRef(config.elementRef);"
                                >
                                  <i 
                                    class="fa fa-lock" 
                                    aria-hidden="true"
                                  ></i>
                                </a> -->
                              </div>
                              <a class="button" (click)="removeElement(getValueByPath(config.elementRef).texts, option); updateChangeCounterByRef(config.elementRef);">
                                  <i class="fas fa-trash"  aria-hidden="true"></i>
                              </a>   
                            </div>
                        </div>
                    </div>
                    <button style="width: 100%" (click)="insertMCQEntry(texts, elementTypes.TEXT); updateChangeCounterByRef(config.elementRef);" class="button is-primary">Add</button>
                  </ng-container>
                </div>
                <div *ngIf="isConfigTypeMatch('table', config)">
                  <template-table
                    [config]="config"
                    [elementRef]="config.elementRef"
                    [tableElement]="getValueByPath(config.elementRef)"
                    [hasValidator]="hasValidator()"
                    [twiddleToggleMap]="twiddleToggleMap"
                    (onRemoveElement)="removeElement($event.content, $event.element)"
                    (onValidatorAlign)="alignValidators()"
                    (onAssignEntryId)="ensureResponseEntryIds()"
                    (onImageUploaded)="onImageUploaded()"
                    (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                  ></template-table>
                </div>
                <div *ngIf="isConfigTypeMatch('image', config)">
                    <capture-image [element]="getValueByPath(config.elementRef)" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploaded()" (change)="handleImageChange(config.elementRef)"></capture-image>
                    <asset-library-link [element]="getValueByPath(config.elementRef)"></asset-library-link>
                </div>
                <div *ngIf="isConfigTypeMatch('validator', config)" class="text-list">
                    <div class="space-between gap" *ngIf="getValueByPath(config.elementRef).mode == 'ADVANCED_COMBINATION'">
                        <div>
                            Entry IDs
                        </div>
                        <input
                            style="width: 12em;"
                            class="input"
                            type="text"
                            [value]="getValidatorIDs(getValueByPath(config.elementRef))"
                            disabled
                        >
                    </div>
                    <div class="space-between gap" *ngIf="getValueByPath(config.elementRef).mode == 'ALL_OR_NOTHING'">
                        <div>
                            Entry IDs
                        </div>
                        <input
                            style="width: 12em;"
                            class="input"
                            type="text"
                            [value]="getValidatorIDs(getValueByPath(config.elementRef))"
                            disabled
                        >
                    </div>
                    <div class="space-between">
                        <div>
                            Mode
                        </div>
                        <input
                            style="width: 12em;"
                            class="input"
                            type="text"
                            [(ngModel)]="getValueByPath(config.elementRef).mode"
                            disabled
                        >
                    </div>
                    <div class="space-between">
                        <div>
                            Weight
                        </div>
                        <input
                            style="width: 12em;"
                            class="input"
                            type="text"
                            [(ngModel)]="getValueByPath(config.elementRef).scoreWeight"
                            disabled
                        >
                    </div>
                    <!-- <div *ngIf="getValueByPath(config.elementRef).mode == ValidatorMode.ADVANCED_COMBINATION">
                        <strong>Combinations</strong>    
                        <table class="table">
                            <tr *ngIf="getValueByPath(config.elementRef).advCombinations && getValueByPath(config.elementRef).advCombinations.length">
                                <th style="width: 1em;"></th>
                                <th *ngFor="let column of getValueByPath(config.elementRef).advCombinations[0]" style="max-width: 5em;">
                                    Entry: {{getLabelFromEntryId(column.validateId)}}
                                </th>
                            </tr>
                            <tr *ngFor="let combination of getValueByPath(config.elementRef).advCombinations; let combinationIdx = index;">
                                <th style="text-align: center; vertical-align: middle;">
                                    <a class="small-icon" style="color: black;" (click)="deleteAdvCombination(getValueByPath(config.elementRef), combinationIdx);">
                                        <i class="fas fa-trash"  aria-hidden="true"></i>
                                    </a> 
                                </th>
                                <th *ngFor="let answer of combination" style="max-width: 5em;">
                                    <input class="input" style="text-align: center;" type="text" [(ngModel)]="answer.correctValue">
                                </th>
                            </tr>
                        </table>
                        <button [disabled]="!hasEntries(getValueByPath(config.elementRef))" (click)="addAdvCombination(getValueByPath(config.elementRef))" class="button is-primary" style="margin-top: 1em;" type="submit">Add Combination </button>
                    </div> -->
                    <!-- <button class="button" (click)="alignValidatorEntries(config.elementRef, config.secondaryRef)">Sync Entry IDs</button> -->
                </div>
                <div *ngIf="isConfigTypeMatch('canvas', config)">
                    <div *ngFor="let page of getValueByPath(config.elementRef).pages">
                        <template-advanced-inline
                            [advancedList]="page.displayList"
                            [config]="config"
                            [isTextList]="false"
                            [isCanvas]="true"
                            [twiddleToggleMap]="twiddleToggleMap"
                            [hasValidator]="hasValidator()"
                            (onRemoveElement)="removeElement($event.content, $event.element)"
                            (onValidatorAlign)="alignValidators()"
                            (onAssignEntryId)="ensureResponseEntryIds()"
                            (onImageUploaded)="onImageUploaded()"
                            (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                        >
                        </template-advanced-inline>
                    </div>
                </div>
                <div *ngIf="isConfigTypeMatch('el', config)">
                    <element-config [contentElement]="getValueByPath(config.elementRef)"></element-config>
                </div>
                <div *ngIf="isConfigTypeMatch('interactive-diagram', config)">
                    <element-config-interactive-diagram [element] = "getValueByPath(config.elementRef)" [hiddenConfigs]="config.hiddenConfigs" [isSimplified]="true"></element-config-interactive-diagram>
                </div>
                <div *ngIf="isConfigTypeMatch('scientific-notation', config)">
                    <element-config-scientific-notation [element] = "getValueByPath(config.elementRef)" [hiddenConfigs]="config.hiddenConfigs" [isSimplified]="true"></element-config-scientific-notation>
                </div>
                <div *ngIf="isConfigTypeMatch('keyboard-input-freeform', config)" class="text-list" style="gap: 1em">
                    <div *ngFor="let keyboardInput of getValueByPath(config.elementRef) | slice:1; let i = index" class="separator" style="display: flex; justify-content: space-between; gap: 1.25em;">
                        <!-- slice to ignore first element of canvas (image) -->
                        <div class="text-list" style="width: 100%;">
                          <template-input
                              style="width: 100%;"
                              [block]="keyboardInput"
                              [isHideEntryId]="true"
                              (onRemoveElement)="removeElement($event.content, $event.element)"
                          >
                          </template-input>   
                          <ng-container *ngIf="keyboardInput.format != null">
                            <div class="space-between">
                                <div>
                                  Position
                                </div>
                                <ng-container *ngIf="isKeyboardInputFreeformBeingMoved(config.elementRef, i)">
                                  <span>
                                    (Right click to cancel)
                                  </span>
                                </ng-container>
                                <ng-container *ngIf="!isKeyboardInputFreeformBeingMoved(config.elementRef, i)">
                                  <a 
                                    class="button" 
                                    style="margin-right: 0px;" 
                                    (click)="requestMoveKeyboardInputFreeformOption($event, config.elementRef, i)"
                                  >
                                      <i class="fa fa-map-marker-alt"  aria-hidden="true"></i>
                                      <span style="padding-left: 0.5em;">Move</span>
                                  </a>   
                                </ng-container>
                            </div>
                            <ng-container *ngIf="getValueByPath(getParentPath(config.elementRef)).isShowAdvancedOptions">
                              <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em; margin-left: auto">
                                <div>X: </div>
                                <input 
                                    type="number" 
                                    class="input is-small" 
                                    style="width:5em; text-align:center" 
                                    [(ngModel)]="keyboardInput.x"
                                    step="0.1"
                                >
                                <div>Y: </div>
                                <input 
                                    type="number" 
                                    class="input is-small" 
                                    style="width:5em; text-align:center" 
                                    [(ngModel)]="keyboardInput.y"
                                    step="0.1"
                                >
                              </div>
                            </ng-container>
                          </ng-container>
                        </div>
                        <div class="option-container-buttons">
                          <a class="button" (click)="removeElement(getValueByPath(config.elementRef), keyboardInput);">
                              <i class="fas fa-trash"  aria-hidden="true"></i>
                          </a>   
                        </div>
                    </div>
                    <div style="padding-top: 1em;">
                        <button 
                          (click)="
                            insertEntry(getValueByPath(config.elementRef), elementTypes.INPUT, config.defaultWeight);
                            ensureResponseEntryIds();
                            alignValidators();
                          " 
                          class="button is-fullwidth"
                        >
                          Add Input
                        </button>
                    </div>
                    <label class="checkbox">
                      <input type="checkbox" [(ngModel)]="getValueByPath(getParentPath(config.elementRef)).isShowAdvancedOptions"/>
                      Show Advanced Options
                    </label>
                </div>
                <div *ngIf="isConfigTypeMatch('stim', config)">
                  <template-stim
                    [config]="config"
                    [elementRef]="config.elementRef"
                    [element]="getValueByPath(config.elementRef)"
                    [isOuterFrame]="true"
                    [isContainer]="true"
                    [twiddleToggleMap]="twiddleToggleMap"
                    [isVerticalContainerAlwaysFullWidth]="config.hiddenConfigs?.isVerticalContainerAlwaysFullWidth"
                    [isAllowBorderOnlyOnOuterVerticalContainer]="config.hiddenConfigs?.isAllowBorderOnlyOnOuterVerticalContainer"
                    (onRemoveElement)="removeElement($event.content, $event.element)"
                    (onImageUploaded)="onImageUploaded()"
                    (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                  ></template-stim>
                </div>
                <div *ngIf="isConfigTypeMatch('solution', config)">
                  <template-solution
                    [config]="config"
                    [elementRef]="config.elementRef"
                    [element]="getValueByPath(config.elementRef)"
                    [twiddleToggleMap]="twiddleToggleMap"
                    (onRemoveElement)="removeElement($event.content, $event.element)"
                    (onImageUploaded)="onImageUploaded()"
                    (onTwiddleToggle)="setTwiddleToggleInfo($event.elementRef, $event.key, $event.id, $event.toggle)"
                  ></template-solution>
                </div>
            </div>
        </div>
    </div>
    <div style="padding-top: 1em;" *ngIf="!authRestrictions.isCurrentUserRestricted">
        <button (click)="changeToFrame()" class="button is-fullwidth">Change into Frame</button>
    </div>
</div>
