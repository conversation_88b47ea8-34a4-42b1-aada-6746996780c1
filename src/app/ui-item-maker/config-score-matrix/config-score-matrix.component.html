<twiddle [state]="showMatrixTwiddle" [caption]="'Score Matrix'"></twiddle>

<ng-container *ngIf="showMatrixTwiddle.value">
  
  <div class="table-container">
    <table class="table is-size-8">
      <thead>
        <tr>
          <th colspan="1" style="padding-left: 0px;">
            <button (click)="toggleMatrixConfirmation()" class="button is-small is-link" [class.is-inverted]="element.scoreMatrix.isConfirmed">
              <span *ngIf="!element.scoreMatrix.isConfirmed"><tra slug="score_matrix_confirm"></tra></span>
              <span *ngIf="element.scoreMatrix.isConfirmed"><tra slug="score_matrix_cancel_confirm"></tra></span>
            </button>
          </th>
          <th [attr.colspan]="element.scoreMatrix.columns.length" style="background-color: #f2f2f2; text-align: center; border: 1px solid #dbdbdb">Targets</th>
        </tr>
      </thead>
      <tbody>
  
        <tr>
          <th style="background-color: #f2f2f2; text-align: center;">Options</th>
          <td *ngFor="let column of element.scoreMatrix.columns">
            <ng-container [ngSwitch]="column.type">
              <span *ngSwitchCase="MatrixHeaderType.TEXT">{{ column.content }}</span>
              <span *ngSwitchCase="MatrixHeaderType.IMAGE">
                <img class="cell-image" [src]="column.content">
              </span>
              <span *ngSwitchCase="MatrixHeaderType.MATH">
                <render-math [raw]="column.content"></render-math>
              </span>
            </ng-container>
          </td>
        </tr>
  
        <tr *ngFor="let row of element.scoreMatrix.rows; let i_r = index">
          <td>
            <span *ngIf="row.key_id">[{{row.key_id}}] </span>
            <ng-container [ngSwitch]="row.type">
              <span *ngSwitchCase="MatrixHeaderType.TEXT">{{ row.content }}</span>
              <span *ngSwitchCase="MatrixHeaderType.IMAGE">
                <img class="cell-image" [src]="row.content">
              </span>
              <span *ngSwitchCase="MatrixHeaderType.MATH">
                <render-math [obj]="mathObject" [prop]="mathProp" [raw]="row.content"></render-math>
              </span>
            </ng-container>
          </td>
  
          <td *ngFor="let column of element.scoreMatrix.columns; let i_c = index" [class.is-correct]="element.scoreMatrix.values[i_r][i_c]?.value === 1" [class.is-incorrect]="element.scoreMatrix.values[i_r][i_c]?.value ===0">
              <div *ngIf="element.scoreMatrix.values[i_r][i_c]?.value !== null" class="has-text-centered">
                {{cellValueDictionary[element.scoreMatrix.values[i_r][i_c].value]}}
              </div>
          </td>
  
        </tr>
      </tbody>
    </table>
    <div class ="matrix-status">
      <p *ngIf="!element.scoreMatrix.isUpdated" style="color: red;">
        <tra slug="score_matrix_outdated_warning"></tra>
      </p>
      
    </div>
  </div>
</ng-container>
