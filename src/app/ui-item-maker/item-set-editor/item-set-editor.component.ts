// vendor libs
import { Location } from '@angular/common';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { DomSanitizer, Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
// angular libs
import { Subscription } from 'rxjs';
import { ZoomService } from 'src/app/ui-testrunner/zoom.service';
import { AuthService } from '../../api/auth.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { RoutesService } from '../../api/routes.service';
import { DataGuardService } from '../../core/data-guard.service';
import { LangService } from '../../core/lang.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthRolesService } from '../auth-roles.service';
import { AuthScopeSettingsService } from "../auth-scope-settings.service";
import { DropdownService } from '../dropdown.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { IAuthoringGroup, ItemMakerService } from '../item-maker.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { ScriptGenService } from '../script-gen.service';
// app services
import { AssetLibraryService } from '../services/asset-library.service';
import { AssetLibraryCtrl } from './controllers/asset-library';
import { ItemBankAuditor } from './controllers/audits';
import { Destroyable } from './controllers/destroyable';
import { ItemSetFrameworkCtrl } from './controllers/framework';
import { ItemBankCtrl } from './controllers/item-bank';
import { ItemDiffCtrl } from './controllers/item-diff';
import { ItemEditCtrl } from './controllers/item-edit';
import { ItemFilterCtrl } from './controllers/item-filter';
import { MemberAssignmentCtrl } from './controllers/member-assignment';
import { PanelCtrl } from './controllers/mscat';
import { ItemSetPreviewCtrl } from './controllers/preview';
import { ItemSetPrintViewCtrl } from './controllers/print-view';
import { ItemSetPublishingCtrl } from './controllers/publishing';
import { FrameworkQuadrantCtrl } from './controllers/quadrants';
import { ItemBankSaveLoadCtrl } from './controllers/save-load';
import { TestFormGen } from './controllers/testform-gen';
import { TestletCtrl } from './controllers/testlets';
import { ItemBankUtilCtrl } from './controllers/util';
import { QuestionView } from './models/types';
import { HighlighterService } from 'src/app/ui-testrunner/highlighter.service';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { PageModalService, PageModalController } from 'src/app/ui-partial/page-modal.service';
import { P } from '@angular/cdk/keycodes';
import { IItemSetResponse } from 'src/app/ui-testrunner/models';
import { VersionManagerService } from 'src/app/api/version-manager.service';


// exports (legacy)
export { QuestionView };

enum IseModal {
  PROCESS_LOGS = 'PROCESS_LOGS'
}

export interface IAuthRestrictions {
  isCurrentUserRestricted?: boolean;
  isCurrentUserTemplateManager?: boolean;
}

@Component({
  selector: 'item-set-editor',
  templateUrl: './item-set-editor.component.html',
  styleUrls: ['./item-set-editor.component.scss']
})

export class ItemSetEditorComponent implements OnInit, OnDestroy {
  
  constructor(
    public assetLibraryService: AssetLibraryService,
    public assignedUsersService: AssignedUsersService,
    public auth: AuthService,
    public authScopeSettings: AuthScopeSettingsService,
    public dataGuard: DataGuardService,
    public editingDisabled: EditingDisabledService,
    public itemComponentEdit: ItemComponentEditService,
    public lang: LangService,
    public loginGuard: LoginGuardService,
    public myItems: ItemMakerService,
    public printAltText: PrintAltTextService,
    public route: ActivatedRoute,
    public routes: RoutesService,
    public sanitizer: DomSanitizer,
    public scriptGen: ScriptGenService,
    public titleService: Title,
    public whitelabel: WhitelabelService,
    public printMode: PrintModeService,
    public profile: StyleprofileService,
    public zoom: ZoomService,
    public dropdown: DropdownService,
    public authRoles: AuthRolesService,
    public router: Router,
    public location: Location,
    public highlighter: HighlighterService,
    public sidePanel: SidepanelService,
    private breadcrumbsService: BreadcrumbsService,
    private pageModalService: PageModalService,
    private versionManger: VersionManagerService,
    private itemMaker: ItemMakerService,
  ) {

  }

  assetLibraryCtrl:AssetLibraryCtrl;
  frameworkCtrl:ItemSetFrameworkCtrl;
  auditCtrl:ItemBankAuditor;
  controllers: Destroyable[];
  itemBankCtrl:ItemBankCtrl;
  itemEditCtrl:ItemEditCtrl;
  itemFilterCtrl:ItemFilterCtrl;
  memberAssignmentCtrl:MemberAssignmentCtrl;
  panelCtrl:PanelCtrl;
  previewCtrl:ItemSetPreviewCtrl;
  printViewCtrl: ItemSetPrintViewCtrl;
  publishingCtrl: ItemSetPublishingCtrl;
  quadrantCtrl: FrameworkQuadrantCtrl;
  saveLoadCtrl:ItemBankSaveLoadCtrl;
  testFormGen: TestFormGen;
  testletCtrl: TestletCtrl;
  util: ItemBankUtilCtrl;
  itemDiffCtrl: ItemDiffCtrl;

  isInited:boolean;
  private routeSub:Subscription;
  private routeDataSubscription:Subscription;
  private routeQuerySub:Subscription;
  subscription = new Subscription();
  snapshotFilter:string
  // currentQuestionTags = [];
  // currentQuestionTagListener: Subscription;
  // currentQuestionCommentListener: Subscription;

  pageModal: PageModalController;
  IseModal = IseModal;

  ngOnInit() {
    this.versionManger.checkVersion();
    this.loginGuard.activate([]);
    this.dataGuard.activate();
    this.sidePanel.deactivate();
    this.initQueryParams();
    this.initControllers();
    this.initSubscriptions();
    this.loadPersonalEditorSettings();
    this.isInited = true;
    this.initBreadcrumb()
    this.initRouteParams();
    this.assetLibraryCtrl.closeAssetLibrary();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.profile.initializeTemplates()
  }

  breadcrumb = [];
  initBreadcrumb(){
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
      this.breadcrumbsService._CURRENT('Set '+this.itemBankCtrl.customTaskSetId, this.route.toString())
    ];
  }

  initQueryParams(){
    this.snapshotFilter = this.route.snapshot.queryParams['snapshot'];
    console.log('snapshot', this.snapshotFilter)
  }

  ngOnDestroy() {
    if (this.routeSub) { this.routeSub.unsubscribe(); }
    if (this.routeQuerySub) { this.routeQuerySub.unsubscribe(); }
    // if (this.customTaskSetSub) { this.customTaskSetSub.unsubscribe(); }
    // if (this.currentQuestionTagListener) { this.currentQuestionTagListener.unsubscribe(); }
    // if (this.currentQuestionCommentListener) { this.currentQuestionCommentListener.unsubscribe(); }
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.dataGuard.deactivate();
    this.printAltText.resetAltTextVisible();
    this.editingDisabled.setCurrQState({isLocked: false, isTrackingChanges: false});

    for(const controller of this.controllers) {
      controller.destroy();
    }
  }

  initSubscriptions(){
    this.subscription.add(
      this.itemComponentEdit.update.subscribe(val => {
        if (val) {
          this.saveLoadCtrl.updateCurrentQuestionExpectedAnswer();
        }
      })
    );
  }


  // initControllers(){
  //   this.itemBankCtrl         = new ItemBankCtrl(this.auth, this.profile, this.routes, this.lang, this.editingDisabled);
  //   this.frameworkCtrl        = new ItemSetFrameworkCtrl(this.auth, this.myItems, this.itemBankCtrl);
  //   this.memberAssignmentCtrl = new MemberAssignmentCtrl(this.auth, this.assignedUsersService);
  //   this.saveLoadCtrl         = new ItemBankSaveLoadCtrl( this.auth, this.myItems, this.authScopeSettings, this.routes, this.editingDisabled, this.memberAssignmentCtrl, this.frameworkCtrl, this.itemBankCtrl, );
  //   this.testletCtrl          = new TestletCtrl(this.frameworkCtrl, this.itemFilterCtrl, this.printViewCtrl);
  //   this.testFormGen          = new TestFormGen(this.lang, this.frameworkCtrl, this.itemBankCtrl, this.saveLoadCtrl, this.testletCtrl);
  //   this.previewCtrl          = new ItemSetPreviewCtrl(this.whitelabel, this.lang, this.testFormGen, this.itemBankCtrl, this.saveLoadCtrl );
  //   this.assetLibraryCtrl     = new AssetLibraryCtrl(this.assetLibraryService, this.saveLoadCtrl);
  //   this.quadrantCtrl         = new FrameworkQuadrantCtrl(this.frameworkCtrl, this.printMode, this.itemFilterCtrl, this.itemBankCtrl, this.printViewCtrl, this.testletCtrl);
  //   this.itemEditCtrl         = new ItemEditCtrl(this.lang, this.scriptGen, this.frameworkCtrl, this.itemBankCtrl, this.saveLoadCtrl, this.editingDisabled, this.quadrantCtrl, this.testletCtrl);
  //   this.itemFilterCtrl       = new ItemFilterCtrl(this.frameworkCtrl, this.itemBankCtrl, this.panelCtrl, this.quadrantCtrl, this.testletCtrl );
  //   this.auditCtrl            = new ItemBankAuditor(this.scriptGen, this.itemFilterCtrl, this.itemBankCtrl, this.frameworkCtrl, this.quadrantCtrl);
  //   this.printViewCtrl        = new ItemSetPrintViewCtrl(this.printMode, this.printAltText, this.lang, this.itemBankCtrl, this.frameworkCtrl);
  //   this.publishingCtrl       = new ItemSetPublishingCtrl(this.auth, this.lang, this.routes, this.frameworkCtrl, this.itemBankCtrl, this.testFormGen);
  //   this.panelCtrl            = new PanelCtrl(this.frameworkCtrl, this.itemBankCtrl, this.itemFilterCtrl, );
    
    
  //   this.itemBankCtrl.frameworkCtrl = this.frameworkCtrl;
  //   this.itemBankCtrl.itemFilterCtrl = this.itemFilterCtrl;
  //   this.itemBankCtrl.itemEditCtrl = this.itemEditCtrl;
  //   this.itemBankCtrl.saveLoadCtrl = this.saveLoadCtrl;
  //   this.frameworkCtrl.saveLoadCtrl = this.saveLoadCtrl;
  //   this.frameworkCtrl.panelCtrl = this.panelCtrl;
  //   this.frameworkCtrl.quadrantCtrl = this.quadrantCtrl;
  //   this.frameworkCtrl.testletCtrl = this.testletCtrl;
  //   this.frameworkCtrl.itemFilterCtrl = this.itemFilterCtrl;
  //   this.frameworkCtrl.publishingCtrl = this.publishingCtrl;
  //   this.saveLoadCtrl.itemEditCtrl = this.itemEditCtrl;
  //   this.itemFilterCtrl.auditCtrl = this.auditCtrl
  //   this.testletCtrl.printMode = this.printMode
  //   this.testletCtrl.itemBankCtrl = this.itemBankCtrl
  //   this.testletCtrl.quadrantCtrl = this.quadrantCtrl
  //   this.itemEditCtrl.itemFilterCtrl = this.itemFilterCtrl
  // }

  // initRouteParams(){
  //   this.routeSub = this.route.params.subscribe(params => {
  //     this.frameworkCtrl.isFrameworkView = !!params['isFramework'];
  //     this.itemBankCtrl.customTaskSetId = params['itemSetId'];
  //     this.itemBankCtrl.targetQuestionLabel = params['questionLabel'];
  //     this.saveLoadCtrl.initCustomTaskSet();
  //     this.titleService.setTitle( 'Item Set ' + this.itemBankCtrl.customTaskSetId + ' | ' + this.lang.tra(this.whitelabel.currentBrandName) );

  initControllers(){
    this.itemBankCtrl         = new ItemBankCtrl(this.auth, this.authRoles, this.profile, this.routes, this.lang, this.editingDisabled, this.authScopeSettings, this.itemComponentEdit, this.loginGuard, this.whitelabel, this.highlighter);
    this.frameworkCtrl        = new ItemSetFrameworkCtrl(this.auth, this.myItems, this.itemBankCtrl, this.lang);
    this.memberAssignmentCtrl = new MemberAssignmentCtrl(this.auth, this.assignedUsersService, this.authRoles, this.routes);
    this.saveLoadCtrl         = new ItemBankSaveLoadCtrl( this.auth, this.myItems, this.authScopeSettings, this.routes, this.editingDisabled, this.authRoles, this.memberAssignmentCtrl, this.frameworkCtrl, this.itemBankCtrl, this.itemComponentEdit, this.lang, this.highlighter, this.auditCtrl, this.loginGuard);
    this.testletCtrl          = new TestletCtrl(this.frameworkCtrl, this.itemFilterCtrl, this.printViewCtrl, this.lang);
    this.testFormGen          = new TestFormGen(this.lang, this.whitelabel, this.frameworkCtrl, this.itemBankCtrl, this.saveLoadCtrl, this.testletCtrl);
    this.assetLibraryCtrl     = new AssetLibraryCtrl(this.assetLibraryService, this.saveLoadCtrl);
    this.quadrantCtrl         = new FrameworkQuadrantCtrl(this.frameworkCtrl, this.printMode, this.itemFilterCtrl, this.itemBankCtrl, this.printViewCtrl, this.testletCtrl);
    this.itemEditCtrl         = new ItemEditCtrl(this.lang, this.scriptGen, this.frameworkCtrl, this.itemBankCtrl, this.saveLoadCtrl, this.editingDisabled, this.quadrantCtrl, this.testletCtrl, this.testFormGen, this.auth, this.routes);
    this.itemFilterCtrl       = new ItemFilterCtrl(this.frameworkCtrl, this.itemBankCtrl, this.panelCtrl, this.quadrantCtrl, this.testletCtrl, this.lang);
    this.printViewCtrl        = new ItemSetPrintViewCtrl(this.printMode, this.printAltText, this.lang, this.itemBankCtrl, this.testFormGen, this.frameworkCtrl);
    this.publishingCtrl       = new ItemSetPublishingCtrl(this.auth, this.lang, this.routes, this.frameworkCtrl, this.itemBankCtrl, this.testFormGen, this.itemEditCtrl, this.scriptGen);
    this.panelCtrl            = new PanelCtrl(this.frameworkCtrl, this.itemBankCtrl, this.itemFilterCtrl, );
    this.itemDiffCtrl         = new ItemDiffCtrl(this.itemBankCtrl, this.itemComponentEdit, this.auth, this.lang, this.routes, this.authScopeSettings, this.saveLoadCtrl, this.highlighter);
    this.previewCtrl          = new ItemSetPreviewCtrl(this.whitelabel, this.lang, this.testFormGen, this.itemBankCtrl, this.saveLoadCtrl, this.zoom, this.itemDiffCtrl, this.itemFilterCtrl, this.publishingCtrl );
    this.auditCtrl            = new ItemBankAuditor(this.scriptGen, this.itemFilterCtrl, this.itemBankCtrl, this.frameworkCtrl, this.quadrantCtrl, this.auth, this.routes, this.lang, this.itemEditCtrl, this.loginGuard, this.itemMaker);
    
    this.controllers = [this.itemBankCtrl, this.frameworkCtrl, this.memberAssignmentCtrl, this.saveLoadCtrl,
       this.testletCtrl, this.testFormGen, this.assetLibraryCtrl, this.quadrantCtrl, this.itemEditCtrl,
       this.itemFilterCtrl, this.auditCtrl, this.printViewCtrl, this.publishingCtrl, this.panelCtrl, this.itemDiffCtrl,
        this.previewCtrl];

    this.itemBankCtrl.frameworkCtrl = this.frameworkCtrl;
    this.itemBankCtrl.itemFilterCtrl = this.itemFilterCtrl;
    this.itemBankCtrl.itemEditCtrl = this.itemEditCtrl;
    this.itemBankCtrl.saveLoadCtrl = this.saveLoadCtrl;
    this.itemBankCtrl.itemDiffCtrl = this.itemDiffCtrl;
    this.itemBankCtrl.previewCtrl = this.previewCtrl;
    this.frameworkCtrl.saveLoadCtrl = this.saveLoadCtrl;
    this.frameworkCtrl.panelCtrl = this.panelCtrl;
    this.frameworkCtrl.quadrantCtrl = this.quadrantCtrl;
    this.frameworkCtrl.testletCtrl = this.testletCtrl;
    this.frameworkCtrl.itemFilterCtrl = this.itemFilterCtrl;
    this.frameworkCtrl.publishingCtrl = this.publishingCtrl;
    this.saveLoadCtrl.itemEditCtrl = this.itemEditCtrl;
    this.saveLoadCtrl.itemFilterCtrl = this.itemFilterCtrl;
    this.saveLoadCtrl.itemDiffCtrl = this.itemDiffCtrl;
    this.saveLoadCtrl.auditCtrl = this.auditCtrl;
    this.itemFilterCtrl.auditCtrl = this.auditCtrl
    this.testletCtrl.printMode = this.printMode
    this.testletCtrl.itemBankCtrl = this.itemBankCtrl
    this.testletCtrl.itemFilterCtrl = this.itemFilterCtrl
    this.testletCtrl.quadrantCtrl = this.quadrantCtrl
    this.itemEditCtrl.itemFilterCtrl = this.itemFilterCtrl
    this.quadrantCtrl.itemFilterCtrl = this.itemFilterCtrl
    this.quadrantCtrl.printViewCtrl = this.printViewCtrl;
    this.testletCtrl.printViewCtrl = this.printViewCtrl
  }

  initRouteParams(){
    this.routeSub = this.route.params.subscribe(params => {
      this.frameworkCtrl.isFrameworkView = !!params['isFramework'];
      this.itemBankCtrl.customTaskSetId = params['itemSetId'];
      this.itemBankCtrl.targetQuestionLabel = params['questionLabel']  ? decodeURIComponent(params['questionLabel']) : params['questionLabel'];
      this.itemBankCtrl.targetTargetItemId = params['itemID'];
      this.saveLoadCtrl.initCustomTaskSet({
        snapshotFilter: this.snapshotFilter
      });
      this.titleService.setTitle( 'Item Set ' + this.itemBankCtrl.customTaskSetId + ' | ' + this.lang.tra(this.whitelabel.currentBrandName) );
      this.initBreadcrumb()
    });

    this.routeDataSubscription = this.route.data.subscribe((data: any) => {
      if (data.isStudentPreview){
        this.saveLoadCtrl.setLoaderEvent.subscribe(isLoaded => {
          console.log('setLoaderEvent', isLoaded)
          if (isLoaded){
            console.log('try loading sample...')
            this.previewCtrl.previewLinearTestForm();
          }
        })
      }
    });

    this.routeQuerySub = this.route.queryParams.subscribe(params => {
      this.itemFilterCtrl.setItemIdFilter(params['itemIds']);
    });
  }

  //Disables form controls for read-only access
  
  loadPersonalEditorSettings() {
    
  }

  // loading and saving

  // util
  getUser = () => this.auth.user();

  editItem(itemId) {
    this.itemBankCtrl.isPsychometricViewEnabled = false;
    this.itemBankCtrl.editModeItemId = itemId;
  }
  
  /**
   * 
   * @returns the authoring group of the current chosen item bank.
   */
  async getCurrentAuthGroup() {
    const itemSet: IItemSetResponse = await this.auth
    .apiGet(this.routes.TEST_AUTH_ITEM_SET, this.itemBankCtrl.customTaskSetId);

    return this.myItems.getGroupById(itemSet.group_id) || this.myItems.getGroupById(itemSet.single_group_id);;
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() {
    return this.cModal().config;
  }

  openProcessLogsModal(logQueue) {

    const config = logQueue.map(log => {
      return {
        ...log,
        _info: JSON.parse(log._info)
      }
    })

    this.pageModal.newModal({
      type: IseModal.PROCESS_LOGS,
      config,
      finish: () => {},
      confirmationCaption: "btn_close",
      isProceedOnly: true
    })
  }

  authRestrictions: IAuthRestrictions = {
    isCurrentUserRestricted: false,
    isCurrentUserTemplateManager: true
  }
}
