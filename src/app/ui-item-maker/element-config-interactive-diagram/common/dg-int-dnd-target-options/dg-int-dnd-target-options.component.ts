import { Component, Input, Output, EventEmitter, OnInit, ViewChildren, QueryList, ElementRef } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { IDgIntElementDnDTarget, isUnusedId, DND_OPTION_TEXT_MODE, IDgIntElementDnDOption, isHomeId, DND_UNUSED_ID } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-dnd-target-options',
  templateUrl: './dg-int-dnd-target-options.component.html',
  styleUrls: ['./dg-int-dnd-target-options.component.scss']
})
export class DgIntDndTargetOptionsComponent implements OnInit {
  @Input() dndTargets: IDgIntElementDnDTarget[];
  @Input() dndTargetOptionsForm: { label: string, content: FormControl[] }[];
  @Input() nonUniqueOptionTexts: string[] = [];
  @Input() isUsingReusableDraggable: boolean = false;
  @Input() isAlwaysSeparateAnswerSetup: boolean = false;
  @Input() isAllowGrouping: boolean = false;
  @Input() isUseImage: boolean = false;
  @Input() isShowAdvancedOptions: boolean = false;
  @Input() isInputAlwaysMath: boolean = false;
  @Input() isUsingAnswerGroup: boolean = false;
  @Input() isAllowNonScoringTarget: boolean = false;
  @Input() answerGroupCount: number = 0;
  @Output() onChange = new EventEmitter();
  @Output() onSetIsAllowEmptyTarget = new EventEmitter<boolean>();
  @ViewChildren('targets') targets: QueryList<ElementRef>;
  
  answerGroupOptions: number[] = [];
  isShowEmptyOptionPlaceholder = false;
  
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  constructor(
    private editingDisabled: EditingDisabledService,
    private specialKeyboard: SpecialKeyboardService
  ) { }
  
  ngOnInit() {
  }
  
  ngOnChanges(): void {
    this.answerGroupOptions = Array.from({ length: this.answerGroupCount }, (_, i) => i);
  }
  
  emitChange(){
    this.onChange.emit();
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  get isCombinedOptionContainer(): boolean {
    return this.isAlwaysSeparateAnswerSetup || this.isUsingReusableDraggable;
  }
  
  isTargetEmpty(targetIndex: number): boolean {
    const target = this.dndTargets[targetIndex];
    return target.content.length == 0;
  }
  isTargetLabelMath(targetIndex: number): boolean {
    const target = this.dndTargets[targetIndex];
    return target.isLabelMath;
  }
  isTargetContainOnlyOneOption(targetIndex: number): boolean {
    const target = this.dndTargets[targetIndex];
    return target.content.length == 1;
  }
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  isTargetFirstOptionEmpty(targetIndex: number): boolean {
    const target = this.dndTargets[targetIndex];
    if (this.isUnusedTarget(target)) return false;
    const option = target.content[0];
    if (option == undefined) return false;
    if (option.value != '') return false;
    if (option.image?.url != undefined) return false;
    return true;
  }
  isNonUniqueOption(option: string) {
    if (option.trim() == "") return false;
    return this.nonUniqueOptionTexts.includes(option);
  }
  isAllowAddOption(targetIndex : number) : boolean {
    const target = this.dndTargets[targetIndex];
    if (target.label == DND_UNUSED_ID) return true;
    if (!this.isAllowGrouping) return false;
    return true;
  }
  
  isAllowDeleteOption(targetIndex: number, optionIndex : number) : boolean {
    const target = this.dndTargets[targetIndex];
    if (isUnusedId(target.label)) return true;
    if (!this.isAllowGrouping) return false;
    return true;
  }
  isImageOption(targetIndex: number, optionIndex: number) : boolean {
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    return Boolean(option.image);
  }
  
  
  getOptionTextMode(targetIndex: number, optionIndex: number) : DND_OPTION_TEXT_MODE {
    if (this.isInputAlwaysMath) return DND_OPTION_TEXT_MODE.MATH;
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    return option.textMode ?? DND_OPTION_TEXT_MODE.TEXT;
  }
  getOptionObject(targetIndex: number, optionIndex: number): IDgIntElementDnDOption {
    const target = this.dndTargets[targetIndex];
    return target.content[optionIndex];
  }
  getOptionImageElement(targetIndex: number, optionIndex: number) : IContentElementImage {
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    if (option?.image == undefined) {
      this.elementEditInitOptionImage(targetIndex, optionIndex);
    }
    return option.image;
  }
  getTargetObject(targetIndex: number) : IDgIntElementDnDTarget {
    return this.dndTargets[targetIndex];
  }
  
  elementEditAddOption(targetIndex: number){
    const target = this.dndTargets[targetIndex];
    // if ((!this.element.config.isAllowGrouping) && target.label != DND_UNUSED_ID){
    //   return;
    // }
    target.content.push({'value':""});
    target.isNonScoring = false;
    this.emitChange();
  }
  elementEditDeleteOption(targetIndex: number, optionIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this option?");
    if (!confirm) return;
    const target = this.dndTargets[targetIndex];
    target.content.splice(optionIndex, 1);
    this.emitChange();
  }
  elementEditSetOptionTextMode(targetIndex: number, optionIndex: number, mode: DND_OPTION_TEXT_MODE) {
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.textMode = mode;
    this.emitChange();
  }
  elementEditInitOptionImage(targetIndex:number, optionIndex:number){
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.image = createDefaultElement(ElementType.IMAGE);
    option.image.scale = 25;
  }
  elementEditRemoveOptionImage(targetIndex:number, optionIndex:number){
    const target = this.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    delete option.image;
    delete option.imageLabelPosition;
    this.emitChange();
  }
  elementEditSetTargetEmpty(targetIndex: number){
    const confirm = window.confirm("Are you sure you want to set this target to be empty?");
    if (!confirm) return;
    const target = this.dndTargets[targetIndex];
    target.content = [];
    this.onSetIsAllowEmptyTarget.emit(true);
    this.emitChange();
  }
  elementEditSetTargetNonScoring(targetIndex: number){
    const confirm = window.confirm("Are you sure you want to set this target to be non scoring?");
    if (!confirm) return;
    const target = this.dndTargets[targetIndex];
    target.isNonScoring = true;
    this.emitChange();
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  isShowConfigPreview(){
    return true;
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {}

    let targetCount = 0;
    this.dndTargets.forEach((target, i) => {
      target.content.forEach((targetOption, j) => {
        elements[`target_${i}_${j}`] = this.targets.toArray()[targetCount];
        targetCount += 1;
      })
    })
    
    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.dndTargets,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
  
  /**
  * to check wether `fillEmptyOptionsWithTargetLabel` can be applied
  */
  isFirstEmptyOptionExist() {
    for (const target of this.dndTargets) {
      const firstOption = target.content[0];
      if (isUnusedId(target.label)) continue;
      if (firstOption == undefined) continue;
      if (firstOption.value != '') continue;
      if (firstOption.image?.url != undefined) continue;
      return true;
    }
    return false;
  }
  
  fillEmptyOptionsWithTargetLabel() {
    for (const target of this.dndTargets) {
      const firstOption = target.content[0];
      if (isUnusedId(target.label)) continue;
      if (firstOption == undefined) continue;
      if (firstOption.value != '') continue;
      if (firstOption.image?.url != undefined) continue;
      firstOption.value = target.label;
    }
    this.emitChange();
  }
  
}
