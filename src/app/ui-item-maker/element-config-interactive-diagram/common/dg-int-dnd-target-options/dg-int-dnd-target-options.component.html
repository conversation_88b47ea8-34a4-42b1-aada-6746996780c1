<fieldset [disabled]="isReadOnly()" (change)="emitChange()">
  
  <div *ngIf="!isCombinedOptionContainer && !isAllowGrouping" style="margin-bottom: 1em">
    <button 
      *ngIf="isFirstEmptyOptionExist()"
      (mouseenter)="isShowEmptyOptionPlaceholder = true" 
      (mouseleave)="isShowEmptyOptionPlaceholder = false"
      (click)="fillEmptyOptionsWithTargetLabel()" 
      class="button is-small has-icon"
    >
      <span class="icon"><i class="fas fa-download" aria-hidden="true"></i></span>
      <span>Fill Empty Options with Target Label</span>
    </button>
  </div>
  
  <div>
    <div *ngFor="let target of dndTargetOptionsForm; let i = index; trackBy: trackByFn" class="target-container">
      
      <div *ngIf="isCombinedOptionContainer && isUsingAnswerGroup && !isUnusedTarget(target)">
        <div style="display: inline-block; margin-right: 0.5em;">
          <ng-container *ngIf="!isTargetLabelMath(i); else mathTargetLabel">
            <input 
              type="text" disabled 
              value="{{target.label}}" 
              class="target-name-input-label">
          </ng-container>
          <ng-template #mathTargetLabel>
            <div class="math-target-label">
              <katex-display [expression]="target.label"></katex-display>
            </div>
          </ng-template>
        </div>
        
        <span>group: </span>
        <select [(ngModel)]="getTargetObject(i).answerGroup">
          <option *ngFor="let option of answerGroupOptions; let i = index" [value]="i">
            {{i + 1}}
          </option>
        </select>
      </div>
      
      <ng-container *ngIf="!isCombinedOptionContainer || isUnusedTarget(target)">
      
        <div>
          <ng-container [ngTemplateOutlet]="targetConfigContent"></ng-container>
        </div>
        
        <ng-template #targetConfigContent>
          
          <div *ngIf="!isUnusedTarget(target); else unusedTargetLabelHeader">
            <span class="target-label">target: </span>
            
            <ng-container *ngIf="!isTargetLabelMath(i); else mathTargetLabel">
              <input 
                type="text" disabled 
                value="{{target.label}}" 
                class="target-name-input-label">
            </ng-container>
            <ng-template #mathTargetLabel>
              <div class="math-target-label">
                <katex-display [expression]="target.label"></katex-display>
              </div>
            </ng-template>
            
            <div *ngIf="target.content.length === 0" style="display: inline-block;">
              <div class="warning-container" style="padding: 0.1em 1em; margin-left: 0.5em;">
                <i class="fas fa-exclamation-triangle"></i>
                <span *ngIf="!getTargetObject(i).isNonScoring || !isAllowNonScoringTarget">
                  Empty Target
                </span>
                <span *ngIf="getTargetObject(i).isNonScoring && isAllowNonScoringTarget">
                  Non Scoring
                </span>
              </div>
            </div>
                
            <button 
              *ngIf="isTargetFirstOptionEmpty(i) && !isAllowGrouping"
              (click)="elementEditSetTargetEmpty(i)" 
              class="button is-small has-icon"
            >
              <span class="icon"><i class="far fa-square" aria-hidden="true"></i></span>
              <span>Set As Empty</span>
            </button>
            
            <button 
              *ngIf="target.content.length === 0 && isAllowNonScoringTarget && !getTargetObject(i).isNonScoring"
              (click)="elementEditSetTargetNonScoring(i)" 
              class="button is-small has-icon"
            >
              <span class="icon"><i class="far fa-square" aria-hidden="true"></i></span>
              <span>Set As Non Scoring</span>
            </button>
            
            <div *ngIf="isUsingAnswerGroup">
              <span>group: </span>
              <select [(ngModel)]="getTargetObject(i).answerGroup" [disabled]="isTargetContainOnlyOneOption(i)">
                <option *ngFor="let option of answerGroupOptions; let i = index" [value]="i">
                  {{i + 1}}
                </option>
              </select>
            </div>
            
            <div *ngIf="isAllowGrouping || !isTargetEmpty(i)">
              <span>option:</span>
            </div>
            <div *ngIf="!isAllowGrouping && isTargetEmpty(i)">
              <button 
                (click)="elementEditAddOption(i)" 
                class="button is-small has-icon"
              >
                <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
                <span>Add Option</span>
              </button>
            </div>
          </div>
          
          <ng-template #unusedTargetLabelHeader>
            <ng-container *ngIf="!isCombinedOptionContainer">
              <span>extra options:</span>
            </ng-container>
            <ng-container *ngIf="isCombinedOptionContainer">
              <span>options:</span>
            </ng-container>
          </ng-template>
        </ng-template>
        
        <div class="sub-property-div" style="max-width: 20em;">
          
          <div *ngFor="let optionForm of target.content; let j = index; trackBy: trackByFn" class="option-container">
            
            <div *ngIf="isUseImage; then optionImage; else optionText"></div>
            
            <ng-template #optionText>
              
              <div [ngSwitch]="getOptionTextMode(i,j)" style="flex-grow: 1;">
                <div *ngSwitchCase="DND_OPTION_TEXT_MODE.TEXT">
                  <textarea
                    textInputTransform
                    #targets
                    [formControl]="optionForm"
                    [placeholder]="isShowEmptyOptionPlaceholder ? target.label : ''"
                    style="min-width: 10em; max-width: 20em;"
                    class="textarea is-small target-option-textarea"
                    [class.error-bg-color]="isNonUniqueOption(optionForm.value)"
                    cdkTextareaAutosize
                    [cdkTextareaAutosize]="true"
                    [cdkAutosizeMinRows]="2"
                    (focus)="setTextFocus(optionForm, 'target_' + i + '_' + j)"
                  ></textarea>
                </div>
                <div *ngSwitchCase="DND_OPTION_TEXT_MODE.MATH">
                  <div 
                    class="capture-math-container"
                    [class.error-border]="isNonUniqueOption(optionForm.value)"
                  >
                    <capture-math 
                      #targets
                      [obj]="getOptionObject(i,j)" 
                      (onChange)="emitChange()"
                      [class.is-disabled]="isReadOnly()"
                      [class.is-disabled]="isReadOnly()"
                      prop="value" [isManualKeyboard]="true">
                    </capture-math>
                  </div>
                </div>
              </div>
              
              <div *ngIf="getOptionTextMode(i,j) === DND_OPTION_TEXT_MODE.TEXT"
                (click)="elementEditSetOptionTextMode(i,j,DND_OPTION_TEXT_MODE.MATH)" class="target-option-button"
                [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-square-root-alt" aria-hidden="true"></i>
              </div>
              <div *ngIf="getOptionTextMode(i,j) === DND_OPTION_TEXT_MODE.MATH && !isInputAlwaysMath"
                (click)="elementEditSetOptionTextMode(i,j,DND_OPTION_TEXT_MODE.TEXT)" class="target-option-button"
                [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-font" aria-hidden="true"></i>
              </div>
              
              <div *ngIf="isAllowDeleteOption(i,j)" (click)="elementEditDeleteOption(i,j)" class="target-option-button"
                [class.no-pointer-events]="isReadOnly()"
              >
                <i class="fas fa-trash" aria-hidden="true"></i>
              </div>
            </ng-template>
            
            <ng-template #optionImage>
              <div class="option-image-container">
                <div style="display: flex;">
                  <ng-container *ngTemplateOutlet="optionText"></ng-container>
                </div>
                
                <div *ngIf="!isImageOption(i,j)">
                  <button (click)="elementEditInitOptionImage(i,j)">Add Image</button>
                </div>
                
                <div *ngIf="isImageOption(i,j)">
                  <button (click)="elementEditRemoveOptionImage(i,j)">Remove Image</button>
                  <div *ngIf="!isShowAdvancedOptions && getOptionImageElement(i,j).url">
                    <div class="simple-image-container">
                      <img [src]="getOptionImageElement(i,j).url" style="max-width: 10em; max-height: 10em;">
                    </div>
                    <div>
                      scale:
                      <input type="number" [(ngModel)]="getOptionImageElement(i,j).scale" class="small-input">
                    </div>
                  </div>
                  <div *ngIf="isShowAdvancedOptions || !getOptionImageElement(i,j).url">
                    <capture-image [element]="getOptionImageElement(i,j)" (change)="emitChange()"></capture-image>
                    <asset-library-link [element]="getOptionImageElement(i,j)"></asset-library-link>     
                  </div>
                </div>
                
                <br>
              </div>
            </ng-template>
            
          </div>
          
          <button 
            *ngIf="isAllowAddOption(i)"
            (click)="elementEditAddOption(i)" 
            class="button is-small has-icon"
          >
            <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
            <span>Add Option</span>
          </button>
        </div>
      
      </ng-container>
    </div>
      
  </div>
</fieldset>
