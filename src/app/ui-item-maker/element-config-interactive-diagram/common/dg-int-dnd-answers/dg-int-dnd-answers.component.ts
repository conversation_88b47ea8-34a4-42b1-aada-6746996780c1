import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { Component, EventEmitter, Input, OnInit, Output, TemplateRef, ViewChild, ViewContainerRef } from '@angular/core';
import { isUnusedId } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IDgIntElementDnDAnswerSet } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';

@Component({
  selector: 'dg-int-dnd-answers',
  templateUrl: './dg-int-dnd-answers.component.html',
  styleUrls: ['./dg-int-dnd-answers.component.scss']
})
export class DgIntDndAnswersComponent implements OnInit {
  
  @ViewChild('optionDropdownOverlay') optionDropdownOverlay: TemplateRef<any>;
  private overlayRef: OverlayRef|null = null;

  @Input() defaultAnswerSet: IDgIntElementDnDAnswerSet;
  @Input() altAnswers: IDgIntElementDnDAnswerSet[];
  
  @Input() isShowDefaultAnswerSet: boolean = true;
  @Input() isAllowMultipleAnswer: boolean = false;
  @Input() isAllowEmptyTarget: boolean = false;
  @Input() isAllowGrouping: boolean = false;
  @Input() isAllowNonScoringTarget: boolean = false;
  @Input() targets?: {id: string, isNonScoring: boolean}[] = [];
  
  @Input() idToLabelMap: Map<string, string>;
  @Input() idToUrlMap: Map<string, string> | undefined;
  @Input() idIsMathSet: Set<string> = new Set();
  
  @Input() targetIdList: string[] = [];
  @Input() optionIdList: string[] = [];
  
  @Output() onChange = new EventEmitter();
  isUnusedId = isUnusedId;
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private overlay: Overlay,
    private viewContainerRef: ViewContainerRef,
  ) { }

  ngOnInit(): void {
  }
  
  emitChange(){
    this.onChange.emit();
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return !this.isAllowEmptyTarget;
    return !this.optionIdList.includes(optionId);
  }
  elementEditAddAnswerSet() {
    if (!this.isAllowMultipleAnswer) return;
    const answerSet = this.targetIdList.map((id) => { return { targetId: id, optionIds: [] } });
    this.altAnswers.push(answerSet);
    this.emitChange();
  }
  elementEditDeleteAnswerSet(index: number){
    const confirm = window.confirm("Are you sure you want to delete this answer set?");
    if (!confirm) return;
    this.altAnswers.splice(index, 1);
    this.emitChange();
  }
  
  elementEditAddOptionToAnswerSet(answerSetIndex: number, targetIndex: number) {
    const answerSet = this.altAnswers[answerSetIndex];
    answerSet[targetIndex].optionIds.push("");
    this.emitChange();
  }
  elementEditDeleteOptionFromAnswerSet(answerSetIndex: number, targetIndex: number, optionIndex: number) {
    const confirm = window.confirm("Are you sure you want to delete this option from the answer set?");
    if (!confirm) return;
    const answerSet = this.altAnswers[answerSetIndex];
    answerSet[targetIndex].optionIds.splice(optionIndex, 1);
    this.emitChange();
  }
  
  categorizeOptionType(optionId: string): 'text' | 'math' |  'image' {
    if (this.idIsMathSet.has(optionId)) return 'math';
    if (this.idToUrlMap && this.idToUrlMap.get(optionId)) return 'image';
    return 'text';
  }
  
  openOptionDropdown(event:MouseEvent, obj:any, prop:any){
    const triggerElement = (event.target as HTMLElement).closest('button') as HTMLElement;
    const positionStrategy = this.overlay.position()
      .flexibleConnectedTo(triggerElement)
      .withPositions([
        { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
      ]);
    
    if (this.overlayRef) this.closeOptionDropdown();
    this.overlayRef = this.overlay.create({ 
      positionStrategy,
      hasBackdrop: true,
      backdropClass: 'cdk-overlay-transparent-backdrop',
    });
    this.overlayRef.backdropClick().subscribe(() => this.closeOptionDropdown());
    
    const portal = new TemplatePortal(this.optionDropdownOverlay, this.viewContainerRef, {obj, prop});
    this.overlayRef.attach(portal);
  }
  closeOptionDropdown(){
    if (this.overlayRef) {
      this.overlayRef.dispose();
      this.overlayRef = null;
    }
  }
  selectOption(obj:any, prop:any, value:any){
    obj[prop] = value;
    this.closeOptionDropdown();
    this.emitChange();
  }
  selectOptionEmpty(obj:any, prop:any){
    delete obj[prop];
    this.closeOptionDropdown();
    this.emitChange();
  }
  selectOptionNonScoring(obj:any, prop:any){
    delete obj[prop];
    this.closeOptionDropdown();
    this.emitChange();
  }
  
  
}
