import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { 
  DND_NUMBERLINE_EXTRA_ELEMENT_TYPE,
  IDgIntElementDnDNumberline, LAYOUT_NUMBERLINE_TARGET_POSITION,
  evalTexValue,
  resolveDnDNumberline,
  sortNumberlineElement,
  // resolveDnDNumberlineTargets,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-numberline';
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID, LAYOUT_HOME_POSITION,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { isEqual } from 'lodash';
import { DynamicScriptLoaderService } from 'src/app/ui-testrunner/element-render-interactive-diagram/dynamic-script-loader.service';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { isNgContainer } from '@angular/compiler';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';

@Component({
  selector: 'dg-int-config-dnd-numberline',
  templateUrl: './dg-int-config-dnd-numberline.component.html',
  styleUrls: ['./dg-int-config-dnd-numberline.component.scss']
})
export class ElementConfigDgIntDndNumberlineComponent implements OnInit {
  @Input() element: IDgIntElementDnDNumberline;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;

  twiddleNumberline = new TwiddleState(true);
  twiddleCustomTicks = new TwiddleState(true);
  twiddleTargets = new TwiddleState(false);
  twiddleOrdering = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  
  idToLabelMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  
  isShowAnswerForm = new FormControl(false);
  homePositionForm = new FormControl(LAYOUT_HOME_POSITION.BOTTOM)
  homePositionOptions = [
    {id: LAYOUT_HOME_POSITION.LEFT, caption:'Left'},
    {id: LAYOUT_HOME_POSITION.RIGHT, caption:'Right'},
    {id: LAYOUT_HOME_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_HOME_POSITION.BOTTOM, caption:'Bottom'},
  ]
  targetLayoutOptions = [
    {id: LAYOUT_NUMBERLINE_TARGET_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_NUMBERLINE_TARGET_POSITION.BOTTOM, caption:'Bottom'},
    {id: LAYOUT_NUMBERLINE_TARGET_POSITION.CENTER, caption:'Center'},
  ]
  
  public elementLabelForms: FormControl[] = [];
  prevPositionLabels: string[] = [];
  positionValues: number[] = [];
  positionInvalid: boolean[] = [];
  
  constructor(
    private scriptLoader: DynamicScriptLoaderService,
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService
  ) { }
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    const configFormControls = [
      {f: this.homePositionForm, p:'homePosition'},
    ];
    bindFormControls(this.element.config, configFormControls);
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  isElementTarget(elementIndex: number){
    return this.element.elements[elementIndex].isTarget;
  }
  isElementOption(elementIndex: number){
    return this.element.elements[elementIndex].isOption;
  }
  elementEditToggleElementTarget(elementIndex: number){
    const element = this.element.elements[elementIndex];
    const isTarget = element.isTarget;
    if (isTarget){
      const confirm = window.confirm('Are you sure you want to remove this element as a target?');
      if (!confirm) return;
      element.isTarget = false;
      delete element.targetConfig;
    } else {
      element.targetConfig = {
        layoutPosition: LAYOUT_NUMBERLINE_TARGET_POSITION.CENTER,
        layoutPositionArgument: 2,
      }
      element.isTarget = true;
    }
    this.validateConfig();
  }
  elementEditToggleElementOption(elementIndex: number){
    const element = this.element.elements[elementIndex];
    const isOption = element.isOption;
    if (isOption){
      const confirm = window.confirm('Are you sure you want to remove this element as an option?');
      if (!confirm) return;
      element.isOption = false;
      delete element.optionConfig;
    } else {
      element.optionConfig = {
        customLabel: '',
      }
      element.isOption = true;
    }
    this.validateConfig();
  }
  elementEditDeleteElement(elementIndex: number){
    const confirm = window.confirm('Are you sure you want to delete this element?');
    if (!confirm) return;
    this.element.elements.splice(elementIndex, 1);
    this.validateConfig();
  }
  elementEditAddElement(){
    this.element.elements.push({
      positionLabel: '',
      isTarget: false,
      isOption: false,
    });
    this.validateConfig();
  }
  elementEditDeleteExtraElement(elementIndex: number){
    const confirm = window.confirm('Are you sure you want to delete this element?');
    if (!confirm) return;
    this.element.extraElements.splice(elementIndex, 1);
    this.validateConfig();
  }
  elementEditAddExtraElement(){
    this.element.extraElements.push({
      type: DND_NUMBERLINE_EXTRA_ELEMENT_TYPE.LABELED_TICK,
      xpos: this.element.numberline.min,
      label: '',
    });
    this.validateConfig();
  }
  
  elementEditSort(){
    const confirm = window.confirm('Are you sure you want to sort the elements?');
    if (!confirm) return;
    let evaluatex = window['evaluatex'];
    if (evaluatex){
      sortNumberlineElement(evaluatex, this.element);
      this.validateConfig();
    }
  }
  
  initElementLabelForms() {
    const forms = [];
    for (let element of this.element.elements){
      const form = new FormControl(element.positionLabel);
      form.valueChanges.subscribe(() => {
        element.positionLabel = form.value;
        this.validateConfig();
      });
      forms.push(form);
    }
    this.elementLabelForms = forms;
  }
  updatePositionValues() {
    const positionLabels = this.element.elements.map(e => e.positionLabel);
    
    if (!isEqual(this.prevPositionLabels, positionLabels)) {
      this.scriptLoader.loadScripts([
        {src:"https://cdn.jsdelivr.net/npm/evaluatex@2.2.0/dist/evaluatex.min.js", id:"evaluatex"}
      ]).then(() => {
        let evaluatex = window['evaluatex'];
        if (evaluatex){
          this.positionValues = this.element.elements.map(e => evalTexValue(evaluatex, e.positionLabel));
          this.prevPositionLabels = this.element.elements.map(e => e.positionLabel);
        }
        this.updatePositionValidity();
      })
    } else {
      this.updatePositionValidity();
    }
  }
  updatePositionValidity() {
    this.positionInvalid = this.positionValues.map((v,i) => 
      this.element.elements[i].isTarget && !this.isValidPosition(v));
  }
  
  isTickstepTooDense(tickStep: number): boolean {
    const numberlineInnerWidth = this.element.numberline.max - this.element.numberline.min;
    return numberlineInnerWidth/tickStep > 30;
  }
  isPositionInsideRange(x: number): boolean {
    return x >= this.element.numberline.min && x <= this.element.numberline.max;
  }
  isValidPosition(x: number) : boolean {
    return !isNaN(x) &&  this.isPositionInsideRange(x);
  }
  getPositionValue(elementIndex: number) : number {
    // this.updatePositionValues();
    return this.positionValues[elementIndex];
  }
  
  isAnyPositionInvalid() : boolean {
    this.updatePositionValues();
    return this.positionInvalid.some(v => v);
  }
  isPositionIndexInvalid(elementIndex: number) : boolean {
    return this.positionInvalid[elementIndex];
  }
  
  getExtraElements() {
    if (this.element.extraElements == undefined){
      this.element.extraElements = [];
    }
    return this.element.extraElements;
  }
  
  validateConfig() {
    this.resolveData();
    this.updatePositionValues();
    this.initElementLabelForms();
    this.updateIdToLabelMap();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  resolveData() {
    resolveDnDNumberline(this.element);
    // resolveDnDTableTargets(this.element);
    // this.updateOptionTexts();
  }
  
  drop(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.ordering, event.previousIndex, event.currentIndex);
    this.rerender();
  }
  
  updateOptionTexts() {
    // let options = [];
    // this.element.dndTargets.forEach(target => {
    //   target.content.forEach(option => {
    //     options.push(option.value);
    //   });
    // });
    // this.optionTexts = options;
    
    // // check for non-unique options
    // this.nonUniqueOptionTexts = this.optionTexts.filter((item, index) => this.optionTexts.indexOf(item) != index);
  }
  
  isNonUniqueOption(option: string) {
    // if (option.trim() == "") return false;
    // return this.nonUniqueOptionTexts.includes(option);
  }
  
  isShowingAdvanced(){
    return this.element.isShowAdvancedOptions;
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  updateIdToLabelMap() {
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let element of this.element.elements) {
      const label = element.optionConfig?.customLabel || element.positionLabel;
      this.idToLabelMap.set(element.id, label);
      this.idIsMathSet.add(element.id);
    }
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
}
