<twiddle caption="Numberline" [state]="twiddleNumberline"></twiddle>
<div *ngIf="twiddleNumberline.value">
  <fieldset class="numberline-form-container" (change)="rerender()">
    Min: <input type="number" [(ngModel)]="element.numberline.min"/><br>
    Max: <input type="number" [(ngModel)]="element.numberline.max"/><br>
    Overshoot:<br>
    <div class="sub-property-div">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.numberline.overshoot.isInPercent"/>In Percent
      </label><br>
      left: <input type="number" [(ngModel)]="element.numberline.overshoot.left"/>
      <span *ngIf="element.numberline.overshoot.isInPercent">%</span>
      
      <br>
      right: <input type="number" [(ngModel)]="element.numberline.overshoot.right"/>
      <span *ngIf="element.numberline.overshoot.isInPercent">%</span>
    </div>
    Ticks:<br>
    <div class="sub-property-div">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.numberline.isManualTickStep"/>
        Input Custom Step
      </label>
      <div *ngIf="element.numberline.isManualTickStep">
        <label class="checkbox">
          <input type="checkbox" [(ngModel)]="element.numberline.tickStep.isShowMajorTicks"/>Numbered Ticks
        </label><br>
        Numbered Ticks Step: 
        <input type="number" [(ngModel)]="element.numberline.tickStep.major"
          [class.error-bg-color]="element.numberline.tickStep.isShowMajorTicks && isTickstepTooDense(element.numberline.tickStep.major)"
        />
        <br>
        <label class="checkbox">
          <input type="checkbox" [(ngModel)]="element.numberline.tickStep.isShowMinorTicks"/>Minor Ticks
        </label><br>
        Minor Ticks Step: 
        <input type="number" [(ngModel)]="element.numberline.tickStep.minor"/>
        <br>
      </div >
    </div>
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isHideTickOnTarget"/>
      Hide tick numbers at target points
    </label>
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.numberline.isDrawArrow"/>
      Draw Arrow
    </label>
  </fieldset>
</div>

<div *ngIf="element.config.isShowExtraElements">
  <twiddle caption="Custom Ticks" [state]="twiddleCustomTicks"></twiddle>
  <div *ngIf="twiddleCustomTicks.value">
    
    <div *ngFor="let elem of getExtraElements(); let i = index" class="element-config-container">
      <div style="display: flex;">
        <div class="capture-math-container">
          <capture-math [obj]="elem" prop="label" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()" (onChange)="rerender()"></capture-math>
        </div>
        <div (click)="elementEditDeleteExtraElement(i)" class="target-option-button"
          [class.no-pointer-events]="isReadOnly()"
        >
          <i class="fas fa-trash" aria-hidden="true"></i>
        </div>
      </div>
      
      <div>
        Position : &nbsp;
        <input type="number" [(ngModel)]="elem.xpos" class="small-input" (change)="rerender()">
      </div>
      
    </div>
    
    <button (click)="elementEditAddExtraElement()" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>Add Tick Label</span>
    </button>
    
  </div>
</div>

<br><label class="checkbox">
  <input type="checkbox" [(ngModel)]="element.config.isShowExtraElements"/>
  Add Custom Tick Labels
</label>

<hr />

<fieldset>
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm" (change)="rerender()"/>
    Show Answer
  </label>
  <br>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
    Show Advanced Options
  </label>
</fieldset>

<div class="twiddle-container" [class.error-bg-color]="isAnyPositionInvalid()">
  <twiddle caption="Targets" [state]="twiddleTargets"></twiddle>
</div>
<div *ngIf="twiddleTargets.value">
  
  <button (click)="elementEditSort()" class="button is-small has-icon">
    <span class="icon"><i class="fa fa-sort-numeric-down" aria-hidden="true"></i></span>
    <span>Sort Elements</span>
  </button>
  <br>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="element._showValuesInEditor"/>
    Show Values
  </label>
  <br><br>
  
  <div 
    *ngFor="let elem of element.elements; let i = index" 
    class="element-config-container"
  >
    <div *ngIf="isPositionIndexInvalid(i)" class="error-bg-color">
      The Position is Invalid
    </div >
    
    <div style="display: flex;">
      <div class="capture-math-container">
        <capture-math [obj]="elem" prop="positionLabel" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()" (onChange)="rerender()"></capture-math>
      </div>
      <div (click)="elementEditDeleteElement(i)" class="target-option-button"
        [class.no-pointer-events]="isReadOnly()"
      >
        <i class="fas fa-trash" aria-hidden="true"></i>
      </div>
    </div>
    
    <div *ngIf="element._showValuesInEditor" class="position-value-container">
      position : {{getPositionValue(i)}}
    </div>
    
    <div class="element-type-container">
      <button (click)="elementEditToggleElementTarget(i)" class="button is-small has-icon" [class.is-info]="isElementTarget(i)">
        <span class="icon"><i class="fa fa-square" aria-hidden="true"></i></span>
        <span>Target</span>
      </button>
      <button (click)="elementEditToggleElementOption(i)" class="button is-small has-icon" [class.is-info]="isElementOption(i)">
        <span class="icon"><i class="fa fa-hand-pointer" aria-hidden="true"></i></span>
        <span>Option</span>
      </button>
    </div>
    
    <div *ngIf="isElementTarget(i)" class="element-target-config-container">
      <label for="dropdown">Target Position:</label>
      <div *ngIf="elem.targetConfig" style="display: inline">
        <select [(ngModel)]="elem.targetConfig.layoutPosition" (change)="rerender()">
          <option *ngFor="let option of targetLayoutOptions" [value]="option.id">
            {{option.caption}}
          </option>
        </select>
      </div>
      <div *ngIf="isShowingAdvanced() && elem.targetConfig">
        y position: <input type="number" [(ngModel)]="elem.targetConfig.layoutPositionArgument" (change)="rerender()"/><br>
      </div>
    </div>
    
    <div *ngIf="isShowingAdvanced() && isElementOption(i) && elem.optionConfig" class="element-option-config-container">
      custom label: 
      <div class="capture-math-container">
        <capture-math [obj]="elem.optionConfig" prop="customLabel" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()" (change)="rerender()"></capture-math>
      </div>
    </div>
    
    
  </div>
  
  <button (click)="elementEditAddElement()" class="button is-small has-icon">
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span>Add Element</span>
  </button>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>
<!-- <twiddle caption="Ordering" [state]="twiddleOrdering"></twiddle>
<div *ngIf="twiddleOrdering.value">
  <fieldset [disabled]="isReadOnly()">
    <div class="sub-property-div">
      <div class="list" cdkDropList (cdkDropListDropped)="drop($event)">
        <div class="list-item" *ngFor="let index of element.ordering" cdkDrag>
          {{ optionTexts[index] }}
        </div>
      </div> 
    </div>
  </fieldset>
</div> -->

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    <div class="sub-property-div control">
      Home Position:
      <select [formControl]="homePositionForm">
        <option *ngFor="let option of homePositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
      <br>Target Background Colour:
      <input type="color" [(ngModel)]="element.config.targetBgColor">
      <br>Draggable Background Colour:
      <input type="color" [(ngModel)]="element.config.draggableBgColor">
      <br> Numberline Width:
      <input type="number" [(ngModel)]="element.config.numberlineWidth" class="small-input">
      <br>
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
    </div>
  </fieldset>
</div>
