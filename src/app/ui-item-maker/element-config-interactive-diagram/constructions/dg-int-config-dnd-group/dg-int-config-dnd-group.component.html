<fieldset [disabled]="isReadOnly()">
  <label class="label">Targets</label>
  <div class="list" cdkDropList (cdkDropListDropped)="dropTarget($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let target of element.dndTargets | slice:0:-1; let i = index; trackBy: trackByFn" cdkDrag>
      <div class="target-label-container">
        <span class="target-index">{{ i + 1 }}</span>
        
        <ng-container *ngIf="isTargetLabelMath(i)">
          <div class="capture-math-container">
            <capture-math 
              #targets
              [obj]="getTargetObject(i)" 
              (onChange)="validateConfig()"
              [class.is-disabled]="isReadOnly()"
              prop="label" [isManualKeyboard]="true">
            </capture-math>
          </div>
          <div
            (click)="elementEditSetTargetLabelMath(i,false)" class="target-option-button"
            [class.no-pointer-events]="isReadOnly()">
            <i class="fas fa-font" aria-hidden="true"></i>
          </div>
        </ng-container>
        <ng-container *ngIf="!isTargetLabelMath(i)">
          <textarea
            textInputTransform
            #targets
            (mousedown)="$event.stopPropagation()"
            [formControl]="dndTargetOptionsForm[i].labelForm"
            style="min-width: 10em; max-width: 20em;"
            class="textarea is-small target-option-textarea"
            cdkTextareaAutosize
            (input)="validateConfig()"
            [cdkTextareaAutosize]="true"
            [cdkAutosizeMinRows]="2"
            (focus)="setTextFocus(dndTargetOptionsForm[i].labelForm, 'target_' + i)"
          ></textarea>
          <div
            (click)="elementEditSetTargetLabelMath(i,true)" class="target-option-button"
            [class.no-pointer-events]="isReadOnly()">
            <i class="fas fa-square-root-alt" aria-hidden="true"></i>
          </div>
        </ng-container>
        
        <div (click)="elementEditDeleteTarget(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
          <i class="fas fa-trash" aria-hidden="true"></i>
        </div>
      </div>
    </div>
  </div> 
</fieldset>

<button 
  (click)="elementEditAddTarget()" 
  class="button is-small has-icon"
>
  <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
  <span>Add Target</span>
</button>

<!-- <hr/> -->

<!-- <fieldset [disabled]="isReadOnly()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm"/>
    Show Answer
  </label>
</fieldset> -->

<twiddle caption="Target Options" [state]="twiddleTargets"></twiddle>
<div *ngIf="twiddleTargets.value">
  
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div style="margin: 1em 0">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer"/>
        Accept Multiple Answers
      </label>
    </div>
    <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
      <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
      <span>Image</span>
    </button>
    <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Text</span>
    </button>
    
    <div>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
        Show Advanced Options
      </label>
    </div>
  </fieldset>
  
  <br/>
  
  <dg-int-dnd-target-options
      [dndTargets]="element.dndTargets"
      [dndTargetOptionsForm]="dndTargetOptionsForm"
      [nonUniqueOptionTexts]="nonUniqueOptionTexts"
      [isUsingReusableDraggable]="element.config.isUsingReusableDraggable"
      [isAlwaysSeparateAnswerSetup]="element._isAlwaysSeparateAnswerSetup"
      [isAllowGrouping]="true"
      [isUseImage]="element.config.isUseImage"
      [isShowAdvancedOptions]="element.isShowAdvancedOptions"
      (onChange)="validateConfig()"
    ></dg-int-dnd-target-options>
  
    <div style="margin-top: 1em; background-color: #eee; padding: 1em; border-radius: 0.5em;">
      <span>The item is considered to be answered if:</span>
      <div class="select">
        <select [(ngModel)]="element.dndIsFilledMode" (change)="validateConfig()">
          <option *ngFor="let option of dndIsFilledModeOptions" [value]="option.id">
            <span *ngIf="option.id == 'auto'">Auto ({{dndIsFilledCaptionMap[element._autoDnDIsFilledMode]}})</span>
            <span *ngIf="option.id != 'auto'">{{option.caption}}</span>
          </option>
        </select>
      </div>
    </div>
    
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle 
    *ngIf="element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup"
    caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value 
    && (element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup)
    && element.altAnswers">
      
  <div style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
      Accept Multiple Answers
    </label>
  </div>
  <dg-int-dnd-answers
    [defaultAnswerSet]="defaultAnswerSet"
    [altAnswers]="element.altAnswers"
    [isShowDefaultAnswerSet]="!this.element.config.isUsingReusableDraggable && !this.element._isAlwaysSeparateAnswerSetup"
    [isAllowMultipleAnswer]="element.config.isAllowMultipleAnswer"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    [targetIdList]="targetIdList"
    [optionIdList]="sortedOptionIdList"
    [isAllowGrouping]="true"
    (onChange)="validateConfig()"
  ></dg-int-dnd-answers>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()" style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isNoDraggableBackground"/>
      No Draggable Background
    </label>
  </fieldset>
  <fieldset style="margin-bottom: 0.5em" (change)="rerender()">
    <span>Option Width:</span>
    <input type="number" [(ngModel)]="element.config.optionFixedWidth" class="small-input">
    <br>
  </fieldset>
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br><label class="checkbox">
        <input type="checkbox" [formControl]="isUsingReusableDraggableForm"/>
        Reusable Draggables
      </label>
      <br>
      <br> Maximum Total Width:
      <input type="number" [(ngModel)]="element.config.totalMaxWidth" class="small-input">
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Minimum Target Width:
      <input type="number" [(ngModel)]="element.config.boxMinWidth" class="small-input">
      <br> Minimum Target Height:
      <input type="number" [(ngModel)]="element.config.boxMinHeight" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
      <br>
      <br>
      <label>Diagram Padding</label>
      <br> top:
      <input type="number" [(ngModel)]="element.diagramPadding.top" class="small-input">
      <br> bottom:
      <input type="number" [(ngModel)]="element.diagramPadding.bottom" class="small-input">
      <br> left:
      <input type="number" [(ngModel)]="element.diagramPadding.left" class="small-input">
      <br> right:
      <input type="number" [(ngModel)]="element.diagramPadding.right" class="small-input">
      <br>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element._isAlwaysSeparateAnswerSetup" (change)="validateConfig()"/>
        Always Separate Answer Setup
      </label>
    </div>
  </fieldset>
</div>
