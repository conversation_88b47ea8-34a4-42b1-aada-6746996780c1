import { Component, ElementRef, Input, OnInit, QueryList, ViewChildren } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { IDgIntElementDnDGroup, resolveDnDGroupTargets } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-group';
import { IDgIntElementDnDTarget, isUnusedId, generateDefaultAnswerSet, IDgIntElementDnDAnswerSet, DND_OPTION_TEXT_MODE, IDgIntElementDnDOption, unusedId, dndIsFilledModeOptions, dndIsFilledModeCaptionMap } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-config-dnd-group',
  templateUrl: './dg-int-config-dnd-group.component.html',
  styleUrls: ['./dg-int-config-dnd-group.component.scss']
})
export class ElementConfigDgIntDndGroupComponent implements OnInit {
  @Input() element: IDgIntElementDnDGroup;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChildren('targets') targets: QueryList<ElementRef>;

  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  homeIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  isShowAnswerForm = new FormControl(false);
  isUsingReusableDraggableForm: FormControl;
  dndIsFilledModeOptions = dndIsFilledModeOptions;
  dndIsFilledCaptionMap = dndIsFilledModeCaptionMap;
  
  public dndTargetOptionsForm : {label:string, labelForm:FormControl, content:FormControl[]}[] = [];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ) {}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    this.isUsingReusableDraggableForm = new FormControl(this.element.config.isUsingReusableDraggable);
    this.isUsingReusableDraggableForm.valueChanges.subscribe(() => {
      if (this.isUsingReusableDraggableForm.value) {
        const confirm = window.confirm('Are you sure you want to enable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(false, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = true;
      } else {
        const confirm = window.confirm('Are you sure you want to disable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(true, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = false;
      }
      this.validateConfig();
    });
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      
      let labelForm = new FormControl(target.label);
      labelForm.valueChanges.subscribe(obs => {
        target.label = labelForm.value;
        this.validateConfig();
      });
      
      this.dndTargetOptionsForm.push({
        label: target.label,
        labelForm,
        content: optionsForm
      })
    });
  }
  
  isAllowDeleteTarget(targetIndex: number) : boolean {
    return !this.isUnusedTarget(this.element.dndTargets[targetIndex]);
  }
  isTargetLabelMath(targetIndex: number): boolean {
    const target = this.element.dndTargets[targetIndex];
    return target.isLabelMath;
  }
  getTargetObject(targetIndex: number): IDgIntElementDnDTarget {
    return this.element.dndTargets[targetIndex];
  }
  
  elementEditDeleteTarget(targetIndex: number) {
    const targetLabel = this.element.dndTargets[targetIndex].label;
    const confirm = window.confirm(`Are you sure you want to delete this target? (${targetLabel})`);
    if (!confirm) return;
    this.element.dndTargets.splice(targetIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddTarget(){
    this.element.dndTargets.push({
      label: "",
      content: []
    });
    this.validateConfig();
  }
  
  elementEditSetOptionTextMode(targetIndex: number, optionIndex: number, mode: DND_OPTION_TEXT_MODE) {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    option.textMode = mode;
    this.updateForms();
  }
  elementEditSetTargetLabelMath(targetIndex: number, isMath: boolean) {
    console.log("setTargetLabelMath", targetIndex, isMath);
    const target = this.element.dndTargets[targetIndex];
    target.isLabelMath = isMath;
    this.rerender();
  }
  
  getOptionTextMode(targetIndex: number, optionIndex: number) : DND_OPTION_TEXT_MODE {
    const target = this.element.dndTargets[targetIndex];
    const option = target.content[optionIndex];
    return option.textMode ?? DND_OPTION_TEXT_MODE.TEXT;
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to image mode?");
      if (!confirm) return;
      this.element.config.isUseImage = true;
    } else {
      const confirm = window.confirm("Are you sure you want to change to text mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let target of this.element.dndTargets){
        for (let option of target.content){
          delete option.image;
          delete option.imageLabelPosition;
        }
      }
    }
    this.validateConfig();
  }
  
  isUseImage() : boolean {
    return this.element.config.isUseImage ?? false;
  }
  
  isUnusedTarget(target:IDgIntElementDnDTarget){
    return isUnusedId(target.label);
  }
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return true;
    return !this.optionIdList.includes(optionId);
  }
  
  validateConfig() {
    this.resolveData();
    this.updateForms();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  updateForms() {
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
  }
  
  resolveData() {
    resolveDnDGroupTargets(this.element);
  }
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  dropTarget(event: CdkDragDrop<number[]>) {
    moveItemInArray(this.element.dndTargets, event.previousIndex, event.currentIndex);
    this.validateConfig();
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        this.idToUrlMap.set(option.id, option.image?.url);
        if (option.textMode == DND_OPTION_TEXT_MODE.MATH) this.idIsMathSet.add(option.id);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string) : string {
    return this.idToUrlMap.get(id);
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
    getTextElementReference(text_element: string) {
      const elements = {}
  
      this.element.dndTargets.forEach((target, idx) => {
        elements[`target_${idx}`] = this.targets.toArray()[idx];
      })
  
      return elements[text_element];
    }
  
    setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
      setTextFocusForDnd(
        this.specialKeyboard,
        this.element,
        this.getTextElementReference(htmlElement).nativeElement,
        cell
      );
    }
}
