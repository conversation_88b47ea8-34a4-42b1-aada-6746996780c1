<fieldset [disabled]="isReadOnly()">
  <div style="margin-bottom: 1em">
    <button (click)="setListView()" class="button is-small has-icon" [class.is-info]="element.config.isUseArrayEditor">
      <span class="icon"><i class="fas fa-list" aria-hidden="true"></i></span>
      <span>List Editor View</span>
    </button>
    <button (click)="setInlineView()" class="button is-small has-icon" [class.is-info]="!element.config.isUseArrayEditor">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Inline Editor View</span>
    </button>
  </div>
</fieldset>

<div *ngIf="!element.config.isUseArrayEditor">
  <div style="padding-bottom: 0.5em;">
    <textarea
      textInputTransform
      #inlineEditor
      [formControl]="textContentForm"
      class="textarea"
      cdkTextareaAutosize
      [cdkAutosizeMinRows]="3"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (click)="updateCursorPosition($event)"
      (keyup)="updateCursorPosition($event)"
      (select)="updateCursorPosition($event)"
      (focus)="setTextFocus(textContentForm, 'inlineEditor')"
    ></textarea>
  </div>
  
  <div>
    <button 
      (click)="newTargetText()" 
      (mouseenter)="setNewTargetButtonHoverState(true)"
      (mouseleave)="setNewTargetButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocus"
    >
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>Add New Target</span>
    </button>
  </div>
  
</div>

<div *ngIf="element.config.isUseArrayEditor">
  
  <div class="list" cdkDropList (cdkDropListDropped)="dropContentArray($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let content of element.contentArray; let i = index;" cdkDrag>
      
      <div class="space-between" style="align-items: flex-start; gap: 0.2em">
        
        <div [ngSwitch]="content.type">
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.TEXT">
            <textarea 
              textInputTransform
              #contentArray
              (mousedown)="$event.stopPropagation()"
              [formControl]="contentFormArray.controls[i]"
              class="textarea"
              cdkTextareaAutosize
              [cdkAutosizeMinRows]="3"
              (focus)="setTextFocus(contentFormArray.controls[i], 'content_' + i)"
            ></textarea>
          </div>
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.TARGET">
            <div>
              <span><b>Target</b></span>
              <input 
                class="input is-small"
                (mousedown)="$event.stopPropagation()"
                #contentArray
                [formControl]="contentFormArray.controls[i]" 
                type="text"
              />
            </div>
          </div>
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.MATH">
            <div class="capture-math-container" style="min-width: 10em" (mousedown)="$event.stopPropagation()">
              <capture-math 
                #contentArray
                [obj]="content" 
                (onChange)="validateConfig()"
                [class.is-disabled]="isReadOnly()"
                prop="value" [isManualKeyboard]="true">
              </capture-math>
            </div>
          </div>
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.BOOKMARK_LINK">
            <span><b>Bookmark Link</b></span>
            <div class="list-editor-bookmark-row">
              <span>Caption</span>
              <input type="text" class="input is-small" [(ngModel)]="content.value" (input)="validateConfig()" (mousedown)="$event.stopPropagation()">
            </div>
            <div class="list-editor-bookmark-row">
              <span>Bookmark ID</span>
              <input type="text" class="input is-small" [(ngModel)]="content.bookmarkId" (change)="validateConfig()" (mousedown)="$event.stopPropagation()">
            </div>
            <div class="list-editor-bookmark-row">
              <span>Target Item Label</span>
              <input type="text" class="input is-small" [(ngModel)]="content.targetItemLabel" (change)="validateConfig()" (mousedown)="$event.stopPropagation()">
            </div>
          </div>
        </div>
        
        <div (click)="elementEditDeleteContentArray(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
          <i class="fas fa-trash" aria-hidden="true"></i>
        </div>
      </div>
      
    </div>
  </div>
  
  <div class="field has-addons">
    <div class="control select is-fullwidth">
      <select [formControl]="contentArrayTypeForm">
        <option *ngFor="let option of contentArrayTypeOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </div>
    <div class="control">
      <button class="button is-primary" (click)="elementEditAddContentArray()">Add</button>
    </div>
  </div>
  
</div>

<hr />

<fieldset [disabled]="isReadOnly()" (change)="rerender()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm"/>
    Show Answer
  </label>
</fieldset>


<twiddle caption="Target Options" [state]="twiddleTargets"></twiddle>
<div *ngIf="twiddleTargets.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    
    <div style="margin-bottom: 1em;">
      <ng-container *ngIf="!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer"/>
        Accept Multiple Answers
      </label><br>
      </ng-container>
      <label class="checkbox">
        <input type="checkbox" [formControl]="isUsingReusableDraggableForm"/>
        Reusable Draggables
      </label><br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isUsingAnswerGroup"/>
        Answer Group
      </label>
      <div *ngIf="element.config.isUsingAnswerGroup" class="sub-property-div" style="margin-top: 0; color: #888">
        <span class="icon"><i class="fas fa-info-circle" aria-hidden="true"></i></span>
        Answer Group is based on Home Layout
      </div>
    </div >
    
    <dg-int-dnd-target-options
      [dndTargets]="element.dndTargets"
      [dndTargetOptionsForm]="dndTargetOptionsForm"
      [nonUniqueOptionTexts]="nonUniqueOptionTexts"
      [isUsingReusableDraggable]="element.config.isUsingReusableDraggable"
      [isAlwaysSeparateAnswerSetup]="element._isAlwaysSeparateAnswerSetup"
      [isShowAdvancedOptions]="element.isShowAdvancedOptions"
      [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
      [isUsingAnswerGroup]="element.config.isUsingAnswerGroup"
      [answerGroupCount]="element.homeConfig?.element?.length"
      (onSetIsAllowEmptyTarget)="element.config.isAllowEmptyTarget = $event"
      (onChange)="validateConfig()"
    ></dg-int-dnd-target-options>
  </fieldset>
  
  <div style="margin-top: 1em; background-color: #eee; padding: 1em; border-radius: 0.5em;">
    <span>The item is considered to be answered if:</span>
    <div class="select">
      <select [(ngModel)]="element.dndIsFilledMode" (change)="validateConfig()">
        <option *ngFor="let option of dndIsFilledModeOptions" [value]="option.id">
          <span *ngIf="option.id == 'auto'">Auto ({{dndIsFilledCaptionMap[element._autoDnDIsFilledMode]}})</span>
          <span *ngIf="option.id != 'auto'">{{option.caption}}</span>
        </option>
      </select>
    </div>
  </div>
  
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle 
    *ngIf="element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup"
    caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value 
    && (element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup)
    && element.altAnswers">
  <div style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
      Accept Multiple Answers
    </label>
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowEmptyTarget" (change)="rerender()"/>
      Allow empty target
    </label>
  </div>
  <dg-int-dnd-answers
    [defaultAnswerSet]="defaultAnswerSet"
    [altAnswers]="element.altAnswers"
    [isShowDefaultAnswerSet]="!this.element.config.isUsingReusableDraggable && !this.element._isAlwaysSeparateAnswerSetup"
    [isAllowMultipleAnswer]="element.config.isAllowMultipleAnswer"
    [isAllowEmptyTarget]="element.config.isAllowEmptyTarget"
    [idToLabelMap]="idToLabelMap"
    [idIsMathSet]="idIsMathSet"
    [targetIdList]="targetIdList"
    [optionIdList]="sortedOptionIdList"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [targets]="element.dndTargets"
    (onChange)="validateConfig()"
  ></dg-int-dnd-answers>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <div style="margin-bottom: 0.5em;">
    <span>Content Justify: </span>
    <select [(ngModel)]="element.config.contentJustify" (change)="validateConfig()">
      <option *ngFor="let option of contentJustifyOptions" [value]="option.id">{{option.caption}}</option>
    </select>
  </div>
  
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isUniformLineSpacing"/>
        Uniform line spacing
      </label>
      <br>
      
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
        
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.maxHomeWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
        
      <br>
      <br>
      <label>Diagram Padding</label>
      <br> top:
      <input type="number" [(ngModel)]="element.diagramPadding.top" class="small-input">
      <br> bottom:
      <input type="number" [(ngModel)]="element.diagramPadding.bottom" class="small-input">
      <br> left:
      <input type="number" [(ngModel)]="element.diagramPadding.left" class="small-input">
      <br> right:
      <input type="number" [(ngModel)]="element.diagramPadding.right" class="small-input">
      <br>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowNonScoringTarget" (change)="validateConfig()"/>
        Allow Non Scoring Target
      </label>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element._isAlwaysSeparateAnswerSetup" (change)="validateConfig()"/>
        Always Separate Answer Setup
      </label>
    </div>
  </fieldset>
</div>
