<fieldset [disabled]="isReadOnly()">
  <label class="label">Background Image</label>
  <div class="background-config-container">
    <capture-image [element]="getBackgroundImageElement()" (change)="rerender()"></capture-image>
    <asset-library-link [element]="getBackgroundImageElement()"></asset-library-link>     
  </div >
</fieldset>

<fieldset style="margin: 1em 0" [disabled]="isReadOnly()">
  <label class="label">Label</label>
  <div style="margin-top: 0.5em">
    <label>Label: </label> 
    <textarea
      textInputTransform
      #label
      [formControl]="labelForm"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em"
      class="textarea is-small target-option-textarea"
      cdkTextareaAutosize
      (input)="rerender()"
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(labelForm, 'label')"
    ></textarea>
    <span>Label Position:</span>
    <select [(ngModel)]="element.label.position" (change)="rerender()">
      <option *ngFor="let option of homeLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
    </select>
  </div>
</fieldset>

<hr/>

<twiddle caption="Hotspots" [state]="twiddleHotspots"></twiddle>
<div *ngIf="twiddleHotspots.value" cdkDropList (cdkDropListDropped)="dropHotspotOrdering($event)" style="cursor: move">
  
  <div *ngFor="let hotspot of element.hotspots; let i = index; trackBy: trackByFn" cdkDrag>
    <div 
      class="hotspot-config-container flex-container"
      (mouseenter)="setHoveredHotspot(i)"
      (mouseleave)="resetHoveredHotspot()"
    >
      <div class="letter-index">
        {{ numberToAlphabet(i) }}
      </div>
      <div style="flex-grow: 1;">
        <ng-container *ngIf="!isEditIndex(i)">
          <a [class.is-disabled]="isReadOnly()" class="button" (click)="beginEditHotspotBBox(i)">
            <i class="fas fa-edit" aria-hidden="true"></i>
            <span class="button-caption">Edit</span>
          </a>
          
          <a [class.is-disabled]="isReadOnly()" class="button" (click)="elementEditDuplicateHotspot(i)">
            <i class="fa fa-clone" aria-hidden="true"></i>
            <span class="button-caption">Duplicate</span>
          </a>
        </ng-container>
        <ng-container *ngIf="isEditIndex(i)">
          
          <ng-container *ngIf="editType == polygonEditType.VERTEX">
            <label class="checkbox">
              <input type="checkbox" [(ngModel)]="isAllowAddNewVertex" (change)="updateEditHotspot(i)"/>
              Allow Add New Vertex
            </label>
            <a [class.is-disabled]="isReadOnly()" class="button" (click)="beginEditHotspotBBox(i)">
              <i class="fas fa-vector-square" aria-hidden="true"></i>
              <span class="button-caption">Edit BBox</span>
            </a>
          </ng-container>
          
          <ng-container *ngIf="editType == polygonEditType.BBOX">
            <label class="checkbox">
              <input type="checkbox" [(ngModel)]="isLockAspectRatio" (change)="updateEditHotspot(i)"/>
              Lock Aspect Ratio
            </label>
            <dropdown-select 
              [list]="polygonPresetShapeOptions" 
              (onSelect)="elementEditLoadPresetShape($event, i)"
              placeholder="Load Shape"
            ></dropdown-select>
            <a [class.is-disabled]="isReadOnly()" class="button" (click)="beginEditHotspotVertex(i)">
              <i class="fas fa-draw-polygon" aria-hidden="true"></i>
              <span class="button-caption">Edit Vertices</span>
            </a>
          </ng-container>
          
          <a [class.is-disabled]="isReadOnly()" class="button" (click)="endEditHotspot()">
            <i class="fas fa-check" aria-hidden="true"></i>
            <span class="button-caption">Done</span>
          </a>
        </ng-container>
        <ng-container *ngIf="element.isShowAdvancedOptions">
          <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em; margin-top: 0.5em">
            <div>X: </div>
            <input 
                type="number" 
                class="input is-small" 
                style="width:5em; text-align:center" 
                step="0.01"
                [disabled]="isEditIndex(i)"
                (ngModelChange)="moveToPositionX(i, $event)"
                [ngModel]="element.hotspots[i].position.x"
            >
            <div>Y: </div>
            <input 
                type="number" 
                class="input is-small" 
                style="width:5em; text-align:center" 
                step="0.01"
                [disabled]="isEditIndex(i)"
                (ngModelChange)="moveToPositionY(i, $event)"
                [ngModel]="element.hotspots[i].position.y"
            >
          </div>
        </ng-container>
        
      </div>
      
      <div class="option-container-buttons">
        <a 
          class="button is-correct-toggle"
          [class.is-disabled]="isReadOnly()"
          [class.is-success] = "isOptionCorrect(i)"
          [class.is-danger]  = "!isOptionCorrect(i)"
          (click)="toggleOptionCorrect(i)"
        >
          <i 
            class="fa fa-check" 
            [class.fa-check] = "isOptionCorrect(i)"
            [class.fa-times] = "!isOptionCorrect(i)"
            aria-hidden="true"
          ></i>
        </a>
      </div>
      
      <div class="option-container-buttons">
        <a [class.is-disabled]="isReadOnly()" class="button is-expanded" (click)="elementEditDeleteHotspot(i)">
          <i class="fas fa-trash" aria-hidden="true"></i>
        </a>

      </div>
      
    </div>
  </div>
  
  <button 
    (click)="elementEditAddHotspot()" 
    class="button is-small has-icon"
  >
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span>Add Hotspot</span>
  </button>
  
  <div>
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
      Show Advanced Options
    </label>
  </div>
  
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value" style="padding-left: 1em;">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
  <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; grid-gap: 0.5em;">
    <span class="label">State</span>
    <span class="label">Colour</span>
    <span class="label">Opacity</span>
    
    <span>Off</span>
    <input type="color" [(ngModel)]="element.styleConfig.normal.off.color">
    <input type="number" min="0" max="1" step="0.01" [(ngModel)]="element.styleConfig.normal.off.opacity">
    
    <span>On</span>
    <input type="color" [(ngModel)]="element.styleConfig.normal.on.color">
    <input type="number" min="0" max="1" step="0.01" [(ngModel)]="element.styleConfig.normal.on.opacity">
      
    <span>Off (hover)</span>
    <div>
      <input *ngIf="element.styleConfig.isSeparateHoverColor" 
        type="color" [(ngModel)]="element.styleConfig.hover.off.color">
    </div>
    <input type="number" min="0" max="1" step="0.01" [(ngModel)]="element.styleConfig.hover.off.opacity">
      
    <span>On (hover)</span>
    <div>
      <input *ngIf="element.styleConfig.isSeparateHoverColor"
        type="color" [(ngModel)]="element.styleConfig.hover.on.color">
    </div>
    <input type="number" min="0" max="1" step="0.01" [(ngModel)]="element.styleConfig.hover.on.opacity">
  </div>
  
  
  <br>
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="element.styleConfig.isSeparateHoverColor">
    <span>Separate Hover Colour</span>
  </label>
  </fieldset>
  
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    <div class="sub-property-div control">
      
      <span>Mode : </span>
      <select [(ngModel)]="element.mode">
        <option *ngFor="let option of mcqModeOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
      
      <ng-container *ngIf="element.mode == mcqMode.MCQ">
        <label class="checkbox">
          <input type="checkbox" [(ngModel)]="element.isSelectToggle">
          <span>Allow Unselection? (toggle)</span>
        </label>
      </ng-container>
      
      <ng-container *ngIf="element.mode == mcqMode.MSEL">
        <br><span>Max number of selections : </span>
        <input type="number" min="1" max="100" step="1" [(ngModel)]="element.maxNumberOfSelection">
      </ng-container>
      
      <br>
      <br> Score Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      
    </div>
  </fieldset>
</div>
