import { Component, ElementRef, Input, OnInit, QueryList, Renderer2, ViewChild, ViewChildren } from '@angular/core';

import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { 
  IDgIntElementDnDFreeform, clientPos_to_svgPos, resolveDnDFreeformTargets
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-freeform';
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID,
  IDgIntElementDnDAnswerSet,
  generateDefaultAnswerSet,
  isUnusedId,
  DND_OPTION_TEXT_MODE,
  homeLabelPositionOptions,
  HOME_LABEL_POSITION,
  unusedId,
  diagramLabelPositionOptions,
  dndIsFilledModeOptions,
  dndIsFilledModeCaptionMap,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { contentJustifyOptions, flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';

@Component({
  selector: 'dg-int-config-dnd-freeform',
  templateUrl: './dg-int-config-dnd-freeform.component.html',
  styleUrls: ['./dg-int-config-dnd-freeform.component.scss']
})
export class ElementConfigDgIntDndFreeformComponent implements OnInit {
  @Input() element: IDgIntElementDnDFreeform;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChild('label') label: ElementRef;
  @ViewChildren('targets') targets: QueryList<ElementRef>;

  positionPrecision = 2;
  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  homeIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  
  homeLabelPositionOptions = homeLabelPositionOptions;
  diagramLabelPositionOptions = diagramLabelPositionOptions;
  contentJustifyOptions = contentJustifyOptions;
  dndIsFilledModeOptions = dndIsFilledModeOptions;
  dndIsFilledCaptionMap = dndIsFilledModeCaptionMap;
  // isToggleButtonHovered = false;
  isShowAnswerForm = new FormControl(false);
  isUsingReusableDraggableForm: FormControl;
  labelForm = new FormControl("");
  movingTargetIndex = undefined;
  private listener: Function;
  private removeGuidebox: Function;
  
  // public tableForm = [];
  public dndTargetOptionsForm : {label:string, content:FormControl[]}[] = [];
  public dndTargetLabelForm : FormControl[];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private renderer: Renderer2,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ){}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    this.isUsingReusableDraggableForm = new FormControl(this.element.config.isUsingReusableDraggable);
    this.isUsingReusableDraggableForm.valueChanges.subscribe(() => {
      if (this.isUsingReusableDraggableForm.value) {
        const confirm = window.confirm('Are you sure you want to enable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(false, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = true;
      } else {
        const confirm = window.confirm('Are you sure you want to disable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(true, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = false;
      }
      this.validateConfig();
    });

    bindFormControls(this.element.label, [
      {f: this.labelForm, p: 'text'}, 
    ]);
  }
  
  ngOnDestroy() {
    if (this.listener) this.listener();
    if (this.removeGuidebox) this.removeGuidebox();
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  getBackgroundImageElement(): IContentElementImage {
    if (this.element.backgroundImage == undefined)
      this.element.backgroundImage = {};
    if (this.element.backgroundImage.image == undefined) {
      this.element.backgroundImage.image = createDefaultElement(ElementType.IMAGE);
    }
    return this.element.backgroundImage.image as IContentElementImage;
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }

  /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      label: this.label,
    }

    this.element.dndTargets.forEach((target, idx) => {
      elements[`target_${idx}`] = this.targets.toArray()[idx];
    })

    return elements[text_element];
  }

  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }
  
  
  initDndTargetLabelForm(){
    this.dndTargetLabelForm = this.element.targetData.map((targetData) => {
      let labelForm = new FormControl(targetData.label);
      labelForm.valueChanges.subscribe(obs => {
        targetData.label = labelForm.value;
        this.validateConfig();
      });
      return labelForm;
    })
  }
  initDndTargetOptionsForm(){
    let dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      
      dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm
      })
    });
    this.dndTargetOptionsForm = dndTargetOptionsForm;
  }
  
  createGuideBox(width: number, height: number) {
    if (this.removeGuidebox) this.removeGuidebox();
    
    let guideBox: HTMLDivElement = this.renderer.createElement('div');
    guideBox.style.width = width + 'px';
    guideBox.style.height = height + 'px';
    guideBox.style.backgroundColor = 'rgba(0, 0, 0, 0)';
    guideBox.style.left = '0';
    guideBox.style.top = '0';
    guideBox.style.transform = 'translate(-50%, -50%)';
    guideBox.style.visibility = 'hidden';
    guideBox.style.position = 'absolute';
    guideBox.style.zIndex = '2000';
    guideBox.style.pointerEvents = 'none';
    guideBox.style.border = '1px dashed black';
    document.body.appendChild(guideBox);
    
    // guideBox will follow the mouse
    let listener = this.renderer.listen('document', 'mousemove', (event) => {
      requestAnimationFrame(() => {
        guideBox.style.visibility = 'visible';
        guideBox.style.left = event.clientX + 'px';
        guideBox.style.top = event.clientY + 'px';
      });
    });
    let removeGuidebox = () => {
      guideBox.remove();
      listener();
    };
    this.removeGuidebox = removeGuidebox;
  }
  
  getTargetSize() : [number,number] {
    const dgSharedObject = this.sharedObjectMap.get(this.element) as IDgIntSharedObject;
    const svgElement = dgSharedObject?.svgElement;
    if (!svgElement) return [0,0];
    
    const svgDnD = svgElement.querySelector("svg[meta=dnd_svg]");
    if (!svgDnD) return [0,0];
    
    let containerSvg = svgDnD.querySelectorAll(".diagramatics-dnd-container");
    if (containerSvg.length == 0) return [0,0];
    
    let containerSizes = [];
    containerSvg.forEach((container) => {
      const boundingClient = (container as SVGGElement).getBoundingClientRect();
      containerSizes.push([boundingClient.width, boundingClient.height]);
    })
    
    const minWidth = Math.min(...containerSizes.map((size) => size[0]));
    const minHeight = Math.min(...containerSizes.map((size) => size[1])); 
    return [minWidth, minHeight];
  }
  
  
  requestMoveTarget(ev: MouseEvent, targetIndex: number) {
    const buttonElement = ev.target;
    if (this.listener) this.listener();
    
    const targetSize = this.getTargetSize();
    this.createGuideBox(targetSize[0], targetSize[1]);
    this.movingTargetIndex = targetIndex;
    
    let cancelListener: CallableFunction;
    let cleanup = () => {
      cancelListener();
      if (this.removeGuidebox) this.removeGuidebox();
      if (this.listener) this.listener();
      this.movingTargetIndex = undefined;
    }
    
    cancelListener  = this.renderer.listen('document', 'contextmenu', (event) => {
      event.preventDefault();
      cleanup();
    });
    this.listener = this.renderer.listen('document', 'click', (event) => {
      if (event.target == buttonElement) return;
      cleanup();
      this.elementEditMoveTarget(event, targetIndex);
    });
  }
  
  isMovingTarget(targetIndex: number) : boolean {
    return this.movingTargetIndex == targetIndex;
  }
  
  isPointInsideBox(point: {x: number, y: number}, box: {x: number, y: number, width: number, height: number}) : boolean {
    if (point.x < box.x) return false;
    if (point.x > box.x + box.width) return false;
    if (point.y < box.y) return false;
    if (point.y > box.y + box.height) return false;
    return true;
  }
  
  elementEditMoveTarget(ev: MouseEvent, targetIndex: number) {
    const dgSharedObject = this.sharedObjectMap.get(this.element) as IDgIntSharedObject;
    const svgElement = dgSharedObject?.svgElement?.children?.[0];
    if (!svgElement) return;
    
    const clientPos = {
      x : ev.clientX,
      y : ev.clientY
    };
    const svgPos = clientPos_to_svgPos(clientPos, svgElement as SVGSVGElement);
    const svgBBox = (svgElement as SVGSVGElement).getBBox();
    
    if (!this.isPointInsideBox(svgPos, svgBBox)) {
      const confirm = window.confirm("The new position is outside the Diagram. Do you want to move it anyway?");
      if (!confirm) return;
    }
    
    const target = this.element.targetData[targetIndex];
    const roundedX = this.roundTo(svgPos.x, this.positionPrecision);
    const roundedY = this.roundTo(svgPos.y, this.positionPrecision);
    target.position.x = roundedX;
    target.position.y = roundedY;
    this.validateConfig();
  }
  
  isUseImage(): boolean {
    return this.element.config.isUseImage ?? false;
  }
  
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        if (targetData.optionIds.length == 0) return !this.element.config.isAllowEmptyTarget;
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return !this.element.config.isAllowEmptyTarget;
    return !this.optionIdList.includes(optionId);
  }
  
  setHoveredTarget(targetIndex: number) {
    this.element._hoveredTargetIndex = targetIndex;
    this.rerender();
  }
  resetHoveredTarget() {
    delete this.element._hoveredTargetIndex;
    this.rerender();
  }
  
  elementEditDeleteTarget(targetIndex: number) {
    const targetLabel = this.element.targetData[targetIndex].label;
    const confirm = window.confirm(`Are you sure you want to delete this target? (${targetLabel})`);
    if (!confirm) return;
    this.element.targetData.splice(targetIndex, 1);
    this.validateConfig();
  }
  
  elementEditAddTarget(){
    this.element.targetData.push({
      label: this.getNewTargetLabel(),
      position: {
        x: 0,
        y: 0,
      }
    });
    this.validateConfig();
  }
  getNewTargetLabel(): string {
    const targetLabels = this.element.dndTargets.map(target => target.label);
    // get all target labels in the form `target{number}`
    const standardTargetLabels = targetLabels.filter(label => label.match(/^target\d+$/));
    // get only the numbers
    const targetNumbers = standardTargetLabels.map(label => parseInt(label.slice(6)));
    const maxNumber = Math.max(...targetNumbers,0);
    return `target${maxNumber+1}`;
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to image mode?");
      if (!confirm) return;
      this.element.config.isUseImage = true;
    } else {
      const confirm = window.confirm("Are you sure you want to change to text mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let target of this.element.dndTargets){
        for (let option of target.content){
          delete option.image;
          delete option.imageLabelPosition;
        }
      }
    }
    this.validateConfig();
  }
  
  roundTo(value: number, precision: number) {
    return Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision);
  }
  
  validateConfig() {
    this.resolveData();
    this.initDndTargetOptionsForm();   
    this.initDndTargetLabelForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter++;
  }
  
  resolveData() {
    resolveDnDFreeformTargets(this.element);
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        this.idToUrlMap.set(option.id, option.image?.url);
        if (option.textMode == DND_OPTION_TEXT_MODE.MATH) this.idIsMathSet.add(option.id);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string) : string {
    return this.idToUrlMap.get(id);
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
}
