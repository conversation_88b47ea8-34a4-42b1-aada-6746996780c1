import { StyleprofileService, StylingProcess } from 'src/app/core/styleprofile.service';
import { DgIntConstructionType, DgIntConstructionTypeCaption } from 'src/app/ui-testrunner/element-render-interactive-diagram/common';
import { IContentElementInteractiveDiagram } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import type { IScriptGenMeta } from '../../script-gen.service';
import { 
  IDgIntElementDnDTable, IDgIntElementDnDTableCell,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-table';
import { LangService } from 'src/app/core/lang.service';
import { DND_OPTION_TEXT_MODE, IDgIntElementDnDAnswerSet, IDgIntElementDnDOption, IDgIntElementDnDTarget, IDgIntHomeConfig, isUnusedId } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { IDgIntElementDnDInline } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-inline';
import { IDgIntElementDnDGroup } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-group';
import { IDgIntElementDnDFreeform } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-freeform';
import { IDgIntElementDnDVenn } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-venn';
import { IDgIntElementDnDNumberline, IDgIntElementDnDNumberlineElement } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-numberline';
import { IDgIntElementDnDTally, IDgIntElementDnDTallyOption, IDgIntElementDnDTallyTarget } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-tally';
import { IDgIntElementDnDSorting, IDgIntElementDnDSortingOption } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-sorting';
import { IDgIntElementDnDClozeMath } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-cloze-math';
import { IDgIntElementMcqHotspot } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/mcq-hotspot';
import { IContentElementDynamicImage } from 'src/app/ui-testrunner/element-render-image/model';

export type InteractiveDiagramScriptGenContext = {
  lang: string,
  meta: IScriptGenMeta,
  preProcesses: StylingProcess[],
  profile: StyleprofileService,
  langService: LangService,
  processPlainTextScript: (str:string, lang:string, preProcesses: StylingProcess[]) => string,
  extractScriptFromLatex: (latex:string, lang:string) => string,
  extractScriptFromImageNode: (node:IContentElementDynamicImage, lang:string, meta:IScriptGenMeta, isUploadVoiceover?:boolean) => string,
  getNumberedOrder: (node: any, script: string[], index: number, isLast: boolean, lang: string) => void,
  useScriptDecision: (object: any, meta: IScriptGenMeta, script: string, voiceoverProp?: string) => void,
  uploadNewVoice: (script:string, element:any, lang:string, fromItemVoiceOver?:boolean) => void,
}

const optionLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('')

export function extractScriptFromInteractiveDiagram(
  node: IContentElementInteractiveDiagram,
  ctx: InteractiveDiagramScriptGenContext
) {
  switch (node.constructionType) {
    case DgIntConstructionType.DND_TABLE:
      return extractScriptFromDnDTable(node.constructionElement as IDgIntElementDnDTable, ctx);
    case DgIntConstructionType.DND_INLINE:
      return extractScriptFromDnDInline(node.constructionElement as IDgIntElementDnDInline, ctx);
    case DgIntConstructionType.DND_GROUP:
      return extractScriptFromDnDGroup(node.constructionElement as IDgIntElementDnDGroup, ctx);
    case DgIntConstructionType.DND_FREEFORM:
      return extractScriptFromDndFreeform(node.constructionElement as IDgIntElementDnDFreeform, ctx);
    case DgIntConstructionType.DND_VENN:
      return extractScriptFromDndVenn(node.constructionElement as IDgIntElementDnDVenn, ctx);
    case DgIntConstructionType.DND_NUMBERLINE:
      return extractScriptFromDndNumberline(node.constructionElement as IDgIntElementDnDNumberline, ctx);
    case DgIntConstructionType.DND_TALLY:
      return extractScriptFromDndTally(node.constructionElement as IDgIntElementDnDTally, ctx);
    case DgIntConstructionType.DND_SORTING:
      return extractScriptFromDndSorting(node.constructionElement as IDgIntElementDnDSorting, ctx);
    case DgIntConstructionType.DND_CLOZE_MATH:
      return extractScriptFromDndClozeMath(node.constructionElement as IDgIntElementDnDClozeMath, ctx);
    case DgIntConstructionType.DND_INLINE_2:
      return extractScriptFromDnDInline(node.constructionElement as IDgIntElementDnDInline, ctx);
    case DgIntConstructionType.MCQ_HOTSPOT:
      return extractScriptFromDndMcqHotspot(node.constructionElement as IDgIntElementMcqHotspot, ctx);
    default:
      break;
  }
}

function extractScriptFromDnDTable(node: IDgIntElementDnDTable, ctx: InteractiveDiagramScriptGenContext) {
  const lang = ctx.lang;
  const tableProfile = ctx.profile.getStyleProfile()[lang].voiceScript.table;
  
  let script = [];

  const diagram_type: string = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints: number = node.dndTargets.length - 1; // - 1 because There is an unused target

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");
  
  const optionScript = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx);
  script.push(optionScript);
  script.push(" ... \n");
  
  const beginTableSlug = tableProfile.beginTable;
  script.push(`${ctx.langService.tra(beginTableSlug, lang)} ... `);

  node.table.forEach((row: IDgIntElementDnDTableCell[], i_row) => {
    let cellScript = [];
    row.forEach((cell:IDgIntElementDnDTableCell, i_col) => {
      cellScript = cellScript.concat(extractScriptFromTableCellDnD(cell, i_row, i_col, node, ctx, false));
    });
    
    let rowNumber = i_row + 1;
    if(!tableProfile.onlyReadHeaderCells) {
      script.push(` ... ${ctx.langService.tra(tableProfile.beginRow, lang)} ${rowNumber} ... `);
    }
    script = script.concat(cellScript)
  });

  const endTableSlug = tableProfile.endTable;
  if(endTableSlug) {
    script.push(` ... ${ctx.langService.tra(endTableSlug, lang)} ... `);
  }
  return script.join(' ');
}

function extractScriptFromDnDInline(node: IDgIntElementDnDInline, ctx: InteractiveDiagramScriptGenContext) {
  let script = [];

  const diagram_type: string = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints: number = node.dndTargets.length - 1; // - 1 because There is an unused target

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");
  
  const optionScript = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx);
  script.push(optionScript);
  script.push(" ... \n");
  
  const targetRegex = /\[(.*?)\]/g;
  const targetScript = ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''});
  let text = node.content
  text = text.replace(targetRegex, `... ${targetScript} ...`)
  text = text.replace(/\n/g, ' ... \n');
  script.push(ctx.processPlainTextScript(text, ctx.lang, ctx.preProcesses));
  
  return script.join(' ');
}

function extractScriptFromDnDGroup(node: IDgIntElementDnDGroup, ctx: InteractiveDiagramScriptGenContext) {
  let script = [];
  
  const diagram_type: string = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints: number = node.dndTargets.length - 1; // - 1 because There is an unused target

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  const groupingScript = ctx.profile.getStyleProfile()[ctx.lang].voiceScript.grouping;
  script.push(`... ${ctx.langService.tra(groupingScript?.blocks || 'voice_grouping_terms', ctx.lang)} ...`);
  
  const optionScript = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx, false);
  script.push(optionScript);
  script.push(" ... \n");
  
  const activeTargets = node.dndTargets.filter(t => !isUnusedId(t.label));
  activeTargets.forEach((target, index)=>{
    ctx.getNumberedOrder(node, script, index, index==activeTargets.length-1, ctx.lang);
    const sub = ctx.processPlainTextScript(target.label, ctx.lang, ctx.preProcesses);
    script.push(" ... ");
    script.push(sub);
  });
  
  return script.join(' ');
}

/*
  Get the introduction of the script for dnd diagrams that includes
  the diagram type and the number of targets
*/
function getScriptIntro(diagram_type: string, numTargetPoints: number, ctx: InteractiveDiagramScriptGenContext): string {
  return ctx.langService.tra('voice_dnd_intro', ctx.lang, {
    DIAGRAM_TYPE: diagram_type,
    NUM_TARGETS: numTargetPoints,
    OPTIONAL_S: numTargetPoints === 1 ? '' : 's'});
}

function extractScriptFromDndFreeform(node: IDgIntElementDnDFreeform, ctx: InteractiveDiagramScriptGenContext): string {
  const script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.targetData.length;

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  // retrieves options from dndTargets
  const optionScript = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx);
  script.push(optionScript);
  script.push(" ... \n");

  const activeTargets = node.dndTargets.filter(t => !isUnusedId(t.label));
  activeTargets.forEach((target, index)=>{
    ctx.getNumberedOrder(node, script, index, index==activeTargets.length-1, ctx.lang);
    const sub = ctx.processPlainTextScript(target.label, ctx.lang, ctx.preProcesses);
    script.push(" ... ");
    script.push(sub);
  });

  return script.join(' ');
}

function extractScriptFromDndVenn(node: IDgIntElementDnDVenn, ctx: InteractiveDiagramScriptGenContext): string {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.dndTargets.length - 1; // - 1 because There is an unused target

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  // retrieves options from dndTargets
  const optionScript = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx);
  script.push(optionScript);
  script.push(" ... \n");

  const activeTargets = node.dndTargets.filter(t => !isUnusedId(t.label));
  activeTargets.forEach((target, index)=>{
    ctx.getNumberedOrder(node, script, index, index==activeTargets.length-1, ctx.lang);
    const sub = ctx.processPlainTextScript(target.label, ctx.lang, ctx.preProcesses);
    script.push(" ... ");
    script.push(sub);
  });

  return script.join(' ');
}

function extractScriptFromDndNumberline(node: IDgIntElementDnDNumberline, ctx: InteractiveDiagramScriptGenContext): string {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.elements.length;

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  // number line range
  script.push(`... ${ctx.langService.tra('voice_dnd_numberline_range', ctx.lang, { MIN: node.numberline.min, MAX: node.numberline.max })}`);
  script.push(" ... \n");

  const optionAndTargetScript: Array<string> = extractOptionsAndTargetsFromDndNumberline(node.elements, ctx);
  script = script.concat(optionAndTargetScript)

  return script.join(' ');
}

function extractScriptFromDndTally(node: IDgIntElementDnDTally, ctx: InteractiveDiagramScriptGenContext): string {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.targets.length;

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  const optionAndTargetScript: Array<string> = extractOptionsAndTargetsFromDndTally(node.options, node.targets, ctx);
  script = script.concat(optionAndTargetScript)

  return script.join(' ');
}

function extractScriptFromDndSorting(node: IDgIntElementDnDSorting, ctx: InteractiveDiagramScriptGenContext): string {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.homeConfig.element[0].length;

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  // Start and End labels
  const targetLabels = `... ${ ctx.langService.tra('voice_dnd_sorting_start_end', ctx.lang, { START_LABEL: node.targetLabels.start, END_LABEL: node.targetLabels.end, })} ...\n`;
  script.push(targetLabels);
  script.push("... \n");

  const allOptions = [...node.options, ...node.extraOptions];
  const optionAndTargetScript: Array<string> = extractOptionsAndTargetsFromDndSorting(allOptions, ctx, node.flowSymbol === "none" ? '' :  ctx.langService.tra('voice_dnd_sorting_arrow', ctx.lang, {}));
  script = script.concat(optionAndTargetScript);

  return script.join(' ');
}

function extractScriptFromDndClozeMath(node: IDgIntElementDnDClozeMath, ctx: InteractiveDiagramScriptGenContext) {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.dndTargets.length - 1; // - 1 because There is an unused target

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  // retrives options
  let options: IDgIntElementDnDOption[] = [];
  node.dndTargets.forEach(target => {
    options = options.concat(target.content);
  }); 
  const optionScript: string = extractScriptFromDnDTarget(node.dndTargets, node.homeConfig, ctx, undefined, undefined, true);
  script.push(optionScript);
  script.push(" ... \n");

  const targetRegex = /\\boxed{.*?}/g; 
  const spaceRegex = /(\\mkern1\mu)*/g; // remove spaces
  const targetScript = ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''});
  let text = node.content
  text = text.replace(targetRegex, `... ${targetScript} ...`) // replace \\boxed{answer} with target
  text = text.replace(spaceRegex, ""); // remove spaces
  text = text.replace(/\n/g, ' ... \n');
  script.push(ctx.processPlainTextScript(text, ctx.lang, ctx.preProcesses));

  return script.join(' ');
}

function extractScriptFromDndMcqHotspot(node: IDgIntElementMcqHotspot, ctx: InteractiveDiagramScriptGenContext) {
  let script: Array<string> = [];

  const diagram_type = DgIntConstructionTypeCaption[node.constructionType];
  const numTargetPoints = node.hotspots.length;

  const intro = getScriptIntro(diagram_type, numTargetPoints, ctx);
  script.push(intro);
  script.push(" ... \n");

  const mcqProfile = ctx.profile.getStyleProfile()[ctx.lang].voiceScript.mcq;
  node.hotspots.forEach((hotspot, i) => {
    const optionScript = `${ctx.langService.tra(mcqProfile?.beginOption, ctx.lang)} "${optionLetters[i]}"`;
    ctx.useScriptDecision(hotspot, ctx.meta, optionScript);
    ctx.uploadNewVoice(hotspot.voiceover.script, hotspot.voiceover, ctx.lang);
  });

  return script.join(' ');
}

/*
  Return the options and targets in a suitable format
*/
function formatOptionsAndTargets(ctx: InteractiveDiagramScriptGenContext, options: string, targets: string): [string, string] {
  return [
    `... ${ctx.langService.tra('voice_insertion_terms', ctx.lang)} ...\n ${options}`,
    `... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: 's'})} ...\n ${targets}`
  ];
}

function extractOptionsAndTargetsFromDndNumberline(elements: IDgIntElementDnDNumberlineElement[], ctx: InteractiveDiagramScriptGenContext): string[] {
  const options = elements
      .filter(element => element.isOption)
      .map(element => {
        const label = element.optionConfig?.customLabel ?? element.positionLabel;
        const script = ctx.extractScriptFromLatex(label, ctx.lang);
        // store the voiceover script in the option
        ctx.useScriptDecision(element, ctx.meta, script);
        ctx.uploadNewVoice(element.voiceover.script, element.voiceover, ctx.lang);
        return `... ${script} ...\n`
      })
      .join(' ');

  const targets = elements
      .filter(element => element.isTarget)
      .map((element, index) => `... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''})} ${index + 1} ... ${element.targetConfig.layoutPosition} ...\n`)
      .join(' ');

  return formatOptionsAndTargets(ctx, options, targets);
}

function extractOptionsAndTargetsFromDndTally(options: IDgIntElementDnDTallyOption[], targets: IDgIntElementDnDTallyTarget[], ctx: InteractiveDiagramScriptGenContext): string[] {
  const optionScript = options
      .map(option => {
        const imageScript = (option.image && option.image.url) ? ctx.extractScriptFromImageNode(option.image, ctx.lang, ctx.meta, false) : '';
        const script = imageScript || ctx.processPlainTextScript(option.value.toString(), ctx.lang, ctx.preProcesses);
        // store the voiceover script in the option
        ctx.useScriptDecision(option, ctx.meta, script);
        ctx.uploadNewVoice(option.voiceover.script, option.voiceover, ctx.lang);
        return `... ${script} ...\n`
      })
      .join(' ');

  const targetScript = targets
      .map((target, index) => `... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''})} ${index + 1} ... ${ctx.processPlainTextScript(target.label, ctx.lang, ctx.preProcesses)} ...\n`)
      .join(' ');

  return formatOptionsAndTargets(ctx, optionScript, targetScript);
}

function extractOptionsAndTargetsFromDndSorting(options: IDgIntElementDnDSortingOption[], ctx: InteractiveDiagramScriptGenContext, symbol: string = 'Arrow' ): string[] {
  const optionScript = options
      .filter(option => !option.isPreFilled)
      .map(option => {
        let script = '';
        if (option.image && option.image.url) {
          script += ctx.extractScriptFromImageNode(option.image, ctx.lang, ctx.meta, false) + ' ... ';
        }
        if (option.isMath) {
          script += ctx.extractScriptFromLatex(option.label, ctx.lang);
        } else {
          script += ctx.processPlainTextScript(option.label, ctx.lang, ctx.preProcesses);
        }
        // store the voiceover script in the option
        ctx.useScriptDecision(option, ctx.meta, script);
        ctx.uploadNewVoice(option.voiceover.script, option.voiceover, ctx.lang);
        return `... ${script} ...\n`
      })
      .join(' ');

  const targetScript = options
      .map((option, index) => {
        return (!option.isPreFilled ?
        `... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''})} ${index + 1} ... ` :
        `... ${ ctx.processPlainTextScript(option.label, ctx.lang, ctx.preProcesses)} ... `) + 
        ((index + 1) !== options.length ? symbol : '') + '\n';
      })
      .join(' ');

  return formatOptionsAndTargets(ctx, optionScript, targetScript);
}

function extractScriptFromTableCellDnD(cell:IDgIntElementDnDTableCell, i_row:number, i_col:number, node:IDgIntElementDnDTable, ctx: InteractiveDiagramScriptGenContext, inverted:boolean) {
  const tableProfile = ctx.profile.getStyleProfile()[ctx.lang].voiceScript.table;

  let rowColNumber = (inverted ? i_row : i_col) + 1; //we should actually be looking at the rows if inverted
  
  // const isHeaderCell = (i_row === 0 && firstRowIsHeader) || (i_col === 0 && firstColumnIsHeader);
  const isHeaderCell = false;
  if(!isHeaderCell && tableProfile.onlyReadHeaderCells) {
    return [];
  }

  const preProcesses = isHeaderCell ? tableProfile.headerProcesses : [];
  //TODO: handle math and images
  let str = "";
  
  if (cell.isTarget){
    const targetScript = ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: '', OPTIONAL_S: ''});
    str += targetScript;
    // script.push(`... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: targetCount, OPTIONAL_S: targetCount > 1 ? 's' : ''})} ... \n`);
  }
  else if (!cell.image?.url && !cell.isMath) {
    str += ctx.processPlainTextScript(cell.text, ctx.lang, preProcesses);
  }
  
  // if(isHeaderCell && tableProfile.onlyReadHeaderCells) {
  //   str += " ... "
  // }
  
  let cellScript = [];
  if(!tableProfile.onlyReadHeaderCells) {
    cellScript.push(` ... ${ctx.langService.tra(inverted ? tableProfile.beginRow : tableProfile.beginColumn, ctx.lang)} ${rowColNumber} ... `);
  }
  cellScript.push(str);
  
  return cellScript;
}

function extractScriptFromDnDTarget(
  targetArray: IDgIntElementDnDTarget[], homeConfig: IDgIntHomeConfig, ctx: InteractiveDiagramScriptGenContext, 
  isIncludeInsertionTerms = true, isIncludeTargetCount = true, isAlwaysMath = false
) {
  let script = [];
  const options = flattenArray(targetArray.map(t => t.content));
  const idToOptionMap = new Map<string, IDgIntElementDnDOption>();
  options.forEach(o => idToOptionMap.set(o.id, o));
  const sortedOptionIds = flattenArray(homeConfig.element);
  const sortedOptions = sortedOptionIds.map(id => idToOptionMap.get(id));
  const targetCount = targetArray.filter(t => !isUnusedId(t.label)).length;
  
  if (isIncludeInsertionTerms){
    script.push(`...  ${ctx.langService.tra('voice_insertion_terms', ctx.lang)} ... \n`);
  }
  for (const option of sortedOptions) {
    script.push(extractScriptFromDnDOption(option, ctx, isAlwaysMath));
  }
  if (targetCount >= 1 && isIncludeTargetCount) {
    script.push(`... ${ctx.langService.tra('voice_dnd_num_targets', ctx.lang, {NUM_TARGETS: targetCount, OPTIONAL_S: targetCount > 1 ? 's' : ''})} ... \n`);
  }
  return script.join(' ... \n');
}

function extractScriptFromDnDOption(node: IDgIntElementDnDOption, ctx: InteractiveDiagramScriptGenContext, isAlwaysMath: boolean) {
  let script = '';
  
  if (node.image && node.image.url) {
    script += ctx.extractScriptFromImageNode(node.image, ctx.lang, ctx.meta, false) + ' ... ';
  }
  if (isAlwaysMath || node.textMode == DND_OPTION_TEXT_MODE.MATH) {
    script += ctx.extractScriptFromLatex(node.value, ctx.lang);
  } else {
    script += ctx.processPlainTextScript(node.value, ctx.lang, ctx.preProcesses);
  }
  // store the voiceover script in the option
  ctx.useScriptDecision(node, ctx.meta, script);
  ctx.uploadNewVoice(node.voiceover.script, node.voiceover, ctx.lang);
  return " ... "+script;
}