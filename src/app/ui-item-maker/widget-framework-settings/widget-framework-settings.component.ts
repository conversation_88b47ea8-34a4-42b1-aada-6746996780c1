import { Component, OnInit, Input, Output } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';


import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';


import { IAssessmentFrameworkDetail, IAssessmentFrameworkDef, IAssessmentFrameworkDimensionDetail, IQuadrantConstraint, ITestletConstraint, TestletConstraintFunction, ITestletConstraintCommon, IQuadrant, IQuadrantTestlet, TestFormConstructionMethod, IAssessmentPartition, DimensionType, IRoutingRule, IPanelModulesConfig, QUESTION_WORDING_OPTS, NEXT_BTN_OPTS } from '../item-set-editor/models/assessment-framework';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { EventEmitter } from '@angular/core';
import { IAssessmentParameter } from '../view-im-parameters/view-im-parameters.component';
import { combineParameters, findRelatedObjects, removeParameterDuplicates } from '../services/util';



@Component({
  selector: 'widget-framework-settings',
  templateUrl: './widget-framework-settings.component.html',
  styleUrls: ['./widget-framework-settings.component.scss']
})
export class WidgetFrameworkSettingsComponent implements OnInit {


  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Output() openSectionEditModal = new EventEmitter();

  QUESTION_WORDING_OPTS = QUESTION_WORDING_OPTS;
  NEXT_BTN_OPTS = NEXT_BTN_OPTS;
  TestFormConstructionMethod = TestFormConstructionMethod;
  util = new ItemBankUtilCtrl();
  Object = Object;

  assessmentParameters: IAssessmentParameter[]
  assessmentOptions = [
  { key: 'isAlternativeLinearTest', slug: 'Alternative/Linear Test' },
  { key: 'isQuestionnaireAssessment', slug: 'Is Questionnaire Assessment' },
  { key: 'isSampleTest', slug: 'sample_test' },
  { key: 'sampleAlternative', slug: 'Sample Alternative'},
  { key: 'isResourcePageOnly', slug: 'ie_frmwrk_none_assesment'}
  ];
  selectedAssessmentTypeOption: string = ''

  constructor(
    private editingDisabled: EditingDisabledService,
    private authScopeSettings: AuthScopeSettingsService,
    private whitelabel: WhitelabelService,
    private itemMaker: ItemMakerService,
    private lang: LangService,
  ) { }

  ngOnInit(): void {
    this.initToolbarOption()
    this.initReportOptions()
    this.initParameters();
    this.initTestFormConstructionOptions();
  }

  async initParameters() {
    await this.itemMaker.initializeParameters();
    this.assessmentParameters = this.itemMaker.assessmentParameters;
  }

  getLanguages() {
    return this.lang.getSupportedLanguages();
  }

  /**
   * Refreshes the parameter info if language is changed.
   */
  onLanguageChange() {
    if(!this.frameworkCtrl.asmtFmrk.assessmentType) { 
      return;
    }

    const currParameter = this.assessmentParameters.find((parameter) => parameter.assessment_slug == this.frameworkCtrl.asmtFmrk.assessmentType);
      
    this.selectAssessmentParameter(currParameter);
  }

  getGrades() {
    if(!this.assessmentParameters || !this.assessmentParameters.length) {
      return;
    }
    
    const typeSlugs = this.assessmentParameters.map((assessment) => assessment.type_slug).filter((type_slug => type_slug !== null));
    return typeSlugs.filter(function(item, pos) { // Remove duplicates
      return typeSlugs.indexOf(item) == pos;
    });
  }

  /**
   * 
   * @returns array of assessment parameters related to the current type slug.
   */
  getAssessmentParameterList() {
    if(!this.frameworkCtrl.asmtFmrk.assessmentLanguage || !this.frameworkCtrl.asmtFmrk.assessmentTypeSlug || !this.assessmentParameters || !this.assessmentParameters.length) {
      return;
    }

    return this.assessmentParameters.filter((assessment) => assessment.type_slug == this.frameworkCtrl.asmtFmrk.assessmentTypeSlug);
  }

  /**
   * Sets the current parameters
   * @param assessment the assessment object to select
   */
  selectAssessmentParameter(assessment: IAssessmentParameter) {
    this.frameworkCtrl.asmtFmrk.assessmentType = assessment.assessment_slug;
    const relatedAssessments = findRelatedObjects(this.assessmentParameters, assessment.assessment_slug);
    const combinedParameters = combineParameters(relatedAssessments.reverse(), this.frameworkCtrl.asmtFmrk.assessmentLanguage);

    this.frameworkCtrl.activeAsmtFwrkDimensions[2] = combinedParameters;
    removeParameterDuplicates(this.frameworkCtrl.activeAsmtFwrkDimensions[0], this.frameworkCtrl.activeAsmtFwrkDimensions[1], this.frameworkCtrl.activeAsmtFwrkDimensions[2]);
  }

  get arrangedDimensions() {
    return [this.frameworkCtrl.activeAsmtFwrkDimensions[2], this.frameworkCtrl.activeAsmtFwrkDimensions[0], this.frameworkCtrl.activeAsmtFwrkDimensions[1]];
  }

  trackByIndex(index: number): number {
    return index;  // Tracks by index
  }

  isAssessmentParameterToggled(assessment_slug) {
    return this.frameworkCtrl.asmtFmrk.assessmentType == assessment_slug;
  }

  initToolbarOption(){
    let defaults = {
      zoomIn : true,
      zoomOut: true,
      lineReader: true,
      hiContrast: true,
      toggleEditor: true,
      highlighter: true,
      eraser: true,
      notepad: true,
      infoButton: true
    }
    
    if(!this.frameworkCtrl.asmtFmrk.toolbarOptions){
      this.frameworkCtrl.asmtFmrk.toolbarOptions = defaults
      return;
    } 
    for(let def of Object.keys(defaults)) {
      if(this.frameworkCtrl.asmtFmrk.toolbarOptions[def] == undefined) {
        this.frameworkCtrl.asmtFmrk.toolbarOptions[def] = defaults[def];
      }
    }
    
  }

  initReportOptions(){
    if(!this.frameworkCtrl.asmtFmrk.reportOptions) this.frameworkCtrl.asmtFmrk.reportOptions = {}
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true, true);

  insertUpload() {
    if (!this.frameworkCtrl.asmtFmrk['uploads']) {
      this.frameworkCtrl.asmtFmrk['uploads'] = [];
    }
    // @ts-ignore
    this.frameworkCtrl.asmtFmrk['uploads'].push({});
  }

  canEditPsychoParam() {
    return this.authScopeSettings.getSetting(AuthScopeSetting.ENABLE_PSYCHO_PARAM_EDIT);
  }

  isBCED(){
    return this.whitelabel.getSiteFlag('IS_BCED')
  }

  getNotepadSlug(){
    return !this.isBCED() ? 'lbl_notepad' : 'lbl_notepad_bc'
  }

  getLineReaderCaption(){
    return !this.isBCED() ? 'btn_line_reader' : 'btn_line_reader_osslt'
  }

  isMscatPanelContentDetails:boolean
  rr(module){
    return this.frameworkCtrl.asmtFmrk.panelAssembly.routingRules[module.id]
  }
  TestletConstraintFunction(){
    return TestletConstraintFunction
  }

  updateConstraintExludeBlanks(value:boolean, param:string, constraints:ITestletConstraint[]){
    constraints.forEach(constraint =>{
      if(constraint.config.param === param){
        constraint.config.excludeEmptyParams = value;
      }
    })
    console.log(value);
  }

  /**
   * 
   * @param assessmentParameters the array of assessment parameters to search.
   * @param assessment_slug the assessment slug to search.
   * @returns all assessment parameters associated with the specified slug.
   */
  findRelatedObjects(assessmentParameters: IAssessmentParameter[], assessment_slug: string) {
    // Create a map for quick lookup by ID
    const idMap = {};
    assessmentParameters.forEach(obj => {
        idMap[obj.id] = obj;
    });

    // To store the result and visited IDs
    const result: IAssessmentParameter[] = [];
    const visited = new Set();

    // Start with the object that has the given assessment_slug
    let current = assessmentParameters.find(obj => obj.assessment_slug === assessment_slug);

    while (current && !visited.has(current.id)) {
        // Add current object to the result and mark it as visited
        result.push(current);
        visited.add(current.id);

        // If extend_id is null or has already been visited, break the loop
        if (!current.extend_id || visited.has(current.extend_id)) {
            break;
        }

        // Move to the object referenced by extend_id
        current = idMap[current.extend_id];
    }

    return <string[]><unknown>result.map((assessment) => assessment.parameters);
  }

  /**
   * 
   * @param parameters an array of parameters in JSON string format
   * @param lang the language to pull from the parameter
   * @returns a parsed array of combined parameters
   */
  combineParameters(parameters: string[], lang: string) {
    // Initialize an empty array to hold the combined "en" objects
    let combinedObjects: any[] = [];

    // Iterate over the array of JSON strings
    parameters.forEach(jsonString => {
        // Parse the JSON string into an object
        const parsedObj = JSON.parse(jsonString);

        // Extract the "en" array and concatenate it with combinedObjects
        if (parsedObj[lang] && Array.isArray(parsedObj[lang])) {
            combinedObjects = combinedObjects.concat(parsedObj[lang]);
        }
    });

    // Return the combined object with the "en" key
    return combinedObjects;
  }

  configExpansionMap: Map<string, boolean> = new Map();
  expandConfig(code: string, isExpand=true) {
    this.configExpansionMap.set(code, isExpand);
  }

  isConfigExpanded(code: string){
    return this.configExpansionMap.get(code);
  }
  
   initTestFormConstructionOptions() {
    const isSample = !!this.frameworkCtrl.asmtFmrk['isSampleTest'];
    const isAlternative = !!this.frameworkCtrl.asmtFmrk['isAlternativeLinearTest'];
    if (isSample && isAlternative) {
      this.selectedAssessmentTypeOption = 'sampleAlternative';
    } else if (isSample) {
      this.selectedAssessmentTypeOption = 'isSampleTest';
    } else if (isAlternative) {
      this.selectedAssessmentTypeOption = 'isAlternativeLinearTest';
    } else {
      const activeKey = this.assessmentOptions.find(opt => this.frameworkCtrl.asmtFmrk[opt.key]);
      if (activeKey) this.selectedAssessmentTypeOption = activeKey.key;
    }
  }

  onAssessmentTypeChange() {
    // Reset all to false first
    this.frameworkCtrl.asmtFmrk['isAlternativeLinearTest'] = false;
    this.frameworkCtrl.asmtFmrk['isSampleTest'] = false;
    this.frameworkCtrl.asmtFmrk['isQuestionnaireAssessment'] = false;
    this.frameworkCtrl.asmtFmrk['isResourcePageOnly'] = false;

    // Handle the special case for sampleAlternative
    if (this.selectedAssessmentTypeOption === 'sampleAlternative') {
      this.frameworkCtrl.asmtFmrk['isAlternativeLinearTest'] = true;
      this.frameworkCtrl.asmtFmrk['isSampleTest'] = true;
    } else if (this.selectedAssessmentTypeOption) {
      this.frameworkCtrl.asmtFmrk[this.selectedAssessmentTypeOption] = true;
    }
  }


}
