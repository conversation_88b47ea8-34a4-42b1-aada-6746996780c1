input.short-num {
    width:5em;
    text-align:center;
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    /* Firefox */
    -moz-appearance: textfield;
}

.is-error {
    color: #FF6666;
}

.flex-column {
    display: flex; 
    flex-direction: column; 
}

.flex-row {
    display: flex;
    flex-direction: row;
    gap: 1em;
}
.quadrant-publish-settings {
    display: flex;
    border: none;
    justify-content: center;
    flex-direction: column;
    label {
        display: flex;
        margin-bottom: 1em;
    
        &:last-child {
            margin-bottom: 0;
        }
    }
    input {
        margin-right: 0.4em;
    }    
}
