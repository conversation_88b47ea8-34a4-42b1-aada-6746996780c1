$border-color: #dbdbdb;
$primary-color: #3273dc;
$disabled-color: #888;
$disabled-bg-color: #f8f8f8;
$disabled-label-color: #e74c3c;
$hover-color: #f5f5f5;
$box-shadow: 0 2px 4px rgba(0,0,0,0.1);
$border-radius: 4px;

// Base container styling
.audit-profile-wrapper {
  display: flex;
}

.left-save-btn {

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.audit-profile-container {
  flex-grow: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1em;

  label {
    font-weight: bold;
  }
}

.audit-profile-active {
  font-size: 1rem;
  padding: 0.5em 0.8em;  
}

.filter-container{
  display: flex;
  gap: 0.75em;
}

.filter-input-container {
  position: relative;
  flex-grow: 1;
  min-width: 20em;
  max-width: 30rem;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-input {
  width: 100%;
  padding: 0.6rem;
  border-radius: $border-radius;
  border: 1px solid $border-color;
  font-size: 1rem;
  box-shadow: $box-shadow;
  
  &:focus {
    outline: none;
  }
}

.clear-filter-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: $disabled-color;
  cursor: pointer;

  &:hover {
    color: darken($disabled-color, 20%);
  }
}

.filter-type-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  label {
    font-weight: bold;
  }
}

.filter-type-select {
  padding: 0.5rem;
  border-radius: $border-radius;
  border: 1px solid $border-color;
  background-color: white;
  box-shadow: $box-shadow;
  
  &:focus {
    outline: none;
  }
}

.archived-toggle-btn {
 margin-left: 0.5rem;
 span {
  font-size: 0.7em;
  font-weight: bold;
 }
}

// Table styling
.audit-table {
  margin: auto;
  border-collapse: collapse;
  font-family: sans-serif;
  background-color: #fff;
  padding: 1em;
}

.audit-table th {
  text-align: left;
  font-weight: normal;
  padding: 1.5em;
  border: 1px solid $border-color;
  vertical-align: top;
}

.audit-table tr {
  transition: background-color 0.2s;
  
  &:hover:not(.no-results) {
    background-color: $hover-color;
  }
}

.audit-table-col-main {
  width: 40%;
}

.audit-table-col-checks {
  width: 60%;
  padding-top: 1rem;
}

.audit-check-list {
  list-style-type: none;
  padding-left: 0;
  margin: 0;
}

.audit-check-list li {
  margin-bottom: 0.5rem;
  padding: 0.1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5em;
}

.toggle-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5em;
  font-size: inherit;
  line-height: 1.2; // or inherit, if set globally
  cursor: pointer;

  input[type="checkbox"] {
    margin: 0;
    width: 1em;
    height: 1em;
    flex-shrink: 0;
    vertical-align: middle;
  }

  &.is-excluded {
    opacity: 0.5;
    color: $disabled-color;
    cursor: not-allowed;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}


.is-excluded,
.is-excluded i {
  color: $disabled-color;
  text-decoration: line-through;
}

.is-disabled {
  opacity: 0.65;
  background-color: $disabled-bg-color;
  
  // Add a visual indicator for disabled
  .toggle-btn {
    font-style: italic;
    
    &::after {
      content: " (TBA)";
      color: $disabled-label-color;
      font-weight: bold;
      margin-left: 0.5rem;
    }
  }
}

// Combined states
.is-disabled.has-excluded .toggle-btn::after {
  content: " (disabled & excluded)";
}

.no-results {
  text-align: center;
  color: $disabled-color;
  font-style: italic;
  padding: 1rem;
}

// Loading state
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: $disabled-color;
}

.loading-spinner {
  border: 3px solid $border-color;
  border: 3px solid $primary-color;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

::ng-deep i {
  padding-top: 0.1em;
}

// Row specific styles
.is-readonly {
  opacity: 0.7;
  pointer-events: none;
}

.sticky-header {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: #f5f5f5;
  overflow: scroll;
}

.unsaved-indicator {
  color: $primary-color;
  font-size: 0.8em;
  margin-left: 0.3em;
  vertical-align: middle;
  opacity: 0.8;
  
  &:hover {
    opacity: 1;
  }
}

.scroll-to-top {
  position: sticky;
  bottom: 20px;
  margin-left: auto;
  padding: 0.1em;
  cursor: pointer;
  display: block;
  width: fit-content;
}