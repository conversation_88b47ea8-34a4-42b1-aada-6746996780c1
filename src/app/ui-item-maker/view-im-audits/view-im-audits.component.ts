import { AfterViewInit, Component, ElementRef, Input, OnInit } from '@angular/core';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { AuditConfigs, IAuditConfig } from '../widget-audits/data/audits';
import { IAuthoringGroup, ItemMakerService } from '../item-maker.service';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { Router } from '@angular/router';
import { LangService } from 'src/app/core/lang.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';

interface ExcludedChecks {
  [checkId: string]: boolean;
}

export interface ExcludedAuditEntry {
  isExcluded: boolean;
  excludedChecks: ExcludedChecks;
};

export type ExcludedAuditsMap = { [slug: string]: ExcludedAuditEntry };

export type FilterType = 'both' | 'audits' | 'checks';

export const getDefaultExcludedAuditEntry = () : ExcludedAuditEntry => {
  return {isExcluded:false, excludedChecks:{}}
}

interface AuditViewModel extends IAuditConfig {
  visible: boolean;
  visibleChecks: any[];
}

@Component({
  selector: 'view-im-audits',
  templateUrl: './view-im-audits.component.html',
  styleUrls: ['./view-im-audits.component.scss']
})
export class ViewImAuditsComponent implements OnInit, AfterViewInit {

  @Input() rawAudits: IAuditConfig[] = [];
  @Input() selectedProfileId: number | null = null; // which group are we updating audit permissions for
  @Input() selectedGroupName: string | null = null; // which group are we updating audit permissions for
  @Input() close: ()=> void; // which group are we updating audit permissions for
  
  excludedAudits: { [slug: string]: ExcludedAuditEntry } = {};
  selectedGroup: IAuthoringGroup | null = null;

  // Filtered view models
  audits: AuditViewModel[] = [];
  hasVisibleAudits: boolean = true;
  
  // TBA toggle
  showTBA: boolean = false;

  // Filter properties
  filterText: string = '';
  filterType: FilterType = 'both';
  filterTypes: {value: FilterType, label: string}[] = [
    { value: 'both', label: this.lang.tra('lbl_both') },
    { value: 'audits', label: this.lang.tra('auth_audits') },
    { value: 'checks', label: this.lang.tra('auth_sub_audits') }
  ];


  private savedExcludedAudits: ExcludedAuditsMap = {};

  constructor(
    public myItems: ItemMakerService,
    public routes: RoutesService,
    private auth: AuthService,
    public lang: LangService,
    private loginGuard: LoginGuardService,
  ) { }

  ngOnInit(): void {
    this.loadAuditConfigs()
    if (!this.rawAudits?.length) {
      this.rawAudits = AuditConfigs;
    }  
    
    this.rawAudits.forEach((audit) => {
      if (Array.isArray(audit.checks)) {
        audit.checks = audit.checks.filter((check) => !!check.caption?.length && check.id);
      } 
    });
    
    // Sort audits so that disabled ones appear at the bottom
    this.rawAudits.sort((a, b) => {
      return (a.isDisabled === b.isDisabled) ? 0 : a.isDisabled ? 1 : -1;
    });
    // Initialize filtered audits
    this.applyFilters();
  }

  ngAfterViewInit(): void {
  }
  

  trackBySlug(index: number, audit: AuditViewModel) {
    return audit.slug;
  }

  toggleAuditVisibility(slug: string): void {
    const audit = this.excludedAudits[slug] ||= { isExcluded: false, excludedChecks: {} };
    audit.isExcluded = !audit.isExcluded;
    this.applyFilters();
  }
  
  toggleCheckVisibility(slug: string, checkId: string): void {
    const audit = this.excludedAudits[slug] ||= { isExcluded: false, excludedChecks: {} };
    audit.excludedChecks[checkId] = !audit.excludedChecks[checkId];
    this.applyFilters();
  }

  getAuditExcluded(slug: string): boolean {
    return !!this.excludedAudits?.[slug]?.isExcluded;
  }
  
  getCheckExcluded(slug: string, checkId: string): boolean {
    return !!this.excludedAudits?.[slug]?.excludedChecks?.[checkId];
  } 

  isLoadingAuditConfigs:boolean = false;
  async loadAuditConfigs(){
    this.isLoadingAuditConfigs = true;
    this.auth.apiGet(this.routes.TEST_AUTH_AUDIT_MANAGEMENT, this.selectedProfileId)
    .then((res) => {
      const {config} = res;
      this.excludedAudits = config;
      this.savedExcludedAudits = JSON.parse(JSON.stringify(config));
      this.applyFilters();
      this.sortAudits();
    })
    .catch(() => {
      this.excludedAudits = {};
      this.savedExcludedAudits = {};
      this.applyFilters();
      this.sortAudits();
    })
    .finally(() => this.isLoadingAuditConfigs = false);
  }

  isSavingAuditConfigs:boolean = false;
  async saveAuditConfigs(){
    this.isSavingAuditConfigs = true;
    this.auth.apiCreate(this.routes.TEST_AUTH_AUDIT_MANAGEMENT, {config: this.excludedAudits, group_id: this.selectedProfileId}, this.excludedAudits)
    .then(() => {
      this.savedExcludedAudits = JSON.parse(JSON.stringify(this.excludedAudits)); // Deep copy
    })
    .catch((err) => this.loginGuard.quickPopup(this.lang.tra('msg_error_saving_generic')))
    .finally(() => this.isSavingAuditConfigs = false);
  }

  hasUnsavedChanges(slug: string): boolean {
    const current = this.excludedAudits[slug];
    const saved = this.savedExcludedAudits[slug];
    
    if (!current && !saved) return false;
    if (!current || !saved) return true;
    
    // Check if audit exclusion state changed
    if (current.isExcluded !== saved.isExcluded) return true;
    
    // Check if any check states changed
    const currentChecks = Object.keys(current.excludedChecks);
    const savedChecks = Object.keys(saved.excludedChecks);
    
    if (currentChecks.length !== savedChecks.length) return true;
    
    return currentChecks.some(checkId => 
      current.excludedChecks[checkId] !== saved.excludedChecks[checkId]
    );
  }

  get readOnly() {
    return this.isSavingAuditConfigs || this.isLoadingAuditConfigs;
  }

  // Toggle TBA visibility
  toggleTBA(): void {
    this.showTBA = !this.showTBA;
    this.applyFilters();
  }

  // Filter methods
  applyFilters(): void {
    const searchTerm = this.filterText.toLowerCase();
    
    this.audits = this.rawAudits
      // First filter based on TBA toggle
      .filter(audit => this.showTBA || !audit.isDisabled)
      .map(audit => {
        // Determine if audit caption matches filter
        const auditMatchesFilter = !searchTerm || 
          ((this.filterType === 'both' || this.filterType === 'audits') && 
          audit.caption.toLowerCase().includes(searchTerm));
        
        // If audit matches filter, include all checks
        // Otherwise, filter checks based on their captions
        let visibleChecks = [];
        
        if (auditMatchesFilter) {
          // Show all checks when audit matches
          visibleChecks = audit.checks || [];
        } else if (this.filterType === 'both' || this.filterType === 'checks') {
          // Filter checks by search term only if audit doesn't match
          visibleChecks = (audit.checks || []).filter(check => 
            check.caption.toLowerCase().includes(searchTerm)
          );
        }
        
        // Audit is visible if it matches filter or if any of its checks match
        const visible = auditMatchesFilter || visibleChecks.length > 0;
        
        return {
          ...audit,
          visible,
          visibleChecks
        };
      });
    
    this.hasVisibleAudits = this.audits.some(audit => audit.visible);
    this.sortAudits();
  }

  private sortAudits(): void {
    // Sort audits: normal first, excluded second, disabled last
    this.audits.sort((a, b) => {
      // First priority: disabled audits go last
      if (a.isDisabled !== b.isDisabled) {
        return a.isDisabled ? 1 : -1;
      }
      
      // Second priority: excluded audits go second to last
      const aExcluded = this.getAuditExcluded(a.slug);
      const bExcluded = this.getAuditExcluded(b.slug);
      
      if (aExcluded !== bExcluded) {
        return aExcluded ? 1 : -1;
      }
      
      // Default: keep original order
      return 0;
    });
  }

  clearFilter(): void {
    this.filterText = '';
    this.applyFilters();
  }

  onFilterChange(): void {
    this.applyFilters();
  }
}
