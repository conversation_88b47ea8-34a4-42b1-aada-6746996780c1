
<div>
  <div>
    <div class="sticky-header">
      <div class="audit-profile-wrapper">
        <!-- Left-aligned Save button -->
        <div class="left-save-btn">
          <button class="button" 
            [class.is-info]="isSavingAuditConfigs" 
            [disabled]="readOnly"
            (click)="saveAuditConfigs()"
          >
            <tra slug="save_btn"></tra>
          </button>
        </div>
      
        <!-- Centered dropdown -->
        <div class="audit-profile-container">

          <span 
            id="authGroup"
            class="audit-profile-active"
          >
            <span style="color: gray">ID: {{ selectedProfileId }}</span>&nbsp;
            {{ selectedGroupName }}
            
          </span>

        </div>
      </div>
      
      <div class="filter-container">
        <div class="filter-input-container">
          <input
            type="text"
            [(ngModel)]="filterText"
            (ngModelChange)="onFilterChange()"
            [placeholder]="lang.tra('auth_audit_filter')"
            class="filter-input"
          />
          <button *ngIf="filterText" class="clear-filter-btn" (click)="clearFilter()">×</button>
        </div>
        
        <div class="filter-controls">
          <div class="filter-type-container">
            <label>Filter by:</label>
            <select [(ngModel)]="filterType" (ngModelChange)="onFilterChange()" class="filter-type-select">
              <option *ngFor="let type of filterTypes" [value]="type.value">
                {{ type.label }}
              </option>
            </select>
          </div>
        </div>                
        <button 
          class="archived-toggle-btn button" 
          [class.is-info]="showTBA"
          (click)="toggleTBA()">
          <span>{{ showTBA ? lang.tra('lbl_hide_archived') : lang.tra('lbl_show_archived') }}</span>
        </button>
      </div>
    </div>
    
    <table class="audit-table" *ngIf="!isLoadingAuditConfigs; else loading">
      <ng-container *ngFor="let audit of audits; let idx = index; trackBy: trackBySlug">
        <tr *ngIf="audit.visible" 
          [class.is-disabled]="audit.isDisabled" 
          [class.has-excluded]="getAuditExcluded(audit.slug)"
          [class.is-readonly]="readOnly">
          <th class="audit-table-col-main">
            <label  
              class="toggle-btn" 
              style="font-weight: bold"
              [ngClass]="{ 'is-excluded': getAuditExcluded(audit.slug) }"
            >
              <input 
                type="checkbox" 
                [checked]="!getAuditExcluded(audit.slug)" 
                (click)="$event.stopPropagation()" 
                (change)="toggleAuditVisibility(audit.slug)" 
                [disabled]="readOnly || audit.isDisabled"
              />
              {{ audit.caption }}
              <span *ngIf="hasUnsavedChanges(audit.slug)" class="unsaved-indicator" title="Unsaved changes">●</span>
            </label>
          </th>
          <th class="audit-table-col-checks">
            <div *ngIf="audit.visibleChecks?.length">
              <ul class="audit-check-list">
                <li *ngFor="let check of audit.visibleChecks" class="check-item">
                  <label 
                    class="toggle-btn" 
                    [ngClass]="{ 'is-excluded': getCheckExcluded(audit.slug, check.id) || getAuditExcluded(audit.slug)}"
                  >
                    <input 
                      type="checkbox" 
                      [checked]="!getCheckExcluded(audit.slug, check.id)" 
                      (click)="$event.stopPropagation()" 
                      (change)="toggleCheckVisibility(audit.slug, check.id)" 
                      [disabled]="getAuditExcluded(audit.slug) || readOnly || audit.isDisabled"
                    />
                    {{ check.caption }}
                  </label>
                </li>
              </ul>
            </div>
            <div *ngIf="!audit.visibleChecks?.length">
              <b><tra slug="audit_does_not_support_sub_audit_config"></tra></b>
            </div>
          </th>
        </tr>
      </ng-container>
      
      <tr *ngIf="!hasVisibleAudits" class="no-results">
        <td colspan="2">No matching audits or checks found.</td>
      </tr>
    </table>
    
    <ng-template #loading>
      <div class="loading-container">
        <div class="loading-spinner"></div>
        {{lang.tra('auth_audit_load_config')}}
      </div>
    </ng-template>
  </div>
  <span class="scroll-to-top" (click)="close()">
    <i class="fa fa-arrow-up fa-xl"></i>
  </span>
</div>