<span class="widget-framework-status">
        <ng-container *ngIf="showLabel">
                <tra slug="ie_audit_summary"></tra>
        </ng-container>
        <ng-container *ngIf="!auditCtrl.retrievingAuditLogs; else loading">
                <div class="audit-summary" *ngIf="auditStatus">
                        <div class="audit-ran-time-range" *ngIf="showDate" [tooltip]="'Time range in which the audits were ran'">
                        {{auditTimeRange}}
                        </div>
                        <div class="status-count"  [style.color]="(auditStatus.informative && filterControl(AuditFilterOptions.INFORMATIVE)) ? AuditStatusConfig[AuditFilterOptions.INFORMATIVE].color : 'dimgrey'" [tooltip]="AuditStatusConfig[AuditFilterOptions.INFORMATIVE].tooltip">
                                <i [class]="AuditStatusConfig[AuditFilterOptions.INFORMATIVE].icon"></i>
                                {{auditStatus.informative || 0}}
                        </div>
                        <div class="status-count" [style.color]="(auditStatus.pass && filterControl(AuditFilterOptions.PASSED))? AuditStatusConfig[AuditFilterOptions.PASSED].color : 'dimgrey'" [tooltip]="AuditStatusConfig[AuditFilterOptions.PASSED].tooltip">
                                <i [class]="AuditStatusConfig[AuditFilterOptions.PASSED].icon"></i>
                                {{auditStatus.pass || 0}}
                        </div>
                        <div class="status-count" [style.color]="(auditStatus.fail && filterControl(AuditFilterOptions.ISSUES))? AuditStatusConfig[AuditFilterOptions.ISSUES].color : 'dimgrey'"  [tooltip]="AuditStatusConfig[AuditFilterOptions.ISSUES].tooltip">
                                <i [class]="AuditStatusConfig[AuditFilterOptions.ISSUES].icon"></i>
                                {{auditStatus.fail || 0}}
                        </div>
                </div>
        </ng-container>
        <ng-template #loading>
                <b><i class="fa fa-spinner fa-spin" aria-hidden="true"></i></b>
        </ng-template>
</span>

