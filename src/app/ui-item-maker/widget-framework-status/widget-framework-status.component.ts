import { Component, OnInit, Input } from '@angular/core';
import { IAuditResultSummary, ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { AuditFilterOptions, AuditStatusConfig } from '../services/audit-filter.service';

@Component({
  selector: 'widget-framework-status',
  templateUrl: './widget-framework-status.component.html',
  styleUrls: ['./widget-framework-status.component.scss']
})
export class WidgetFrameworkStatusComponent implements OnInit {

  @Input() auditCtrl: ItemBankAuditor;
  @Input() auditStatus: IAuditResultSummary;
  @Input() showLabel: boolean = false;
  @Input() showDate: boolean = false;
  @Input() filterControl = (input:string) => true;

  AuditStatusConfig = AuditStatusConfig;
  AuditFilterOptions = AuditFilterOptions

  constructor() { }

  ngOnInit(): void {
  }

  get auditTimeRange(): string {
    if (!this.auditStatus?.lastRan || !this.auditStatus?.firstRan) {
      return 'None Ran';
    }
    const format = (date: string | number | Date) => {
      const d = new Date(date);
      return d.toLocaleString(undefined, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    };
    const start = format(this.auditStatus.firstRan);
    const end = format(this.auditStatus.lastRan);
    return start === end ? start : `${start} - ${end}`;
  }

}
