<fieldset [disabled]="isReadOnly()" (input)="update()">
  <div class="simple-form-container" *ngIf="!isSimplified" >
    <label>   
        Entry ID  
        <span  *ngIf="!isEditingEntryId">{{element.entryId}}</span>
        <input *ngIf="isEditingEntryId" type="number" [(ngModel)]="element.entryId" /> 
    </label>
    <button *ngIf="!isEditingEntryId" (click)="startEditingEntryId()">Edit</button>
    <button *ngIf="isEditingEntryId" (click)="isEditingEntryId = false">Save</button>
  </div>
  
  <hr>
  
  <div class="form-row">
    <div class="form-row-label">
      Coefficient Value
    </div>
    <div class="form-row-input" style="display:flex; align-items: baseline; flex-grow: 0;">
      <label class="sign-checkbox" style="margin-right: 0.5em;">
        <input type="checkbox" [(ngModel)]="element.isNegativeCoefficient"/>
        <span class="symbol">{{ element.isNegativeCoefficient ? '−' : '+' }}</span>
      </label>
      <input type="text" class="input" (input)="validateIntegerInput($event)" [(ngModel)]="element.whole" style="width:60px; text-align:center">
      <span class="math-symbol"><tra slug="decimal_delim"></tra></span>
      <input type="text" class="input" (input)="validateIntegerInput($event)" [(ngModel)]="element.fractional" style="width:60px; text-align:center">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Exponent Value
    </div>
    <div class="form-row-input" style="display:flex; align-items: baseline; flex-grow: 0;">
      <label class="sign-checkbox" style="margin-right: 0.5em;">
        <input type="checkbox" [(ngModel)]="element.isNegativeExponent"/>
        <span class="symbol">{{ element.isNegativeExponent ? '−' : '+' }}</span>
      </label>
      <input type="text" class="input" (input)="validateIntegerInput($event)" [(ngModel)]="element.exponent" style="width:60px; text-align:center">
    </div>
  </div>
  
  <!-- <hr>
    
  <div class="form-row">
    <div class="form-row-label">
      Negative Coefficient
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isNegativeCoefficient">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Negative Exponent
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isNegativeExponent">
    </div>
  </div> -->
  
  <hr>
    
  <ng-container *ngIf="!hiddenConfigs.hideFixedOptions">
  <div class="form-row">
    <div class="form-row-label">
      Fixed Whole Part?
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isFixedWhole">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Fixed Fractional Part?
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isFixedFractional">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Fixed Exponent Part?
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isFixedExponent">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Fixed Coefficient Sign?
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isFixedCoefficientSign">
    </div>
  </div>
  <div class="form-row">
    <div class="form-row-label">
      Fixed Exponent Sign?
    </div>
    <div class="form-row-input">
      <input type="checkbox" [(ngModel)]="element.isFixedExponentSign">
    </div>
  </div>
  </ng-container>
  
</fieldset>

<fieldset [disabled]="isReadOnly()" 
  *ngIf="element.isAllowMultipleAnswers && !hiddenConfigs.hideAllowMultipleAnswers"
  (change)="update()" (input)="update()">
  <hr>
  <div class="form-row">
    <div class="form-row-label">
      Answers
    </div>
  </div>
  <table class="multilple-answer-container">
    <tr class="answer-header">
      <td>Coefficient</td>
      <td>Exponent</td>
      <td></td>
    </tr>
    
    <tr class="answer-row">
      <td>
        <div style="display: flex;">
          <div style="display: flex;">
            <label class="sign-checkbox" style="margin-right: 0.1em;">
              <input disabled type="checkbox" [(ngModel)]="element.isNegativeCoefficient"/>
              <span class="symbol">{{ element.isNegativeCoefficient ? '−' : '+' }}</span>
            </label>
            <input disabled type="text" class="input is-small" [(ngModel)]="element.whole">
          </div>
          <div style="display: flex;">
            <span class="math-symbol"><tra slug="decimal_delim"></tra></span>
            <input disabled type="text" class="input is-small" [(ngModel)]="element.fractional">
          </div>
        </div>
      </td>
      <td>
        <div style="display: flex;">
          <label class="sign-checkbox" style="margin-right: 0.1em;">
            <input disabled type="checkbox" [(ngModel)]="element.isNegativeExponent"/>
            <span class="symbol">{{ element.isNegativeExponent ? '−' : '+' }}</span>
          </label>
          <input disabled type="text" class="input is-small" [(ngModel)]="element.exponent">
        </div>
      </td>
      <td>
      </td>
    </tr>
    
    <tr 
      *ngFor="let answerSet of answerSets; let i = index"
      class="answer-row" 
      [class.error-bg-color]="isNonUniqueAnswerString[i+1]"
      [title]="isNonUniqueAnswerString[i+1] ? 'This answer is not unique.' : ''"
    >
      <td>
        <div style="display: flex;">
          <div style="display: flex;">
            <label class="sign-checkbox" style="margin-right: 0.1em;">
              <input type="checkbox" [(ngModel)]="answerSet.isNegativeCoefficient"/>
              <span class="symbol">{{ answerSet.isNegativeCoefficient ? '−' : '+' }}</span>
            </label>
            <input 
              [class.error-bg-color]="answerSet.whole.length != element.whole.length"
              [title]="answerSet.whole.length != element.whole.length ? 'The number of characters doesn\'t math the main value.' : ''"
              type="text" class="input is-small" [(ngModel)]="answerSet.whole"
            >
          </div>
          <div style="display: flex;">
            <span class="math-symbol"><tra slug="decimal_delim"></tra></span>
            <input 
              [class.error-bg-color]="answerSet.fractional.length != element.fractional.length"
              [title]="answerSet.fractional.length != element.fractional.length ? 'The number of characters doesn\'t math the main value.' : ''"
              type="text" class="input is-small" [(ngModel)]="answerSet.fractional"
            >
          </div>
        </div>
      </td>
      <td>
        <div style="display: flex;">
          <label class="sign-checkbox" style="margin-right: 0.1em;">
            <input type="checkbox" [(ngModel)]="answerSet.isNegativeExponent"/>
            <span class="symbol">{{ answerSet.isNegativeExponent ? '−' : '+' }}</span>
          </label>
          <input 
            [class.error-bg-color]="answerSet.exponent.length != element.exponent.length"
            [title]="answerSet.exponent.length != element.exponent.length ? 'The number of characters doesn\'t math the main value.' : ''"
            type="text" class="input is-small" [(ngModel)]="answerSet.exponent"
          >
        </div>
      </td>
      <td class="trash-cell" (click)="deleteAnswerSet(i)" [class.no-pointer-events]="isReadOnly()">
        <i class="fas fa-trash" aria-hidden="true"></i>
      </td>
    </tr>
  </table>
  <button (click)="addAnswerSet()" class="button is-small has-icon" style="margin-top: 0.5em">
    <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
    <span>New Answer</span>
  </button>
</fieldset>

<fieldset [disabled]="isReadOnly()" *ngIf="element.isShowAdvancedOptions && !hiddenConfigs.hideAdvancedOptions">
  <hr>
    
  <div class="form-row">
    <div class="form-row-label">
      Score Weight
    </div>
    <div class="form-row-input">
      <input type="number" class="input" [(ngModel)]="element.scoreWeight">
    </div>
  </div>
</fieldset>

<div style="margin-top:1em;" *ngIf="!hiddenConfigs.hideAllowMultipleAnswers || !hiddenConfigs.hideAdvancedOptions">
  <label class="checkbox" *ngIf="!hiddenConfigs.hideAllowMultipleAnswers">
    <input type="checkbox" [(ngModel)]="element.isAllowMultipleAnswers">
    <span>Allow Multiple Answers</span>
  </label>
  <br>
  <label class="checkbox" *ngIf="!hiddenConfigs.hideAdvancedOptions && !isSimplified">
    <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions">
    <tra slug="auth_show_advanced_options"></tra>
  </label>
</div>
