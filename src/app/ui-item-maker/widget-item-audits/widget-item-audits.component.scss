@import '../../../styles/partials/_modal.scss';
@import '../../ui-testrunner/test-runner/test-runner.component.scss';

.item-audit-check-container {
    margin-top: 0.5em;
    padding: 0 1em;
    .item-audit-row {
        font-size: smaller;
        height: 2rem;
        border-bottom: 0.1em solid #ccc;
        &:last-child{
            margin-bottom: -0.05rem;
        }
    }
    border: 0.1em solid #ccc;  /* Light border color */
    box-shadow: inset 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.1), inset -0.1em -0.1em 0.2em rgba(255, 255, 255, 0.2); /* Inner shadow for sunken effect */
}

.item-audit-header {
    display: flex;
    cursor: pointer;
    flex-direction: row; 
    justify-content: flex-start;
    align-items: center;
    div:not(:last-child) {
        margin-right: 0.5em;
    }

}
.item-audit-status{
    padding: 1em;
    font-size: smaller;
    width: 100%;
    background: white;
    .item-audit-check-status {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
}

.audit-summary {
    margin-left: 0.5em;
    width: fit-content;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: 0.1em;
    div {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-right: 0.5em;
        i {
            margin-right: 3px;
        }
    }
}

.item-audit-row {
    cursor: pointer;
    display: flex;
    flex-direction: row; 
    justify-content: space-between;
    align-items: center;
}