<div *ngIf="publishingCtrl">
  <button class="button is-main" [disabled]="publishingCtrl.isPublishingTestDesign || publishingCtrl.isPublishingOngoing() || isReadOnly()" (click)="publishingCtrl.publishTestDesign()"><tra slug="ie_publish_test_design"></tra></button>
  <div class="notification" *ngIf="publishingCtrl.isPublishingTestDesign">
    <p>
      Generating test design for publication...
      ({{publishingCtrl.displayProgressPercentage()}})
    </p>
    <p class="has-text-danger">
      <b>Do NOT refresh the page</b>
    </p>
  </div>
  <div style="margin-top:1em;" *ngIf="publishingCtrl.testDesignReleaseHistory">
    <strong><tra slug="ie_publishing_history"></tra>: </strong> 
    <button [disabled]="isReadOnly()" (click)="publishingCtrl.isShowingTestDesignReleaseHistory = !publishingCtrl.isShowingTestDesignReleaseHistory" class="button is-small">
      <span>
        {{publishingCtrl.testDesignReleaseHistory.length}} 
        <tra slug="ie_release_history_releases"></tra>
      </span>
    </button>
    <tra *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory" slug="auth_releases_shown"></tra> <input *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory" type="number" min="1" class="input is-small" [formControl]="filterNum" style="width:4em;">
    <div style="display: flex; align-items: center; margin-bottom: 1em"><tra style="margin-right: 0.5em" slug="ie_pub_ver_pwd_protected"></tra> <check-toggle [isChecked]="itemBankCtrl.publicPwdProtected" (toggle)="publishingCtrl.togglePwdProtected($event)"></check-toggle></div>
    <div *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory && resetter" class="space-between" style="align-items:flex-start;">
      <div style="display: flex; flex-direction: column; gap: 1em;">
        <div>
          <button class="button is-small" (click)="publishingCtrl.loadTestDesignReleaseHistory()"><tra slug="mrkg_refresh"></tra></button>
        </div>
        <table class="table is-condensed is-small is-bordered" style="width:auto;">
          <tr>
            <th><tra slug="ie_id"></tra></th>
            <th><tra slug="ie_name"></tra></th>
            <th><tra slug="lbl_stat"></tra></th>
            <th><tra slug="lbl_curr_fraction_publishing"></tra></th>
            <th><tra slug="lbl_last_completed_stage"></tra></th>
            <th><tra slug="ie_item_bank_style_profile"></tra></th>
            <th><tra slug="ie_date_released"></tra></th>
            <th><tra slug="Sign Off"></tra></th>
            <th>Forms</th>
            <th>Public</th>
          </tr>
          
          <tr *ngFor="let record of getReleases()" [class.is-publish-focus]="activePublishedTestDesign==record.id">
              <td>{{record.id}}</td>
              <td>
                <div style="font-size: 1.2em;">
                  {{record.name}}
                </div>
                <div *ngIf="record.twtdars" style="margin-top:0.5em; margin-left:1em">
                  <strong><u>Administration Windows</u></strong>
                  <ul  style="margin-top:0em; "> 
                    <li *ngFor="let twtdar of record.twtdars">
                      <div>
                        <strong>{{renderTwTitle(twtdar.title)}}</strong>
                      </div>
                      <div>
                        {{renderDate(twtdar.date_start, 'datefmt_long_date_line2')}} to
                        {{renderDate(twtdar.date_end, 'datefmt_long_date_line2')}}
                        <a (click)="twtdar.__isExpanded=!twtdar.__isExpanded">(more)</a>
                      </div>
                      <div *ngIf="twtdar.__isExpanded">
                        <code>
                          [{{twtdar.tw_id}}] {{twtdar.type_slug}}
                          <div style="color: #888">
                            {{twtdar.user_metas_filter}}
                          </div>
                        </code>
                      </div>
                    </li>
                  </ul>
                </div>
              </td>
              <td>
                <span *ngIf="record.is_publishing_in_progress == 1 && record.is_error == 0">
                  <tra slug="lbl_status_inprog"></tra>
                </span>
                <span *ngIf="record.is_publishing_in_progress == 0 && record.is_error == 0">
                  <tra slug="lbl_status_published"></tra>
                </span>
                <div style="display: flex; align-items: center;" *ngIf="record.is_error == 1">
                  <tra slug="lbl_error"></tra> &nbsp;
                  <button
                  *ngIf="record.is_error && !record.twtdars"
                  style="margin-right: 0;"
                  class="button is-small is-danger"
                  (click)="publishingCtrl.revokeTestDesigns(record.id)"
                  >
                    <i class="fas fa-trash" aria-hidden="true"></i>
                  </button>
                </div>
              </td>
              <td>
                {{record.curr_fraction_publishing}}
              </td>
              <td>
                {{record.last_stage_completed}}
              </td>
              <td>
                <tra [slug]="getStyleProfileCaption(record.style_profile_id)"></tra>
              </td>
              <td>{{renderDate(record.published_on || record.created_on, 'datefmt_timestamp')}}</td>
              <td>
                <button *ngIf="!isSignOffStarted(record)" [disabled]="isSignOffLoading" (click)="startTestDesignSignOff(record)" class="button is-small">Sign-Off</button>
                <div *ngIf="isSignedOff(record)">
                  <span *ngIf="hasComparedTestDesign(record)" class="compared-to">
                    Compared to {{record.compared_to_td_id}}
                  </span>
                  <span *ngIf="!hasComparedTestDesign(record)" class="compared-to">
                    Initial
                  </span>
                  &nbsp;
                  Signed off by {{record.approved_by}} on {{record.approved_on}}
                </div>
                <div *ngIf="isSignOffStarted(record) && !isSignedOff(record)">
                  Sign off <span class="compared-to">in progress</span> started by {{record.started_by}} on {{record.started_on}}
                  <br>
                  <a (click)="restoreTestDesignSignOff(record.tdso_id)">Click here to continue</a>
                </div>
              </td>
              <td>
                <div>
                  <a *ngIf="activePublishedTestDesign !== record.id" (click)="selectPublishedTestDesign(record.id)">
                    View ({{record.num_forms}} {{record.num_forms == 1 ? 'form' : 'forms'}})
                  </a>
                  <a *ngIf="activePublishedTestDesign === record.id" (click)="clearActiveTestDesignForms()" class="has-text-danger">
                    Close View ({{record.num_forms}} {{record.num_forms == 1 ? 'form' : 'forms'}})
                  </a>
                </div>
                <div style="margin-top: 0.2em">
                  <button 
                   *ngIf="publishingCtrl.isMultiForm()"
                    class="button is-small is-link is-light"
                    [disabled]="publishingCtrl.isPublishingTestDesign || isReadOnly()" 
                    (click)="publishingCtrl.publishTestDesign(record.id)"
                  >
                    Publish More
                  </button>
                </div>
              </td>
              <td>
                <check-toggle [isChecked]="record.is_public==1" (toggle)="publishingCtrl.toggleTestDesignIsPublic(record)"></check-toggle>
              </td>
          </tr>
        </table>
      </div>
      <div *ngIf="activeTestDesignForms" style="margin-left: 2em; position: relative">
        <button class="close-button button is-small is-danger is-outlined" (click)="clearActiveTestDesignForms()"><tra slug="ie_close_modal"></tra></button>
        <h4>Associated Test Forms/Panels</h4>
        <p>Student counts indicated below include sample school students.</p>
        <div style="display: flex; flex-direction: row; justify-content: space-between;">
          <button class="button is-dark is-small" (click)="downloadFormsTable()" style="margin: 1em 0">Download table</button>
          <button class="button is-dark is-small" (click)="downloadTFMITable()" style="margin: 1em 0">Download TFMI table</button>
        </div>
        <table class="table is-condensed is-small is-bordered" style="width:auto;">
          <tr>
            <td *ngFor="let colTitle of formTableColumnTitlesArr">{{colTitle}}</td>
          </tr>
          <tr *ngFor="let testForm of activeTestDesignForms">
            <td>{{testForm.id}}</td>
            <td>{{testForm.source_tf_id}} </td>
            <td>{{testForm.lang}}</td>
            <td>{{testForm.num_students_assigned}}</td>
            <td>{{testForm.num_students_submitted}}</td>
            <td>{{renderDate(testForm.created_on, 'datefmt_timestamp')}}</td>
            <td>{{renderDate(testForm.last_assigned_on, 'datefmt_timestamp')}}</td>
            <td>
              <span *ngIf="testForm.is_revoked == 1">
                Revoked <button (click)="unrevokeForm(testForm)">Undo</button>
              </span>
              <span *ngIf="testForm.is_revoked == 0">
                <button style="color:red;" (click)="revokeForm(testForm)">Revoke</button>
              </span>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <!-- to do -->
  </div>
</div>