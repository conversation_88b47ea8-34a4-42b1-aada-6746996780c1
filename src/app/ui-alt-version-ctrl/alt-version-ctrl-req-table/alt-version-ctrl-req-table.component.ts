import { Component, OnInit, ViewChild, ElementRef, Input, SimpleChanges } from '@angular/core';
import { ColumnApi, GridApi, ColDef, GridOptions, IGetRowsParams  } from 'ag-grid-community';
import { RoutesService } from 'src/app/api/routes.service';
import { AuthService } from 'src/app/api/auth.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { LangService } from "src/app/core/lang.service";
import { isStartedPopup, confirmCancelPopup} from '../data/types';
import { LoginGuardService } from '../../api/login-guard.service';
import { IAgreementConfirmConfig } from 'src/app/ui-schooladmin/view-schooladmin-dashboard/data/types';
import * as moment from 'moment-timezone';
import { saveAs } from 'file-saver';
import {TemplateFormatType, TargetStatus, templateStatuses} from './../view-alt-version-ctrl-settings/types'
import { Observable, of } from 'rxjs';
import {BRAILLE_BUSINESSDAY_OFFSET, OTHER_BUSINESSDAY_OFFSET, Modal, ACTIONS, ALT_VERSION_REQUEST_STATUS,  ClientFilters, dateTimeFiltercolumns, ReqFieldGroupTypes, RequestQueryMode, reqFieldGroups, GridType, ReqFieldGroup, MutExclusiveToggle} from './types'
import {TestWindowType, ExtendedColDef} from './types'
import qs from "qs";
import { downloadCsv } from './../../core/util/download'
 
//Constants
const REQS_PER_PAGE = 10;

@Component({
  selector: 'alt-version-ctrl-req-table',
  templateUrl: './alt-version-ctrl-req-table.component.html',
  styleUrls: ['./alt-version-ctrl-req-table.component.scss']
  // encapsulation: ViewEncapsulation.None
})
export class AltVersionCtrlReqTableComponent implements OnInit {

  @Input() requestFilter?: string;
  @Input() title: string;

  selectedRequest;
  ACTIONS = ACTIONS;

  // Grouping fields in detailed view
  reqFieldGroups = reqFieldGroups;
  ReqFieldGroupTypes = ReqFieldGroupTypes
  
  // Modal
  pageModal: PageModalController;
  Modal = Modal;
  
  //Mutually exclude filter toggles
  MutExclusiveToggle = MutExclusiveToggle

  // Shipping Info Input
  shippingCompanySample: string;
  trackingNumberSample: number;
  trackingUrlSample: number;
  shippingCompanyOp: string;
  trackingNumberOp: number;
  trackingUrlOp: string;
  trackingNumberReturn: number;
  trackingUrlReturn: string;
  deliveredDateSample;
  deliveredDateOperational;
  
  //notes
  notes: string[];
  notesInput: string;

  //Toggles below the table
  showPendingReq: boolean = false;
  showApprovedReq: boolean = false;
  showAutopOpReadyReq: boolean = false;
  showActiveReq: boolean = true;
  showAutoErrorsReq:boolean = false;

  //Actions in progress
  isProgress: {
    export:boolean,
    approve:boolean,
    operational:boolean,
    reject:boolean
  } = {
    export:false,
    approve:false,
    operational:false,
    reject:false
  }

  //Export modal
  exportFileName:string;

  //Template preview modal
  TemplateFormatType = TemplateFormatType;
  TargetStatus = TargetStatus;
  messagePreviewTab:TargetStatus;

  constructor(
    private routes:RoutesService,
    private auth:AuthService,
    private pageModalService: PageModalService,
    public lang: LangService,
    private  loginGuard: LoginGuardService,
  ) { }
  // Request ag-grid information
  requestGridColumnApi: ColumnApi;
  requestGridApi: GridApi;
  requestRows=[]
  columnDefs: ExtendedColDef[] = [
    {width:50, checkboxSelection:true },
    //ID - Request Number
    { headerName:'ID', field:'id', width:75},
    // Date of Request
    { headerName: this.lang.tra('request_date_alt_version'), field:'created_on', width:180, cellRenderer: this.parseDate.bind(this), filter: 'agDateColumnFilter'},
    // Request Status
    { headerName: this.lang.tra('request_status_alt_version'), field:'status_string', width:175,},
    // Has custom template(s)
    { headerName: this.lang.tra('has_custom_templates_alt_version'), field:'has_custom_templates', width:175,},
    //Sample Test Date
    { hide: true, headerName: this.lang.tra('sample_admin_date_alt_version'), field:'sample_administration_date', width:225, cellRenderer: this.parseDate.bind(this) },
    //Assessment Date  (Operational Administration Date)
    { headerName: this.lang.tra('op_admin_date_alt_version'), field:'op_administration_date', width:225, filter: 'agDateColumnFilter', cellRenderer: params => this.parseOpDateWithAutoOpColour(params, "red")},
    // Tentative Assessment Date
    { headerName: this.lang.tra('tent_assessment_date_alt_version'), field:'tent_session_date', width:175, filter: 'agDateColumnFilter', cellRenderer: function(params){
      if(!params.value) return null;
      return moment(params.value, "YYYY-MM-DD").format("YYYY-MM-DD");
    }},
    //Request Format(s)
    { headerName: this.lang.tra('req_formats_alt_version'), field:'requested_formats', width:400,},
    //Prerequisite type
    { headerName: this.lang.tra('prereq_type_col_alt_version'), field:'prereq_type', width:175, hide:true},
    //Student Name
    { headerName: this.lang.tra('student_name_alt_version'), field:'student_name', width:175, },
    //Student OEN
    { headerName: this.lang.tra('oen_alt_version'), field:'student_oen', width:175, },
    //Student SASN
    { hide: true, headerName: this.lang.tra('sasn_alt_version'), field:'student_sasn', width:175, },
    //Assessment Name
    { hide: true, headerName: this.lang.tra('assessment_name_alt_version'), field:'assessment_name', width:200, cellRenderer: function(params){
      if (!params.value) return;
      const names = JSON.parse(params.value);
      return `${names.en}/${names.fr}`;
    }},
    //Class Name
    { hide: true, headerName: this.lang.tra('class_name_alt_version'), field:'class_name', width:175, },
    //Class Type
    { headerName: this.lang.tra('class_group_type_alt_version'), field:'class_group_type', width:175, },
    //Test Window ~ Administration Window
    { headerName: this.lang.tra('test_window_alt_version'), field:'test_window_id', width:175, },

    { hide: true, headerName: this.lang.tra('alt_version_access_link_pdf_regular_sample'), field:'sample_link_pdf_regular', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute },
    { hide: true, headerName: this.lang.tra('alt_version_access_link_pdf_large_sample'), field:'sample_link_pdf_large', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_mp3_sample'), field:'sample_link_mp3', getCustomHeaderName: (params) => this.getMp3AccessLinkName(params), cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_asl_sample'), field:'sample_link_asl', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_ebraille_sample'), field:'sample_link_ebraille', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_pdf_regular_assessment'), field:'operational_link_pdf_regular', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_pdf_large_assessment'), field:'operational_link_pdf_large', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_mp3_assessment'), field:'operational_link_mp3', getCustomHeaderName: (params) => this.getMp3AccessLinkName(params, true), cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_asl_assessment'), field:'operational_link_asl', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    { hide: true, headerName: this.lang.tra('alt_version_access_link_ebraille_assessment'), field:'operational_link_ebraille', cellRenderer: this.renderLink.bind(this), exportRenderer: this.renderLinkAbsolute},
    // Is FI
    { headerName: this.lang.tra('is_fi_col_alt_version'), field:'is_fi_current', width:175, valueGetter: this.formatYesNo.bind(this)},
    // FI Change
    { hide: true, headerName: this.lang.tra('fi_change_col_alt_version'), field:'is_fi_submit', width:175, valueGetter: params => {
      const {is_fi_submit, is_fi_current} = params.data
      if (is_fi_submit == 0 && is_fi_current == 1) return "Regular to FI"
      else if (is_fi_submit == 1 && is_fi_current == 0) return "FI to Regular"
      else return null
    }},
    // FI Option
    { hide: true, headerName: this.lang.tra('fi_option_col_alt_version'), field:'fi_option_current', width:175, },
    // Sch Mident
    { headerName: this.lang.tra('sch_mident_alt_version'), field:'schl_mident', width:175, },
    //Sch Name
    { headerName: this.lang.tra('sch_name_alt_version'), field:'schl_name', width:175, },
    //Sch Admin Name
    { headerName: this.lang.tra('sch_admin_name_alt_version'), field:'schl_admin_name', width:175, },
    //Sch Admin Email
    { headerName: this.lang.tra('sch_admin_email_col_alt_version'), field:'schl_admin_email', width:175, },
    //NON-BRAILLE sch admin phone
    { hide: true, headerName: this.lang.tra('sch_admin_phone_non_braille_alt_version'), field:'schl_admin_phone_non_braille', width:175, },
    //Teacher Name
    { headerName: this.lang.tra('teacher_alt_version'), field:'teacher_name', width:175, },
    //Teacher Email
    { headerName: this.lang.tra('teacher_email_col_alt_version'), field:'teacher_email', width:175, },
    //Language 
    { headerName: this.lang.tra('language_alt_version'), field:'lang', width:175, },
    //Alt Version Reason
    { headerName: this.lang.tra('reason_alt_version'), field:'reason', width:175, },
    // Approved by
    { hide: true, headerName: this.lang.tra('approved_by_alt_version'), field:'approved_by_email', width:175, },
    // Date Approved
    { hide: true, headerName: this.lang.tra('date_approved_alt_version'), field:'approved_on', width:180, cellRenderer: this.parseDate.bind(this)},
    // Operational Send by
    { hide: true, headerName: this.lang.tra('operational_send_by_alt_version'), field:'operational_send_by_email', width:175, },
    // Date Operational Send
    { hide: true, headerName: this.lang.tra('date_operational_send_alt_version'), field:'operational_send_on', width:180, cellRenderer: this.parseDate.bind(this)},
    // Cancelled By
    { hide: true, headerName: this.lang.tra('canceled_by_alt_version'), field:'canceled_by_email', width:175, valueGetter: params => {
      if(params.data.canceled_by_email) return params.data.canceled_by_email
      else if (params.data.rejected_by_email) return params.data.rejected_by_email
      else return null
    }},
    // Date Cancelled
    { hide: true, headerName: this.lang.tra('canceled_on_alt_version'), field:'canceled_on', width:180, cellRenderer: params => {
      if(params.data.canceled_on) return this.parseDate({value: params.data.canceled_on})
      else if (params.data.rejected_on) return this.parseDate({value: params.data.rejected_on})
      else return null
    }},
    //Started test?
    { hide: true, headerName: this.lang.tra('is_started_alt_version'), field:'is_started', width: 175, valueGetter: this.formatYesNo.bind(this)},
    //TW is QA?
    { hide: true, headerName: this.lang.tra('is_tw_qa_alt_version'), field: 'tw_is_qa', valueGetter: this.formatYesNo.bind(this)},
    // BRAILLE Sch contact
    { hide: true, headerName: this.lang.tra('sch_contact_braille_alt_version'), field:'schl_contact_braille', width:175, },
    //BRAILLE sch Address
    { hide: true, headerName: this.lang.tra('sch_address_braille_alt_version'), field:'schl_address_braille', width:175, },
    //BRAILLE Sch Email
    { hide: true, headerName: this.lang.tra('sch_email_braille_alt_version'), field:'schl_email_braille', width:175, },
    //BRAILLE Sch Phone
    { hide: true, headerName: this.lang.tra('sch_phone_alt_version'), field:'schl_phone_braille', width:175, },
    //Shipping Required
    { hide:true, headerName: this.lang.tra('ship_required_alt_version'), field:'shipping_required', width:175, valueGetter: this.formatYesNo.bind(this)},
    //Shipping Company (sample)
    { hide:true, headerName: this.lang.tra('ship_company_sample_alt_version'), field:'shipping_company_sample', width:175, },
    //Tracking num (sample)
    { hide:true, headerName: this.lang.tra('ship_track_sample_alt_version'), field:'tracking_number_sample', width:175, cellRenderer: this.parseSampleTracking.bind(this), exportRenderer: this.renderSimple},
    //Tracking URL Sample - Hidden column for export
    { hide:true, headerName: this.lang.tra('ship_track_url_sample_alt_version'), field:'tracking_url_sample', width:175},
    //Date Delivered to School (sample)
    { hide:true, headerName: this.lang.tra('ship_deliver_date_sample_alt_version'), field:'shipping_delivered_date_sample', width:175, },
    //Shipping Company (assessment)
    { hide:true, headerName: this.lang.tra('ship_company_op_alt_version'), field:'shipping_company_operational', width:175, },
    //Tracking num (assessment)
    { hide:true, headerName: this.lang.tra('ship_track_op_alt_version'), field:'tracking_number_operational', width:175, cellRenderer: this.parseOpTracking.bind(this), exportRenderer: this.renderSimple},
    //Tracking URL assessment - Hidden column for export
    { hide:true, headerName: this.lang.tra('ship_track_url_op_alt_version'), field:'tracking_url_operational', width:175, },
    //Date Delivered to School (Assessment)
    { hide:true, headerName: this.lang.tra('ship_deliver_date_op_alt_version'), field:'shipping_delivered_date_operational', width:175, },
    //Tracking num (Return)
    { hide:true, headerName: this.lang.tra('ship_track_return_alt_version'), field:'tracking_number_return', width:175, cellRenderer: this.parseReturnTracking.bind(this), exportRenderer: this.renderSimple},
    //Tracking URL Return - Hidden column for export
    { hide:true, headerName: this.lang.tra('ship_track_url_return_alt_version'), field:'tracking_url_return', width:175, },
    //Fully Hidden field
    {hide: true, invisible:true, field: 'tw_is_active'},
    //Error auto-sending assessment
    { headerName: this.lang.tra('auto_op_send_error_alt_version'), field:'auto_op_send_error', width:400, },
  ];


  requestGridOptions:GridOptions = {
    columnDefs: this.columnDefs,
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
      filterParams:{
        suppressAndOrCondition: true // Filtering is on API side due to pagination, complex combinations not implemented
      },
    },
    rowSelection: 'single',
    enableCellTextSelection: true,
    cacheBlockSize: REQS_PER_PAGE,
    paginationPageSize: REQS_PER_PAGE,
    rowModelType: 'infinite',
    onFirstDataRendered: (event: any) => {this.toggleRequests()},
    onSelectionChanged: (event: any) => {
    },
    onRowSelected: (event: any) => {
      const requests = event.api?.getSelectedRows();

      // When one row selected, enter detailed view
      if (requests.length == 1) this.loadSelectedRequest(requests[0].id);
      else this.selectedRequest = null;
    }
  };

  /** Return if the selected request meets the conditions for a certain button to be enabled for it */
  isButtonEnabled = (action:ACTIONS) => {
    if (!this.selectedRequest) return false;
    const currStatus = this.selectedRequest.status
    const shippingRequired = this.selectedRequest.shipping_required
    switch(action){
      case(ACTIONS.APPROVE):
        return (currStatus == ALT_VERSION_REQUEST_STATUS.Pending)
      case(ACTIONS.REJECT):
        return (currStatus !== ALT_VERSION_REQUEST_STATUS.Canceled)
      case(ACTIONS.OPERATIONAL):
        return (currStatus == ALT_VERSION_REQUEST_STATUS.Approved)
      case(ACTIONS.MESSAGE_PREVIEW):
        return (currStatus == ALT_VERSION_REQUEST_STATUS.Pending || currStatus == ALT_VERSION_REQUEST_STATUS.Approved)
      case(ACTIONS.SHIPPING):
        return(shippingRequired && currStatus !== ALT_VERSION_REQUEST_STATUS.Canceled)
    }
  } 

  /** Store more details about the request, and fetch its notes */
  async loadSelectedRequest(requestId:number){
    this.selectedRequest = await this.getRequestDetails(requestId);
    this.loadNotes(requestId);
  }

  /** Get more details about the request */
  async getRequestDetails(requestId:number){
    return await this.auth.apiGet(this.routes.ALT_VERSION_CTRL_REQUESTS, requestId, {})
  }

  onGridReady(params) {
    this.requestGridColumnApi = params.columnApi;
    this.requestGridApi = params.api;

    // Set up data source for API-side pagination
    const datasource = {
      getRows: (params: IGetRowsParams) => {
        // Pass the offset and limit based on the first row of the new page, and the page size
        this.loadRequests(params.startRow, this.requestGridOptions.paginationPageSize)
        .subscribe(data => { 
          params.successCallback(data.requests, data.count) 
        });
      }
    }
    this.requestGridApi.setDatasource(datasource);
  }

  /**
   * Prepare filters for API-side filtering: adjust ag-grid filters, manually add more filters based on selected toggles
   * @returns Object with request properties as keys and filter objects as values
   */
  prepFilters(): ClientFilters {

    /**
     * Remove extra characters from the year only
     * e.g. Given "202333-09-01 00:00:00" return "2023-09-01 00:00:00"
     * Given "2023-09-01 00:00:00" return without changes
     */
    const dateFormatEnforceYYYY = (input:string) => {
      return input.substring(0, 4) + input.substring(input.indexOf('-'))
    }

    const localToUTC = (inputDate) => {
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      return moment.tz(inputDate, userTimezone).utc().format('YYYY-MM-DD HH:mm:ss');
    }

    const filters = this.requestGridApi.getFilterModel();    

    //Modify filters chosen on ag-grid
    for (const [prop, filter] of Object.entries(filters)) {

      // Somehow while the filter has yyyy-mm-dd placeholder it lets you type up to 6 numbers into the year e.g. 202333-09-01
      // But a valid YYYY year is needed in the API query
      // If the year exceeds four characters, trim it and reset the filters on the UI - on the next pass it will not have to be corrected
      if (filter.dateFrom) {
        const ogDateFrom = filter.dateFrom
        filter.dateFrom = dateFormatEnforceYYYY(ogDateFrom)
        if (ogDateFrom !== filter.dateFrom) this.requestGridApi.setFilterModel(filters)
      }
      if (filter.dateTo) {
        const ogDateTo = filter.dateTo
        filter.dateTo = dateFormatEnforceYYYY(ogDateTo)
        if (ogDateTo !== filter.dateTo) this.requestGridApi.setFilterModel(filters)
      }

      // In datetime columns, convert back to UTC from client's timezone
      if(dateTimeFiltercolumns.has(prop)) {
        filter.dateFrom = localToUTC(filter.dateFrom);
        filter.dateTo = localToUTC(filter.dateTo);
      }
    }

    //Add/replace additional filters based on toggles
    if (this.showApprovedReq) {
      filters.status = {filterType: 'text', type: 'equals', filter: ''+ALT_VERSION_REQUEST_STATUS.Approved}
    }
    if (this.showPendingReq) {
      filters.status = {filterType: 'text', type: 'equals', filter: ''+ALT_VERSION_REQUEST_STATUS.Pending}
    }
    if (this.showAutoErrorsReq) {
      filters.auto_op_send_error = {filterType: 'text', type: 'notEqual', filter: null}
    }
    if (this.showActiveReq){
      filters.tw_is_active = {filterType: 'text', type: 'equals', filter: '1'}
      filters.tw_is_over = {filterType: 'text', type: 'equals', filter: '0'}
      // In the request table, filter out QA test windows.
      // In QA table, allow requests from QA test windows to be seen as active requests.
      if (this.requestFilter == GridType.REAL) filters.tw_is_qa = {filterType: 'text', type: 'equals', filter: '0'}
    }
    // Handled in a custom way from other filters, so only indicate if it's on
    if (this.showAutopOpReadyReq){
      filters.showAutopOpReadyReq = true
    }
    return filters;
  }

  async ngOnInit(): Promise<void> {
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  loadRequests(offset:number, limit:number): Observable<{ requests: any[], count: number }> {
    this.requestGridApi.deselectAll();
    return new Observable(observer => {
      const filters = this.prepFilters();
      const sortRules = this.requestGridApi.getSortModel();

      this.auth.apiFind(this.routes.ALT_VERSION_CTRL_REQUESTS, {
        query: {
            //Filters and sort objects are encoded into url params
            clientFilters: qs.stringify(filters),
            //Only the latest sorting is applied
            clientSort: sortRules.length? qs.stringify(sortRules[0]) : undefined,
            requestType: this.requestFilter,
            requestQueryMode: RequestQueryMode.ALT_CONTROLLER_BASIC,
            offset,
            limit
        }})
      .then((requestData) => {
        const {requests, count} = requestData;
        observer.next({requests, count});
        observer.complete();
      })
    });
  }

  onPaginationChanged($event){
    this.selectedRequest = null;
  }

  async loadNotes(id: number) {
    this.notesInput = null;
    try{
      this.notes = await this.auth.apiFind(this.routes.ALT_VERSION_CTRL_REQUEST_NOTES, {query: {alt_version_req_ids: id}});
    } catch {
      this.notes = null;
    }
  }

  /** Return whether the selected request has operational link sent*/
  isOperationalSent(){
    if (this.selectedRequest.status == ALT_VERSION_REQUEST_STATUS.Operational_Link_Send) return true
    return false
  }

  /** Return whether the selected request has been approved */
  isApproved():boolean {
    return [ALT_VERSION_REQUEST_STATUS.Approved, ALT_VERSION_REQUEST_STATUS.Operational_Link_Send].includes(this.selectedRequest.status)
  }

  /** Which template format is needed depending on the request */
  getTemplateFormat():TemplateFormatType{
    let format:TemplateFormatType = TemplateFormatType.PDF_OTHER
    if (this.selectedRequest.requests_braille_regular) format = TemplateFormatType.BRAILLE_REGULAR
    else if (this.selectedRequest.requests_online_only) format = TemplateFormatType.ONLINE_ONLY
    return format
  }

  /**
   * List of tabs that can be viewed in the preview message modal for the selected request
   * Don't show tabs where it doesn't make sense to edit the template
   * E.g. The sample/approval status change can only happen if the current status is pending
   */
  getValidPreviewTabs():{value: TargetStatus; caption:string}[]{
    const currReqStatus = this.selectedRequest.status
    return templateStatuses.filter(status => {
      if (status.value == TargetStatus.SAMPLE) return currReqStatus == ALT_VERSION_REQUEST_STATUS.Pending
      else if (status.value == TargetStatus.ASSESSMENT) return (currReqStatus == ALT_VERSION_REQUEST_STATUS.Pending || currReqStatus == ALT_VERSION_REQUEST_STATUS.Approved)
      else if (status.value == TargetStatus.CANCEL) return (currReqStatus == ALT_VERSION_REQUEST_STATUS.Pending || currReqStatus == ALT_VERSION_REQUEST_STATUS.Approved)
    })
  }

  previewModalStart(){
    this.messagePreviewTab = this.getValidPreviewTabs()[0].value
    this.pageModal.newModal({type: Modal.PREVIEW_MESSAGE, config: {reqId: this.selectedRequest.id}, finish: () => {}, cancel: this.previewModalClose.bind(this)});
  }

  /** 
   * When the preview modal closes, templates could have been changed between default and custom.
   * Re-pull the data for the request, update the detailed view and the table
   */
  async previewModalClose(config: {reqId: number}){
    this.selectedRequest = await this.getRequestDetails(config.reqId);
    // Update data in the table
    this.requestGridOptions.api.forEachNode(node => {
      if (node.data.id === config.reqId) {
        const updatedData = {...node.data, has_custom_templates: this.selectedRequest.has_custom_templates}
        node.setData(updatedData);
      }
    })
  }

  shippingModalStart() {
    const data = this.selectedRequest;
    this.shippingCompanySample = data.shipping_company_sample;
    this.shippingCompanyOp = data.shipping_company_operational;
    this.trackingNumberSample = data.tracking_number_sample;
    this.trackingNumberOp = data.tracking_number_operational;
    this.trackingNumberReturn = data.tracking_number_return;
    this.trackingUrlSample = data.tracking_url_sample;
    this.trackingUrlOp = data.tracking_url_operational;
    this.trackingUrlReturn = data.tracking_url_return;
    this.deliveredDateSample = data.shipping_delivered_date_sample
    this.deliveredDateOperational = data.shipping_delivered_date_operational
    this.pageModal.newModal({type: Modal.SHIPPING, config: {}, finish: this.shippingModalFinish});
  }

  shippingModalFinish = (config) =>  {
    const requestId = this.selectedRequest.id
    let potentialShippingUpdates = {
      shipping_company_sample: this.shippingCompanySample,
      tracking_number_sample: this.trackingNumberSample,
      tracking_url_sample: this.trackingUrlSample,
      shipping_company_operational: this.shippingCompanyOp,
      tracking_number_operational: this.trackingNumberOp,
      tracking_url_operational: this.trackingUrlOp,
      tracking_number_return: this.trackingNumberReturn,
      tracking_url_return: this.trackingUrlReturn,
      shipping_delivered_date_sample: this.deliveredDateSample, 
      shipping_delivered_date_operational: this.deliveredDateOperational
    }
    this.auth.apiPatch(this.routes.ALT_VERSION_CTRL_REQUESTS, requestId, potentialShippingUpdates, {query: {action: ACTIONS.SHIPPING}})
    .then(() => {
      // Find the row corresponding to the request (only one)
      this.requestGridOptions.api.forEachNode(node => {
        if (node.data.id === requestId) {
          const updatedData = {...node.data, ...potentialShippingUpdates}
          // Update data in the table
          node.setData(updatedData);
          // If the detailed view is open, update data there too
          if (this.selectedRequest) this.selectedRequest = {...this.selectedRequest, ...potentialShippingUpdates}
        }
      })
      // Close the modal
      this.pageModal.closeModal();
    }).catch((err) => {
      this.loginGuard.quickPopup("There was an error saving your shipping info: \n\n" + err.message)
    });
  }

  saveNote() {
    const reqId = this.selectedRequest.id;
    if( this.notesInput == null || this.notesInput.length === 0 || this.notesInput.trim().length === 0) {
      return;
    }
    const data = {
      alt_version_request_id: reqId,
      note: this.notesInput,
    }
    this.auth.apiCreate(this.routes.ALT_VERSION_CTRL_REQUEST_NOTES, data).then((res) => {
      res.username = this.auth.getFirstName() + " " + this.auth.getLastName()
      this.notes.push(res);
      this.notesInput = null;
    }).catch((err) => {
      console.error(err);
    });
  }

  toggleRequests(triggerSource = null){
    //If pending toggle on, turn off approved toggle and vice versa (since having both on would always leave no records)
    if (triggerSource === MutExclusiveToggle.PENDING && this.showPendingReq) this.showApprovedReq = false
    else if (triggerSource === MutExclusiveToggle.APPROVED && this.showApprovedReq) this.showPendingReq = false
    
    // Manually set the grid pagination to the first page
    this.requestGridApi.paginationGoToFirstPage();
    // Trigger a refresh of the grid data
    this.requestGridApi.purgeInfiniteCache();
  }

  exportModalStart(){
    this.exportFileName = null;
    this.pageModal.newModal({
      type: Modal.EXPORT,
      config: {},
      finish: () => this.exportRequests()
    });
  }

  /**
   * Fetch the detailed request data and associated notes, format for export
   */
  async exportRequests() {

    this.isProgress.export = true;
    let rawRequests: any[] = [];

    // Get the count, filters, and sort that is in the current table
    const totalCount = this.requestGridApi.paginationGetRowCount()
    const filters = this.prepFilters();
    const sortRules = this.requestGridApi.getSortModel();

    let offset = 0;
    // Fetch all requests in page-size increments
    while (totalCount - offset > 0){
      const query = {
        //Filters and sort objects are encoded into url params
        clientFilters: qs.stringify(filters),
        //Only the latest sorting is applied
        clientSort: sortRules.length? qs.stringify(sortRules[0]) : undefined,
        requestType: this.requestFilter,
        requestQueryMode: RequestQueryMode.ALT_CONTROLLER_DETAIL,
        offset,
        limit: REQS_PER_PAGE
      }
      const requestRes = await this.auth.apiFind(this.routes.ALT_VERSION_CTRL_REQUESTS, {query});
      rawRequests = [...rawRequests, ...requestRes]
      offset += REQS_PER_PAGE;
    }
    
    // Find the the notes attached to these requests
    const requestIds = rawRequests.map(req => req.id).join(",")
    const rawRequestNotes =  await this.auth.apiFind(this.routes.ALT_VERSION_CTRL_REQUEST_NOTES, {query: {alt_version_req_ids: requestIds}})

    const columnHeaders = this.columnDefs.filter(col => !col.invisible).map(col => col.headerName).concat('Notes')
    
    const sanitizedData = rawRequests.map(rawData => {
      const cleanReqData = {}
      // Get the correctly rendered data
      Object.keys(rawData).forEach(dataField => {
        const colDefMatch = this.columnDefs.find(coldef => coldef.field == dataField && !coldef.invisible)
        if (colDefMatch) {
          cleanReqData[colDefMatch.headerName] = this.getFinalRenderedData(colDefMatch, rawData) || null
        }
      })
      // Attach formatted notes to the request they belong to
      cleanReqData['Notes'] = rawRequestNotes
      .filter(note => note.alt_version_request_id == rawData.id)
      .map(rawNote => {
        return `{Author: '${rawNote.username}', Text: '${rawNote.note}', Date: '${this.parseDate({value:rawNote.created_on})}'}`
      })
      .join(', ');
      return cleanReqData;
    })
    downloadCsv(sanitizedData, columnHeaders as string[], this.exportFileName as string);
    this.exportFileName = null;
    this.isProgress.export = false;
  }

  formatYesNo(params){
    if (!params.data) return null;
    return params.data[params.colDef.field] == 1 ? this.lang.tra('lbl_yes') : this.lang.tra('lbl_no')
  }

  /**
   * Parse the operational date, and colour it if it fits the auto-operational-ready criteria
   * @param params - Params of the table row 
   * @param colour - String of the colour to render to conditionally render the date in
   * @returns 
   */
  parseOpDateWithAutoOpColour(params, colour?:string){
    const opDate = params.value
    if(!opDate) return null;

    const iputDate = new Date(Date.parse(opDate));
    const parsedDate = iputDate.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric'});
    const parsedTime = iputDate.toLocaleTimeString('default');
    const renderedDate = `${parsedDate} ${parsedTime}`;
    
    // Colour if fits the criteria, colour provided, and not plain mode (when exporting)
    const isApprovedStatus = params.data.status === ALT_VERSION_REQUEST_STATUS.Approved
    const isTwActive = params.data.tw_is_active
    const isWithinAutosendOffset = this.isWithinOffset(opDate, !!params.data.shipping_required)
    const isColoured = isApprovedStatus && isTwActive && isWithinAutosendOffset && !params.plainTextMode && colour
    
    return isColoured ? `<div style="color: ${colour};">${renderedDate}</div>` : renderedDate
  }

  parseDate(params) {
    if(!params.value) return null;
    const current = new Date(Date.parse(params.value));
    const parsedDate = current.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric'});
    const parsedTime = current.toLocaleTimeString('default');
    return `${parsedDate} ${parsedTime}`;
  }

  renderSimple(params){
    return params.value
  }

  renderLink(params){
    const linkName = params.colDef.getCustomHeaderName ? params.colDef.getCustomHeaderName(params) : params.colDef.headerName
    return params.value ? `<a href="${params.value}" target="_blank">${linkName}</a>` : ''
  }

  /** New access links are relative, get the current homepage and append to it to get the url in the exported file 
   * But don't do it if it's an old link that was not relative
  */
  renderLinkAbsolute(params){
    const linkRecord = params.value
    if (!linkRecord) return null
    if (linkRecord[0] !== "/") return linkRecord
    const host = window.location.host
    return (host + linkRecord)
  }

  /**
   * Get the wording of the mp3 access link, because in OSSLT it specifies large or regular depending on the PDF type in the request
   * @param params 
   * @param isOperational - True if operational, otherwise sample
   * @returns The translated string
   */
  getMp3AccessLinkName(params, isOperational:boolean = false){
    const {assessment_type, requests_pdf_large, requests_pdf_regular} = params.data
    const isOsslt = assessment_type == TestWindowType.EQAO_G10L
    let slug = isOperational ? 'alt_version_access_link_mp3_assessment' : 'alt_version_access_link_mp3_sample'
    if (isOsslt) {
      if (requests_pdf_large && isOperational) slug = 'alt_version_access_link_mp3_large_assessment'
      else if (requests_pdf_regular && isOperational) slug = 'alt_version_access_link_mp3_regular_assessment'
      else if (requests_pdf_large) slug = 'alt_version_access_link_mp3_large_sample'
      else if (requests_pdf_regular) slug = 'alt_version_access_link_mp3_regular_sample'
    }
    return this.lang.tra(slug)
  }

  parseSampleTracking(params){
    if(params.data.tracking_number_sample) return `<a href=${params.data.tracking_url_sample} target=_blank>${params.data.tracking_number_sample}</a>`
    else return null
  }

  parseOpTracking(params){
    if(params.data.tracking_number_operational) return `<a href=${params.data.tracking_url_operational} target=_blank>${params.data.tracking_number_operational}</a>`
    else return null
  }

  parseReturnTracking(params){
    if(params.data.tracking_number_return) return `<a href=${params.data.tracking_url_return} target=_blank>${params.data.tracking_number_return}</a>`
    else return null
  }

  isStartedPopup() {
    if(this.selectedRequest.is_started != 1) {
      this.initStatusChange(ACTIONS.APPROVE)
    } else {
      this.showPopup(isStartedPopup, () => this.initStatusChange(ACTIONS.APPROVE));
    }
  }

  /** Confirm to cancel the selected request */
  confirmCancelPopup(){
    this.showPopup(confirmCancelPopup, () => this.initStatusChange(ACTIONS.REJECT));
  }

  /** Change the status of the request (Cancel, approve, or operational send) */
  async initStatusChange(action:ACTIONS, targetRequestId?:number){

    if (action == ACTIONS.APPROVE) this.isProgress.approve = true;
    else if (action == ACTIONS.REJECT) this.isProgress.reject = true;
    else if (action == ACTIONS.OPERATIONAL) this.isProgress.operational = true;

    const selectedRequest = this.requestGridOptions.api.getSelectedRows()[0]
    const requestId = targetRequestId ? targetRequestId : selectedRequest.id;
    const query = {action}

    // Perform the action
    this.auth.apiPatch(this.routes.ALT_VERSION_CTRL_REQUESTS, requestId, {}, {query})
    .then(() => {
      // Get the updated data for this request (the status, who changed the status, any links will change)
      return this.getRequestDetails(requestId)
    })
    .then(updatedRequestInfo => {
       // Find the row corresponding to the request (only one)
      this.requestGridOptions.api.forEachNode(node => {
        if (node.data.id === requestId) {
          // Update data in the table
          node.setData(updatedRequestInfo);
          // If the detailed view is open, update data there too
          if (this.selectedRequest) this.selectedRequest = updatedRequestInfo

          // If the request was just approved and the assessment date is within the offset, offer to send the assessment immediately
          if (action == ACTIONS.APPROVE && node.data.tw_is_active && this.isWithinOffset(node.data.op_administration_date, !!node.data.shipping_required)){
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('upcoming_op_send_confirm_alt_version'),
              btnProceedConfig: {caption: this.lang.tra('lbl_yes')},
              btnCancelConfig: {caption: this.lang.tra('lbl_no')},
              confirm: () => {
                this.initStatusChange(ACTIONS.OPERATIONAL, requestId)
              }
            })
          }

        }
      })
    })
    .catch((err) => {
      this.loginGuard.quickPopup("There was an error with this status change: \n\n" + err.message)
    })
    .finally(() => {
      if (action == ACTIONS.APPROVE) this.isProgress.approve = false;
      else if (action == ACTIONS.REJECT) this.isProgress.reject = false;
      else if (action == ACTIONS.OPERATIONAL) this.isProgress.operational = false;
    })
  }

 /**
  * @param opDate string of the assessment date (UTC)
  * @param isBrailleRegular If request contains braille regular formats
  * @returns True/False if the assessment date is before the appropriate number of business days from now
  */
 isWithinOffset(opDate:string, isBrailleRegular: boolean){
    if (!opDate) return false;
    const bdOffset = isBrailleRegular ? BRAILLE_BUSINESSDAY_OFFSET : OTHER_BUSINESSDAY_OFFSET
    const inputDate = moment.utc(opDate);
    // Start with current date and add target number of business days
    const targetDate = moment.utc();
    let businessDayCounter = 0;
    while (businessDayCounter < bdOffset) {
      // Skip weekends (6 = Saturday, 7 = Sunday)
      if (![6, 7].includes(targetDate.isoWeekday())) businessDayCounter++;
      targetDate.add(1, 'days');
    }

    return inputDate.isSameOrBefore(targetDate);
  }

  showPopup(agreement: Partial<IAgreementConfirmConfig>, callback?: (agreement: Partial<IAgreementConfirmConfig>) => void): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      const caption = this.lang.tra(agreement.captionSlug);
      const btnSlug = this.lang.tra(agreement.btnSlug);
      this.loginGuard.confirmationReqActivate({
        caption,
        btnProceedConfig: {
          caption: btnSlug
        },
        width: '25em',
        confirm: () => {
          callback(agreement);
          resolve();
        }
      });
    })
  }

  /** Use the column definition to get the text that was rendered, to be reused outside the ag-grid (in detailed view and exported csv)
   * @param columnDef - one of the entries from `columnDefs`
   * @param exportRequestData - when exporting only, pass the object with the raw data
   * @returns 
   */
  getFinalRenderedData(columnDef, exportRequestData?){
    if (columnDef.invisible) return;
    // Reconstruct the basics of the ag-grid parameters
    const targetRequest = exportRequestData || this.selectedRequest
    const params = {data:targetRequest, value:targetRequest[columnDef.field], colDef:columnDef, plainTextMode: !!exportRequestData}
    // Get the rendered cell content
    // If exporting, prioritize the exportRenderer custom function above cellRenderer etc.
    if (columnDef.exportRenderer && exportRequestData) return columnDef.exportRenderer(params)
    if (columnDef.cellRenderer) return columnDef.cellRenderer(params)
    else if (columnDef.valueGetter) return columnDef.valueGetter(params)
    else return params.value
  }

  /** Filter the column definitions that belong to the category only  */
  getFilteredColDefs(requestGroup:ReqFieldGroup){
    return this.columnDefs.filter(columnDef => requestGroup.columns.includes(columnDef.field))
  }

  /** Field is in the Links category */
  isLinkSection(reqFieldGroup){
    return reqFieldGroup.type == ReqFieldGroupTypes.LINKS
  }

}
