<div class="table-holder">
    <div style="display: flex; justify-content: space-between; padding-bottom: 1em;">
        <h2 class="section-header">
            <tra slug={{title}}></tra>
        </h2>
    </div>
    <div style="display: flex; flex-direction:column; gap: 20px; padding-bottom: 1em;">
        <div style="display: flex;justify-content:space-between; align-items: center;">
            <div class="request-buttons">
                <button class="button is-warning" [class.is-loading]="isProgress.approve" (click)="isStartedPopup()" [disabled]="!isButtonEnabled(ACTIONS.APPROVE)">
                    <tra slug="approve_alt_version"></tra>
                </button>
                <button class="button is-danger" [class.is-inverted]="!isProgress.reject" [class.is-loading]="isProgress.reject" (click)="confirmCancelPopup()" [disabled]="!isButtonEnabled(ACTIONS.REJECT)">
                    <tra slug="cancel_alt_version"></tra>
                </button>
                <button class="button is-link" [class.is-loading]="isProgress.operational" (click)="initStatusChange(ACTIONS.OPERATIONAL)" [disabled]="!isButtonEnabled(ACTIONS.OPERATIONAL)">
                    <tra slug="operational_alt_version"></tra>
                </button>
                <button class="button is-primary" (click)="previewModalStart()" [disabled]="!isButtonEnabled(ACTIONS.MESSAGE_PREVIEW)">
                  <tra slug="preview_message_alt_version"></tra>
                </button>
                <button class="button is-inverted" [disabled]="!isButtonEnabled(ACTIONS.SHIPPING)" (click)="shippingModalStart()">
                    <tra slug="shipping_alt_version"></tra>
                </button>
            </div>
            <div>
              <button [class.is-loading]="isProgress.export" class="button is-info has-icon" (click)="exportModalStart()">
                <span><tra slug="sa_classrooms_export"></tra></span>
                <span class="icon"><i class="fas fa-table"></i></span>
              </button>
            </div>
        </div>

        <div class="space-between align-top">

          <div class="grid-container" style="flex-grow: 1;">
              <ag-grid-angular
                  class="ag-theme-alpine ag-grid-fullpage"
                  style="border: none;"
                  [gridOptions]="requestGridOptions"
                  [pagination]="true"
                  (gridReady)="onGridReady($event)"
                  (paginationChanged)="onPaginationChanged($event)"
              ></ag-grid-angular>
          </div>

          <div *ngIf="selectedRequest" class="request-detail-view">

            <h2 class="has-text-info">Information</h2>

            <ng-container *ngFor="let reqFieldGroup of reqFieldGroups">
              <h3 class="has-text-info"><b><tra slug={{reqFieldGroup.slug}}></tra></b></h3>
              <ng-container *ngFor="let columnDef of getFilteredColDefs(reqFieldGroup)">
                <div *ngIf="getFinalRenderedData(columnDef)">
                  <span *ngIf="!isLinkSection(reqFieldGroup)"><b>{{columnDef.headerName}}: </b></span>
                  <span [innerHTML]="getFinalRenderedData(columnDef)"></span>
                </div>
              </ng-container>
            </ng-container>

            <h2 class="has-text-info">Notes</h2>
              <div *ngIf="notes" class="note-container">
                  <div *ngFor="let note of notes" class="note">
                      <div style="display: flex; gap: 4px; align-items: center;">
                          <div class="username">{{note.username}}</div>
                          <div class="date">
                              {{note.created_on | date: 'MMM dd, yyyy'}} {{note.created_on| date:'shortTime'}}
                          </div>
                      </div>
                      <div class="text">
                          {{note.note}}
                      </div>
                      <div></div>
                  </div>
              </div>
          </div>

        </div>

        <div class="columns">
          <!-- NOTE INPUT -->
          <div *ngIf="selectedRequest" class="column is-mobile-pulled-bottom">
            <div>
              <textarea class="internal-notes"[(ngModel)]="notesInput" placeholder="{{lang.tra('note_placeholder_alt_version')}}"></textarea>
              <button class="button" style="margin-top: 10px" (click)="saveNote()">
                  <tra slug="save_note_alt_version"></tra>
              </button>
            </div>
          </div>
          
          <!-- TOGGLES -->
          <div class="column is-mobile-pulled-top" [class.is-narrow]="selectedRequest" style="display: flex; flex-direction: column; gap: 10px; align-items: flex-end;">
            <div style="display: flex; gap: 10px;">
              <div style="font-weight: 300;">
                  <tra slug="show_active_req_alt_version"></tra>
              </div>
              <mat-slide-toggle [(ngModel)]="showActiveReq" (change)="toggleRequests()"></mat-slide-toggle>
            </div>
            <div style="display: flex; gap: 10px;">
                <div style="font-weight: 300;">
                    <tra slug="show_pending_req_alt_version"></tra>
                </div>
                <mat-slide-toggle [(ngModel)]="showPendingReq" (change)="toggleRequests(MutExclusiveToggle.PENDING)"></mat-slide-toggle>
            </div>
            <div style="display: flex; gap: 10px;">
                <div style="font-weight: 300;">
                    <tra slug="show_approved_req_alt_version"></tra>
                </div>
                <mat-slide-toggle [(ngModel)]="showApprovedReq" (change)="toggleRequests(MutExclusiveToggle.APPROVED)"></mat-slide-toggle>
            </div>
            <div style="display: flex; gap: 10px;">
                <div style="font-weight: 300;">
                    <tra slug="show_auto_op_ready_alt_version"></tra>
                </div>
                <mat-slide-toggle [(ngModel)]="showAutopOpReadyReq" (change)="toggleRequests()"></mat-slide-toggle>
            </div>
            <div style="display: flex; gap: 10px;">
              <div style="font-weight: 300;">
                  <tra slug="show_error_req_alt_version"></tra>
              </div>
              <mat-slide-toggle [(ngModel)]="showAutoErrorsReq" (change)="toggleRequests()"></mat-slide-toggle>
            </div>
          </div>
        </div>

    </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="Modal.SHIPPING" style="width: 35em; ">
              <h3><tra slug="shipping_alt_version"></tra></h3>
              <table>
                  <tr>
                      <td><tra slug="ship_company_sample_alt_version"></tra></td>
                      <td><input class="input" placeholder="{{lang.tra('alt_version_shipping_company_placeholder')}}" type="text" [(ngModel)]="shippingCompanySample"></td>
                  </tr>
                  <tr>
                      <td><tra slug="ship_track_sample_alt_version"></tra></td>
                      <td>
                          <input class="input" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" type="text" [(ngModel)]="trackingNumberSample">
                          <input class="input" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" type="text" [(ngModel)]="trackingUrlSample">
                      </td>
                  </tr>
                  <tr>
                    <td><tra slug="ship_deliver_date_sample_alt_version"></tra></td>
                    <td><input class="input" type="date" [(ngModel)]="deliveredDateSample" [disabled]="!isApproved()"></td>
                  </tr>
                  <tr>
                      <td><tra slug="ship_company_op_alt_version"></tra></td>
                      <td><input class="input" type="text" placeholder="{{lang.tra('alt_version_shipping_company_placeholder')}}" [(ngModel)]="shippingCompanyOp"></td>
                  </tr>
                  <tr>
                      <td><tra slug="ship_track_op_alt_version"></tra></td>
                      <td>
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" [(ngModel)]="trackingNumberOp">
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" [(ngModel)]="trackingUrlOp">
                      </td>
                  </tr>
                  <tr>
                    <td><tra slug="ship_deliver_date_op_alt_version"></tra></td>
                    <td><input class="input" type="date" [(ngModel)]="deliveredDateOperational" [disabled]="!isOperationalSent()"></td>
                  </tr>
                  <tr>
                      <td><tra slug="ship_track_return_alt_version"></tra></td>
                      <td>
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_num_placeholder')}}" [(ngModel)]="trackingNumberReturn">
                          <input class="input" type="text" placeholder="{{lang.tra('alt_version_tracking_url_placeholder')}}" [(ngModel)]="trackingUrlReturn">
                      </td>
                  </tr>
              </table>
              <modal-footer [pageModal]="pageModal" [isConfirmAlert]="true"></modal-footer>
          </div>
          <div *ngSwitchCase="Modal.PREVIEW_MESSAGE" style="width: 90em; max-width: 100%;">
            <h2 class="section-header"><tra slug="preview_message_header_alt_version"></tra></h2>
            <div class="tabs">
              <ul>
                <li *ngFor="let tab of getValidPreviewTabs()" [class.is-active]="messagePreviewTab == tab.value">
                  <a (click)="messagePreviewTab = tab.value"><tra [slug]="tab.caption"></tra></a>
                </li>
              </ul>
            </div>
            <template-editor 
              [reqAssessment]="selectedRequest.assessment_type"
              [reqFormat]="getTemplateFormat()"
              [reqStatus]="messagePreviewTab"
              [reqLang]="selectedRequest.lang"
              [selectedRequest]="selectedRequest"
              style="width: 100%"
            ></template-editor>
            <modal-footer [pageModal]="pageModal" [confirmButton]="false" [closeMessage]="'btn_close'"></modal-footer>
          </div>
          <div *ngSwitchCase="Modal.EXPORT" style="width: 30em; max-width: 100%;">
            <h2 class="section-header"><tra slug="alt_version_csv_export_header"></tra></h2>
            <p><tra-md slug="alt_version_csv_export_message"></tra-md></p>
            <h3><tra slug="alt_version_csv_export_filename_label"></tra></h3>
            <mat-form-field style="width: 100%">
              <input matInput [maxlength]="70" [placeholder]="lang.tra('alt_version_csv_export_filename_placeholder')" [(ngModel)]="exportFileName">
              <mat-hint align="end">{{exportFileName?.length || 0}}/70</mat-hint>
            </mat-form-field>
            <modal-footer [isEditDisable]="!exportFileName" [pageModal]="pageModal"></modal-footer>
        </div>
      </div>
  </div>
</div>