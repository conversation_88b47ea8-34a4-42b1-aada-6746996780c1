export interface IContextData {
    id: string,
    homepageRoute: string,
    loginRoute?: string,
    brandName: string,
    logo: string,
    footer: IFooterDef[][],
    apiAddress: (hostname:string) => string,
    siteFlags: {[key:string]:boolean},
    siteText: {[key:string]:string},
    siteData?: {[key:string]:any},
    hosts: string[],
    defaultTimezone: string
}

export type IFooterDef = {
    route?: string, // internal links
    link?: string, // external links
    caption: string, // external links
}

export const API_ADDRESS_LOCAL = 'http://localhost:3030';
