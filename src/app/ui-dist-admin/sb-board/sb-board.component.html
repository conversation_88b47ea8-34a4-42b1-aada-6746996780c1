<div class="page-body">
  <div>
    <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true"></header>
    
    <div class="page-content is-fullpage " *ngIf="!hasAccess">
      Loading...
    </div>
    <div class="page-content is-fullpage " *ngIf="hasAccess">
      <img *ngIf="lang.c() === 'en'" style="width:8em;" [src]="whiteLabel.getSiteText('sb_board_logo_en')">
      <img *ngIf="lang.c() === 'fr'" style="width:8em;" [src]="whiteLabel.getSiteText('sb_board_logo_fr')">
      <h2 style="margin-bottom: 0"> {{schoolBoardInfo.name}} ({{schoolBoardInfo.foreign_id}}) </h2>
      <ntf-new-messages></ntf-new-messages>
      <div style="margin-top: 1em;">
        <menu-bar [menuTabs]="views" [tabIdInit]="selectedView" (change)="selectView($event)"></menu-bar>
        <div [ngSwitch]="selectedView">
          <div *ngSwitchCase="SchoolBoardView.TECH_READI">
            <sb-tech-readiness [boardInfo]="schoolBoardInfo"></sb-tech-readiness>
          </div>
          <!-- <div *ngSwitchCase="SchoolBoardView.CONTACT">
            <sb-it-contact [boardInfo]="schoolBoardInfo"></sb-it-contact>
          </div> -->
          <div *ngSwitchCase="SchoolBoardView.SECURITY">
            <tra-md slug="txt_board_it_landing_bottom"></tra-md>
          </div>
          <div *ngSwitchCase="SchoolBoardView.SESSIONS">
          <sb-sessions [boardInfo]="schoolBoardInfo"></sb-sessions>
          </div>
          <div *ngSwitchCase="SchoolBoardView.REPORTS">
            <sb-report
              (currentAdminWindowChange)="onCurrentAdminWindowChange($event)"
              [configureQueryParams]="configureQueryParams.bind(this)"
              [csvTestWindows]="csvTestWindows"
              [schlDistGroupId]="schlDistGroupId"
              [currentAdminWindow]="currentAdminWindow"
            ></sb-report>
          </div>
          <div *ngSwitchCase="SchoolBoardView.LOCKDOWN_INFO">
            <tra-md slug="txt_ldb_sb_info"></tra-md>
            <hr>
            <button class="button is-info" (click)="openLDBConfig()">
              <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'ldl_ldb_show_config_beta' : 'ldl_ldb_show_config'"></tra>
            </button>
          </div>
        </div>
      </div>

      <!-- <sb-tech-readiness [boardInfo]="schoolBoardInfo"></sb-tech-readiness><br/> -->
      <div>
        <!-- <table style="width:auto; margin-bottom: 2em;">
          <tr>
            <th><tra slug="device_type"></tra></th>
            <th><tra slug="soft_ware"></tra></th>
            <th><tra slug="config_policy"></tra></th>
            <th><tra slug="pass_word"></tra></th>
          </tr>
          <tr>
            <td>
              Windows, Mac, iPad
            </td>
            <td>
              <a href="https://safeexambrowser.org/download_en.html" target="_blank">
                <tra slug="tech_redi_download_seb_sw"></tra>
              </a>
            </td>
            <td>
              <a target="_blank" [href]="schoolBoardInfo.seb.config">
                <tra slug="tech_redi_download_seb_cf"></tra>
              </a>
            </td>
            <td>
              <button (click)="isShowSebPass = !isShowSebPass" class="button is-small">
                <tra slug="tech_redi_download_seb_pass"></tra>
              </button>
              <div style="white-space: pre;"  *ngIf="isShowSebPass">{{schoolBoardInfo.seb.creds}}</div>
            </td>
          </tr>
          <tr>
            <td>
              Chromebook, Android (or Desktops requiring Chrome-based Accessibility Tools)
            </td>
            <td>
              <a target="_blank" href="https://chrome.google.com/webstore/detail/kiosk/afhcomalholahplbjhnmahkoekoijban" target="_blank">
                <tra slug="tech_redi_device_kiosk_ext"></tra>
              </a>
            </td>
            <td>
              <a target="_blank" [href]="schoolBoardInfo.kiosk.config">
                <tra slug="tech_redi_device_kiosk_cfg"></tra>
              </a>
            </td>
            <td>
              <button (click)="isShowKioskPass = !isShowKioskPass" class="button is-small">
                <tra slug="tech_redi_device_kiosk_pass"></tra>
              </button>
              <div style="white-space: pre;" *ngIf="isShowKioskPass">{{schoolBoardInfo.kiosk.creds}}</div>
            </td>
          </tr>
        </table> -->
      </div>

    </div>
  </div>
  <footer [hasLinks]="true"></footer>
</div>
<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="LDBModal.LDB_CONFIG_MODAL" style="width: 80em;">
        <modal-ldb-config 
          [config]="cmc()"
          [pageModal]="pageModal"
        ></modal-ldb-config>
      </div>
    </div>
  </div>
</div>