import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { csvOSSLTResultDataService } from 'src/app/ui-partial/isr-reports/csv-osslt-result-data.service';
import { TW_ISR_VERSION } from 'src/app/ui-partial/isr-reports/isr-reports.component';
import { ClassFilterId, TestWindowType } from 'src/app/ui-schooladmin/my-school.service';
import { downloadCSVFromExportData, IExportColumn } from 'src/app/ui-testctrl/tc-table-common/tc-table-common.component';
import { MyBoardService } from '../my-board.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';

@Component({
  selector: 'sb-report',
  templateUrl: './sb-report.component.html',
  styleUrls: ['./sb-report.component.scss']
})
export class SbReportComponent implements OnInit {
  @Input() adminWindows: any[] = [];
  @Input() schlDistGroupId!: string;
  @Input() currentAdminWindow: any;
  @Input() csvTestWindows: any
  @Input() configureQueryParams: (schlDistGroupId: string) => { query: Record<string, any> };
  @Input() classFilterToggles?: any

  @Output() currentAdminWindowChange = new EventEmitter<any>();

  classFilterId = ClassFilterId;
  currentClassFilter: any;
  isShowingReports: boolean = false;
  csvReportGenerationRecords:any
  

  OSSLTResultColumns =  [ 
  "SchMident", "SchName", "Grouping", "StudentOEN", "SASN"
  , "FirstName", "LastName", "EligibilityStatus", "HasStarted", "HasSubmitted", "StartedOn"
  , "SubmittedOn", "HasReport", "Result", "OSSLTScaleScore", 'ScaleScoreDescription', "Note"
  ];

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    public lang: LangService,
    public myBoard: MyBoardService,
    private csvOssltService: csvOSSLTResultDataService,
    private loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    if (this.classFilterToggles === undefined) {
      this.classFilterToggles = this.myBoard.classFilterToggles;
    }
  }

  async setClassFilter(filterId){
    this.currentClassFilter = filterId;
    this.isShowingReports = false;

    if (this.currentClassFilter){
      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior){
        this.isShowingReports = false;
      } else if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT){
        this.createAdminWindows(this.csvTestWindows)
        this.isShowingReports = true;
      }
    }
  }

  createAdminWindows(csvTestWindows){
    this.adminWindows = [] // reset
    csvTestWindows.forEach( csvTestWindow => {
      const endOn = new Date(csvTestWindow.date_end)
      const endOnYear = endOn.getFullYear()
      const endOnMonth = endOn.getMonth()+1
      let schoolYear
      if(csvTestWindow.type_slug === TestWindowType.EQAO_G10L && this.currentClassFilter === ClassFilterId.OSSLT) {
        if(endOnMonth < 7){
          schoolYear = ''+endOnYear+' '+this.lang.tra('lbl_osslt_report_term_spring')
        }else{
          schoolYear = ''+endOnYear+' '+this.lang.tra('lbl_osslt_report_term_fall')
        }
        this.adminWindows.push({tw_id:csvTestWindow.id, stringValue:schoolYear, date_start: csvTestWindow.date_start, date_end: csvTestWindow.date_end, isr_version:csvTestWindow.isr_version})
      } else if (csvTestWindow.type_slug === TestWindowType.EQAO_G9M && this.currentClassFilter === ClassFilterId.G9){
        if(endOnMonth < 4){
          schoolYear = ''+endOnYear+' '+this.lang.tra('lbl_report_term_winter')
        }else{
          schoolYear = ''+endOnYear+' '+this.lang.tra('lbl_osslt_report_term_spring')
        }
        this.adminWindows.push({tw_id:csvTestWindow.id, stringValue:schoolYear, date_start: csvTestWindow.date_start, date_end: csvTestWindow.date_end, isr_version:csvTestWindow.isr_version})
      }
    })

    if(this.adminWindows.length > 0){
      this.onAdminWindowChanged(0)
    }  
  }

  async onAdminWindowChanged(schoolYearIndex){
    this.currentAdminWindow = this.adminWindows[schoolYearIndex];
    this.currentAdminWindowChange.emit(this.currentAdminWindow);
    this.csvReportGenerationRecords = {} // Reset
    await this.findGeneratedCsvReport()
  }

  currentAdminWindowIndex(){
    return this.adminWindows.indexOf(this.currentAdminWindow);
  }

  getAdminWindowSlug(sy){
    return sy.stringValue;
  }

  getOSSLTResultColumns(){
    const columnsName = this.OSSLTResultColumns;
    let columns: IExportColumn[] = [];
    columnsName.forEach(column =>{
      columns.push({
        prop: column,
        caption: column,
        isClickable: false
      })
    })
    return columns;
  }

  exportOSSLTResult(schlDistGroupId : string){
    const user = this.auth.user().value;
    let isVersion2:boolean = false

    if (+this.currentAdminWindow.isr_version === +TW_ISR_VERSION.reportVersion2) {
      isVersion2 = true
    }
    
    if(schlDistGroupId) {
      this.auth
      .apiGet(this.routes.DIST_ADMIN_OSSLT_REPORT, user.uid, {
        query: {
          ...this.configureQueryParams(schlDistGroupId).query,
          isVersion2: isVersion2
        }
      }).then(res =>{
        const columns = this.getOSSLTResultColumns();
        if(res.length > 0){
          const exportData = this.csvOssltService.getOSSLTResultData(res, isVersion2)
          downloadCSVFromExportData('osslt-report',exportData, columns)
        }
      })
    }
  }

  /**
   * Fetch latest CSV generation report record
   */
  async findGeneratedCsvReport() {
    if (this.currentClassFilter === ClassFilterId.G9) {
      const csvReportGenerationRecords = await this.auth.apiFind(this.routes.DIST_ADMIN_G9_REPORT, {
        query: {
          ...this.configureQueryParams(this.schlDistGroupId).query
        }
      })
      this.csvReportGenerationRecords = csvReportGenerationRecords
    }
  }
  
  getReportGenerateOn(){
    if(!this.csvReportGenerationRecords.length){
      return ''
    }
    const currentTestWindowReport:any = this.csvReportGenerationRecords.find((report:any) => +report.test_window_id === +this.currentAdminWindow.tw_id)
    const reportGenerateOn = currentTestWindowReport.report_generate_on
    if(reportGenerateOn && reportGenerateOn!= ''){
      return (new Date(reportGenerateOn)).toLocaleString()
    }
  } 

  /**
   * Download G9 CSV report
   * @param schlDistGroupId 
   */
  downloadG9CsvReport(schlDistGroupId : string){
    if(schlDistGroupId) {
      this.auth
      .apiGet(this.routes.DIST_ADMIN_G9_REPORT, schlDistGroupId, {
        query: {
          ...this.configureQueryParams(schlDistGroupId).query
        }
      }).then(res =>{
        this.downloadReport(res.reportURL)
      }).catch(err => {
        this.loginGuard.quickPopup(err.message)
      })
    }
  }

  // Download Report file and save it to local
  async downloadReport(fileURL:string){
    if(!this.currentAdminWindow){
      return; 
    }
    if(fileURL){
      let a = document.createElement('a');
      a.href = fileURL;
      a.download = 'StudentResponseSheets.pdf';
      a.dispatchEvent(new MouseEvent('click'));
    }
  }
}
