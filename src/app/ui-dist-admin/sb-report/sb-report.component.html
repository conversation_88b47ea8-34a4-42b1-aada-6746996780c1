<filter-toggles 
    [state]="classFilterToggles"
    (id)="setClassFilter($event)"
></filter-toggles>
<div *ngIf="adminWindows.length == 0 && isShowingReports" style="margin-left:1em; margin-bottom:1em;">
  <tra-md slug="txt_report_csv_note"></tra-md>
</div>

<div *ngIf="adminWindows.length > 0 && isShowingReports">
  <div>
    <label for="select-school-year-dropdown">
      <tra slug="sa_class_test_window_lb"></tra>
    </label>
    <select id="select-school-year-dropdown" (change)="onAdminWindowChanged($event.target.value)" class="school-year-select">
      <option *ngFor="let aw of adminWindows; let i = index" [value]="i" [selected]="i === currentAdminWindowIndex()">
        {{ getAdminWindowSlug(aw) }}
      </option>
    </select>
  </div>

  <ng-container [ngSwitch]="currentClassFilter">
    <ng-container *ngSwitchCase="classFilterId.OSSLT">
      <tra-md slug="board_it_csv_info"></tra-md>
      <button class="file-cta" (click)="exportOSSLTResult(schlDistGroupId)" style="cursor: pointer;">
        <span class="icon"><i class="fas fa-table"></i></span>
        <span><tra slug="ba_osslt_result_download"></tra></span>
      </button> 
    </ng-container>

    <ng-container *ngSwitchCase="classFilterId.G9">
      <div *ngIf="csvReportGenerationRecords.length">
        <button class="file-cta" (click)="downloadG9CsvReport(schlDistGroupId)" style="cursor: pointer;">
          <span class="icon"><i class="fas fa-table"></i></span>
          <span><tra slug="ba_g9_result_download"></tra></span>
        </button>
        <div><b><tra slug="sa_text_report_generated_on"></tra>&nbsp;</b>{{getReportGenerateOn()}}</div>
      </div>
    </ng-container>
  </ng-container>
</div>
