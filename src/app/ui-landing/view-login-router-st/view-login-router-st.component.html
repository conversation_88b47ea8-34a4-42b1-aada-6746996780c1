
<div class="page-container">
  <div class="header-container">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <top-banner></top-banner>
  <div class="panel-container">
    <div class="router-container is_wide">
<!--      <div *ngIf="ifFieldTest(true)" style="margin-bottom:3em;">-->
<!--        <div style="    font-size: 0.6em; line-height: 1.2em; width: 24em;;">-->
<!--          <tra slug="lbl_bc_ft12_2021_msg"></tra>-->
<!--        </div>-->
<!--        <div>-->
<!--          <a class="button" routerLink="/{{assessment.lang || 'en'}}/field/{{assessment.slug}}" *ngFor="let assessment of getFieldTestAssessmentList()">-->
<!--            {{assessment.name}}-->
<!--          </a>-->
<!--        </div>-->

<!--        <hr style="margin-top:4em;"/>-->

<!--        <button -->
<!--          (click)="dismissMessage=true" -->
<!--          class="button is-warning"-->
<!--          style="height: auto; max-width: 100%; white-space: normal;"-->
<!--        >-->
<!--          <tra slug="lbl_bc_ft12_2021_btn"></tra>-->
<!--        </button>-->
<!--        -->
<!--      </div>-->
      <div>
        <div>
          <!-- <img src="assets/icons/student_diverse_icon_2.png"> -->
          <tra slug="login_as_a"></tra>
<!--          <tra slug="Log in as a(n)"></tra>-->
        </div>
        <div *wlCtxNot="'IS_LANDING_MIN_FOR_CUSTOM_PAGE'">
          <a routerLink="/{{lang.c()}}/login-educator" class="button is-fullwidth" style="height: auto; word-wrap: break-word"><tra [slug]="getAdminLoginSlug()"></tra></a>
        </div>
        <div *wlCtxNot="'IS_LANDING_MIN_FOR_CUSTOM_PAGE'">
          <a routerLink="/{{lang.c()}}/{{isTestCenter() ? 'login-test-taker' : 'login-student'}}" class="button is-fullwidth"><tra [slug]="getStudentButtonSlug()"></tra></a>
        </div>
        <div *ngIf="showPracticeTest">
          <a routerLink="/{{lang.c()}}/public-practice" class="button is-fullwidth"><tra [slug]="getPracticeTestTxt()"></tra></a>
        </div>
        <div *wlCtx="'IS_RESPONDUS'">
          <a class="button is-fullwidth" (click)="runRespondusDiagnostics()"> <tra [slug]="'Respondus Demo'"></tra></a>
        </div>
      </div>
    </div>
  </div>
</div>
