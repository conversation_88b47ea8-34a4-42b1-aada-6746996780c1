<div class="page-container">
  <div class="header-container">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <div class="panel-container">
    <div class="form-card is-wide">
      <div class="form-heading">
        <!-- <img src="assets/icons/student_diverse_icon_2.png"> -->
        <tra slug="Start the test with Lockdown Browser"></tra>
      </div>
      <div class="form-container">
        <div>
          <p *wlCtx="'IS_RESPONDUS'">This is a Respondus LockDown Browser demo designed to showcase the browser's technical capabilities. The browser offers various security levels that can be enforced to ensure a secure testing environment.</p>

          <tra-md [slug]="this.lockdown.renderInstructionsText"></tra-md>
        </div>
        <hr>
        <div>
          <p>
            After pressing this button, you will be redirected to diagnostics page in the Lockdown Browser. Please ensure you have the Lockdown Browser installed on your device.
          </p>
          <br>
          <div *ngIf="!lockdown.isChromeBook">
            <a [attr.href]="lockdown.ldbLaunchURL" 
                class="button menu-button is-info is-fullwidth" 
                [class.ABED-button]="whitelabel.isABED()"
                [class.is-loading]="!lockdown.ldbLaunchURL" 
                (click)="lockdown.onRespondusLaunchBtn($event)"
              >
              <span><i class="fa fa-globe"></i></span>&nbsp;&nbsp;
              <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'lbl_ldb_launch_btn_beta' : 'lbl_ldb_launch_btn'"></tra>
            </a>
          </div>
          <div *ngIf="lockdown.isChromeBook">
            <a href="#" 
               class="button menu-button is-info is-fullwidth" 
               [class.ABED-button]="whitelabel.isABED()"
               [class.is-loading]="!lockdown.ldbLaunchURL" 
               (click)="lockdown.onRespondusLaunchBtn($event)"
            >
              <span><i class="fa fa-globe"></i></span>&nbsp;&nbsp;
              <tra [slug]="IS_RESPONDUS_BETA_MODE ? 'lbl_ldb_launch_btn_beta' : 'lbl_ldb_launch_btn'"></tra>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <iframe [src]="lockdown.ldbLaunchURL" *ngIf="lockdown.launchLinkTriggered" class="iframe-ldb"></iframe>
</div>
