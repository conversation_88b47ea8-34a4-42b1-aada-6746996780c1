import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LauncherComponent } from '../ui-mpt-launcher/launcher/launcher.component';
import { ApplicantLandingComponent } from './applicant-landing/applicant-landing.component';
import { ApplicantLearnmoreComponent } from './applicant-learnmore/applicant-learnmore.component';
import { ProctorLearnmoreComponent } from './proctor-learnmore/proctor-learnmore.component';
import { ProctorLandingComponent } from './proctor-landing/proctor-landing.component';
import { RouterModule } from '@angular/router';
import { CreateArticlesComponent } from './create-articles/create-articles.component';
import { LearnmoreDisplayComponent } from './learnmore-display/learnmore-display.component';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { HttpClientModule } from '@angular/common/http';
import { MarkdownModule } from 'ngx-markdown';
import { ContactusComponent } from './contactus/contactus.component';
import { NewsComponent } from './news/news.component';
import { PrivacyComponent } from './privacy/privacy.component';
import { LegalComponent } from './legal/legal.component';
import { SampleQuestionsPreComponent } from './sample-questions-pre/sample-questions-pre.component';
import { ReactiveFormsModule } from '@angular/forms';
import { LoginComponent } from './login/login.component';
import { LogoutComponent } from './logout/logout.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { UiLoginModule } from '../ui-login/ui-login.module';
import { PasswordResetComponent } from './password-reset/password-reset.component';
import { PasswordChangeComponent } from './password-change/password-change.component';
import { ConfirmRegistrationComponent } from './confirm-registration/confirm-registration.component';
import { ViewIndexComponent } from './view-index/view-index.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { FormsModule } from '@angular/forms';
import { UiLandingRoutingModule } from './ui-landing-routing.module';
import { AttestationComponent } from './attestation/attestation.component';
import { G9SampleTestComponent } from './g9-sample-test/g9-sample-test.component';
import { ViewLoginRouterStComponent } from './view-login-router-st/view-login-router-st.component';
import { ViewLoginEducatorComponent } from './view-login-educator/view-login-educator.component';
import { ViewLoginStudentComponent } from './view-login-student/view-login-student.component';
import { G9LinearSampleTestComponent } from './g9-linear-sample-test/g9-linear-sample-test.component';
import { ViewFieldTestComponent } from './view-field-test/view-field-test.component';
import { MfaSetupComponent } from './mfa-setup/mfa-setup.component';
import { ViewLoginPortalComponent } from './view-login-portal/view-login-portal.component';
import { LdbRedirectComponent } from './ldb-redirect/ldb-redirect.component';
import { LdbLaunchPageComponent } from './ldb-launch-page/ldb-launch-page.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    UiPartialModule,
    UiLoginModule,
    HttpClientModule,
    MarkdownModule,
    UiLandingRoutingModule,
    UiTestrunnerModule,
    MatSlideToggleModule,
    FormsModule,
  ],
  declarations: [
    LauncherComponent, 
    ApplicantLandingComponent, 
    ApplicantLearnmoreComponent, 
    ProctorLearnmoreComponent, 
    ProctorLandingComponent, 
    CreateArticlesComponent, 
    LearnmoreDisplayComponent, 
    ContactusComponent, 
    NewsComponent, 
    PrivacyComponent, 
    LegalComponent, 
    SampleQuestionsPreComponent, 
    LoginComponent, 
    LogoutComponent,
    ForgotPasswordComponent,
    PasswordResetComponent,
    PasswordChangeComponent,
    ConfirmRegistrationComponent,
    ViewIndexComponent,
    AttestationComponent,
    G9SampleTestComponent,
    ViewLoginRouterStComponent,
    ViewLoginEducatorComponent,
    ViewLoginStudentComponent,
    G9LinearSampleTestComponent,
    ViewFieldTestComponent,
    MfaSetupComponent,
    ViewLoginPortalComponent,
    LdbRedirectComponent,
    LdbLaunchPageComponent,
  ],
  exports: [
    ViewLoginStudentComponent
  ]
})
export class UiLandingModule { }
