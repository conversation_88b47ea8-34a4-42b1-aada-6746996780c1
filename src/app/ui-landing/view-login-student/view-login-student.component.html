<div class="page-container">
  <div class="header-container">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <top-banner></top-banner>
  <div class="panel-container">
    <div class="form-card">
      <div class="form-heading">
        <!-- <img src="assets/icons/student_diverse_icon_2.png"> -->
        <tra slug="student_access"></tra>
      </div>
      <div class="form-container">
        <form [formGroup]="formGroup" (ngSubmit)="gotoDashboard()">
          <div class="field">
            <label class="label"><tra slug="access_code"></tra></label>
            <div class="control">
              <input id="accesscode" autocomplete="username" [formControl]="formGroup.controls.accessCode" class="input is-success" type="text">
            </div>
          </div>
          <div class="field">
            <label class="label"><tra [slug]="getStudentIdent()"></tra></label>
            <div class="control">
              <input id="sin" autocomplete="username" [formControl]="formGroup.controls.userId" class="input is-success" type="text">
            </div>
          </div>
          <div *ngIf="isDobReq()" class="field">
            <label class="label">
              <tra slug="abed_dob_lbl"></tra>
            </label>
            <div class="control">
              <dob-input id="dob" autocomplete="bday" [formControlModel]="formGroup.controls.date_of_birth"> </dob-input>
            </div>
            <!-- <div class="control">
              <input [formControl]="formGroup.controls.date_of_birth" class="input is-success" type="date">
            </div> -->
          </div>
          <div class="control" *ngIf="!isInSelfRegistrationMode">
            <input 
              type="submit"
              value={{signInText}}
              id="signIn"
              class="button is-link is-fullwidth"
              [disabled]="isLoggingIn"
            />
            <!-- <button (click)="gotoDashboard()"  class="button is-link is-fullwidth">Sign In</button> -->
          </div>
        </form>
      </div>
    </div>
  </div>
  <div class="troubleshooting-container">
    <button class="button troubleshoot-btn" (click)="goToTroubleshootAssesssment()" [title] = "lang.tra('stu_btn_troubleshooting_desc')"><tra slug="stu_btn_troubleshooting"></tra></button>
  </div>
</div>