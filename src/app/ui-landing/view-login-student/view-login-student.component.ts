import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { BreadcrumbsService } from "../../core/breadcrumbs.service";
import { Router, ActivatedRoute } from "@angular/router";
import { LangService } from "../../core/lang.service";
import { FormGroup, FormControl } from "@angular/forms";
import { AuthService, getFrontendDomain } from "../../api/auth.service";
import { LoginGuardService } from "../../api/login-guard.service";
import { StudentG9ConnectionService } from '../../ui-student/student-g9-connection.service';
import { Subscription } from "rxjs";
import { RoutesService } from "src/app/api/routes.service";
import { WhitelabelService } from "../../domain/whitelabel.service";
import { SEEN_WELCOME } from "src/app/ui-student/view-student-g9-dashboard/view-student-g9-dashboard.component";
import { sanitizeAccessCodeStudentNumber } from './util'
import { randInt } from "../../core/util/random";
@Component({
  selector: "view-login-student",
  templateUrl: "./view-login-student.component.html",
  styleUrls: ["./view-login-student.component.scss"]
})
export class ViewLoginStudentComponent implements OnInit, OnDestroy {
  constructor(
    private breadcrumbsService: BreadcrumbsService, 
    private auth: AuthService, 
    private router: Router, 
    private route: ActivatedRoute, 
    private loginGuard: LoginGuardService, 
    public lang: LangService, 
    private routes: RoutesService, 
    private whitelabel: WhitelabelService,
    private studentG9Connection: StudentG9ConnectionService
    ) {}

  breadcrumb = [];
  public signInText = this.lang.tra("g9_sign_in");
  isLoginAttempted: boolean;
  isLoggingIn: boolean;
  isSimpleTestTaker:boolean;

  private userSub: Subscription;
  private routeParamsSub: Subscription;

  ngOnInit(): void {
    this.breadcrumb = [this.breadcrumbsService._CURRENT(this.lang.tra("lbl_login"), `/${this.lang.c()}/login-router-st`), this.breadcrumbsService._CURRENT(this.lang.tra("lbl_students"), this.router.url)];

    this.routeParamsSub = this.route.params.subscribe(params => {
      const kioskPassword = params["kioskPassword"];
      if (kioskPassword) {
        this.auth.setKioskPassword(kioskPassword);
      }
      this.userSub = this.auth.user().subscribe(info => {
        if (info && this.isLoginAttempted) {
          localStorage.setItem(SEEN_WELCOME, 'false')
          this.loginGuard.gotoUserDashboard(info);
          this.auth.apiCreate(this.routes.LOG, {slug: "STUDENT_LOGIN",data: info});
        }
      });
    });
    // Reset all student OEN/Name validation data
    this.auth.deleteCookie("isStudentLastNameVerified");
    localStorage.removeItem("nameValidationSessionId");
    localStorage.removeItem("stuNameValidateAttempts");
    localStorage.setItem("nameValidationSessionId", this.generateNameValidationId());
    localStorage.setItem("stuNameValidateAttempts", "1");

    if (this.studentG9Connection.isConnected()) {
      this.studentG9Connection.disconnect();
    }
  }

  ngOnDestroy(): void {
    if (this.userSub){ this.userSub.unsubscribe(); }
    if (this.routeParamsSub){ this.routeParamsSub.unsubscribe(); }
  }

  public formGroup = new FormGroup({
    accessCode: new FormControl(),
    userId: new FormControl(),
    date_of_birth: new FormControl()
  });

  getSanitizedAccessCodeInput(){
    return this.formGroup.controls.accessCode.value?.replace(/[\W_]+/g, "") || ""; // not case sensitive
  }

  getSanitizedStudentNumber(){
    return this.formGroup.controls.userId.value?.replace(/[\W_]+/g, "")  || "";
  }

  isDobReq(){
    return this.whitelabel.getSiteFlag('IS_STU_DOB_LOGIN') && !this.isSimpleTestTaker // todo: more conditions
  }

  getSanitizedDob(){
    // I guess the sanitization is happening on the API?
    return this.formGroup.controls?.date_of_birth?.value;
  }

  async gotoDashboard() {
    const accessCode = this.getSanitizedAccessCodeInput()
    const studentNumber = this.getSanitizedStudentNumber()
    const dob = this.getSanitizedDob()
    if (accessCode.length < 3) {
      alert(this.lang.tra("student_error_message"));
      return;
    }
    if (!this.isSasnLogin() && studentNumber.length !== 9) {
      if (this.whitelabel.getSiteFlag("IS_EQAO")) {
        alert(this.lang.tra("report_error_message_oen"));
        return;
      }
    }

    if (this.isSasnLogin() && studentNumber.length > 9) {
      if (this.whitelabel.getSiteFlag("IS_EQAO")) {
        alert(this.lang.tra("report_error_message_sasn"));
        return;
      }
    }
    this.isLoginAttempted = true;
    this.isLoggingIn = true;
    //if ABED system then use the ABED-specific login strategy
    if(this.whitelabel.isABED()){
      const isSasnLogin = (this.isSasnLogin() || this.isSimpleTestTaker) ? 1 : 0

      // if(useLDB) {
      //   return this.router.navigateByUrl(`/${this.lang.c()}/ldb-launch-page/${accessCode}/${studentNumber}/${+this.isSasnLogin()}/${dob}`);
      // }

      this.auth.abedLoginStudent(studentNumber, accessCode, isSasnLogin, dob)
        .catch(e => {
          if (e?.data?.isAllowStuSelfReg && this.whitelabel.getSiteFlag('IS_STU_LOGIN_SELFREG')){
            this.selfRegStart()
          } else {
            this.loginGuard.quickPopup(this.lang.tra(e.message));
          }
          this.isLoggingIn = false;
        });
    } else {
      this.auth.loginStudent(studentNumber, accessCode, this.isSasnLogin()? 1:0).catch(e => {
        if (this.whitelabel.isEQAO()) {
          this.loginGuard.quickPopup(this.lang.tra("report_error_message_oen_3"));
        }
        this.isLoggingIn = false;
      });
    }
  }

  isInSelfRegistrationMode:boolean;
  selfRegForm:{first_name:string, last_name:string} | null;
  selfRegCancel(){
    this.isInSelfRegistrationMode = false;
    this.formGroup.controls.accessCode.enable()
    this.formGroup.controls.userId.enable()
    this.formGroup.controls.date_of_birth.enable()
  }
  async selfRegSubmit(){
    this.isLoggingIn = true;
    if (this.selfRegForm){
      if (!this.selfRegForm.first_name && !this.selfRegForm.last_name){
        this.loginGuard.quickPopup('Please enter your name before proceeding.')
        throw new Error()
      }
      await this.auth.selfRegStudent({
        first_name: this.selfRegForm.first_name || '',
        last_name: this.selfRegForm.last_name || '',
        dob: this.getSanitizedDob(),
        accessCode: this.getSanitizedAccessCodeInput(),
        studentNumber: this.getSanitizedStudentNumber(),
      })
      .catch(e => {
        console.log('selfRegSubmit fail', e)
        this.isLoggingIn = false
      } )
    }
    this.isLoggingIn = false;
  }
  selfRegStart(){
    this.isInSelfRegistrationMode = true;
    this.selfRegForm = {
      first_name: '', 
      last_name: ''
    }
    // todo: determine if we can iterate through form controls
    this.formGroup.controls.accessCode.disable()
    this.formGroup.controls.userId.disable()
    this.formGroup.controls.date_of_birth.disable()
  }

  getStudentIdent() {
    return this.isSasnLogin() ? this.whitelabel.getSiteText("student_ident_2") :this.whitelabel.getSiteText("student_ident");
  }

  isSasnLogin() {
    return this.router.url.includes("login-student-sasn")
  }

  goToTroubleshootAssesssment() {
    this.router.navigateByUrl(`/${this.lang.c()}/student/diagnostics-assessment`) 
  }

  generateNameValidationId(){
    const verifyStudentSessionId = String.fromCharCode(randInt(0,26)+65, randInt(0,26)+97, randInt(0,26)+65, randInt(0, 26)+97) + randInt(10000, 1000000) // Output example: AaBb123456
    return verifyStudentSessionId
  }
}
