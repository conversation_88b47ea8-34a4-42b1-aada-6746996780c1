import { Component, OnInit, Renderer2 } from '@angular/core';
import { LangService } from '../../core/lang.service';
import { Subscription } from "rxjs";
import { Router, ActivatedRoute } from "@angular/router";
import { AuthService } from "../../api/auth.service";
import { SEEN_WELCOME } from "src/app/ui-student/view-student-g9-dashboard/view-student-g9-dashboard.component";
import { LoginGuardService } from "../../api/login-guard.service";
import { RoutesService } from "src/app/api/routes.service";
import { StudentG9ConnectionService } from '../../ui-student/student-g9-connection.service';
import { randInt } from "../../core/util/random";
import { WhitelabelService } from "../../domain/whitelabel.service";
import { LockdownService } from 'src/app/ui-student/lockdown.service';

@Component({
  selector: 'ldb-redirect',
  templateUrl: './ldb-redirect.component.html',
  styleUrls: ['./ldb-redirect.component.scss']
})
export class LdbRedirectComponent implements OnInit {
  public readonly MAX_RETRY = 6;
  public readonly RETRY_INTERVAL = 1000;
  public readonly LOAD_DELAY = 1000;
  retryCount = 0;

  private routeParamsSub: Subscription;
  private userSub: Subscription;
  
  public document = document;
  public window = window;

  constructor(
    private lang:LangService,
    private route: ActivatedRoute,
    public auth: AuthService,
    private loginGuard: LoginGuardService,
    private routes: RoutesService,
    private studentG9Connection: StudentG9ConnectionService,
    private whitelabel: WhitelabelService,
    public router: Router,
    private renderer: Renderer2,
    public lockdown: LockdownService
  ) { }
  
  ngOnInit(): void {
    // Save retryCount in the local storage if not there already
    if (!sessionStorage.getItem('retryCount')) {
      sessionStorage.setItem('retryCount', '0');
    } else {
      this.retryCount = parseInt(sessionStorage.getItem('retryCount'));
    }

    // Subscribe to auth user so after they login they will get redirect to landing page 
    this.routeParamsSub = this.route.params.subscribe(params => {
      this.userSub = this.auth.user().subscribe(info => {
        if (info && this.auth.isLoggingIn) {
          localStorage.setItem(SEEN_WELCOME, 'false')
          this.loginGuard.gotoUserDashboard(info);
        }
      });
    });

    // Start LDB validation after the page is fully loaded
    this.renderer.listen('window', 'load', (event) => {
      setTimeout(() => this.validateLDB(this.route.snapshot.params), this.LOAD_DELAY);
    });

    this.resetSessionStorage();

    if (this.studentG9Connection.isConnected()) {
      this.studentG9Connection.disconnect();
    }
  }

  /** Reset all student OEN/Name validation data */
  private resetSessionStorage() {
    this.auth.deleteCookie("isStudentLastNameVerified");
    localStorage.removeItem("nameValidationSessionId");
    localStorage.removeItem("stuNameValidateAttempts");
    localStorage.setItem("nameValidationSessionId", this.generateNameValidationId());
    localStorage.setItem("stuNameValidateAttempts", "1");
  }

  /** Reloads the page if the validation cookie is not set */
  private retryValidation() {
    this.retryCount++
    sessionStorage.setItem('retryCount', this.retryCount.toString());

    if(this.retryCount < this.MAX_RETRY) {
      return setTimeout(() => {
        sessionStorage.setItem('retryCount', this.retryCount.toString());

        // this.validateLDB(params) // Re-validate
        return window.location.reload(); // Refresh
      }, this.RETRY_INTERVAL)
    }

    sessionStorage.setItem('retryCount', '0');
  }

  /**
   * Validate the LDB cookie and login the student
   *  - 1. If user is debug student, redirect to diagnostics assessment
   *  - 2. If ARV cookie is not set, retry validation up to MAX_RETRY times
   *  - 3. If validation fails, log the error and prompt the user to exit
   *  - 4. If validation is successful, login the student using
   *    - 4.a. Auth Token Strategy
   *    - 4.B. Access Code Stategy
   */
  async validateLDB(params: any) {
    const { accessCode, studentNumber, isSasnLogin, dob } = params
    const rldbarv = this.auth.getCookie('rldbarv')

    // For debug students - redirect to diagnostics assessment
    if(studentNumber === 'debug' && accessCode === 'debug') {
      return window.location.href = `${window.location.origin}/#/${this.lang.c()}/student/diagnostics-assessment`;
    }

    if(!rldbarv) {
      return this.retryValidation();
    }

    // Reset retry count on successful cookie validation
    sessionStorage.setItem('retryCount', '0'); 

    const res = await this.auth.apiGet(this.routes.AUTH_LOCK_DOWN_BROWSER, -1, {query: { rldbarv }})
      .catch(error =>{
        switch(error.message){
          default:
            alert(error.message)
            break
        }

        this.auth.apiCreate(this.routes.LOG, {
          slug: 'LDB_VALIDATION_ERR',
          data: {
            errorMsg: error.message,
            studentNumber,
            accessCode,
            isSasnLogin,
            dob,
            error
          },
        })
        this.lockdown.ldbExitPrompt()
      });

    this.auth.apiCreate(this.routes.LOG, {
      slug: 'LDB_VALIDATION_RESULT',
      data: res,
    })

    if(!res || !res.result) {
      await this.auth.apiCreate(this.routes.LOG, {
        slug: 'LDB_VALIDATION_ERR',
        data: {
          errorMsg: 'LDB validation failed',
          studentNumber,
          accessCode,
          isSasnLogin,
          dob,
          res
        },
      })
      return this.lockdown.ldbExitPrompt()
    }

    // Auth Token Strategy Login
    if(params.authToken) {
      console.log('authToken', params.authToken)
      this.auth.accessTokenLoginStudent(params.authToken)
      return 'L'
    }

    // Access Code Strategy Login
    return this.loginStudent(studentNumber, accessCode, +isSasnLogin, dob);    
  }

  /**
   * Login the student with their log-in information
   * - Prompt to exit if login fails
   */
  private loginStudent(studentNumber: string, accessCode: string, isSasnLogin: number, dob: string) {
    if(this.whitelabel.isABED()){
      this.auth.abedLoginStudent(studentNumber, accessCode, isSasnLogin, dob)
        .catch(e => {
          this.lockdown.ldbExitPrompt(this.lang.tra(e.message));
        });
    } else {
      this.auth.loginStudent(studentNumber, accessCode, isSasnLogin).catch(e => {
        this.lockdown.ldbExitPrompt(this.lang.tra("report_error_message_oen_3"));
      });
    }
  }

  generateNameValidationId() {
    const verifyStudentSessionId = String.fromCharCode(randInt(0,26)+65, randInt(0,26)+97, randInt(0,26)+65, randInt(0, 26)+97) + randInt(10000, 1000000) // Output example: AaBb123456
    return verifyStudentSessionId
  }

  public refreshPage() {
    this.window.location.href = `${this.window.location.href}`;
  }
}
