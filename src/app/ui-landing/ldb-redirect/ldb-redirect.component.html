
<div>
    <p>Redirecting...  Please Wait</p>
    <!-- <p>Retry Count: {{retryCount}} / {{MAX_RETRY}};</p>
    <p>Retry Delay: {{RETRY_INTERVAL}}ms;</p>
    <p>Load Delay: {{LOAD_DELAY}}ms;</p>
    <p><PERSON><PERSON>: {{document?.cookie}};</p>
    <p>Auth Cookie {{auth.getCookie('rldbarv')}};</p>
    <p>URL1: {{window.location.search}};</p>
    <p>URL2: {{router.url}};</p>
    <div class="buttons">
        <button class="button" (click)="window.location.reload()">Refresh Page .location.reload()</button>
        <button class="button" (click)="refreshPage()">Refresh Page .location.href =</button>
        <button class="button" (click)="exitBrowser()">Exit Browser</button>
    </div> -->
</div>
