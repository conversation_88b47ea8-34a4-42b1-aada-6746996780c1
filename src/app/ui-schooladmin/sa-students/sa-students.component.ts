import { Component, OnInit, ElementRef, ViewChild, Output, EventEmitter } from '@angular/core';
import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import {
  IAvailableSession,
  randId,
} from "../../ui-testadmin/demo-data.service";
import { LangService } from "../../core/lang.service";
import { apiErrMsgToSlug } from '../../core/util/api-error-messages';
import {
  IClassroom,
  IEQAOStudentAccommodationFilter,
  IStudentAccount,
  IStudentTestSession,
  IValidationError,
  IValidationResult,
  IValidationResults,
  AUTOSAVE_MESSAGES
} from '../data/types';
import { IColumnFilter, IColumnFilterType } from '../../ui-partial/custom-column-filter/custom-column-filter.component';
import { ActivatedRoute} from "@angular/router";
import { FilterSettingMode } from '../../ui-partial/capture-filter-range/capture-filter-range.component';
import { IActiveModal } from "../../ui-partial/modal/types.ts/model";
import { Subscription } from "rxjs";
import { AvailableSessionsService } from "../../ui-testadmin/available-sessions.service";
import { AuthService, getFrontendDomain } from "../../api/auth.service";
import { RoutesService } from "../../api/routes.service";
import {
  AccountModal,
  IModalAssignClassroom,
  IModalAssignSession,
  IModalNewAccount,
  TestSessionSelection,
  UserAttr,
  IModalImport
} from './model/types';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { ListSelectService } from '../../ui-partial/list-select.service';
import { G9DemoDataService, overlapVariables } from '../g9-demo-data.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { IMappedList, renderMappedValue, initMappedList } from "../data/util";
import { EQAO_DB_NULL, STUDENT_ASSIST_TECH_1, STUDENT_G9_COURSES, STUDENT_YES_NO, STUDENT_OSSLT_SUCC, Learning_Format_Primary, Learning_Format_Junior, STUDENT_SEMESTET_INDICATOR } from '../data/constants';
import { downloadFromExportData, generateExportFileName, IExportColumn, saveDataAsTsv, downloadExportData } from "../../ui-testctrl/tc-table-common/tc-table-common.component";
import { APIStudentColumnOSSLTList,  APIStudentColumnG9List, APIStudentColumnPJList} from "./export/config";
import { /*accommodationProps,*/ getActivatedAccommodations, getValProps, getAccommodationProps } from '../data/mappings/accommodations';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { MySchoolService, ClassFilterId } from '../my-school.service';
import {WhitelabelService} from "../../domain/whitelabel.service";
import {v4 as uuidv4} from 'uuid';
import {TW_ISR_VERSION} from "../../ui-partial/isr-reports/isr-reports.component"
import { AltVersionRequestStatus } from './../sa-modal-student-personal/sa-modal-student-personal.component';
import { scanUploadTypeCaption } from './../../ui-testctrl/panel-upload-scans/panel-upload-scans.component'

enum IStudentAccommodationFilter {
  IEP,
  BRAILLE,
  ANY_ASSISTIVE,
};
enum FlaggedItemCase {
  ALL = 'ALL',
  ALL_FLAG = 'ALL_FLAG',
  IMPORT_FLAG = 'IMPORT_FLAG',
};

import { downloadFile } from '../../core/download-string';
import { isBuffer } from 'cypress/types/lodash';
import { data } from 'jquery';
import { C } from '@angular/cdk/keycodes';
import { DATA_MAPPING_EQAO_G9_STUDENT } from '../data/mappings/eqao-g9';
import {
  Math_Class_When,
  Learning_Format_G9,
  Learning_Format_OSSLT,
} from '../data/constants';
import { userInfo } from 'os';
import { formatDate } from '@angular/common';
import { SaStudentPersonalComponent } from '../sa-modal-student-personal/sa-modal-student-personal.component';

@Component({
  selector: 'sa-students',
  templateUrl: './sa-students.component.html',
  styleUrls: ['./sa-students.component.scss']
})

export class SaStudentsComponent implements OnInit {
  /*
  assistiveTechLabels = {
    'eqao_acc_assistive_tech_1_chrome': 'sdc_assist_tech_chrome',
    'eqao_acc_assistive_tech_2_kurz_dl': 'sdc_assist_tech_kurz_dl',
    'eqao_acc_assistive_tech_2_kurz_ext': 'sdc_assist_tech_kurz_ext',
    'eqao_acc_assistive_tech_2_nvda': 'sdc_assist_nvda',
    'eqao_acc_assistive_tech_2_voiceover': 'sdc_assist_voiceover',
    'eqao_acc_assistive_tech_2_readaloud': 'sdc_assist_readaloud',
    'eqao_acc_assistive_tech_2_jaws': 'sdc_jaws',
    'eqao_acc_assistive_tech_2_chromevox': 'sdc_chromevox',
    'eqao_acc_assistive_tech_2_read': 'sdc_natural_reader',
    'eqao_acc_assistive_tech_custom': 'sdc_1_assist_tech_other',
  };
  */
  
  

  @ViewChild('semesterFilterContainer', { static: false }) semesterFilterContainer:ElementRef<HTMLElement>;
  static overlapVariables: any;
  @ViewChild('uploadPDFScanInput', { static: false }) uploadPDFScanInput: ElementRef<HTMLInputElement>;
  @ViewChild(SaStudentPersonalComponent) SaStudentPersonalComponent: SaStudentPersonalComponent;

  constructor(
    public lang: LangService,
    private availSess: AvailableSessionsService,
    private auth: AuthService,
    private routes: RoutesService,
    private g9demoService: G9DemoDataService,
    private pageModalService: PageModalService,
    private listSelectService: ListSelectService,
    private loginGuard: LoginGuardService,
    public mySchool: MySchoolService,
    protected route: ActivatedRoute,
    private whitelabelService: WhitelabelService,
    public fb: FormBuilder,
  ) { }

  public accountsTable: MemDataPaginated<IStudentAccount>;
  public students: IStudentAccount[] = [];
  public isAllSelected = false;
  public isAnySelected = false;
  public isLoaded: boolean;
  public isInited: boolean;
  public baseFilter:string;
  queryParamFilter: Map<string, string> = new Map();
  public currentModal: IActiveModal;
  AccountModal = AccountModal;
  UserAttr = UserAttr;
  school_group_id: number;
  availableSessions: IAvailableSession[] = [];
  availableClassrooms: IClassroom[] = [];
  classRoomsToAssign: IClassroom[] = [];
  public columnLabels;
  public filterLabels;
  public columnFilters: Map<string, IColumnFilter> = new Map();
  testSessionCaptionRef: Map<number, string> = new Map();
  testSessionTimeRef: Map<number, string> = new Map();
  testSessionWindowRef: Map<number, number> = new Map();
  isSaving: boolean;
  isRemoving: boolean = false;
  showFlaggedItems: FlaggedItemCase;
  selectedStudentIndex:number;
  additionalDownload = {
    urlSlug: '', // import   setClassFilter() to set this
    urlSlugExport: '', // export setClassFilter() to set this
    captionSlug: 'sa_student_sdc_caption',
    captionSlugExport: 'sa_student_sdc_export',
  };
  vrs: IValidationResults<IStudentAccount> = {
    // passed: [],
    successCreate: [],
    successUpdate: [],
    noChange: [],
    failed: [],
    results: []
  };
  subscription = new Subscription();
  pageModal:PageModalController;
  schoolType = this.route.snapshot.data['schoolType']

  assignedForm = new FormGroup({
    assignedClass: new FormControl()
  });
  IS_SESSION_STATUS_IMPLEMENTED = false;
  currentClassFilter:ClassFilterId;
  isShowingReports:boolean;
  isPrivateSchool = false;
  isShowingSemesterFilter:boolean;
  ClassFilterId = ClassFilterId;
  isEditDisable = true;
  currentImportNumber = 0
  totalImportNumber = 0;
  submissionDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '#', display: 'lbl_no'}];
  selectedSubmissionVal = 'any';

  scanUploadBtnDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '0', display: 'lbl_no'}];
  selectedscanUploadBtnVal = 'any';

  paymentDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '#', display: 'lbl_no'}, {val: 'p', display: 'lbl_pending'}];
  selectedPaymentVal = 'any';
  accommodationDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_student_has_accommodation'}, {val: '#', display: 'lbl_student_has_no_accommodation'}];
  selectedAccommodationVal = 'any';
  students_before_date = new FormControl();
  
  currentTestWindow;
  selectedIsFlagged = false;
  isSingleSelect = true;
  isSecreteUser;
  disableImportExport = false;
  studentRemovalRecords;
  isLoadingUploadScanInfo
  uploadScanInfo = []
  selecteduploadScanInfo
  uploadScanInfoGridApi
  uploadScanInfoGridOptions:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:this.lang.tra('sa_upload_stu_sca_model_grid_col_upload_date'), field:'upload_date', width:230 , checkboxSelection:true},
      { headerName:this.lang.tra('lbl_uploaded_from'), field:'upload_type', width:200, valueGetter: params => this.lang.tra(scanUploadTypeCaption[params.data.upload_type])},
      { headerName:this.lang.tra('sa_upload_stu_sca_model_grid_col_comment'), field:'comment', width:200},
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };
  isUploadingScanFile:boolean  =false
  uploadScanFileComment
  selectedUploadScanFile
  schl_admin_upload_scan_folder = 'admin_upload_scans/';
  queryParam;
  isPreAltReqSubmitMode:boolean;

  semesterValues = {"First semester": "sdc_student_math_class_when_1", "Second semester": "sdc_student_math_class_when_2", 
  "First quadmester": "sdc_student_math_class_when_A", "Second quadmester": "sdc_student_math_class_when_B", "Third quadmester": "sdc_student_math_class_when_C", "Fourth quadmester": "sdc_student_math_class_when_D", 
  "First octomester": "sdc_student_math_class_when_E", "Second octomester": "sdc_student_math_class_when_F", "Third octomester": "sdc_student_math_class_when_G", "Fourth octomester": "sdc_student_math_class_when_H",
  "Fifth octomester": "sdc_student_math_class_when_I", "Sixth octomester": "sdc_student_math_class_when_J", "Seventh octomester": "sdc_student_math_class_when_K", "Eighth octomester": "sdc_student_math_class_when_L",
  "First hexamester": "sdc_student_math_class_when_M", "Second hexamester": "sdc_student_math_class_when_N", "Third hexamester": "sdc_student_math_class_when_O", "Fourth hexamester": "sdc_student_math_class_when_P",
  "Fifth hexamester": "sdc_student_math_class_when_Q", "Sixth hexamester": "sdc_student_math_class_when_R", "Full year": "lbl_full_year", };

  isPaymentModuleEnabled: boolean = false;

  @Output() unPaidStudentCurrentFilter = new EventEmitter<string>();
  @Output() onSetClassFilter = new EventEmitter();

  ngOnInit() {
    this.initRouteView();
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow())
    this.mySchool.initStuAsmtSignoff();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.wipeSelections()
    console.log(this.isBCSite())
    const schoolLang = this.g9demoService.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);
    this.showFlaggedItems = FlaggedItemCase.ALL;

    this.subscription.add(this.g9demoService.autosaveModalMessage.subscribe(message => {
      if(message == AUTOSAVE_MESSAGES.INIT_PRE_ALT_REQ_AUTOSAVE){
        this.isPreAltReqSubmitMode = true;
        this.pageModal.confirmModal(false);
      }
    }))
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  ngOnChanges(){
    this.initRouteView();
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.mySchool.initStuAsmtSignoff();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.wipeSelections()
    console.log(this.isBCSite())
    const schoolLang = this.g9demoService.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);
  }

  initRouteView() {
    this.columnLabels = {
      id: this.lang.tra('sa_students_col_id'),
      courseType: this.lang.tra('Course Type'),
    };


    if (this.isBCSite()) {
        this.filterLabels = {};
    } 
    else {
      this.filterLabels = {
        [IStudentAccommodationFilter.IEP]: this.lang.tra('student_acc_filter_iep'),
        [IStudentAccommodationFilter.BRAILLE]: this.lang.tra('student_acc_filter_braille'),
        [IStudentAccommodationFilter.ANY_ASSISTIVE]: this.lang.tra('student_acc_filter_any_assist')
      }
  
      // custom column filters
      this.columnFilters.set('accommodation', {
        type: IColumnFilterType.BOOLEAN_LIST,
        options: [{
          key: IStudentAccommodationFilter.IEP,
          label: this.filterLabels[IStudentAccommodationFilter.IEP],
          checked: false
        }, {
          key: IStudentAccommodationFilter.BRAILLE,
          label: this.filterLabels[IStudentAccommodationFilter.BRAILLE],
          checked: false
        }, {
          key: IStudentAccommodationFilter.ANY_ASSISTIVE,
          label: this.filterLabels[IStudentAccommodationFilter.ANY_ASSISTIVE],
          checked: false
        }]
      });
    }

    this.route.queryParams.subscribe(queryParams => {
      this.queryParam = queryParams;
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
    })
    
    if (!this.isInited) {
      this.isInited = true;
      this.school_group_id = randId();
      this.loadAccounts();
      this.cacheAvailableSessions();
      this.applyFilterQueryParams();
    }
  }

  // filterLoadedTime(student: IStudentAccount){
  //   if ( student.created_loaded_time && student.created_loaded_time < this.students_before_date.value){
  //     return true;
  //   }else{
  //     return false;
  //   }
  // }

  studentsPriorToDate(){
    if(this.students_before_date.value){
      this.accountsTable.activeFilters['studentsBeforeDate'] = { mode: FilterSettingMode.VALUE, config: { value: this.students_before_date.value } }
    }
    this.accountsTable.refreshFilters();
  }

  toggleSemesterFilter(){
    if (this.isShowingSemesterFilter){
      this.changeCategory(null)
      this.isShowingSemesterFilter = false; // to do: clear filter as well
    }
    else{
      this.isShowingSemesterFilter = true;
      setTimeout(()=>{
        const el = this.semesterFilterContainer.nativeElement;
        if (el){ el.scrollIntoView({behavior: "smooth"}); }
      }, 500);
    }
  }

  isSignoffAvail(){
    return this.currentClassFilter && !this.isStuAsmtInfoSignoff(); // to do, should show alternate version if signed off
  }

  isStuAsmtInfoSignoff(){
    return this.mySchool.checkStuAsmtSignoff(this.currentClassFilter);
  }

  getStuAsmtInfoSignoffLog(){
    return this.mySchool.getStuAsmtSignoffHistory(this.currentClassFilter);
  }

  applyFilterQueryParams() {
    // session_id
    const session_id = this.route.snapshot.queryParamMap.get("session_id");
    if (session_id) {
      this.queryParamFilter.set('session_id', session_id);
    }
    if (this.queryParamFilter.size) {
      this.accountsTable.activeFilters['test_session'] = {
        mode: FilterSettingMode.VALUE,
        config: { value: session_id }
      }
      this.accountsTable.refreshFilters();
    }
  }

  cacheAvailableSessions() {
    this.subscription.add(this.availSess.sub().subscribe(availableSessions => this.availableSessions = availableSessions));
  }
  
  // private getFilterSettings(): {[propName: string]: (IStudentAccount, any) => boolean} {
  //   if (this.isBCSite()) {
  //       return {}
  //   } 
  //   else {
  //     return {
  //         accommodation: (student: IStudentAccount, val: any) => {
  //             let include = false;
  //             const filterIEP = val.find(el => el.key === IEQAOStudentAccommodationFilter.IEP).checked;
  //             const filterBraille = val.find(el => el.key === IEQAOStudentAccommodationFilter.BRAILLE).checked;
  //             const filterAnyAssistive = val.find(el => el.key === IEQAOStudentAccommodationFilter.ANY_ASSISTIVE).checked;
  //             // All filters off - include all students 
  //             if (!filterIEP && !filterBraille && !filterAnyAssistive) {
  //                 return true;
  //             }
  //             const checkMarkMapping =this.g9demoService.checkMarkMapping;
  //             if (filterIEP) {
  //                 if (checkMarkMapping["eqao_iep"][student.eqao_iep]) {
  //                     include = true;
  //                 }
  //             }
  //             if (filterBraille) {
  //                 if (checkMarkMapping["eqao_acc_braille"][student.eqao_acc_braille]) {
  //                     include = true;
  //                 }
  //             }
  //             const assistivePropList = getValProps('eqao_acc_assistive_tech', STUDENT_ASSIST_TECH);
  //             if(filterAnyAssistive) {
  //                 for(let prop of assistivePropList) {
  //                     if(checkMarkMapping[prop][student[prop]]) {
  //                         include = true;
  //                         break;
  //                     }
  //                 }
  //                 if(!!student.eqao_acc_assistive_tech_custom) {
  //                     include = true;
  //                 }
  //             }
  //             return include;
  //         },
  //         eqao_g9_class_code: (student: IStudentAccount, val: string) => _.includes(student.eqao_g9_class_code, val),
  //         test_session: (student: IStudentAccount, val: string) => _.includes(student.test_sessions, +val),
  //         test_window: (student: IStudentAccount, val: string) => _.includes(student.test_sessions.map(tsid => +this.testSessionWindowRef.get(tsid)), +val),
  //     }        
  //   }
  // }
  

  setClassFilter(filterId){
    this.onSetClassFilter.emit(filterId);

    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9demoService.schoolData;

    this.currentClassFilter = filterId;

    this.isShowingReports = false;
    this.accountsTable.activeFilters['is_g3'] = null;
    this.accountsTable.activeFilters['is_g6'] = null;
    this.accountsTable.activeFilters['is_g9'] = null;
    this.accountsTable.activeFilters['is_g10'] = null;

    if (this.currentClassFilter){
      if(this.currentClassFilter === ClassFilterId.Primary){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g3'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_pj_sdc_url', 'sa_student_pj_sdc_url');
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.Junior){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g6'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_pj_sdc_url', 'sa_student_pj_sdc_url');
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.OSSLT){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g10'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_sdc_osslt_export_url', 'sa_student_sdc_osslt_export_url');
        this.isPrivateSchool && payment_req_osslt ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.G9){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g9'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_sdc_url', 'sa_student_sdc_url');
        this.isPrivateSchool && payment_req_g9 ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
    }
    this.accountsTable.refreshFilters();
  }

  setTestWindowFilter(tw){
    this.currentTestWindow = tw;
    this.accountsTable.activeFilters['testWindowFilter'] = {mode: FilterSettingMode.VALUE, config: { value: this.currentTestWindow } }
    this.accountsTable.refreshFilters();
  }

  viewStudentReport(acct:any){
    return `/${this.lang.c()}/school-admin/reports`
    // this.loginGuard.quickPopup(this.lang.tra('msg_report_in_process'));
  }

  getSingleStudentReportParam(acct:any){
    let returnQueryParam = {};
    if(this.queryParam){
      returnQueryParam = {
        ...this.queryParam,
      }
    }

    returnQueryParam = {
      ...returnQueryParam,
      studentUID: ''+acct.id,
      reportAssessment: ''+this.currentClassFilter
    }
    return returnQueryParam
  }

  showSingleReportColumn() {
    if(this.isShowingReports) {
      if(this.currentClassFilter === ClassFilterId.OSSLT) {
        return true;
      }
      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior) {
        if(this.currentTestWindow.id !== 39 && this.currentTestWindow.id !== 40) {  // hardcode this parts because only these two PJ test window has no single report
          return true;
        } 
      }
    }
    return false;
  }

  setAdditionalDownload(slugUrlImportTemplate:string, slugUrlExportTemplate:string){
    this.additionalDownload.urlSlug = slugUrlImportTemplate;
    this.additionalDownload.urlSlugExport = slugUrlExportTemplate;
  }
  loadAccounts() {
    this.students = [];
    this.g9demoService.schoolAdminStudents.list.forEach(student => this.processNewStudentAccount(student));
    // sampleTestSessions.forEach(testSession => this.processNewTestSessions(testSession));
    this.accountsTable = new MemDataPaginated({
      data: this.students,
      configurablePageSize: true,
      filterSettings: {
        // accommodation: (student: IStudentAccount, val: any) => this.filterAccommodation(student, val),
        accommodation: (student: IStudentAccount, val: any) => _.includes(this.filterAccommodations(student), val),
        isPaid: (student: IStudentAccount, val: any) => _.includes(this.filterPayments(student), val),
        eqao_learning_format: (student:IStudentAccount, val: string) => _.includes(this.getLearningFormatLabel(student).toLowerCase(), val.toLowerCase()),
        _g10_eqao_learning_format: (student:IStudentAccount, val: string) => _.includes(this.getLearningFormatLabel(student).toLowerCase(), val.toLowerCase()),
        eqao_g9_class_code: (student: IStudentAccount, val: string) => _.includes(this.renderClassCodeAndGroupingLabel(student).toLowerCase(), val.toLowerCase()),
        class_code_label: (student: IStudentAccount, val: string) => _.includes(this.renderClassCodeAndGroupingLabel(student).toLowerCase(), val.toLowerCase()),
        homeroom: (student: IStudentAccount, val: string) =>  _.includes(this.renderHomeroom(student).toLowerCase(), val.toLowerCase()),
        teacher: (student:IStudentAccount, val: string) =>  _.includes(this.getTeacher(student).toLowerCase(), val.toLowerCase()),
        is_g3: (student: IStudentAccount) => student.eqao_is_g3 === '1',
        is_g6: (student: IStudentAccount) => student.eqao_is_g6 === '1',
        is_g9: (student: IStudentAccount) => student.eqao_is_g9 === '1',
        is_g10: (student: IStudentAccount) => student.eqao_is_g10 === '1',
        TermFormat: (student: IStudentAccount, val: string) => _.includes(this.getMathClassWhenLabel(+student.eqao_math_class_when||+student.TermFormat).toLowerCase(), val.toLowerCase()),
        test_session: (student: IStudentAccount, val: string) => _.includes(student.test_sessions, +val),
        test_window: (student: IStudentAccount, val: string) => _.includes(student.test_sessions.map(tsid => +this.testSessionWindowRef.get(tsid)), +val),
        //showFlaggedItems:  (student: IStudentAccount) => (JSON.parse(student.errMsg).length > 0 ||student.SDC_conflict!= undefined) ,
        showFlaggedItems:  (student: IStudentAccount) => this.filterFlaggedItems(student),
        is_submitted: (student:IStudentAccount, val:string) => this.isSubmitted(student,val), // this.currentClassFilter == ClassFilterId.G9 ? _.includes('('+student.haveG9Submitted+')'+ this.renderSubmStatus(student.haveG9Submitted), val) : _.includes('('+student.haveOSSLTSubmitted+')'+this.renderSubmStatus(student.haveOSSLTSubmitted), val),
        //studentsBeforeDate:  (student: IStudentAccount) => this.filterLoadedTime(student), 
        testWindowFilter:  (student:IStudentAccount) => this.filterStudentTestWindow(student),
        upload_scan: (student: IStudentAccount, val: string) => this.filterShowUploadScanBtn(student, val),
      },
      sortSettings: {
        accommodation: (student:IStudentAccount) => this.hasAccommodations(student),
        // isPaid: (student: IStudentAccount) => _.sortBy(student.isPaid),
        eqao_learning_format: (student:IStudentAccount) => _.sortBy(this.getLearningFormatLabel(student)),
        _g10_eqao_learning_format: (student:IStudentAccount) => _.sortBy(this.getLearningFormatLabel(student)),
        TermFormat: (student: IStudentAccount) => _.sortBy(student.eqao_math_class_when||student.TermFormat),
        homeroom: (student: IStudentAccount) => _.orderBy(this.renderHomeroom(student).toLowerCase(), ['asc']),
        class_code_label: (student: IStudentAccount) => _.orderBy(student[this.filterStudentClassCodeLabel()].toLowerCase(), ['asc']),
        teacher: (student:IStudentAccount) => _.orderBy(this.getTeacher(student).toLowerCase(), ['asc']),
        is_submitted: (student:IStudentAccount) => this.currentClassFilter == ClassFilterId.G9 ? student.haveG9Submitted === '1' : student.haveOSSLTSubmitted === '1',
        upload_scan: (student: IStudentAccount) => this.showUploadScanBtn(student),
      }
    });
    // this.students = this.students.filter(student => { return (student.semester === 'First semester') || (student.semester === 'Third hexamester') })
    // this.accountsTable.refreshFilters();
    // this.accountsTable.injestNewData(this.processFilterConditions(''))
    this.getSemester(this.students)
    this.isLoaded = true;
  }

  private filterAccommodation(student: IStudentAccount, val: any) {
    let include = false;
    const filterIEP = val.find(el => el.key === IStudentAccommodationFilter.IEP).checked;
    const filterBraille = val.find(el => el.key === IStudentAccommodationFilter.BRAILLE).checked;
    const filterAnyAssistive = val.find(el => el.key === IStudentAccommodationFilter.ANY_ASSISTIVE).checked;
    // All filters off - include all students
    if (!filterIEP && !filterBraille && !filterAnyAssistive) {
      return true;
    }
    const checkMarkMapping = this.g9demoService.checkMarkMapping;
    if (filterIEP) {
      if (checkMarkMapping["eqao_iep"][student.eqao_iep]) {
        include = true;
      }
    }
    if (filterBraille) {
      if (checkMarkMapping["eqao_acc_braille"][student.eqao_acc_braille]) {
        include = true;
      }
    }
    const assistivePropList = getValProps('eqao_acc_assistive_tech', STUDENT_ASSIST_TECH_1);
    if (filterAnyAssistive) {
      for (let prop of assistivePropList) {
        if (checkMarkMapping[prop][student[prop]]) {
          include = true;
          break;
        }
      }
      if (!!student.eqao_acc_assistive_tech_custom) {
        include = true;
      }
    }
    return include;
  }

  renderSemester(id) {
   let semesterslug = this.semesterValues[renderMappedValue(this.g9demoService.semesters, id)];
   return this.lang.tra(semesterslug);
  }

  processNewStudentAccount(student: IStudentAccount) {
    if(student.test_sessions){
      student.test_sessions = _.sortBy(student.test_sessions.map(ts_id => +ts_id));
    }  
    student.semester_label = this.renderSemester(student.semester) //only G9 semester
    this.students.splice(0,0,student);
  }

  processNewTestSessions(testSession: IStudentTestSession) {
    const titleRef = JSON.parse(testSession.test_window_title);
    const title = titleRef[this.lang.c()] || titleRef['en'] || 'Unnamed Test Window';
    const dateTimeStr = this.renderTestSessionDate(testSession.date_time_start);
    this.testSessionCaptionRef.set(testSession.id, title);
    this.testSessionTimeRef.set(testSession.id, dateTimeStr);
    this.testSessionWindowRef.set(testSession.id, 72);
  }
  semesterFilters = []
  filteredStudents = []
  filteredStudentsList = []

  processFilterConditions(config) {
    this.filteredStudentsList = []
    //this.semesterFilters.push(value)
    if (config.checked) {
      this.semesterFilters.push(config.semester_group)
    }
    else {
      const i = this.semesterFilters.indexOf(config.semester_group)
      this.semesterFilters.splice(i, 1);
    }
    let filterCondition
    let filteredStudents
    if (this.semesterFilters.length > 0) {
      this.semesterFilters.forEach(item => {
        filterCondition = (student) => student.semester_label === item
        filteredStudents = this.filteredStudentsList;
        this.filteredStudentsList = filteredStudents.concat(this.students.filter(filterCondition))
      })
      this.accountsTable.injestNewData(this.filteredStudentsList)
    }
    else{
      this.accountsTable.injestNewData(this.students)
    }
  }

  changeCategory($event) {
    //this.processFilterConditions({checked:true,semester_group:this.baseFilter})
    this.semesterFilters = []
    this.accountsTable.injestNewData(this.students)
  }

  getSemester(students) {
    var numMapping = {};
    for (var i = 0; i < students.length; i++) {
      if (numMapping[students[i].semester_label] === undefined) {
        if (students[i].semester_label !== undefined) {
          numMapping[students[i].semester_label] = 0;
        }
      }
      numMapping[students[i].semester_label] += 1;
    }
    var greatestFreq = 0;
    var mode;
    for (var prop in numMapping) {
      if (numMapping[prop] > greatestFreq) {
        greatestFreq = numMapping[prop];
        mode = prop;
      }
    }
    //console.log(numMapping, mode)
    this.baseFilter = mode;
    // this.processFilterConditions({checked:true,semester_group:mode})
    // this.semesterFilters=[];
  }
  visibleStudents() {
    return this.accountsTable.getCurrentPageData();
  }
  toggleSelectAll() {
    this.setSelectAll(this.isAllSelected);
  }
  setSelectAll(state: boolean) {
    this.isAllSelected = state;
    this.students.forEach(student => student.__isSelected = state);
    this.isAnySelected = state;
  }
  checkSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false
    this.students.forEach(student => {
      if (student.__isSelected) {
        this.isAnySelected = true;
      }
    });
  }
  wipeSelections() {
    this.students.forEach(student => {
      student.__isSelected = false
    });
  }

  renderCourse(id){
    return renderMappedValue(STUDENT_G9_COURSES, id);
  }

  renderSubmStatus(id) {
    /*
    const slug = this.currentClassFilter === ClassFilterId.G9? 'G9_OPERATIONAL':'OSSLT_OPERATIONAL'
    const haveSubmitted = acct.testAttempts.filter(ta => ta.slug === slug && (ta.submitted_test_session_id != undefined ||ta.submitted_test_session_id != null))
    if(haveSubmitted!=undefined){
      return renderMappedValue(STUDENT_YES_NO, 1);
    }
    else{
      return renderMappedValue(STUDENT_YES_NO, 0);
    }
    */
    return renderMappedValue(STUDENT_YES_NO, id);
  }

  unrenderCourse(label) {
    const relevantCourses = STUDENT_G9_COURSES.list
      .filter((course) => {
        return course.label === label
      });
    if (!relevantCourses || relevantCourses.length === 0) {
      return;
    }

    return relevantCourses[0].id;
  }


  renderClassCode(classId: number) {
    const classrooms = this.g9demoService.classrooms;
    let classCodeName: string;
    classrooms.forEach(classroom => {
      if (+classroom.id === +classId) {
        classCodeName = classroom.class_code;
      }
    });
    return classCodeName;
  }

  unrenderClassCode(classCodeName: string) {
    const classrooms = this.g9demoService.classrooms;
    let classId;
    classrooms.forEach(classroom => {
      if (('' + classroom.class_code).trim() === ('' + classCodeName).trim()) {
        classId = classroom.id;
      }
    });
    return classId;
  }

  hasApprovedAltVersionRequest(account: IStudentAccount) {
    if(!this.currentTestWindow){
      return false;
    }
    const altVersionRequestName = this.g9demoService.getStudentAltVersionRequestName(this.currentTestWindow)
    const altVersionRequest = account[altVersionRequestName]
    let hasApprovedAltVersionRequest = false;
    if (altVersionRequest) {
      hasApprovedAltVersionRequest = altVersionRequest.approved_on && !altVersionRequest.is_revoked 
    }
    return hasApprovedAltVersionRequest
  }

  hasAccommodations(account: IStudentAccount) {
    /*
    const accProps = getAccommodationProps(this.whitelabelService);
    return !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService).length;
    */
    /*
    return account.eqao_acc_braille == '3' ||account.eqao_acc_braille == '4'||account.AccAudioVersion == '1'||
           account._extended_time =='1' || account.eqao_pres_format == '1' || account._audio_recording_of_resp == '1'||
           account.AccVideotapeResponse == '1' || account.eqao_acc_scribing =='1' || account.AccOther == '1'||
           account.AccOther == '1'||account._audio_recording_of_resp == '1' ;
    */

    //return  this.currentClassFilter === ClassFilterId.G9? account.eqao_dyn_linear === '1' : account._g10_eqao_dyn_linear === '1'

    const curricShort = this.g9demoService.classFilterToCurricShort(this.currentClassFilter);

    const accProps = getAccommodationProps(this.whitelabelService, curricShort, this.g9demoService);
    const hasAccom = !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService, curricShort, this.g9demoService).length;
    
    const hasApprovedAltVersionRequest = this.hasApprovedAltVersionRequest(account)
    return hasAccom || hasApprovedAltVersionRequest;
    /*
    if(hasAccom){
      account['eqao_dyn_linear'] = '1'
    }else{
      account['eqao_dyn_linear'] = '#'
    }
    */
  }
  filterAccommodations(account: IStudentAccount) {
    const curricShort = this.g9demoService.classFilterToCurricShort(this.currentClassFilter);
    const accProps = getAccommodationProps(this.whitelabelService, curricShort, this.g9demoService);
    const hasAccom = !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService, curricShort, this.g9demoService).length;
    const hasApprovedAltVersionRequest = this.hasApprovedAltVersionRequest(account)
    let filterResult = null;
    if(hasAccom || hasApprovedAltVersionRequest){
      filterResult = '1'
    }else{
      filterResult = '#'
    }
    return filterResult;
  }

  filterPayments(account: IStudentAccount) {
    const studentClass = this.findStudentClass(account)
    const paymentStatus = account.paidForClass[studentClass.id]
    const isPaid = paymentStatus.isPaid;

    let filterResult = null;
    if(isPaid === 1){
      filterResult = '1'
    } else {
      filterResult = '#'
    }
    if (account.altPaymentStatus == 1 || account.altPaymentStatus == 2) {
      filterResult = 'p'
    } 
    return filterResult;
  }

  renderIsPaid(student: IStudentAccount) {
    const studentClass = this.findStudentClass(student);
    const paymentStatus = student.paidForClass[studentClass.id];

    if(paymentStatus?.altPaymentStatus == 1 || paymentStatus?.altPaymentStatus == 2)
      return 'lbl_pending'
    
    return paymentStatus?.isPaid ? 'lbl_yes' : 'lbl_no'
  }

  renderTestSessionDate(date_time_start: string) {
    return moment.tz(date_time_start, moment.tz.guess()).format(this.lang.tra('datefmt_dashboard_long'));
  }

  renderQuickDate(date: string) {
    return moment.tz(date, moment.tz.guess()).format('MMM D');
  }

  getStudentTestSessions(student: IStudentAccount) {
    return student.test_sessions;
  }

  getTSCaption(testSessionId: number) {
    return this.testSessionCaptionRef.get(testSessionId);
  }
  getTSTime(testSessionId: number) {
    return this.testSessionTimeRef.get(testSessionId);
  }
  getTSWindow(testSessionId: number) {
    return this.testSessionWindowRef.get(testSessionId);
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  getSelectedStudents(throwNullSet: boolean = false) {
    const students: IStudentAccount[] = [];
    this.visibleStudents().forEach(student => {
      if (student.__isSelected) {
        students.push(student);
      }
    });
    if (throwNullSet && students.length === 0) {
      //alert('Please select a student before proceeding with this action.');
      alert(this.lang.tra("mng_select_student"));
      throw new Error('No account selected');
    }
    return students;
  }

  // accountDetail
  accountDetailModalStart(student?: IStudentAccount) {
    this.selectedStudentIndex = this.accountsTable.getEntries().indexOf(student);
    let students;
    if (student) {
      students = [student];
      this.selectedIsFlagged = this.isRedAlertItem(student) || this.isYellowAlertItem(student)
      this.isSingleSelect = true;
    } else {
      students = this.getSelectedStudents(true);
      this.selectedIsFlagged = false;
      this.isSingleSelect = students.length <2 ;
    }
    let config: IModalNewAccount = { accounts: students };
    if (this.isBCSite()) {
      config.confirmationCaption = this.lang.tra('Save & Close');
    }    
    else{
      config.closeCaption = this.lang.tra('btn_close');
      config.confirmationCaption = this.lang.tra('btn_save');
    }
    this.isStudentDetailAltReqTabOpen = false;
    this.pageModal.newModal({
      type: AccountModal.ACCOUNT_DETAILS,
      config,
      finish: this.accountDetailModalFinish,
    });
  }
  configureQueryParams(isBulk = false) {
    const schoolData: any = this.g9demoService.schoolData;
    let namespace
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        namespace = 'eqao_sdc_g3'
      break;
      case ClassFilterId.Junior:
        namespace = 'eqao_sdc_g6'
      break;  
      case ClassFilterId.G9:
        namespace = 'eqao_sdc'
      break;
      case ClassFilterId.OSSLT:
        namespace = 'eqao_sdc_g10'
      break;
      default:
        namespace = 'eqao_sdc_g3'
      break; 
    }
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
          lang: this.lang.c(),
          mode: isBulk ? 'Import' : '',
          //namespace: this.currentClassFilter === ClassFilterId.G9?'eqao_sdc':'eqao_sdc_g10'
          namespace,
          isSecreteUser: this.isSecreteUser,
          test_windows_id: this.currentTestWindow.id,
          isStudentClassChanged: this.g9demoService.isStudentClassChanged ? 1 : 0
        }
      }
    }
    return null;
  }

  isStudentDetailAltReqTabOpen: boolean = false;
  onIsAltReqTab(event){
    this.isStudentDetailAltReqTabOpen = event;
  }

  /** If saving the modal in the alt request tab and the request is not submitted yet, show modified message to make it clear that saving modal doesn't send request
   * Otherwise, show default saving confirmation message.
   */
  getConfirmMsg(){
    if (this.cModal().type !== AccountModal.ACCOUNT_DETAILS){
      return 'alert_kk_confirm_save'
    }
    const currentTestWindow = this.mySchool.getCurrentTestWindow()
    const altVersionRequestName = this.g9demoService.getStudentAltVersionRequestName(currentTestWindow)
    const request_info_record = this.cmc().payload.record[altVersionRequestName]
    const isAltRequestSubmitted = request_info_record && request_info_record.alt_version_requests_status === AltVersionRequestStatus.Pending

    const isAltReqSpecialConfirmMsg = this.isStudentDetailAltReqTabOpen && !isAltRequestSubmitted
    if (isAltReqSpecialConfirmMsg){
      return 'alert_kk_confirm_save_alt_version'
    } else {
      return 'alert_kk_confirm_save'
    }
  }

  accountDetailModalFinish = (config: { payload: { record: Partial<IStudentAccount>, data: any } }, closeModelAfter = true) => {
    const payload = config.payload;
    const record = payload.record;
    const data = payload.data;

    if(!this.clientRuleCheck(data)){
      return;
    }
    this.modifyStudentData(data, false);
    this.updateAccTechAndNPS(data);
    this.setupLinear(data)
    const apiPayload = this.getApiData(data);
    const schoolClass = this.g9demoService.classOptions.map[data.eqao_g9_class_code];
    if (schoolClass) {
      data.eqao_g9_class_code_label = schoolClass.label
    }
    this.auth
      .apiPatch(
        this.routes.SCHOOL_ADMIN_STUDENT,
        record.id,
        apiPayload,
        this.configureQueryParams()
      ).then(result =>{
        const course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
        if(result.ErrorMessages.length > 0){
          const errSlug =  this.apiErrMsgToSlug(result.ErrorMessages[0]);
          const popupMsg = this.lang.tra(errSlug);
          setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
        }
        else if(result.warningMessages.length > 0){
          data['errMsg'] = JSON.stringify([]);
          Object.keys(data).forEach(prop => {
            if(this.currentClassFilter == ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1){
              record[prop] = data[prop];
            }else if(this.currentClassFilter == ClassFilterId.Primary){
              record['_g3_'+prop] = data[prop]
            }else if(this.currentClassFilter == ClassFilterId.Junior){
              record['_g6_'+prop] = data[prop]
            }else{
              record['_g10_'+prop] = data[prop]
            }  
          });
          if (schoolClass) {
            const classroom = this.g9demoService.classrooms.find (cr => {
              const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
              const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
              const tw_isActive = (new Date(test_window.date_end) > new Date ())
              if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                return cr;
              }
            })
            //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
            if(classroom){
              if(this.currentClassFilter == ClassFilterId.Primary){
                record._g3_eqao_pj_french = classroom.is_fi?'1':'#'
              }
              if(this.currentClassFilter == ClassFilterId.G9){
                record.eqao_g9_french = classroom.is_fi?'1':'#'
              }
              record.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.G9){
                record.semester_label = this.renderSemester(classroom.semester)
              }  
            }
          }
          const warnSlug =  this.apiErrMsgToSlug(result.warningMessages[0]);
          const popupMsg = this.lang.tra(warnSlug);
          setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
          if(closeModelAfter){
            this.pageModal.closeModal();
          }else{
            //update student red/yellow flags
            this.SaStudentPersonalComponent.initErrMsgAndConflict()
            if(this.isPreAltReqSubmitMode) {
              this.g9demoService.newAutosaveModalMessage(AUTOSAVE_MESSAGES.PRE_ALT_REQ_VALIDATION_PASSED)
              this.isPreAltReqSubmitMode = false;
            }
          }  
        }else{
          data['errMsg'] = JSON.stringify([]);
          Object.keys(data).forEach(prop => {
            if(this.currentClassFilter == ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1){
              record[prop] = data[prop];
            }else if(this.currentClassFilter == ClassFilterId.Primary){
              record['_g3_'+prop] = data[prop]
            }else if(this.currentClassFilter == ClassFilterId.Junior){
              record['_g6_'+prop] = data[prop]
            }else{
              record['_g10_'+prop] = data[prop]
            } 
          });
          if (schoolClass) {
            const classroom = this.g9demoService.classrooms.find (cr => {
              const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
              const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
              const tw_isActive = (new Date(test_window.date_end) > new Date ())
              if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                return cr;
              }
            })
            //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
            if(classroom){
              if(this.currentClassFilter == ClassFilterId.Primary){
                record._g3_eqao_pj_french = classroom.is_fi?'1':'#'
              }
              if(this.currentClassFilter == ClassFilterId.G9){
                record.eqao_g9_french = classroom.is_fi?'1':'#'
              }
              record.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.G9){
                record.semester_label = this.renderSemester(classroom.semester)
              } 
            }
          }
          if(closeModelAfter){
            this.pageModal.closeModal();
          }else{
            //update student red/yellow flags
            this.SaStudentPersonalComponent.initErrMsgAndConflict()
            if(this.isPreAltReqSubmitMode) {
              this.g9demoService.newAutosaveModalMessage(AUTOSAVE_MESSAGES.PRE_ALT_REQ_VALIDATION_PASSED)
              this.isPreAltReqSubmitMode = false;
            }
          }
        } 
        
        this.mySchool.initStuAsmtSignoff();
      }).catch((err:Error)=>{
        if(err.message.includes("Invalid Secrete User")){
          setTimeout(() => this.loginGuard.quickPopup("Invalid Secrete User"), 0);  //this message is for support when they try to unsubmit and have wrong secrete code 
        }      
      });
  }

  getApiData(record: Partial<IStudentAccount>, fromApplyBtn = false) {
    //Convert the record back to a format which is acceptable for the API
    var forApi;
    forApi = this.g9demoService.clientToApiPayloadStudent(record); 
    const student = { id: forApi.id, first_name: forApi.first_name, last_name: forApi.last_name };
    let student_meta = [];
    let non_meta_props = ["id", "first_name", "last_name", "roles", "class_code", "teacher", "teacher_notes"];
    // console.log('getApiData')
    for (let [key, value] of Object.entries(forApi)) {
      if (non_meta_props.includes(key) || value === undefined) {
        continue;
      }
      const uid = forApi.id;
      const key_namespace = forApi.key_namespace ||  "eqao_sdc";
      student_meta.push({
        uid,
        key_namespace,
        key,
        value,
      })
    }
    this.availableClassrooms
    let data = {
      student,
      student_meta,
      school_class_name: record.eqao_g9_class_code_label,
      school_class_id: forApi.class_code,
      sch_group_id: this.mySchool.getCurrentSchoolGroupId(),
      alt_version_req: this.clientToApiAltVersionRequest(record)
    }
    return data;
  }

  clientToApiAltVersionRequest(record){
    const altVersionRequestName = this.g9demoService.getStudentAltVersionRequestName(this.currentTestWindow)
    const studentAltVersionRequest = record[altVersionRequestName]
    return studentAltVersionRequest
  }

  private getStudentDefaults() {
    return {
      eqao_is_g10: (this.currentClassFilter === ClassFilterId.OSSLT) ? '1' : '#',
      eqao_is_g9: (this.currentClassFilter === ClassFilterId.G9) ? '1' : '#',
    }
  } 
  
  // newAccount
  newAccountModalStart() {
    const config: IModalNewAccount = {
      studentDefaults: this.getStudentDefaults()
    };
    this.pageModal.newModal({
      type: AccountModal.NEW_ACCOUNT,
      config,
      finish: this.newAccountModalFinish
    });
  }
  newAccountModalFinish = (config: { payload: { record: Partial<IStudentAccount>, data: any } }) => {
    if(!this.clientRuleCheck(config.payload.data)){
      return;
    }
    const newStudentData = config.payload.data;
    if(this.currentClassFilter === ClassFilterId.Primary){
      newStudentData.eqao_is_g3 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.Junior){
      newStudentData.eqao_is_g6 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.G9){
      newStudentData.eqao_is_g9 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.OSSLT){
      newStudentData.eqao_is_g10 = '1';
    }
    newStudentData.paidForClass = {};
    this.addNewAccounts([newStudentData]);
  }

  async addNewAccounts(dataList: any[], isBulk = false) {
    if (!dataList) {
      return;
    }

    let matchPropName = this.isBCSite() ? '' : 'eqao_student_gov_id'; // TODO: Add match prop for BC!
    let apiRecords = [];
    const oenMap = {};
    const matchPropMap = oenMap;
    for (let data of dataList) {
      if (data.eqao_student_gov_id) {
        oenMap[data.eqao_student_gov_id] = data;
      }
      data.test_sessions = [];
      if(data.CreatedDate === undefined || data.CreatedDate === '#'){
        data.CreatedDate =  moment().format()
      }
      this.modifyStudentData(data, true);
      this.updateAccTechAndNPS(data);
      this.setupLinear(data);
      const apiRecord = this.getApiData(data);
      apiRecords.push(apiRecord);

      const uuid = uuidv4(); //to pair up data and apiRecord.
      data['uuid'] = uuid;
      apiRecord['uuid'] = uuid;
      //console.log(data)
    }

    const importSize = 20;
    let returnResLength = 0;
    this.totalImportNumber = apiRecords.length;
    this.currentImportNumber = 0;
    this.vrs = {
      successCreate: [],
      successUpdate: [],
      noChange: [],
      failed: [],
      results: []
    };
    let newClasses:any = [];
    for(let i = 0; i < apiRecords.length; i+=importSize){
      this.currentImportNumber = i;
      var portionApiRecords = apiRecords.length >= i+importSize ? apiRecords.slice(i, i+importSize): apiRecords.slice(i, apiRecords.length)
      await this.auth
      .apiCreate(this.routes.SCHOOL_ADMIN_STUDENT, portionApiRecords, this.configureQueryParams(isBulk))
      .then((result) => {
        if(result.newClassRecords.length > 0){
          result.newClassRecords.forEach(classRecord => {
            if(newClasses.find(newClass => +newClass.id === +classRecord.id) === undefined){
              newClasses.push(classRecord);
              // Create new added Class from Student Import
              const semester = this.g9demoService.semesters.list.find(s => s.id == classRecord.semester_id);
              const newClassroomRecord:any = {
                assessment: 0,
                class_code: classRecord.name,
                course_type: classRecord.group_type,
                currentStudents: initMappedList([]),
                id: classRecord.id,
                is_grouping: classRecord.is_grouping,
                onboarding: 0,
                semester: String(classRecord.semester_id),
                semester_label: semester ? semester.label : '',
                students: 0,
                educator: "",
                course: "",
                is_fi: classRecord.is_fi
              }

              this.g9demoService.classrooms.push(newClassroomRecord);
              this.g9demoService.teacherClassrooms.list.push(newClassroomRecord);

              const classOptionList = [];
              const classOptionEl = {id: classRecord.id, label: classRecord.name, group_type: classRecord.group_type};
              classOptionList.push(classOptionEl);

              const mappedClassEl = initMappedList(classOptionList);
              this.g9demoService.classOptions = {map: {...this.g9demoService.classOptions.map, ...mappedClassEl.map}, list: this.g9demoService.classOptions.list.concat(mappedClassEl.list)}
              this.g9demoService.teacherClassrooms.map[newClassroomRecord.id] = newClassroomRecord;
            }
          });
        }

        const res = result.studentRecords;
        if (res && res.length) {
          let validateResult;
          //let successImport = 0;
          //let vrs: IValidationResults<IStudentAccount> = {passed: [],failed: [],results: []};
          returnResLength += res.length

          res.forEach(resStudent => {
            //*** Sucesss Created/Imported********************************************************/     
            if (resStudent.validateResult.ErrorMessages.length === 0){ 
              //***update the client side students data ********************************************/

              // handle the Invalid OEN pass exception 
              const meta_errMsg = resStudent.user_meta.find(meta => meta.key =='errMsg')
              const errMsg = meta_errMsg!= undefined?meta_errMsg.value:JSON.stringify([])
              //************************************************************************ */  

              let student = dataList.find(obj=>{return obj.uuid === resStudent.uuid})
              if(!student.eqao_g9_class_code) {
                const newClass = newClasses.find(cl => 
                  cl.name === student.eqao_g9_class_code_label
                );
                student.eqao_g9_class_code = newClass.id;
              }
              const schoolClass = this.g9demoService.classOptions.map[student.eqao_g9_class_code];
              if (student) {  
                if(this.currentClassFilter !== ClassFilterId.G9){
                    let prefix;
                    switch(this.currentClassFilter){
                      case ClassFilterId.Primary:
                        prefix = '_g3_'
                      break;
                      case ClassFilterId.Junior:
                        prefix = '_g6_'
                      break;
                      case ClassFilterId.OSSLT:
                        prefix = '_g10_'
                      break;
                      default:
                        prefix = '_g3_'
                      break;
                    }
                    for (const key in student) {
                      if(overlapVariables.indexOf(key) == -1 && key != 'uuid'){
                        student[prefix+key] = student[key];
                        delete student[key]
                      }
                    }
                    student[prefix+"errMsg"] = errMsg;
                  }else{
                    student.errMsg = errMsg;
                  }
                  student.id = resStudent.uid;
                  student.paidForClass = {
                    [schoolClass.id]: {
                      altPaymentStatus: 0,
                      isPaid: 0
                    }
                  }
              }
              this.students.splice(0, 0, student);
              this.g9demoService.schoolAdminStudents.list.push(student);
              const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9demoService.schoolData;
              let showAlertForUnPaidStudent
              let classroom;
              const course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
              if (schoolClass) {
                if(this.currentClassFilter == ClassFilterId.Primary){
                  student._g3_eqao_g9_class_code_label = schoolClass.label
                  student._g3_eqao_g9_class_code = schoolClass.id
                  showAlertForUnPaidStudent = payment_req_pj ? true : false
                }else if (this.currentClassFilter == ClassFilterId.Junior){
                  student._g6_eqao_g9_class_code_label = schoolClass.label
                  student._g6_eqao_g9_class_code = schoolClass.id
                  showAlertForUnPaidStudent = payment_req_pj ? true : false
                }else if(this.currentClassFilter == ClassFilterId.G9){
                  student.eqao_g9_class_code_label = schoolClass.label
                  student.eqao_g9_class_code = schoolClass.id
                  showAlertForUnPaidStudent = payment_req_g9 ? true : false
                }else if (this.currentClassFilter == ClassFilterId.OSSLT){
                  student._g10_eqao_g9_class_code_label = schoolClass.label
                  student._g10_eqao_g9_class_code = schoolClass.id
                  showAlertForUnPaidStudent = payment_req_osslt ? true : false
                }
                classroom = this.g9demoService.classrooms.find (cr => {
                  const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
                  const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
                  const tw_isActive = (new Date(test_window.date_end) > new Date ())
                  if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                    return cr;
                  }
                })  
                //classroom = this.getClassroom(schoolClass.label)
                if(classroom){
                  if(showAlertForUnPaidStudent){
                    this.promptForUnPaidStudent(this.currentClassFilter, classroom.id);
                  }
                  student.teacher = classroom.educator;
                  student.semester_label = this.renderSemester(classroom.semester)
                  const teacherClassrooms = this.g9demoService.teacherClassrooms.map[classroom.id]
                  teacherClassrooms.currentStudents.list.push(student)
                  teacherClassrooms.currentStudents.map[student.id]= data;
                  if(this.currentClassFilter == ClassFilterId.Primary){
                    student._g3_eqao_pj_french = classroom.is_fi?'1':'#'
                  }
                  if(this.currentClassFilter == ClassFilterId.G9){
                    student.eqao_g9_french = classroom.is_fi?'1':'#'
                  }
                }
              }
            
              //*********************************************************************************/ 
              //*** success messages **************************************************************/
              if (!isBulk) {
                validateResult = resStudent.validateResult;
              }else{
                if(this.isSasnLogin){
                  this.vrs.successCreate.push(student.SASN);
                } else {
                  this.vrs.successCreate.push(student.eqao_student_gov_id);
                }
              }
              //*********************************************************************************/ 
            }
            //*** fail messages *****************************************/
            else{
              if(!isBulk){
                validateResult = resStudent.validateResult;
              }else{
                const student = dataList.find(obj=>{return obj.uuid === resStudent.uuid})
                let errors = [];
                let uniqueMessages = [];
                resStudent.validateResult.ErrorMessages.forEach(errMsg => {
                  const errSlug = this.apiErrMsgToSlug(errMsg);
                  const message = this.lang.tra(errSlug);
                  // making sure there are no duplicate errors
                  if (!uniqueMessages.includes(message)) {
                    const error = { "": "", message };
                    errors.push(error);
                    uniqueMessages.push(message);
                  }
                });
                this.vrs.failed.push(student);
                const validationResult: IValidationResult<IStudentAccount> = {
                  obj: student,
                  errors
                };
                this.vrs.results.push(validationResult);    
              }
            }
            //************************************************************************************/   
          });

          //handle Messages
          this.accountsTable.injestNewData(this.students);
          if(!isBulk){
            if(validateResult.ErrorMessages.length > 0){
              const errSlug =  this.apiErrMsgToSlug(validateResult.ErrorMessages[0]);
              const popupMsg = this.lang.tra(errSlug);
              setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
            }else if(validateResult.warningMessages.length > 0){
              const warnSlug =  this.apiErrMsgToSlug(validateResult.warningMessages[0]);
              const popupMsg = this.lang.tra(warnSlug);
              setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
              this.pageModal.closeModal()
            }else{
              alert(this.lang.tra("sa_student_new_acc_alert_single"))
              this.pageModal.closeModal()
            }
          }
          else{  
            // if(successImport >0) {
            //   alert(this.lang.tra("sa_student_new_acc_alert", undefined, { NUM_ACCOUNTS: successImport }));
            // }
            // if ((res.length - successImport) > 0) {
            //   setTimeout(() => { this.importValidationModalStart(vrs);}, 0)
            // }  
          }  
        }

        if((result.SDC_conflicts_g3 && this.currentClassFilter === ClassFilterId.Primary)
         ||(result.SDC_conflicts_g6 && this.currentClassFilter === ClassFilterId.Junior)
         ||(result.SDC_conflicts_g9 && this.currentClassFilter === ClassFilterId.G9)
         ||(result.SDC_conflicts_g10 && this.currentClassFilter === ClassFilterId.OSSLT)
         ){
          let SDC_conflicts_key;
          let SDC_conflict_key;
          switch(this.currentClassFilter){
            case ClassFilterId.Primary:
              SDC_conflicts_key = 'SDC_conflicts_g3'
              SDC_conflict_key = 'SDC_conflict_g3'
              break;
            case ClassFilterId.Junior:
              SDC_conflicts_key = 'SDC_conflicts_g6'
              SDC_conflict_key = 'SDC_conflict_g6'
              break;
            case ClassFilterId.G9:
              SDC_conflicts_key = 'SDC_conflicts_g9'
              SDC_conflict_key = 'SDC_conflict_g9'
              break;
            case ClassFilterId.OSSLT:
            default:
              SDC_conflicts_key = 'SDC_conflicts_g10'
              SDC_conflict_key = 'SDC_conflict_g10'
              break
          }
          
          //check if anyupdate student have class switch from old test window to new test window
          const prefix = this.currentClassFilter === ClassFilterId.Primary?"_g3_":this.currentClassFilter === ClassFilterId.Junior?"_g6_":this.currentClassFilter === ClassFilterId.G9?"":"_g10_";
          const classCodePropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code' // TODO: Add prop for BC!
          const classCodeLabelPropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code_label' // TODO: Add prop for BC!
          result.changeGrpStudents.forEach( cgs => {
            const student:any = this.students.find(st => st.id == cgs.uid);
            const classroom = this.g9demoService.classrooms.find(cr=> cr.id == cgs.class_id)
            if(student && classroom) {
              student[classCodePropName] = classroom.id;
              student[classCodeLabelPropName] = classroom.class_code;
              student.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.Primary){
                student._g3_eqao_pj_french = classroom.is_fi?'1':'#'
              }
              if(this.currentClassFilter == ClassFilterId.G9){
                student.eqao_g9_french = classroom.is_fi?'1':'#'
              }
              if(this.currentClassFilter == ClassFilterId.G9){
                student.semester_label = this.renderSemester(classroom.semester)
              }  
            }  
          });

          
          if(result[SDC_conflicts_key].length > 0) {
            result[SDC_conflicts_key].forEach(conflict => {
              if(conflict.compare_result !== '[]') {
                const student:any = this.students.find(st => conflict.uid == st.id);
                if(student) {
                  student[SDC_conflict_key] = conflict;
                  if(this.isSasnLogin){
                    this.vrs.successUpdate.push(student.SASN);
                  } else {
                    this.vrs.successUpdate.push(student.eqao_student_gov_id);
                  }
                  let index = result.updateStudentsIds.indexOf(student.id)
                  if(index > -1) {
                    result.updateStudentsIds.splice(index, 1);
                  }
                }
              }
            })
          }

          result.updateStudentsIds.forEach(stuId => {
            const student:any = this.students.find(st => stuId == st.id);
            if(student) {
              if(this.isSasnLogin){
                this.vrs.noChange.push(student.SASN);
              } else {
                this.vrs.noChange.push(student.eqao_student_gov_id);
              }
            }
          }); 
        }
      });// end api call
    }// end for loop for each import
    if(isBulk){
      if(this.pageModal){
        this.pageModal.closeModal();
      }  
      if(this.vrs.successUpdate.length > 0) {
        this.showFlaggedItems = FlaggedItemCase.IMPORT_FLAG;
        this.accountsTable.activeFilters['showFlaggedItems'] = { mode: FilterSettingMode.VALUE, config: { value: '#' } }
        this.accountsTable.refreshFilters();
      }
      // Error message Summary: 
      // if ((returnResLength - this.successCreateNum) > 0) {
      setTimeout(() => { this.importValidationModalStart(this.vrs);}, 0)
      // }  

    }
    this.mySchool.initStuAsmtSignoff()
  }

  getTotalImportStudentNumber() {
    return this.lang.tra("sa_student_total_acc_alert", undefined, { NUM_ACCOUNTS: this.totalImportNumber });
  }

  getSuccessCreateStudentNumber() {
    return this.lang.tra("sa_student_new_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.successCreate.length });
  }

  getSuccessUpdateStudentNumber() {
    return this.lang.tra("sa_student_update_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.successUpdate.length });
  }

  getNoChangeStudentNumber() {
    return this.lang.tra("sa_student_no_change_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.noChange.length });
  }

  getFailedImportStudentNumber() {
    return this.lang.tra("sa_student_fail_import_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.failed.length });
  }

  processSelectedTestSessions(testSessionsSelected: TestSessionSelection) {
    const testSessionIds = [];
    Object.keys(testSessionsSelected).forEach(testSessionId => {
      if (testSessionsSelected[testSessionId]) {
        testSessionIds.push(+testSessionId);
      }
    });
    return testSessionIds;
  }
  getClassroom(classCode){
    const classroom = this.g9demoService.classrooms.find(classroom => classroom.class_code == classCode);
    if(classroom){
      return classroom
    }
    return null;
  }
  // assign session
  assignSessionModalStart() {
    const students = this.getSelectedStudents(true);
    const config: IModalAssignSession = {
      accounts: students,
      testSessionsSelected: {}
    };
    this.pageModal.newModal({
      type: AccountModal.ASSIGN_SESSION,
      config,
      finish: this.assignSessionModalFinish
    });
  }
  assignSessionModalFinish = (config: IModalAssignSession) => {
    const uids = config.accounts.map(acct => acct.uid);
    const testSessionIds = this.processSelectedTestSessions(config.testSessionsSelected);
    this.auth.apiCreate(this.routes.TEST_ADMIN_STUDENTS_BOOKING, {
      isRevoke: false,
      testSessionIds,
      uids,
    }, this.mySchool.constructPermissionsParams())
      .then(res => {
        config.accounts.forEach(student => student.test_sessions = _.union(student.test_sessions, testSessionIds));
        this.pageModal.closeModal();
      });
  }

  // unassign session
  unassignSessionModalStart() {
    const students = this.getSelectedStudents(true);
    const availableTestSessions: number[] = _.uniq(_.flatten(students.map(student => student.test_sessions)));
    const config: any = {
      accounts: students,
      availableTestSessions,
      testSessionsSelected: {}
    };
    this.pageModal.newModal({
      type: AccountModal.UNASSIGN_SESSION,
      config,
      finish: this.unassignSessionModalFinish
    });
  }

  unassignSessionModalFinish = (config: any) => {
    const uids = config.accounts.map(student => student.uid);
    const testSessionIds = this.processSelectedTestSessions(config.testSessionsSelected);
    this.auth.apiCreate(this.routes.TEST_ADMIN_STUDENTS_BOOKING, {
      isRevoke: true,
      testSessionIds,
      uids,
    }, this.mySchool.constructPermissionsParams())
      .then(res => {
        config.accounts.forEach(student => {
          _.pull(student.test_sessions, ...testSessionIds);
        });
        this.pageModal.closeModal();
      });
  }

  /**
   * @returns `number[]` the ids of the test windows that are active and not locked in current class filter
   */
  get activeTestWindowIds() {
    const type_slug = this.currentClassFilter === ClassFilterId.Primary?"EQAO_G3P":this.currentClassFilter === ClassFilterId.Junior?"EQAO_G6J":this.currentClassFilter === ClassFilterId.G9?"EQAO_G9M":"EQAO_G10L";
    return this.g9demoService.testWindows.filter(tw => !tw.reg_locked && (new Date(tw.date_end) > new Date ()) && tw.type_slug === type_slug).map(tw => tw.id);
  }

  assignClassroomModalStart() {
    // eligible classrooms
    const courseType = this.currentClassFilter === ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter === ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter === ClassFilterId.G9?'EQAO_G9':'EQAO_G10';
    this.classRoomsToAssign = this.g9demoService.classrooms.filter((classroom) => {
      const semester = this.g9demoService.semesters.map[+classroom.semester]
      return classroom.course_type == courseType && semester && this.activeTestWindowIds.includes(semester.testWindowId)
    });

    const students = this.getSelectedStudents(true);
    const config: IModalAssignClassroom = {
      accounts: students,
      classroomSelected: {}
    };
    this.pageModal.newModal({
      type: AccountModal.ASSIGN_CLASSROOM,
      config,
      finish: this.assignClassroomModalFinish
    });
  }
  assignClassroomModalFinish = (config: IModalAssignClassroom) => {
    const prefix = this.currentClassFilter === ClassFilterId.Primary?"_g3_":this.currentClassFilter === ClassFilterId.Junior?"_g6_":this.currentClassFilter === ClassFilterId.G9?"":"_g10_";
    const classCodePropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code' // TODO: Add prop for BC!
    const classCodeLabelPropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code_label' // TODO: Add prop for BC!
    const classrooms = Object.entries(config.classroomSelected)
    const selectedClassroom = classrooms.filter(([key, value]) => value == true);
    const classroomId = selectedClassroom[0][0];
    const classroom = this.classRoomsToAssign.find(classroom => {
      return classroom.id === Number(classroomId);
    })
    if (classroom) {
      // config.accounts.forEach(account => {
      //   account[classCodePropName] = classroom.id;
      //   account[classCodeLabelPropName] = classroom.class_code;
      //   account.teacher = classroom.educator;
      //   if(this.currentClassFilter == ClassFilterId.Primary){
      //     account._g3_eqao_pj_french = classroom.is_fi?'1':'0'
      //   }
      // });
    }
    let schoolData: any = this.g9demoService.schoolData

    const schl_group_id = schoolData.group_id;
    const schl_dist_group_id = schoolData.schl_dist_group_id;
    // data.educator
    const namespace= this.currentClassFilter === ClassFilterId.Primary?"eqao_sdc_g3":this.currentClassFilter === ClassFilterId.Junior?"eqao_sdc_g6":this.currentClassFilter === ClassFilterId.G9?"eqao_sdc":"eqao_sdc_g10";
    const configs = { ...config, schl_group_id, schl_dist_group_id, namespace}
    this.auth.apiCreate(this.routes.SCHOOL_ADMIN_STUDENT_ASSIGN, configs, this.configureQueryParams()).then(res => {
      
      //reload school admin sign off data due to student data had been changed and need school admin to sign off again.
      this.mySchool.initStuAsmtSignoff();

      config.accounts.forEach(account => {
        if (classroom){
          const oldClassroom = this.g9demoService.classrooms.find(classroom => {
            return classroom.id === account[classCodePropName];
          })
          account[classCodePropName] = classroom.id;
          account[classCodeLabelPropName] = classroom.class_code;
          account.teacher = classroom.educator;
          if(this.currentClassFilter == ClassFilterId.Primary){
            account._g3_eqao_pj_french = classroom.is_fi?'1':'#'
          }
          if(this.currentClassFilter == ClassFilterId.G9){
            account.eqao_g9_french = classroom.is_fi?'1':'#'
            account.TermFormat = res[0].semester_foreign_id
          }
          if (oldClassroom) {
            oldClassroom.students -= 1;
            this.g9demoService.removeStudentFromClassroom(account, oldClassroom.id);
          }
          classroom.students = classroom.students ? classroom.students + 1 : 1;
          this.g9demoService.teacherClassrooms.map[classroomId].currentStudents.list.push(account)
        }  
        if(this.currentClassFilter == ClassFilterId.G9){
          account['eqao_g9_class_code'] = classroom.id;
          account['eqao_g9_class_code_label'] = classroom.class_code;
          account.semester_label = this.renderSemester(classroom.semester)
        }else{
          account[prefix+'eqao_g9_class_code'] = classroom.id;
          account[prefix+'eqao_g9_class_code_label'] = classroom.class_code;
        }
      });
     this.accountsTable.injestNewData(this.students); 
    }).catch((err:Error)=>{
      if(err.message.includes("INVALID_CLASS")){
        setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_assign_class_error')), 0);
      }
      console.error(err);
    })  
    this.pageModal.closeModal();
  }

  /**
   * @returns true if at least one classroom is selected in ASSIGN_CLASSROOM modal
   */
  get isClassroomSelected() {
    return Object.values(this.cmc()?.classroomSelected || {}).filter((c) => c).length > 0;
  }

  getAssignModalTitle(){
    if(this.currentClassFilter === ClassFilterId.G9){
      return 'sa_assign_students'
    } else if(this.currentClassFilter === ClassFilterId.OSSLT){
      return  'sa_assign_students_grouping'
    }
  }
  getAssignModalTitle2(){
    if(this.currentClassFilter === ClassFilterId.G9){
      return 'sa_student_assign_class'
    } else if(this.currentClassFilter === ClassFilterId.OSSLT){
      return  'sa_student_assign_grouping'
    }

  }

  // unassign classroom
  unassignClassroomModalStart() {
    const students = this.getSelectedStudents(true);
    const config: any = {
      accounts: students
    };
    this.pageModal.newModal({
      type: AccountModal.UNASSIGN_CLASSROOM,
      config,
      finish: this.unassignClassroomModalFinish
    });
  }

  unassignClassroomModalFinish = (config: any) => {
    const students = this.getSelectedStudents(true);
    students.forEach(students => {
      students.classCode = null;
    });
    this.pageModal.closeModal();
  }

  removeStudent = () => {
    const config = {};
    this.pageModal.newModal({
      type: AccountModal.REMOVE_STUDENT,
      config,
      finish: this.removeStudentModalFinish
    })
  }

  removeStudentModalFinish = () => {
    const targets: any= this.getSelectedStudents(false);
    const currentTestWindowId = this.currentTestWindow.id;
    if(this.isRemoving) {
      return;
    }
    this.isRemoving = true;
    //const removePromises = [];
    const studentClasses :any[] =[];
    let namespace
    let classCodeVarName
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        namespace = 'eqao_sdc_g3'
        classCodeVarName = '_g3_eqao_g9_class_code'
      break;
      case ClassFilterId.Junior:
        namespace = 'eqao_sdc_g6'
        classCodeVarName = '_g6_eqao_g9_class_code'
      break;
      case ClassFilterId.G9:
        namespace = 'eqao_sdc'
        classCodeVarName = 'eqao_g9_class_code'
      break;
      case ClassFilterId.OSSLT:
        namespace = 'eqao_sdc_g10'
        classCodeVarName = '_g10_eqao_g9_class_code'
      break;
      default:
        namespace = 'eqao_sdc_g3'
        classCodeVarName = '_g3_eqao_g9_class_code'
      break;
    }
    for (let student of targets) {
      studentClasses.push({
        classIds:student[classCodeVarName],
        //classIds:this.currentClassFilter == ClassFilterId.G9? student.eqao_g9_class_code:student._g10_eqao_g9_class_code,
        studentId: student.id
      })
     
      //removePromises.push(promise);
    }
    
    let query =  { 
      schl_group_id: this.mySchool.getCurrentSchoolGroupId(), 
      //class_id: this.currentClassFilter == ClassFilterId.G9? student.eqao_g9_class_code:student._g10_eqao_g9_class_code,
      studentClasses: null,
      namespace,
      //namespace: this.currentClassFilter == ClassFilterId.G9? 'eqao_sdc':'eqao_sdc_g10'
      testWindowId: currentTestWindowId
    }

    let studentRemovalPromise: Promise<any> = null;
    if(studentClasses.length > 1) {
      studentRemovalPromise = this.auth.apiCreate(this.routes.SCHOOL_ADMIN_STUDENT_DELETE, studentClasses, { query: query });
    } else {
      query.studentClasses = JSON.stringify(studentClasses);
      studentRemovalPromise = this.auth.apiRemove(this.routes.SCHOOL_ADMIN_STUDENT, -1, { query: query });
    }  
    studentRemovalPromise
    .then(res => {
      if(res.studentRecords && res.studentRecords.length > 0){
        this.studentRemovalRecords = res.studentRecords;
        this.isRemoving = false;
        const config = {};
        this.pageModal.newModal({
          type: AccountModal.REMOVE_STUDENT_VALIDATION,
          config,
          isProceedOnly: true,
          finish: this.removeStudentValidationModalFinish
        })
      } else {
        this.removeStudentAlter(targets)
      }
    })
    .catch( (err) =>{
      this.isRemoving = false;
      setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('sa_remove_student_error')), 0);
    })
  }

  removeStudentValidationModalFinish = () => {
    this.pageModal.closeModal();
  }

  removeStudentAlter(targets){
      alert(this.lang.tra("sa_student_remove_success", undefined, {['studentNum']:targets.length}))
      //await Promise.all(removePromises);
      targets.map(entry => {
        if(this.currentClassFilter == ClassFilterId.Primary){
          entry.eqao_is_g3 = '#'
        }
        if(this.currentClassFilter == ClassFilterId.Junior){
          entry.eqao_is_g6 = '#'
        }
        if(this.currentClassFilter == ClassFilterId.G9){
          entry.eqao_is_g9 = '#'
        }
        if(this.currentClassFilter == ClassFilterId.OSSLT){
          entry.eqao_is_g10 = '#'
        }
        this.accountsTable.refreshFilters();
        this.checkSelection();
      })
      console.log("confirm finished");
      this.isRemoving = false;
  }

  importStudentsModal() {
    const columns = this.getStudentExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns,
      useConfirmAlert: true
    }

    this.pageModal.newModal({
      type: AccountModal.IMPORT,
      config,
      finish: this.importStudentsModalFinish
    })
  }

  importStudentsModalFinish = (config: { importData: any[] }) => {
    config.importData.forEach(data => {
      if(data.eqao_g9_class_code){
        data.eqao_g9_class_code = data.eqao_g9_class_code.trim();
      }  
      if(this.currentClassFilter === ClassFilterId.OSSLT){
        data.eqao_is_g10 = '1';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.G9){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '1';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.Primary){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '1';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.Junior){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '1';
      }
      if(data.NonParticipationStatus === '1'){
        data.NonParticipationStatus_exempted = '1'
      }
      if(data.NonParticipationStatus === '2'){
        data.NonParticipationStatus_deferred = '1'
      } 
      if(data.NonParticipationStatus === '3'){
        data.NonParticipationStatus_osslc = '1'
      }
      if(data.NonParticipationStatus === '4'){
        data.NonParticipationStatus_osslc_spring = '1'
      }
      if(data.eqao_acc_assistive_tech === '2') {
        data.eqao_acc_assistive_tech_1_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech === '1') {
        data.eqao_acc_assistive_tech_1_other  = '1'
      }

      if(data.eqao_acc_assistive_tech_pj_reading === '2') {
        data.eqao_acc_assistive_tech_1_pj_reading_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_reading === '1') {
        data.eqao_acc_assistive_tech_1_pj_reading_other  = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_writing === '2') {
        data.eqao_acc_assistive_tech_1_pj_writing_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_writing === '1') {
        data.eqao_acc_assistive_tech_1_pj_writing_other  = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_mathematics === '2') {
        data.eqao_acc_assistive_tech_1_pj_mathematics_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_mathematics === '1') {
        data.eqao_acc_assistive_tech_1_pj_mathematics_other  = '1'
      }
      data.paidForClass = {}

      let classType = (this.currentClassFilter === ClassFilterId.Primary) ? 'EQAO_G3' : (this.currentClassFilter === ClassFilterId.Junior) ?'EQAO_G6':(this.currentClassFilter === ClassFilterId.G9) ?'EQAO_G9':'EQAO_G10';
      // const schoolClass = this.g9demoService.classOptions.list.find( 
      //   c => c.label == data.eqao_g9_class_code && c.group_type == classType
      // );
      const schoolClass = this.g9demoService.classrooms.find (cr => {
        const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
        const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
        const tw_isActive = (new Date(test_window.date_end) > new Date ()) && test_window.id == this.currentTestWindow.id
        if(cr.class_code == data.eqao_g9_class_code && cr.course_type == classType && tw_isActive){
          return cr;
        }
      })
      if (schoolClass) {
        data.eqao_g9_class_code = schoolClass.id
        data.eqao_g9_class_code_label = schoolClass.class_code
      } else{
        data.eqao_g9_class_code_label = data.eqao_g9_class_code;
        data.eqao_g9_class_code = '';
      }
    })
    this.pageModal.closeModal();

    this.pageModal.newModal({
       type: AccountModal.IMPORT_PROGRESS,
       config: {
        useConfirmAlert: false
       },
       finish: this.importProgressFinish
    })

    this.addNewAccounts(config.importData, true);
  }

  importProgressFinish(){
    this.pageModal.closeModal();
  }

  isImportModal(cModal){
    return cModal == AccountModal.IMPORT
  }
  isExportModal(cModal){
    return cModal == AccountModal.EXPORT
  }

  async exportStudentsModal() {
    const students = this.accountsTable.getFilteredData();
    const columns = this.getStudentExportColumns();
    let exportData = this.getExportData(students, columns);
    downloadFromExportData(exportData, columns, 'students-export', this.auth);
  }

  async exportStudentsImportSummaryModal() {
    let failedStudents: any = [];
    if(this.isSasnLogin){
      failedStudents = this.vrs.failed.map(st => {return st.SASN});
    } else {
      failedStudents = this.vrs.failed.map(st => {return st.eqao_student_gov_id});
    }

    // Deep Clone the arrays
    let source: any;
    if(this.isSasnLogin){
      source = JSON.parse(JSON.stringify({
        CreatedSASNs: this.vrs.successCreate,
        UpdatedSASNs: this.vrs.successUpdate,
        NoChangesSASNs: this.vrs.noChange,
        FailedSASNs: failedStudents,
      }));
    } else {
      source = JSON.parse(JSON.stringify({
        CreatedOENs: this.vrs.successCreate,
        UpdatedOENs: this.vrs.successUpdate,
        NoChangesOENs: this.vrs.noChange,
        FailedOENs: failedStudents,
      }));
    }
     
    // Excel Column Headers
    let columns = ["CreatedOENs", "UpdatedOENs", "NoChangesOENs", "FailedOENs"];
    if(this.isSasnLogin){
      columns = ["CreatedSASNs", "UpdatedSASNs", "NoChangesSASNs", "FailedSASNs"];
    } 
    
    let longestList = 0;
    for (var key of Object.keys(source)) {
      const value = source[key];
      if (value && value.length > longestList) {
        longestList = value.length;
      }
    }
    // Find the next element in the passed array
    const getNextValue = (sourceList: any[]): string => {
      if (sourceList && sourceList.length > 0) {
        const nextValue = sourceList[sourceList.length - 1];
        sourceList.pop();
        return `${nextValue}`;
      }
      return null;
    };

    let exportData = [];
    // Make Excel data
    for(let i = 0; i < longestList; i++) {
      let ed = {};
      columns.forEach((c: string) => {
        ed[c] = getNextValue(source[c]);
      });
      exportData.push(ed);
    }
    downloadExportData(exportData, 'import-summary', this.auth);
  }

  exportStudentsModalStart() {
    const columns = this.getStudentExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns,
      useConfirmAlert: false,
    }

    this.pageModal.newModal({
      type: AccountModal.EXPORT,
      config,
      confirmationCaption: this.lang.tra('btn_close'),
      isProceedOnly: true,
      finish: this.exportStudentsModalFinish
    })
  }

  exportStudentsModalFinish = (config: { importData: any[] }) => {
    this.pageModal.closeModal();
  }

  importValidationModalStart(data: IValidationResults<IStudentAccount>) {
    const columns = this.getStudentExportColumns();
    const formattedResults = [];
    data.results.forEach(result => {
      formattedResults.push(result.obj || {});
      formattedResults.push(result.errors || []);
    });

    const config = {
      successCreate: data.successCreate,
      successUpdate: data.successUpdate,
      noChange: data.noChange,
      results: formattedResults,
      columns,
      useConfirmAlert: false
    };
    this.pageModal.newModal({
      type: AccountModal.IMPORT_VALIDATION,
      config,
      isProceedOnly: true,
      finish: this.importValidationModalFinish
    })
  }
  importValidationModalFinish = () => {
    this.pageModal.closeModal();
  }

  private updateAccTechAndNPS(record){
    if(this.currentClassFilter===ClassFilterId.G9||this.currentClassFilter===ClassFilterId.OSSLT){
      // const otherTechfields = [
      //   "eqao_acc_assistive_tech_1_other", "eqao_acc_assistive_tech_2_kurz_dl",
      //   "eqao_acc_assistive_tech_2_kurz_ext","eqao_acc_assistive_tech_2_nvda",
      //   "eqao_acc_assistive_tech_2_voiceover","eqao_acc_assistive_tech_2_readaloud",
      //   "eqao_acc_assistive_tech_2_jaws","eqao_acc_assistive_tech_2_chromevox",
      //   "eqao_acc_assistive_tech_2_read","eqao_acc_assistive_tech_2_other",
      // ]
      const otherTechfields = [
           "eqao_acc_assistive_tech_1_other", 
      ]
      const hasOtherTech:boolean = (otherTechfields.find( field => record[field] === '1') !== undefined )
      if(record.eqao_acc_assistive_tech_1_chrome === '1'){
        record.eqao_acc_assistive_tech = '2';
      }else if(hasOtherTech) {
        record.eqao_acc_assistive_tech = '1';
      }else{
        record.eqao_acc_assistive_tech = '#';
      }
    }
    if(this.currentClassFilter===ClassFilterId.Primary||this.currentClassFilter===ClassFilterId.Junior){
      const prefixs = [ {source:"eqao_acc_assistive_tech_1_pj_reading", target:"eqao_acc_assistive_tech_pj_reading"},
                        {source: "eqao_acc_assistive_tech_1_pj_writing",target :"eqao_acc_assistive_tech_pj_writing"},
                        {source: "eqao_acc_assistive_tech_1_pj_mathematics", target:"eqao_acc_assistive_tech_pj_mathematics"}]
      prefixs.forEach(prefix =>{
        const otherTechfields = [ prefix.source+"_other" ]
        const hasOtherTech:boolean = (otherTechfields.find( field => record[field] === '1') !== undefined )
        if(record[prefix.source+"_chrome"] === '1'){
          record[prefix.target] = '2';
        }else if(hasOtherTech) {
          record[prefix.target]  = '1';
        }else{
          record[prefix.target]  = '#';
        }
      })
    }

    //NonParticipationStatus OSSLT ONLY
    record.NonParticipationStatus = '#';
    if(record.NonParticipationStatus_exempted =='1'){
      record.NonParticipationStatus = '1';
    }
    if(record.NonParticipationStatus_deferred =='1'){
      record.NonParticipationStatus = '2';
    }
    if(record.NonParticipationStatus_osslc =='1'){
      record.NonParticipationStatus = '3';
    }
    if(record.NonParticipationStatus_osslc_spring =='1'){
      record.NonParticipationStatus = '4';
    }
  }

  private getExportData( students: Partial<IStudentAccount>, columns: IExportColumn[]): IStudentAccount[] {
    return students.map((student): Partial<IStudentAccount> => {
      //const forApi = this.g9demoService.clientToApiPayloadStudent(student);
      let entry: Partial<IStudentAccount> = {};
      columns.forEach(col => {
        const cap = col.caption;
        const prop = col.prop;
        //const inputVal = (this.currentClassFilter ==ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1)? student[prop]:student['_g10_'+prop];
        let inputVal = student['_g10_'+prop]
        if(overlapVariables.indexOf(prop) > -1){
          inputVal = student[prop]
        }else{
          switch(this.currentClassFilter){
            case ClassFilterId.Primary:
              inputVal = student['_g3_'+prop]
              break;
            case ClassFilterId.Junior:
              inputVal = student['_g6_'+prop]
              break;
            case ClassFilterId.G9:
              inputVal = student[prop]
              break;    
            case ClassFilterId.OSSLT:
            default:
              inputVal = student['_g10_'+prop]
              break
          }
        }
        let exportVal;

        if(cap === 'ClassCode' || cap === 'Grouping'){
          //modify ClassCode and Grouping
          exportVal = this.renderClassCode(inputVal);
        }
        else if(cap === 'SchMident'){
          exportVal = this.g9demoService.schoolData.foreign_id
        } 
        else if(cap === 'BrdMident'){
          exportVal = this.g9demoService.schoolDistrict.foreign_id  
        }
        else if(cap === 'MathTeacherLastName'|| cap === 'MathTeacherFirstName'|| cap === 'ClassTeacherLastName'||cap === 'ClassTeacherFirstName'){
          const namespace = this.currentClassFilter === ClassFilterId.Primary? '_g3_':this.currentClassFilter === ClassFilterId.Junior?'_g6_':this.currentClassFilter === ClassFilterId.G9?'':'_g10_'
          const classroom = this.g9demoService.classrooms.find(classroom => classroom.id == (student[namespace+"eqao_g9_class_code"]))
          var teacher_uid
          if(classroom){
            teacher_uid = this.g9demoService.classrooms.find(classroom => classroom.id == student[namespace+"eqao_g9_class_code"]).teacher_uid;
          }  
          if(teacher_uid){
            const teacher = this.g9demoService.teachers.map[+teacher_uid]
            if(cap === 'MathTeacherLastName' && teacher != undefined){
              exportVal = teacher.lastName;
            }
            if(cap === 'MathTeacherFirstName' && teacher != undefined){
              exportVal = teacher.firstName;
            }
            if(cap === 'ClassTeacherLastName' && teacher != undefined){
              exportVal = teacher.lastName;
            } 
            if(cap === 'ClassTeacherFirstName' && teacher != undefined){
              exportVal = teacher.firstName;
            }  
          }else{
            exportVal = '#'
          }
        }
        else if (cap === 'AccAssistiveTech'){
          const namespace = this.currentClassFilter ==ClassFilterId.G9?'':'_g10_'
          //modify AccAssistiveTech
          if(student[namespace+'eqao_acc_assistive_tech_1_chrome'] === '1'){
            exportVal = '2';
          }else if(student[namespace+'eqao_acc_assistive_tech_1_other'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_kurz_dl']==='1'||
                   student[namespace+'eqao_acc_assistive_tech_2_kurz_ext']==='1'||student[namespace+'eqao_acc_assistive_tech_2_nvda'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_2_voiceover'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_readaloud'] ==='1'||
                   student[namespace+'eqao_acc_assistive_tech_2_jaws'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_chromevox'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_2_read'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_other'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_3_chrome_2'] === '1'){
            exportVal = '1';
          }else{
            exportVal = '#';
          }
        } else if(cap === 'NonParticipationStatus'){
          //modify NonParticipationStatus
          if(student['_g10_NonParticipationStatus_exempted'] === '1'){
            exportVal = '1';
          }else if(student['_g10_NonParticipationStatus_deferred'] === '1'){
            exportVal = '2';
          }else if(student['_g10_NonParticipationStatus_osslc'] === '1'){
            exportVal = '3';
          }else if(student['_g10_NonParticipationStatus_osslc_spring'] === '1'){
            exportVal = '4';
          }else{
            exportVal = '#'
          }
        }
        else {
          exportVal = inputVal;
        }
        //modify unassign value
        if(exportVal == undefined || exportVal == null||exportVal == ''){
          exportVal = '#'
        }

        if(prop!=""){
          entry[prop] = exportVal;
        }else{
          entry[cap] = exportVal;
        }  
      });
      return entry;
    });
  }

  initStudentInfoSignoffs(){

  }

  private isNullUndefinePound(data):boolean{
    return (data === null || data === undefined || data ==='' || data ==='#')
  }

  private getStudentExportColumns = (): IExportColumn[] => {
    //let exportColumnsList = this.currentClassFilter === ClassFilterId.OSSLT? APIStudentColumnOSSLTList() : APIStudentColumnG9List();
    let exportColumnsList = APIStudentColumnOSSLTList();
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        exportColumnsList = APIStudentColumnPJList(this.lang.c());
        break;
      case ClassFilterId.Junior:
        exportColumnsList = APIStudentColumnPJList(this.lang.c());
        break;
      case ClassFilterId.G9:
        exportColumnsList = APIStudentColumnG9List();
        break;
      case ClassFilterId.OSSLT:
      default:
        exportColumnsList = APIStudentColumnOSSLTList();
        break;
    }
    let columns: IExportColumn[] = [];
    
    let modifiedLabels:{[key:string]:any} = {
      'FirstName': 'first_name',
      'LastName': 'last_name',
    };
    if (this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
      modifiedLabels["ClassCode"] = 'class_code'
    }
    if (this.currentClassFilter === ClassFilterId.OSSLT) {
      modifiedLabels["Grouping"] = 'class_code'
      const RemovedColums = ["ClassCode","FrenchImmersionOrExtended","MathTeacherFirstName","MathTeacherLastName","LearningFormat_G9"]
      RemovedColums.forEach(colums => {
        const index = exportColumnsList.indexOf(colums);
        if(index!= -1){
          exportColumnsList.splice(index,1)
        }  
      })
    }
    if (this.currentClassFilter === ClassFilterId.G9) {
      modifiedLabels["ClassCode"] = 'class_code'
      const RemovedColums = ["AccOther", "DateOfFTE", "EligibilityStatus", "Graduating", "Grouping", 
      "Homeroom", "LevelofStudyLanguage", "NonParticipationStatus", "SpecPermIEP", "LearningFormat_G10"]
      RemovedColums.forEach(colums => {
        const index = exportColumnsList.indexOf(colums);
        if(index!= -1){
          exportColumnsList.splice(index,1)
        }  
      })
      if(this.lang.c() == 'fr'){
        const index = exportColumnsList.indexOf('FrenchImmersionOrExtended');
          if(index!= -1){
            exportColumnsList.splice(index,1)
          }  
      }
    }

    exportColumnsList.forEach(caption => {
      let source = modifiedLabels[caption] || caption ;
      const target = this.g9demoService.getAPITargetMapping(source)?this.g9demoService.getAPITargetMapping(source):caption
      columns.push({
        prop: target,
        caption: caption,
        isClickable: false
      });
    });
    return columns;
  }

  public apiErrMsgToSlug(errMsg: string): string {
    return apiErrMsgToSlug(errMsg, this.lang, {
      isPrimary: this.isPimary(),
      isJunior: this.isJunior()
    });
  }


  private setupLinear(account){
    //if there is a submitted alternative request, don't change the Linear value 
    const altVersionRequestName = this.g9demoService.getStudentAltVersionRequestName(this.currentTestWindow)
    const studentAltVersionRequest = account[altVersionRequestName]||{}
    if( studentAltVersionRequest.id && studentAltVersionRequest.id > 0 ){
      return
    }

    if(account.eqao_alternate_form_pj_mathematics == '2'){
      account['eqao_dyn_linear'] = '2'
    } else if(account.g9_alternative_version_request == '1' && this.currentClassFilter == ClassFilterId.G9) {
      account['eqao_dyn_linear'] = '1'
    } else if(account.g9_alternative_version_request == '2' && this.currentClassFilter == ClassFilterId.G9) {
      account['eqao_dyn_linear'] = '2'
    }
    else if(account.eqao_acc_braille == '1'||account.eqao_acc_braille == '2'||account.eqao_acc_braille == '3'||account.eqao_acc_braille == '4'
      ||account.eqao_acc_braille_pj_reading == '1'||account.eqao_acc_braille_pj_reading == '2'||account.eqao_acc_braille_pj_reading == '3'||account.eqao_acc_braille_pj_reading == '4'
      ||account.eqao_acc_braille_pj_writing == '1'||account.eqao_acc_braille_pj_writing == '2'||account.eqao_acc_braille_pj_writing == '3'||account.eqao_acc_braille_pj_writing == '4'
      ||account.eqao_acc_braille_pj_mathematics == '1'||account.eqao_acc_braille_pj_mathematics == '2'||account.eqao_acc_braille_pj_mathematics == '3'||account.eqao_acc_braille_pj_mathematics == '4'
      ||account.AccAudioVersion == '1'||account.eqao_alternate_form_pj_reading == '1'||account.eqao_alternate_form_pj_writing == '1'||account.eqao_alternate_form_pj_mathematics == '1'
      ||account.osslt_alternative_version_test == '1'
      ){
      account['eqao_dyn_linear'] = '1'
    }
    else{
      account['eqao_dyn_linear'] = '#'
    }
  }

  private clientRuleCheck(record){
    if(record.eqao_acc_assistive_tech_2_other === '1'){
      if(record.eqao_acc_assistive_tech_custom === undefined ||  record.eqao_acc_assistive_tech_custom.trim().length < 1){
        setTimeout(() => this.loginGuard.quickPopup('sa_oat_string_empty'), 0);
        return false;
      }
    }
    return true;
  }

  private modifyStudentData(record, isBulk = true){
    //***Modify Data Base on the Business Rule *******************************/
    /* 
     Re- set Unified English Braille Value
    */

    if(record.first_name){
      record.first_name = record.first_name.trim();
    }

    if(record.last_name){
      record.last_name = record.last_name.trim();
    }
    if (record.eqao_student_gov_id) {
      record.eqao_student_gov_id = record.eqao_student_gov_id.trim()
    }

    if (this.lang.c() === 'fr' && (record.eqao_acc_braille === '3' || record.eqao_acc_braille === '4')){
      record.eqao_acc_braille = '#';
    }
    if (this.lang.c() === 'en' && (record.eqao_acc_braille === '1' || record.eqao_acc_braille === '2')){
      record.eqao_acc_braille = '#';
    }

    /*
    OutOfProvinceResidence field value must be “1” if the value in the field StatusInCanada is “4," "5" or "6."
    */
    let warnMsgs: string = '';
    const StatusInCanada = record.eqao_status_in_canada
    if (StatusInCanada =='4'||StatusInCanada == '5'||StatusInCanada == '6'){
      record.eqao_out_province_residence = '1';
    }
    /*
    If BornOutsideCanada = 1 (Yes), did this student's family come to Canada as refugees? 
    */
    // Show warning message, no need to change the data (OSSLT BR Role Change No.63c)
    // const Refugee = record.eqao_refugee
    // if(Refugee == '1'){
    //   record.eqao_born_outside_canada = '1';
    // }
    /*
    If “BornOutsideCanada” = # (missing/unknown), then “TimeInCanada” must be # (missing/unknown/not applicable).
    If “BornOutsideCanada” = 1 (Yes), then “TimeInCanada” must be 
        1 (less than one year),
        2 (one year or more but less than two years),
        3 (two years or more but less than three years),
        4 (three years or more but less than five years),
        5 (five years or more), 
        6. or # (missing/unknown/not applicable).
    If “BornOutsideCanada” = 2 (No), then “TimeInCanada” must be # (missing/unknown/not applicable).
    */
    let BornOutsideCanada = record.eqao_born_outside_canada;
    if (BornOutsideCanada == undefined || BornOutsideCanada =='' || BornOutsideCanada=='#'||BornOutsideCanada == '2'){
      record.eqao_time_in_canada = '#';
    }
    /*
    Student cannot have special provisions and be exempted, deferred or OSSLC. Only the non-participation status will be saved.
    Student cannot be accommodated and exempted, deferred or OSSLC. Only the non-participation status will be saved.
    Student cannot have a special consideration and be exempted, deferred or OSSLC. Only the non-participation status will be saved.
    */
    const NonParticipationStatus_exempted = record.NonParticipationStatus_exempted;
    const NonParticipationStatus_deferred = record.NonParticipationStatus_deferred;
    const NonParticipationStatus_osslc = record.NonParticipationStatus_osslc;
    const NonParticipationStatus_osslc_spring = record.NonParticipationStatus_osslc_spring;
    const NonParticipationStatus = (NonParticipationStatus_exempted == '1'|| NonParticipationStatus_deferred == '1'||NonParticipationStatus_osslc == '1'||NonParticipationStatus_osslc_spring == '1')
    if (NonParticipationStatus){
      if(record._periodic_breaks == '1'){
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_3'))
        warnMsgs += '\n';
      }

      if(record.eqao_acc_assistive_tech_1_chrome == '1'|| record.eqao_acc_assistive_tech_1_other == '1'||record.eqao_acc_assistive_tech_2_kurz_dl == '1'|| record.eqao_acc_assistive_tech_2_kurz_ext == '1'
         ||record.eqao_acc_assistive_tech_2_nvda == '1'||record.eqao_acc_assistive_tech_2_voiceover == '1'||record.eqao_acc_assistive_tech_2_readaloud == '1'
         ||record.eqao_acc_assistive_tech_2_jaws== '1'||record.eqao_acc_assistive_tech_2_chromevox == '1'||record.eqao_acc_assistive_tech_2_read == '1'
         ||record.eqao_acc_assistive_tech_2_other == '1'||record.eqao_acc_assistive_tech_3_chrome_2 == '1'||record.eqao_acc_braille == '3'||record.eqao_acc_braille == '4'
         ||record.AccAudioVersion == '1'||record._extended_time == '1'||record.eqao_pres_format == '1'||record._audio_recording_of_resp == '1'||record.AccVideotapeResponse == '1'
         ||record._audio_recording_of_resp == '1'||record.eqao_acc_scribing == '1' || record.eqao_acc_alt_version == '1'||record.AccOther == '1'||record.eqao_acc_braille == '1'||record.eqao_acc_braille == '2'){
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_1'))
        warnMsgs += '\n';
      }

      if(record.SpecPermIEP == '1' || record._no_iep_temp_inj == '1'||record._no_iep_recent_arriv == '1' ){  //SpecPermMoved
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_2'))
        warnMsgs += '\n';
      }

      //record._ell = '#';  //'ESLELD'
      //record.ALFPANA = '#';  
      record.eqao_acc_assistive_tech_1_chrome = '#';
      record.eqao_acc_assistive_tech_1_other = '#';
      record.eqao_acc_assistive_tech_2_kurz_dl = '#';
      record.eqao_acc_assistive_tech_2_kurz_ext = '#';
      record.eqao_acc_assistive_tech_2_nvda = '#';
      record.eqao_acc_assistive_tech_2_voiceover = '#';
      record.eqao_acc_assistive_tech_2_readaloud = '#';
      record.eqao_acc_assistive_tech_2_jaws = '#';
      record.eqao_acc_assistive_tech_2_chromevox = '#';
      record.eqao_acc_assistive_tech_2_read = '#';
      record.eqao_acc_assistive_tech_2_other = '#';
      record.eqao_acc_assistive_tech_3_chrome_2 = '#';
      record.eqao_acc_assistive_tech_custom = '';
      record._audio_recording_of_resp = '#';
      record.AccAudioVersion = '#';
      record.eqao_pres_format = '#'; //AccSign
      record._audio_recording_of_resp = '#'; //AccAudioResponse
      record.AccVideotapeResponse = '#'; //AccVideotapeResponse
      record.eqao_acc_scribing = '#'; //AccScribing
      record.AccOther = '#'; //AccOther
      record.SpecPermIEP = '#'; //SpecPermIEP
      record._no_iep_temp_inj = '#'; //SpecPermTemp
      record._no_iep_recent_arriv = '#';  //SpecPermMoved
      record._ell_early = '#';  //NEW
      record.eqao_acc_alt_version = '#';  //NEW

      // OSSLT 2024-2025 Data definition update: fields removed
      // G9 2024-2025 Data definition update: fields removed
      if(record.eqao_is_g10 !== '1' && record.eqao_is_g9!=='1'){
        record._extended_time = '#'; //AccBreaks 
        record._periodic_breaks = '#'; //SpecProvBreaks
        record.eqao_acc_braille = '#'; //AccBraille
      }
      //student cannot be indentified as "graduating" and "deferred"  Only gradudating will be saved
      //https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/4055
      if(record.Graduating == '1' && NonParticipationStatus_deferred == '1'){
        record.NonParticipationStatus_deferred = '#';
        record.NonParticipationStatus = '#'
      }
    }
    if(warnMsgs.length>0 && !isBulk){
      // setTimeout(() => this.loginGuard.quickPopup(warnMsgs), 0);
      window.confirm(warnMsgs);
    }
    //***End of Modify Data Base on the Business Rule *******************************/
  }

  isConfirmAlert(): boolean {
    const defaultConfirmAlert = true;
    let confirmAlert: boolean;
    if (typeof this.cmc().useConfirmAlert === 'boolean') {
      confirmAlert = this.cmc().useConfirmAlert;
    } else {
      confirmAlert = defaultConfirmAlert;
    }
    return confirmAlert;
  }

  isOdd(i): boolean {
    return i % 2 !== 0;
  }

  pageChanged() {
    if (!this.isAllSelected) {
      this.students.forEach(student => student.__isSelected = false);
    }
  }
  
  isBCSite(): boolean {
    return this.whitelabelService.getSiteFlag('BC_STUDENT') 
  }

  totolErrorStudentRecords():number{
    const errMsgKey = this.currentClassFilter == ClassFilterId.Primary?"_g3_errMsg":this.currentClassFilter == ClassFilterId.Junior?"_g6_errMsg":this.currentClassFilter == ClassFilterId.G9?"errMsg":"_g10_errMsg";
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary?"SDC_conflict_g3":this.currentClassFilter == ClassFilterId.Junior?"SDC_conflict_g6":this.currentClassFilter == ClassFilterId.G9?"SDC_conflict_g9":"SDC_conflict_g10";
    const studentCourse = this.currentClassFilter == ClassFilterId.Primary?"eqao_is_g3":this.currentClassFilter == ClassFilterId.Junior?"eqao_is_g6":this.currentClassFilter === ClassFilterId.G9? "eqao_is_g9":"eqao_is_g10"
    if(this.currentTestWindow){
      return this.students.filter((student) => 
        student[studentCourse] =='1' && this.filterStudentTestWindow(student) && ((student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)||student[conflictKey]!= undefined)
      ).length
    } 
    return 0;

    // return this.accountsTable.getEntries().filter((student) =>  
    //   (student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)||student[conflictKey]!= undefined
    // ).length;

  }

  numberOfInvalude():number{
    /*
    const errMsgKey = this.currentClassFilter == ClassFilterId.G9?"errMsg":"_g10_errMsg";
    const totalInvalid = this.accountsTable.getEntries().filter((student) => { 
      (student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)
    }).length;
    return totalInvalid - this.numberOfDiscrepancy();
    */
   return this.totolErrorStudentRecords() - this.numberOfDiscrepancy()
  }
  numberOfDiscrepancy():number{
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary?"SDC_conflict_g3":this.currentClassFilter == ClassFilterId.Junior?"SDC_conflict_g6":this.currentClassFilter == ClassFilterId.G9?"SDC_conflict_g9":"SDC_conflict_g10";
    if(this.currentTestWindow){
      return this.students.filter((student) => 
        this.filterStudentTestWindow(student) && (student[conflictKey]!= undefined)
      ).length
    } 
    return 0;
    //return this.accountsTable.getEntries().filter((student) => (student[conflictKey]!= undefined)).length;
  }

  filterFlaggedItems(student: IStudentAccount){
    const errMsgKey = this.currentClassFilter==ClassFilterId.Primary? "_g3_errMsg" :this.currentClassFilter == ClassFilterId.Junior ? "_g6_errMsg" :this.currentClassFilter == ClassFilterId.G9 ? "errMsg" : "_g10_errMsg";
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary ? "SDC_conflict_g3" :this.currentClassFilter == ClassFilterId.Junior ? "SDC_conflict_g6" :this.currentClassFilter == ClassFilterId.G9 ? "SDC_conflict_g9" : "SDC_conflict_g10";
    if(this.showFlaggedItems){
      if(this.showFlaggedItems === FlaggedItemCase.ALL_FLAG) {
        if ((student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0) || student[conflictKey]!= undefined){
          return true;
        }
      }
      if(this.showFlaggedItems === FlaggedItemCase.IMPORT_FLAG) {
        if (student[conflictKey] && student[conflictKey].IsImportUpdate == 1){
          return true;
        }
      }
    }
    return false;
  }

  setShowFlaggedItems(input:FlaggedItemCase){
    this.showFlaggedItems = input;
    if(input != FlaggedItemCase.ALL){
      this.accountsTable.activeFilters['showFlaggedItems'] = { mode: FilterSettingMode.VALUE, config: { value: '#' } }
    } else {
      this.accountsTable.activeFilters['showFlaggedItems'] = null;
    } 
    this.accountsTable.refreshFilters();
  }

  async confirmDatamodify(){
    if(this.totolErrorStudentRecords() > 0){
      this.loginGuard.quickPopup(this.lang.tra('ab_confirm_alert'))
    }
    else { 
      await this.mySchool.signoffStuAsmt(this.currentClassFilter)
      .then(res=>{
        //After signoff all student change the page to show all student data  form (yellow flag or red flag student filter).
        //Otherwise admin need to refresh to see students, due to there's no red/yellow flag student anymore 
        this.setShowFlaggedItems(FlaggedItemCase.ALL)  
      });
    }
  }

  deadlineCompleteValidate(){
    //TODO
    return '';
  }

  isYellowAlertItem(student){
    if(this.currentClassFilter == ClassFilterId.Primary && student.SDC_conflict_g3 !=  undefined ){
      return true
    }
    if(this.currentClassFilter == ClassFilterId.Junior && student.SDC_conflict_g6 !=  undefined ){
      return true
    }
   if(this.currentClassFilter == ClassFilterId.G9 && student.SDC_conflict_g9 !=  undefined ){
     return true
   }
   if(this.currentClassFilter == ClassFilterId.OSSLT && student.SDC_conflict_g10 !=  undefined){
     return true
   }
   return false;
  }

  isRedAlertItem(student){
    const a = this.currentClassFilter == ClassFilterId.Primary;
    const b = student._g3_errMsg
    if(student._g3_errMsg) {const c = JSON.parse(student._g3_errMsg)}
    if(this.currentClassFilter == ClassFilterId.Primary && student._g3_errMsg && JSON.parse(student._g3_errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.Junior && student._g6_errMsg && JSON.parse(student._g6_errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.G9 && student.errMsg && JSON.parse(student.errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.OSSLT && student._g10_errMsg && JSON.parse(student._g10_errMsg).length > 0){
      return true;
    }
    return false; 
  }

  

  confirmStudentInformation(){
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('alert_kk_confirm_save'),
      confirm: () => this.pageModal.confirmModal(false)
    })
  }

  preRecord(){
    if(this.selectedStudentIndex > 0){
      this.pageModal.closeModal();
      this.selectedStudentIndex--;
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex]
      this.accountDetailModalStart(student)
    }
  }

  nextRecord(){
    if(this.selectedStudentIndex < this.accountsTable.numEntries()-1){
      this.pageModal.closeModal();
      this.selectedStudentIndex ++;
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex]
      this.accountDetailModalStart(student)
    }
  }
  clickApplyBtn(event: {studentRecord:any, choosedConflicts:any}){
    const config = this.pageModal.currentModal.config;
    const record = config.payload.record;
    const data = config.payload.data;
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary ? "SDC_conflict_g3" :this.currentClassFilter == ClassFilterId.Junior ? "SDC_conflict_g6" :this.currentClassFilter == ClassFilterId.G9 ? "SDC_conflict_g9" : "SDC_conflict_g10";
    const courseType = this.currentClassFilter == ClassFilterId.Primary ? 'EQAO_G3' :this.currentClassFilter == ClassFilterId.Junior ? 'EQAO_G6' :this.currentClassFilter == ClassFilterId.G9 ? 'EQAO_G9' : 'EQAO_G10';
    var uid = record.id
    var user_metas_import_disc_id = record[conflictKey].id
    event.choosedConflicts.forEach(conflict => {
      if(conflict.fieldname =='FirstName'){
        data.first_name = conflict.value
      }
      else if(conflict.fieldname =='LastName'){
        data.last_name = conflict.value
      }
      else if(conflict.fieldname == 'ClassCode' || conflict.fieldname == 'Grouping'){
        let class_id;
        if(conflict.fieldname == 'ClassCode') {
          const theClass = this.g9demoService.classrooms.find (cr => {
            const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
            const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
            const tw_isActive = (new Date(test_window.date_end) > new Date ())
            if(cr.class_code == conflict.value && cr.course_type == courseType && tw_isActive){
              return cr;
            }
          })
          if(theClass){
            class_id = theClass.id
          }
        }
        if(conflict.fieldname == 'Grouping') {
          const theClass = this.g9demoService.classrooms.find (cr => {
            const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
            const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
            const tw_isActive = (new Date(test_window.date_end) > new Date ())
            if(cr.class_code == conflict.value && cr.course_type == 'EQAO_G10' && tw_isActive){
              return cr;
            }
          })
          if(theClass){
            class_id = theClass.id
          }
        }
        data["eqao_g9_class_code"] =  class_id ? class_id : null;
        // event.studentRecord["class_code"] =  class_id ? class_id : null;
        data["eqao_g9_class_code_label"] = conflict.value;
      } 
      else{
        const map_result = DATA_MAPPING_EQAO_G9_STUDENT.find(r=>r.source==conflict.fieldname)
        // const namespace2 = (overlapVariables.indexOf(map_result.target) > -1 || this.currentClassFilter == ClassFilterId.G9)?'':'_g10_'
        // event.studentRecord[namespace2+map_result.target] = conflict.value;
        data[map_result.target] = conflict.value;
      }
    });

    if(data.NonParticipationStatus === '1'){
      data.NonParticipationStatus_exempted = '1'
    }
    if(data.NonParticipationStatus === '2'){
      data.NonParticipationStatus_deferred = '1'
    } 
    if(data.NonParticipationStatus === '3'){
      data.NonParticipationStatus_osslc = '1'
    }
    if(data.NonParticipationStatus === '4'){
      data.NonParticipationStatus_osslc_spring = '1'
    }
    if(data.eqao_acc_assistive_tech === '#') {
      data.eqao_acc_assistive_tech_1_chrome = '#';
      data.eqao_acc_assistive_tech_1_other = '#';
      data.eqao_acc_assistive_tech_2_kurz_dl = '#';
      data.eqao_acc_assistive_tech_2_kurz_ext = '#';
      data.eqao_acc_assistive_tech_2_nvda = '#';
      data.eqao_acc_assistive_tech_2_voiceover = '#';
      data.eqao_acc_assistive_tech_2_readaloud = '#';
      data.eqao_acc_assistive_tech_2_jaws = '#';
      data.eqao_acc_assistive_tech_2_read = '#';
      data.eqao_acc_assistive_tech_2_other = '#';
      data.eqao_acc_assistive_tech_3_chrome_2 = '#';
    }
    if(data.eqao_acc_assistive_tech === '2') {
      data.eqao_acc_assistive_tech_1_chrome = '1';
    }
    if(data.eqao_acc_assistive_tech === '1') {
      data.eqao_acc_assistive_tech_1_chrome = '#';
      data.eqao_acc_assistive_tech_1_other  = '1';
    }
    if(data.eqao_acc_assistive_tech_pj_reading === '2') {
      data.eqao_acc_assistive_tech_1_pj_reading_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_reading === '1') {
      data.eqao_acc_assistive_tech_1_pj_reading_other  = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_writing === '2') {
      data.eqao_acc_assistive_tech_1_pj_writing_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_writing === '1') {
      data.eqao_acc_assistive_tech_1_pj_writing_other  = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_mathematics === '2') {
      data.eqao_acc_assistive_tech_1_pj_mathematics_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_mathematics === '1') {
      data.eqao_acc_assistive_tech_1_pj_mathematics_other  = '1'
    }

    this.modifyStudentData(data, false);
    this.updateAccTechAndNPS(data);
    this.setupLinear(data);
    const apiPayload:any = this.getApiData(data, true);
    apiPayload.user_metas_import_disc_id = user_metas_import_disc_id;
    this.auth.apiPatch(
      this.routes.SCHOOL_ADMIN_STUDENT,
      uid,
      apiPayload,
      this.configureQueryParams()
    ).then(result=>{
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex];
      Object.keys(data).forEach(prop => {
        const name_space = (overlapVariables.indexOf(prop) > -1 || this.currentClassFilter == ClassFilterId.G9) ? '' : this.currentClassFilter == ClassFilterId.Primary?'_g3_':this.currentClassFilter == ClassFilterId.Junior?'_g6_':'_g10_';
        student[name_space+prop] = data[prop];
      });
      const course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
      const classroom = this.g9demoService.classrooms.find (cr => {
        const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
        const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
        const tw_isActive = (new Date(test_window.date_end) > new Date ())
        if(cr.class_code == data["eqao_g9_class_code_label"] && cr.course_type == course_type && tw_isActive){
          return cr;
        }
      })
      //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
      if(classroom){
        if(this.currentClassFilter == ClassFilterId.Primary){
          record._g3_eqao_pj_french = classroom.is_fi?'1':'#'
        }
        if(this.currentClassFilter == ClassFilterId.G9){
          record.eqao_g9_french = classroom.is_fi?'1':'#'
        }
      }
      student[conflictKey] = undefined;
      const namespace = this.currentClassFilter == ClassFilterId.Primary?'_g3_':this.currentClassFilter == ClassFilterId.Junior?'_g6_':this.currentClassFilter == ClassFilterId.G9?'':'_g10_';
      if(result.ErrorMessages.length > 0){
        student[namespace+"errMsg"] = JSON.stringify(result.ErrorMessages)
      }else{
        student[namespace+"errMsg"] = JSON.stringify([])
      }
      this.g9demoService.isStudentClassChanged = false; // Reset after saving
      this.pageModal.closeModal();
      this.accountDetailModalStart(student)
    })
  }
  getNumbersubmit(){
    const filteredStudents = this.accountsTable.getFilteredData();
    const submittedStudents = filteredStudents.filter(student => this.isSubmitted(student, '1'))
    return submittedStudents.length;
  }

  getProgress(){
    const total = this.accountsTable.numEntries();
    if(total != 0){
      return this.getNumbersubmit()*100/this.accountsTable.numEntries()
    }else{
      return 0;
    }  
  } 
  getMathClassWhenLabel(id){
    const mcw = STUDENT_SEMESTET_INDICATOR.list.find(e => e.id == id)
    if(mcw){
      return this.lang.tra(mcw.label);
    }else{
      return '';
    }
  }

  getLearningFormatLabel(acct){
    //const id :any =  this.currentClassFilter === ClassFilterId.G9?acct.eqao_learning_format:acct._g10_eqao_learning_format;
    //const Learning_Format:any  = this.currentClassFilter === ClassFilterId.G9?Learning_Format_G9:Learning_Format_OSSLT;
    let id, Learning_Format;
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        id = acct._g3_eqao_learning_format
        Learning_Format = Learning_Format_Primary
      break;
      case ClassFilterId.Junior:
        id = acct._g6_eqao_learning_format
        Learning_Format = Learning_Format_Junior
      break;  
      case ClassFilterId.G9:
        id = acct.eqao_learning_format
        Learning_Format = Learning_Format_G9
      break;
      case ClassFilterId.OSSLT:
        id = acct._g10_eqao_learning_format
        Learning_Format = Learning_Format_OSSLT
      break;
      default:
        id = acct._g3_eqao_learning_format
        Learning_Format = Learning_Format_Primary
      break; 
    }
    const lf = Learning_Format.list.find(e => e.id == id)
    if(lf){
      return this.lang.tra(lf.label);
    }else{
      return '';
    }
  }

  getTeacher(acct){
    //const classID = this.currentClassFilter == ClassFilterId.G9?acct.eqao_g9_class_code:acct._g10_eqao_g9_class_code
    let classID
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        classID = acct._g3_eqao_g9_class_code
      break  
      case ClassFilterId.Junior:
        classID = acct._g6_eqao_g9_class_code
      break
      case ClassFilterId.G9:
        classID = acct.eqao_g9_class_code
      break
      case ClassFilterId.OSSLT:
        classID = acct._g10_eqao_g9_class_code
      break
      default:
        classID = acct._g3_eqao_g9_class_code
      break
    }
    var classroom;
    if(classID){
      classroom = this.g9demoService.classrooms.find(theClass => theClass.id == classID )
      if(classroom.teacher_uid != undefined){
        const teacher = this.g9demoService.teachers.list.find(teacher => teacher.id == classroom.teacher_uid )
        if(teacher != undefined){
          return teacher.firstName+ ' '+teacher.lastName
        }
      }
    } 
    return ''; 
  }

  /**
   * Renders the value of the class code / group column in the students view
   * @param acct - student's data
   * @returns the class code of the class that matches the acct class code based on currentClassFilter
   */
  renderClassCodeAndGroupingLabel(acct: IStudentAccount) {
    const classroom = this.findStudentClass(acct)
    return classroom.class_code
    // if (this.currentClassFilter === ClassFilterId.G9) return acct.eqao_g9_class_code_label;
    // else return acct._g10_eqao_g9_class_code_label;
  }

  /**
   * Renders the value of homeroom for OSSLT students in the student view
   * @param acct - student's data
   * @returns {string}
   */
  renderHomeroom(acct: IStudentAccount):string {
    return acct._g10_Homeroom || ""
  }

  getImportProgress(){
    return this.currentImportNumber*100/this.totalImportNumber
  }

  /**
   * Finds a class for the student based on the current class filter
   */
  findStudentClass(student: IStudentAccount, classFilter: ClassFilterId = this.currentClassFilter) {
    return this.g9demoService.findStudentClass(student, classFilter);
  }

  filterStudentTestWindow(student:IStudentAccount){
    //const semester = this.g9DemoData.semesters.list.find(sm => sm.id === +theClass.semester);
    const theClass = this.findStudentClass(student)
    if(theClass){
      const semester = this.g9demoService.semesters.map[+theClass.semester]
      if(semester){
        return this.currentTestWindow.id === semester.testWindowId
      }
    }  
    return false;
  }

  filterStudentClassCodeLabel():string{
    if(this.currentClassFilter === ClassFilterId.Primary) return '_g3_eqao_g9_class_code_label';
    if(this.currentClassFilter === ClassFilterId.Junior) return '_g6_eqao_g9_class_code_label';
    if(this.currentClassFilter === ClassFilterId.G9) return 'eqao_g9_class_code_label';
    if(this.currentClassFilter === ClassFilterId.OSSLT) return '_g10_eqao_g9_class_code_label';
  }
  /*
  isSubmitted(acct: any){
    const slug = this.currentClassFilter === ClassFilterId.G9? 'G9_OPERATIONAL':'OSSLT_OPERATIONAL'
    const haveSubmitted = acct.testAttempts.find(ta => ta.slug === slug && (ta.submitted_test_session_id != undefined ||ta.submitted_test_session_id != null))
    if(haveSubmitted!=undefined){
      return '1'
    }
    else{
      return '#'
    }
  }
  */

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) > new Date ()
    }
    return false 
  }

  /**
   * Indicates whether current test window is registration unlocked
   * @returns {boolean} true - if current tw is not locked
   */
  isCurrentTestWindowRegUnlocked() {
    if(!this.currentTestWindow) return false
    
    return !this.currentTestWindow.reg_locked
  }

  isCreateEdit(){
    return (this.cModal().type === AccountModal.NEW_ACCOUNT || this.cModal().type === AccountModal.ACCOUNT_DETAILS)
  }

  selectedRecord(student){
    this.pageModal.closeModal();
    this.accountDetailModalStart(student)
  }

  public get FlaggedItemCase():typeof FlaggedItemCase {
    return FlaggedItemCase;
  }

  public isPimary(){
    return this.currentClassFilter == ClassFilterId.Primary
  }

  public isJunior(){
    return this.currentClassFilter == ClassFilterId.Junior
  }  
  haveSubmission(student){
    const ta_slug = this.currentClassFilter === ClassFilterId.Primary?'PRIMARY_OPERATIONAL':this.currentClassFilter === ClassFilterId.Junior?'JUNIOR_OPERATIONAL':this.currentClassFilter === ClassFilterId.G9?'G9_OPERATIONAL':'OSSLT_OPERATIONAL';
    if(student.testAttempts){
      const haveSubmitted = student.testAttempts.find(ta => ta.slug === ta_slug && +ta.is_submitted === 1 && ta.test_window_id == this.currentTestWindow.id)
      if(haveSubmitted){
        return '1'
      }
    }
    return '#';
  }

  /**
   * check if student have report for current selected test window
   * is_submitted_state stored the student ISR info
   * @param student
   * @returns boolean
   */
  haveReport(student){
    if(student.is_submitted_state?.test_window_ids){
      const studentISRTWs = student.is_submitted_state.test_window_ids.split(",")
      if(studentISRTWs.some(ISRTw => +this.currentTestWindow.id === +ISRTw)){
        return true;
      }
    }

    if (this.currentTestWindow.is_allow_results_tt === 1 || this.isSecreteUser) {
      // From TW 126 onward (after summer 2024), all registered students should have report except being temporarily pended
      if (this.currentClassFilter === ClassFilterId.OSSLT && +this.currentTestWindow.isr_version === +TW_ISR_VERSION.reportVersion2) {
        if (!student.twes_is_pended){
          return true;
        }
      }
    }
    return false
  }

  isSubmitted(student,val){
    if(val.includes('All')){
      return true
    }
    const haveSubmission = this.haveSubmission(student)
    if(val.includes('1') &&  haveSubmission =='1'){
      return true
    }
    if(val.includes('#') &&  haveSubmission == '#'){
      return true
    }
    return false
  }

  filterShowUploadScanBtn(student, val){
    if(val.includes('All')){
      return true
    }
    const haveUploadScanBtn = this.showUploadScanBtn(student)
    if(val.includes('1') &&  haveUploadScanBtn){
      return true
    }
    if(val.includes('0') &&  !haveUploadScanBtn){
      return true
    }
    return false
  }

  get isSasnLogin():boolean {
    return this.mySchool.isSASNLogin();
  }

  getSlugProps() {
    return {
      DOMAIN: getFrontendDomain(),
      LANG_CODE: this.lang.c(),
      SCHOOL_GROUP_ID: this.g9demoService.schoolData.group_id
    }
  }

  private promptForUnPaidStudent( currentClassFilter: string, classRoomID?: number, groupID?: number): boolean {
    let activeSession = null;
    if (classRoomID) {
      activeSession = this.g9demoService.schoolSessions.find(sc => sc.school_class_id === classRoomID && sc.is_closed === 0);
    }
    if (activeSession) {
      let classFilterSlug:string = null;
      if(currentClassFilter == 'Primary') {
        classFilterSlug = 'lbl_primary'
      }
      if(currentClassFilter == 'Junior') {
        classFilterSlug = 'lbl_junior'
      }
      if(currentClassFilter == 'G9') {
        classFilterSlug = 'txt_g9_math'
      }
      if(currentClassFilter == 'OSSLT') {
        classFilterSlug = 'lbl_osslt'
      }
      this.unPaidStudentCurrentFilter.emit(classFilterSlug);
      return true;
    }
    return false;
  }

  /**
   * @returns true - if current modal is supposed to have confirm alert
   */
  get modalFooterWithConfirmAlert() {
    const modalTypesWithoutConfirmAlert = [
      AccountModal.IMPORT_VALIDATION,
      AccountModal.IMPORT_PROGRESS,
      AccountModal.REMOVE_STUDENT,
      AccountModal.REMOVE_STUDENT_VALIDATION,
      AccountModal.ASSIGN_CLASSROOM,
      AccountModal.UPLOAD_SCAN,
    ];
    return !modalTypesWithoutConfirmAlert.includes(<AccountModal>this.cModal().type);
  }

  /**
   * @returns true - if current modal is supposed to have confirm button
   */
  get modalFooterWithConfirmButton() {
    const modalTypesWithConfirmButton = [
      AccountModal.IMPORT_VALIDATION,
      AccountModal.REMOVE_STUDENT_VALIDATION,
      AccountModal.UPLOAD_SCAN,
    ];
    return modalTypesWithConfirmButton.includes(<AccountModal>this.cModal().type);
  }

  /**
   * @example 'May 2023 To Jun 2023'
   */
  renderTestWindowForClassroom(classroom: IClassroom) {
    const tw = this.g9demoService.getTestWindowFromClassroom(classroom);
    if(tw) {
      const startDate = formatDate(new Date(tw.date_start), 'MMM yyyy', 'en_US')
      const endDate = formatDate(new Date(tw.date_end), 'MMM yyyy', 'en_US')
      return `${startDate} ${this.lang.tra("lbl_date_to")} ${endDate}`;
    }
  }

  showUploadScanColumn(){
    return this.g9demoService.twHaveScanResponseRequire(this.currentTestWindow)
  }

  showUploadScanBtn(student){
    const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
    if(student[scanResponseRequireName]){
      return true
    }
    return false;
  }

  /**
   * genereate the uploaded scans count translation string
   * @param student - selected student from the UI
   * @returns props object {session_slugs} where session_slugs is a string 
   */
  getNumFileUploadCountTransString(student){ 
    const props = {
      NUM_FILE_UPLOAD: 0
    }
    const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
    props["NUM_FILE_UPLOAD"] = student[scanResponseRequireName]?.upload_count || 0

    return this.lang.tra("sa_scan_upload_num_file_uploaded", this.lang.c(), props)
  }

  uploadScanBtnClick(student){
    const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
    if(!student[scanResponseRequireName]) return

    const scan_response_require_id = student[scanResponseRequireName].srr_id
    this.isLoadingUploadScanInfo = true
    this.uploadScanInfo = [];
    
    this.auth.apiGet(this.routes.SCHOOL_ADMIN_UPLOAD_SCAN, scan_response_require_id, this.configureQueryParams())
      .then(res =>{
        this.isLoadingUploadScanInfo = false
        this.uploadScanInfo = res
      }).catch(error =>{
        switch(error.message) {
          default:
            alert(error.message)
            break
        }
      })
    const config = {
      student
    };
    this.pageModal.newModal({
      type: AccountModal.UPLOAD_SCAN,
      config,
      finish: ()=> { 
        //clear all the class variable used by the modal.
        this.pageModal.closeModal();
      },
      cancel: ()=> { 
        //clear all the class variable used by the modal.
        this.isLoadingUploadScanInfo = false
        this.uploadScanInfo = []
        this.selecteduploadScanInfo = null
        this.uploadScanInfoGridApi = null
        this.uploadScanFileComment = null
        this.selectedUploadScanFile = null;
      }
    });
  }

  /**
   * comppute the props values {session_slugs} for the student store in the modal from tsss_slug
   * @returns props object {session_slugs} where session_slugs is a string 
   */
  getMissingScanSesssionSlugProps(){
    const student = this.cmc().student
    const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
    if(!student[scanResponseRequireName]) return null

    const missScanSlugs = student[scanResponseRequireName].tsss_slugs.sort((a, b) => a.slug.localeCompare(b.slug))

    const sessionSlugToLang = {
      'lang_session_a': 'session_a',
      'lang_session_b': 'session_b',
      'lang_session_c': 'session_c',
      'lang_session_d': 'session_d'
    }

    const session_slugs = missScanSlugs.map( missingSlug => this.lang.tra(sessionSlugToLang[missingSlug.slug])).join('<br>')
    return { session_slugs: session_slugs }
  }

  /**
   * Generate the translation string for "sa_upload_stu_sca_model_desc" with missingScanSesssionSlugProps.
   * Doing here instead in html because tra-md does not update its props value
   * @returns translation slug "sa_upload_stu_sca_model_desc" string with session_slugs string
   */
  getMissingScanSessions(){
   const missingScanSesssionSlugProps =  this.getMissingScanSesssionSlugProps();
   return missingScanSesssionSlugProps?this.lang.tra("sa_upload_stu_sca_model_desc", this.lang.c(), missingScanSesssionSlugProps ):''
  }

  uploadScanInfoRecordSelectionChanged(params){
    const selectedRow = this.uploadScanInfoGridOptions.api.getSelectedRows();
    if (selectedRow.length > 0){
      this.selecteduploadScanInfo = selectedRow[0]
    }
    else {
      this.selecteduploadScanInfo = null;
    }
  }

  uploadScanInfoGridReady(params){
    this.uploadScanInfoGridApi = params.api;
  } 

  onUploadScanFileSelected(event) {
    this.selectedUploadScanFile = event.target.files[0];
  }

  async uploadPDFScan(){
    
    if(!this.selectedUploadScanFile) return

    const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
    let student;
    if (this.pageModal.currentModal && this.pageModal.currentModal.config.student){
      student = this.pageModal.currentModal.config.student
    }
    let srr_id
    if(student && scanResponseRequireName && student[scanResponseRequireName]){
      srr_id = student[scanResponseRequireName].srr_id
    }
    if(! srr_id) return

    const purpose = 'admin-upload-scan'
    const schl_mident = this.g9demoService.schoolData.foreign_id
    const test_window_id = this.currentTestWindow.id
    let class_code = this.renderClassCodeAndGroupingLabel(student)
    class_code = class_code.replace(/[^a-zA-Z0-9]/g, '');  //just keep Alphanumeric value for class code
    const test_attempt_id = student[scanResponseRequireName].test_attempt_id
    const timestamp = moment().utc().format('YYYY-MM-DD_HH_mm_ss')
    const file_name = `schl_mid_${schl_mident}_tw_id_${test_window_id}_class_code_${class_code}_ta_id_${test_attempt_id}_${timestamp}.pdf`

    this.isUploadingScanFile = true

    const uploadPdfRes = await this.auth.uploadFile(this.selectedUploadScanFile, file_name, purpose)
    if (!uploadPdfRes.success || !uploadPdfRes.url){
      this.isUploadingScanFile = false
      throw new Error('Upload Failed')
    }

    let data = {
      srr_id,
      comment:this.uploadScanFileComment,
      file_path: this.schl_admin_upload_scan_folder + file_name
    }

    // Call the `create` function of the PDF service to upload the file
    this.auth.apiCreate( this.routes.SCHOOL_ADMIN_UPLOAD_SCAN, data, this.configureQueryParams())
    .then((res) => {
      const student = this.cmc().student
      const scanResponseRequireName = this.g9demoService.getStudentScanResponseRequireName(this.currentTestWindow)
      // Close model when all scan upload successfully and remove the upload scan button
      if(res.StuScanResponseUploadsInfo.length && !!res.StuScanResponseUploadsInfo[0].is_resolved){
        this.loginGuard.quickPopup(this.lang.tra("sa_upload_stu_sca_model_all_resolved", this.lang.c(),  {studentName: `${student.first_name} ${student.last_name}`   } ))
        //close the upload button
        student[scanResponseRequireName] = undefined
        //Close the Modal
        this.pageModal.closeModal();
        return
      }

      //Remove success upload(resolved) scan session 
      student[scanResponseRequireName].tsss_slugs = res.scan_response_require_tsss_slugs

      //update uploaded scan count
      student[scanResponseRequireName].upload_count = res.StuScanResponseUploadsInfo.length

      this.uploadScanInfo = res.StuScanResponseUploadsInfo
      if(this.uploadScanInfoGridApi){
        this.uploadScanInfoGridApi.refreshCells()
      }
    }).catch(error =>{
      switch(error.message) {
        case "PDF_PAGE_SIZE_TOO_LARGE":
          this.loginGuard.disabledPopup(this.lang.tra("sa_upload_stu_sca_model_pdf_page_size_exceed"))
          break
        case "ERR_LOAD_PDF_FILE":
          this.loginGuard.disabledPopup(this.lang.tra("Err_load_pdf_file"))
          break
        case "ERR_LOAD_PDF_FILE":
          alert(this.lang.tra("Err_load_pdf_file"))
          break
        default:
          alert(error.message)
          break
      }
    }).finally(() => {
      this.isUploadingScanFile = false 
      this.uploadScanFileComment = null;
      this.selectedUploadScanFile = null;
      if(this.uploadPDFScanInput){
        this.uploadPDFScanInput.nativeElement.value = null
      }
    });
  }

  downloadUploadScanInfoBtn(){
    const s3_file_link = this.selecteduploadScanInfo.s3_file_link
    const params = this.configureQueryParams();
    params.query["s3_file_link"] = s3_file_link
    this.auth.apiFind( this.routes.SCHOOL_ADMIN_UPLOAD_SCAN, params)
    .then((res) => {
      const file_name = s3_file_link.split('/').pop() || 'upload_scan';
      let a = document.createElement('a');
      a.href = res;
      a.download = file_name;
      a.dispatchEvent(new MouseEvent('click'));
    }).catch(error =>{
      switch(error.message) {
        default:
          alert(error.message)
          break
      }
    }).finally(() => {
    });
  }
}
