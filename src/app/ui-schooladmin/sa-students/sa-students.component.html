<div>
    <div *ngIf="!isPrivateSchool" class="filter-panels">
        <div>
            <filter-toggles 
                [state]="mySchool.getClassFilterToggles()"
                (id)="setClassFilter($event)"
            ></filter-toggles>
        </div> 
        <div>
            <sa-test-window-filter
              [currentClassFilter] = "currentClassFilter"
              (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>   
    </div>
    <div *ngIf="isPrivateSchool" class="filter-panels">
        <div>
            <filter-toggles 
                [state]="mySchool.getPrivateSchoolClassFilterToggles()"
                (id)="setClassFilter($event)"
            ></filter-toggles>
        </div> 
        <div>
            <sa-test-window-filter
            [currentClassFilter] = "currentClassFilter"
            (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>
    </div>
    <ng-container *ngIf="currentTestWindow">
        <div 
            *ngIf="isSignoffAvail() || totolErrorStudentRecords() > 0" 
            class="student-info-conf-notif-box" 
            [class.student-info-no-err]="totolErrorStudentRecords() == 0"
        >
            <div style = "display: table-cell; width:1em;">
                <img 
                    class="student-info-notif-icon" 
                    src="https://eqao.vretta.com/authoring/user_uploads/515512/authoring/red_exclamation/*************/red_exclamation.png" 
                />
            </div>        
            <div style = "display: table-cell; padding-left: 1em;">
                <div style = "font-weight:bold;" *ngIf = 'currentClassFilter === ClassFilterId.OSSLT'><tra slug='ab_review_osslt_student_data_head'></tra></div>
                <div style = "font-weight:bold;" *ngIf = 'currentClassFilter === ClassFilterId.G9' ><tra slug='ab_review_g9_student_data_head'></tra></div>
                <div><tra-md slug = 'ab_review_student_data_content'></tra-md></div>
                <div style = "font-weight:bold;"><p>
                    <tra slug = 'ab_flag_items'></tra> {{totolErrorStudentRecords()}} 
                    ( <tra slug='sm_invalid_information' style = "font-weight:bold;"></tra>: {{numberOfInvalude()}}
                    , <tra slug='sm_schoolboard_discrepancy' style = "font-weight:bold;"></tra> : {{numberOfDiscrepancy()}} )
                </p></div>
            </div>
            <div *ngIf="getStuAsmtInfoSignoffLog() && getStuAsmtInfoSignoffLog().length && totolErrorStudentRecords() == 0" style = "display: table-cell; width: 22em; padding-left: 2em;;">
                <strong><tra slug="lbl_prev_val"></tra></strong>
                <ul>
                    <li *ngFor="let signoffLog of getStuAsmtInfoSignoffLog()">
                        <strong>{{renderQuickDate(signoffLog.created_on)}}</strong>
                        <span *ngIf="signoffLog.is_revoked == 1">
                            <tra slug="lbl_unset_by"></tra> {{signoffLog.revoke_reason}} <tra slug="lbl_on"></tra> {{renderQuickDate(signoffLog.revoked_on)}}
                        </span>
                    </li>
                </ul>
            </div>
            <br>
        </div>
        <br>
        <div class="show-in-row" *ngIf="isSignoffAvail() || totolErrorStudentRecords() > 0">
            <button (click)="confirmDatamodify()" class="button is-small is-success"><span></span><tra slug="btn_all_student_modified"></tra></button>
            <button (click)="setShowFlaggedItems(FlaggedItemCase.ALL_FLAG)" *ngIf = 'showFlaggedItems == FlaggedItemCase.ALL' class="button is-small is-alert"><span></span><tra slug="btn_show_flagged_items"></tra></button>
            <button (click)="setShowFlaggedItems(FlaggedItemCase.IMPORT_FLAG)" *ngIf = 'showFlaggedItems == FlaggedItemCase.ALL_FLAG' class="button is-small is-yellow-flag"><span></span><tra slug="btn_show_import_flag"></tra></button>
            <button (click)="setShowFlaggedItems(FlaggedItemCase.ALL)" *ngIf = 'showFlaggedItems == FlaggedItemCase.IMPORT_FLAG' class="button is-small is-select"><span></span><tra slug="btn_show_all_items"></tra></button>  
            <!-- <div class="dont-print"><tra slug="adm_students_created_imported_prior"></tra><input type="date"[formControl]="students_before_date" max="2099-01-01" (change)="studentsPriorToDate()"></div> -->
            <br> 
        </div>
        <p *ngIf="currentClassFilter"><tra-md slug="sa_students_header"></tra-md></p>
        <p *ngIf="currentClassFilter && isPaymentModuleEnabled"><tra-md slug="sa_pay_student_intruction" [props]="getSlugProps()"></tra-md></p>
        <div *ngIf="!currentClassFilter">
            <tra-md slug="txt_msg_sq_stud_req_filter"></tra-md>
        </div>
        <div style = " display:table" *ngIf = 'currentClassFilter === ClassFilterId.OSSLT'>
            <div style = "display: table-cell; top:0px;">
                <p style = "font-weight:bold;"><tra slug='lb_schl_complete_osslt'></tra>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
            </div>   
            <div style = "display: table-cell; top:0px;"> 
                <progress id="file"  max="100" [value] = "getProgress()" style = "height: 2em;"></progress>
                <div style = "font-weight:50; top:0px">{{getNumbersubmit()}} / {{accountsTable.numEntries()}}</div>
            </div>    
        </div>
        <br>    
        <div *ngIf="currentClassFilter"> 
            <div #semesterFilterContainer>
                <sa-semester-filter 
                    *ngIf="isLoaded && isShowingSemesterFilter" 
                    (filter)="processFilterConditions($event)" 
                    (changeCategory)="changeCategory($event)" 
                    [baseFilter]="baseFilter"
                ></sa-semester-filter>
            </div>
            <!--
            <div class = 'notification is-warning'>
                <b><tra slug ='sa_std_msg'></tra></b>    
            </div>
            -->
            <!-- <div class="admin-info-conf-notif-box">
                <tra slug="lbl_registration_not_available"></tra>
            </div> -->
            <br>
            <div class="pre-table-strip">
                <div>
                    <!-- newAccountModalStart -->
                    
                    <button (click)="newAccountModalStart()" class="button is-small has-icon is-success" [disabled]="!isCurrentTestWindowActive()"><span class="icon"><i class="fas fa-plus-square"></i></span><tra slug="btn_creat_a_new_account"></tra></button>
                    <button (click)="accountDetailModalStart()" class="button is-small" [disabled] ="!isAnySelected" > <tra slug="sa_edit_student_info"></tra> </button>
                    <!-- <button (click)="accountDetailModalStart()" class="button is-small" [disabled] ="!isAnySelected || (!isCurrentTestWindowActive() && !selectedAreFlagged()" > <tra slug="sa_edit_student_info"></tra> </button> -->
                    <!-- <button *ngIf="false" (click)="assignSessionModalStart()" class="button is-small" [disabled] ="!isAnySelected" > <tra slug="sa_assign_to_session"></tra> </button> -->
                    <!-- <button *ngIf="false" (click)="unassignSessionModalStart()" class="button is-small" [disabled] ="!isAnySelected" > <tra slug="sa_remove_from_session"></tra> </button> -->
                    <button (click)="assignClassroomModalStart()" class="button is-small" [disabled] ="!isAnySelected || !currentClassFilter" > 
                        <tra *ngIf="currentClassFilter === ClassFilterId.OSSLT" slug="sa_assign_to_grouping"></tra>
                        <tra *ngIf="currentClassFilter !== ClassFilterId.OSSLT" slug="sa_assign_to_classroom"></tra> 
                   </button>
                    <!-- <button (click)="unassignClassroomModalStart()" class="button is-small" [disabled] ="!isAnySelected" > <tra slug="sa_remove_from_classroom"></tra> </button> -->
                    <!--  --> <button (click)="removeStudent()" class="button is-small" [disabled] ="!isAnySelected || !isCurrentTestWindowActive()" > <tra slug="sa-students-remove-student"></tra> </button>
                </div>
                <div>
                    <button class="button is-small has-icon" (click)="importStudentsModal()" [disabled]="!isCurrentTestWindowActive()||disableImportExport">
                        <span class="icon"><i class="fas fa-table"></i></span>
                        <span><tra slug="g9_import"></tra></span>
                    </button>
                    <button class="button is-small has-icon" (click)="exportStudentsModalStart()" [disabled]="disableImportExport">
                        <span class="icon"><i class="fas fa-table"></i></span>
                        <span><tra slug="g9_export"></tra></span>
                    </button>
                </div>
            </div>    
            <paginator 
                [model]="accountsTable.getPaginatorCtrl()" 
                [page]="accountsTable.getPage()" 
                [numEntries]="accountsTable.numEntries()" 
                (pageChange)="pageChanged()">
            </paginator>
            <div class="accounts-table-container">
                <table class="table is-hoverable ">
                    <tr class="header-row">
                        <th><table-row-selector [entry]="this" prop="isAllSelected" (toggle)="toggleSelectAll()"></table-row-selector> </th>
                        <th *ngIf="!isSasnLogin" class="flush"> <table-header id = "eqao_student_gov_id" caption = "sa_students_col_oen" [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--OEN-->
                        <th *ngIf="isSasnLogin" class="flush"> <table-header id = "SASN" caption = "lbl_sasn" [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--SASN-->
                        <th class="flush"> <table-header id = "first_name"  caption = "sa_students_col_fname" [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--First Name-->
                        <th class="flush"> <table-header id = "last_name"   caption = "sa_students_col_lname"  [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--Last Name-->
                        <th class="flush" *ngIf="currentClassFilter !== ClassFilterId.OSSLT"> <table-header id = "eqao_learning_format"  caption = "sa_students_col_learning_format"   [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th> <!--G9 Learning Format-->
                        <th class="flush" *ngIf="currentClassFilter === ClassFilterId.OSSLT"> <table-header id = "_g10_eqao_learning_format"  caption = "sa_students_col_learning_format"   [ctrl] = "accountsTable" [isSortEnabled]="true" [disableFilter]="true"></table-header> </th> <!--OSSLT Learning Format-->
                        <th class="flush" *ngIf="currentClassFilter === ClassFilterId.OSSLT"> <table-header id = "homeroom"  caption = "lbl_sdc_homeroom"   [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!-- Homeroom column for OSSLT students -->
                        <th class="flush" style="min-width:12em;"> <table-header id = "class_code_label" caption = "sa_classrooms_col_class_code"   [ctrl] = "accountsTable" [minWidth]="10" [isSortEnabled]="true"></table-header> </th> <!--Class Code Label-->
                        <!-- <th class="flush" *ngIf="currentClassFilter !== ClassFilterId.OSSLT"> 
                            <div class="space-between">
                                <table-header id = "semester_label"    caption = "sa_students_col_term_format"  [ctrl] = "accountsTable" [isSortEnabled]="true" [disableFilter]="true"></table-header>
                                <button  class="button is-small is-light btn-filter" (click)="toggleSemesterFilter()">
                                    <i class="fa fa-filter" aria-hidden="true"></i>
                                </button>
                            </div>
                        </th> -->
                        <th class="flush" *ngIf="currentClassFilter === ClassFilterId.G9"> <table-header id = "TermFormat"  caption = "sa_students_col_term_format"  [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th> <!--Term Format-->
                        <th class="flush"> <table-header id = "teacher" caption = "sa_students_col_teacher_name" [ctrl]="accountsTable" [isSortEnabled]="true"></table-header> </th><!--Teacher Name-->
                        <!-- <th class="flush"> <table-header id = "accommodation"   caption = "sa_students_col_acc"   [ctrl] = "accountsTable" [customFilter]="columnFilters.get('accommodation')" [disableFilter]="true" [isSortEnabled]="true"></table-header> </th> -->
                        <th class="flush"> <table-header id = "accommodation" caption = "sa_students_col_acc" [filterMode]="'LIST'" [list]="accommodationDropDown" [selectedVal]='selectedAccommodationVal' [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--Accomodation-->
                        <th class="flush" *ngIf="isPaymentModuleEnabled"> <table-header id = "isPaid" caption = "sa_private_student_paid" [filterMode]="'LIST'" [list]="paymentDropDown" [selectedVal]='selectedPaymentVal' [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th><!--Payment-->
                        <th *ngIf="IS_SESSION_STATUS_IMPLEMENTED" class="flush">
                            <div style="display:flex; flex-direction:row; justify-content: space-between;">
                                <table-header id="test_session" caption="sa_students_col_sess" [ctrl]="accountsTable" color="#f14668"></table-header><!--Test Sessions-->
                                <table-header id="test_window" caption="sa_students_col_wind"  [ctrl]="accountsTable" color="#1c48c2"></table-header><!--Test Window-->
                            </div>
                        </th>
                        <th class="flush" *ngIf="isShowingReports"> <table-header id = "is_submitted"  caption = "lbl_submission_status" [filterMode]="'LIST'" [list]="submissionDropDown" [selectedVal]='selectedSubmissionVal' [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th>
                        <th *ngIf="showSingleReportColumn()" class="flush"> <table-header id = "teacher"    caption = "btn_view_report"  [ctrl] = "accountsTable" [isSortEnabled]="false" [disableFilter]="true"></table-header> </th>
                        <th *ngIf="showUploadScanColumn()" class="flush"> <table-header id = "upload_scan"    caption = "sa_scan_upload_btn_column_header"  [filterMode]="'LIST'" [list]="scanUploadBtnDropDown" [selectedVal]='selectedscanUploadBtnVal' [ctrl] = "accountsTable" [isSortEnabled]="true"></table-header> </th> 
                    </tr>
            
                    <tr *ngFor="let acct of accountsTable.getCurrentPageData(); let index = index;">
                        <td><table-row-selector [entry]="acct" prop="__isSelected" (toggle)="checkSelection()"></table-row-selector> </td>
                        <td>
                            <div style="display: table">
                                <div *ngIf="!isSasnLogin" style="display: table-cell"><a class="modal-link" (click)="accountDetailModalStart(acct)">{{acct.eqao_student_gov_id}}</a></div>
                                <div *ngIf="isSasnLogin" style="display: table-cell"><a class="modal-link" (click)="accountDetailModalStart(acct)">{{acct.SASN}}</a></div>
                                <div style="display: table-cell; width:1.6em; height:1.6em;  float:right;" *ngIf = "isYellowAlertItem(acct)"><img  
                                    src="https://eqao.vretta.com/authoring/user_uploads/515512/authoring/yellow_exclamation/*************/yellow_exclamation.png" />
                                </div>
                                <div style="display: table-cell; width:1.6em; height:1.6em;  float:right;" *ngIf = "isRedAlertItem(acct) && !isYellowAlertItem(acct)"><img  
                                    src="https://eqao.vretta.com/authoring/user_uploads/515512/authoring/red_exclamation/*************/red_exclamation.png" />
                                </div>
                            </div>
                        </td>
                        <td ><a class="modal-link" (click)="accountDetailModalStart(acct)">{{acct.first_name}}</a></td>
                        <td ><a class="modal-link" (click)="accountDetailModalStart(acct)">{{acct.last_name}}</a></td>
                        <td>{{getLearningFormatLabel(acct)}}</td>
                        <!-- <td *ngIf="currentClassFilter !== ClassFilterId.OSSLT"><tra [slug]="renderCourse(acct.eqao_g9_course)"></tra></td> -->
                        <!-- <td>{{acct.eqao_g9_class_code_label}}</td> -->
                        <!-- <td>{{(currentClassFilter === ClassFilterId.G9?acct.eqao_g9_class_code_label:acct._g10_eqao_g9_class_code_label)}}</td> -->
                        <td *ngIf="currentClassFilter === ClassFilterId.OSSLT">{{renderHomeroom(acct)}}</td>
                        <td>{{renderClassCodeAndGroupingLabel(acct)}}</td>
                        <td *ngIf="currentClassFilter === ClassFilterId.G9">{{getMathClassWhenLabel(acct.TermFormat||acct.eqao_math_class_when)}}</td>
                        <!-- <td>{{acct.semester_label}}</td> -->
                        <td>{{getTeacher(acct)}}</td>
                        <td>
                            <i *ngIf="hasAccommodations(acct)" class="fa fa-check"></i>
                        </td>
                        <td *ngIf="IS_SESSION_STATUS_IMPLEMENTED">
                            <div class="session-listing" *ngFor="let testSessionId of getStudentTestSessions(acct)">
                                <code class="test-session">{{testSessionId}}</code>
                                <div class="test-session-info">
                                    <strong><tra-md [slug]="getTSCaption(testSessionId)"></tra-md></strong>
                                    <div class="session-time">{{getTSTime(testSessionId)}}</div>
                                </div>
                                <code class="test-window">{{getTSWindow(testSessionId)}}</code>
                            </div>
                        </td>
                        <td *ngIf="isPaymentModuleEnabled"><tra [slug]="renderIsPaid(acct)"></tra></td>
                        <td *ngIf="isShowingReports">({{haveSubmission(acct)}}) <tra [slug]="renderSubmStatus(haveSubmission(acct))"></tra> </td>
                        <td *ngIf="showSingleReportColumn() && ((currentClassFilter === ClassFilterId.OSSLT) || (currentClassFilter === ClassFilterId.Primary || currentClassFilter === ClassFilterId.Junior))">
                            <a 
                              *ngIf="haveReport(acct)"
                              class="button is-small" 
                              [routerLink]="viewStudentReport(acct)"
                              [queryParams]="getSingleStudentReportParam(acct)"
                             >
                              <tra slug="btn_view_report"></tra>
                            </a>
                        </td>
                        <td *ngIf="showUploadScanColumn()">
                            <div *ngIf="showUploadScanBtn(acct)">
                                <button (click)="uploadScanBtnClick(acct)"><tra slug="btn_upload"></tra>
                                </button>
                                <div style= "color: rgb(50, 140, 200);">
                                    <tra-md [slug] = "getNumFileUploadCountTransString(acct)"></tra-md>
                                </div>
                            </div>
                        </td> 
                    </tr>
                </table>
            
            </div>
        </div>
    </ng-container>
</div>
<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">

            <div *ngSwitchCase="AccountModal.NEW_ACCOUNT" style="width: 70em; min-height: 70vh;">
                <sa-modal-student-personal [classFilter]="currentClassFilter" [savePayload]="cmc()" [isCreatingNew]="true" [isPrivateSchool]="isPrivateSchool" saveProp="payload"></sa-modal-student-personal>
            </div>
            <div *ngSwitchCase="AccountModal.ACCOUNT_DETAILS" style="width: 70em; min-height: 70vh;">
                <sa-modal-student-personal [apiErrMsgToSlug] = 'apiErrMsgToSlug' [totalRecords] = 'this.accountsTable.numEntries()' [selectedStudentIndex] ='selectedStudentIndex'
                    (confirmStudentInformation) = 'confirmStudentInformation()' (preRecord)='preRecord()' (nextRecord)='nextRecord()' (applyBtnClicked)="clickApplyBtn($event)" (isAltReqTab)="onIsAltReqTab($event)"
                    [classFilter]="currentClassFilter" [studentRecords]="cmc().accounts" [savePayload]="cmc()" saveProp="payload" [isEditDisable]="(!isCurrentTestWindowActive() && !selectedIsFlagged) || !isCurrentTestWindowRegUnlocked()"
                    [isPrivateSchool]="isPrivateSchool" (selectedRecord) = "selectedRecord($event)" [isSecreteUser]="isSecreteUser">
                </sa-modal-student-personal>
            </div>
            <div *ngSwitchCase="AccountModal.ASSIGN_SESSION" class="simple-content-bounds">
                <p><tra slug="sa_asession_modal_title"></tra><!--The following students will be booked into the specified test session(s).--></p>
                <div style="margin-bottom:2em;">
                    <table>
                        <tr>
                            <th><tra slug="sa_asession_modal_id"></tra><!--ID--></th>
                            <th><tra slug="sa_asession_modal_fname"></tra><!--First Name--></th>
                            <th><tra slug="sa_asession_modal_lname"></tra><!--Last Name--></th>
                            <th><tra slug="sa_asession_modal_oen"></tra><!--OEN (Identifier)--></th>
                        </tr>
                        <tr *ngFor="let acct of cmc().accounts">
                            <td>{{acct.first_name}}</td>
                            <td>{{acct.last_name}}</td>
                            <td>{{acct.eqao_student_gov_id}}</td>
                        </tr>
                    </table>
                </div>
                <p><tra slug="sa_asession_modal_title2"></tra><!--Select the test session(s) in which the students should be booked into.--></p>
                <div>
                    <table>
                        <tr>
                            <th style="width:2em;"></th>
                            <th><tra slug="sa_asession_modal_id2"></tra><!--ID--></th>
                            <th><tra slug="sa_asession_modal_date"></tra><!--Date--></th>
                            <th><tra slug="sa_asession_modal_tw"></tra><!--T.W.--></th>
                        </tr>
                        <tr *ngFor="let session of availableSessions">
                            <td> <table-row-selector [entry]="cmc().testSessionsSelected" [prop]="session.id"></table-row-selector> </td>
                            <td> <code>{{session.id}}</code></td>
                            <td>{{renderTestSessionDate(session.dateTimeStart.toString())}}</td>
                            <td> <code class="test-window">{{session.testWindowId}}</code></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div *ngSwitchCase="AccountModal.UNASSIGN_SESSION" class="simple-content-bounds">
                <p><tra slug="sa_unasession_modal_title"></tra><!--The following students will be removed from the specified test session(s).--></p>
                <div style="margin-bottom:2em;">
                    <table>
                        <tr>
                            <th><tra slug="sa_unasession_modal_id"></tra><!--ID--></th>
                            <th><tra slug="sa_unasession_modal_fname"></tra><!--First Name--></th>
                            <th><tra slug="sa_unasession_modal_lname"></tra><!--Last Name--></th>
                            <th><tra slug="sa_unasession_modal_oen"></tra><!--OEN (Identifier)--></th>
                        </tr>
                        <tr *ngFor="let acct of cmc().accounts">
                            <td>{{acct.first_name}}</td>
                            <td>{{acct.last_name}}</td>
                            <td>{{acct.eqao_student_gov_id}}</td>
                        </tr>
                    </table>
                </div>
                <p><tra slug="sa_unasession_modal_title2"></tra><!--Select the test session(s) in which the students will be booked into.--></p>
                <div>
                    <table>
                        <tr>
                            <th style="width:2em;"></th>
                            <th><tra slug="sa_unasession_modal_id2"></tra><!--ID--></th>
                            <th><tra slug="sa_unasession_modal_testdate"></tra><!--Test / Date--></th>
                            <th><tra slug="sa_unasession_modal_tw"></tra><!--T.W.--></th>
                        </tr>
                        <tr *ngFor="let testSessionId of cmc().availableTestSessions">
                            <td> <table-row-selector [entry]="cmc().testSessionsSelected" [prop]="testSessionId"></table-row-selector> </td>
                            <td> <code>{{testSessionId}}</code></td>
                            <td>
                                <strong>{{getTSCaption(testSessionId)}}</strong>
                                <div>{{getTSTime(testSessionId)}}</div>
                            </td>
                            <td> <code class="test-window">{{getTSWindow(testSessionId)}}</code></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div *ngSwitchCase="AccountModal.ASSIGN_CLASSROOM" class="simple-content-bounds">
                <p><tra [slug]="getAssignModalTitle()"></tra></p>
                <div style="margin-bottom:2em;">
                    <table>
                        <tr>
                            <th><tra slug="sa_asession_modal_id"></tra><!--ID--></th>
                            <th><tra slug="sa_asession_modal_fname"></tra><!--First Name--></th>
                            <th><tra slug="sa_asession_modal_lname"></tra><!--Last Name--></th>
                            <th><tra slug="sa_asession_modal_oen"></tra><!--OEN (Identifier)--></th>
                        </tr>
                        <tr *ngFor="let acct of cmc().accounts">
                            <td>{{acct.id}}</td>
                            <td>{{acct.first_name}}</td>
                            <td>{{acct.last_name}}</td>
                            <td>{{acct.eqao_student_gov_id}}</td>
                        </tr>
                    </table>
                </div>
                <p><tra [slug]="getAssignModalTitle2()"></tra></p>
                <div>
                    <table>
                        <tr>
                            <th style="width:2em;"></th>
                            <th><tra slug="ID"></tra></th>
                            <th><tra slug="sa_classrooms_col_class_code"></tra></th>
                            <th><tra slug="sa_class_semester"></tra></th>
                            <th><tra slug="sa_lbl_purchase_invoice_admin_window"></tra></th>
                            <th><tra slug="sa_class_educator"></tra></th>
                        </tr>
                        <tr *ngFor="let classroom of classRoomsToAssign">
                            <td><table-row-selector [entry]="cmc().classroomSelected" [prop]="classroom.id" [singleSelect]="true"></table-row-selector> </td>
                            <td>{{classroom.id}}</td>
                            <td>{{classroom.class_code}}</td>
                            <td>{{classroom.semester}}</td>
                            <td>{{renderTestWindowForClassroom(classroom)}}</td>
                            <td>{{classroom.educator}}</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div *ngSwitchCase="AccountModal.EXPORT" class="simple-content-bounds student-export">
                <tc-table-common
                [name]="cmc().name"
                [hideTable]="cmc().hideTable"
                [columns]="cmc().columns"
                [exportModal]="true"
                (exportClick)="exportStudentsModal()"
                [additionalDownload]="additionalDownload"
                ></tc-table-common>
            </div>
            <div *ngSwitchCase="AccountModal.IMPORT" class="simple-content-bounds student-export">
                <tc-table-common-import
                [saveImportData]="cmc()"
                [hideTable]="cmc().hideTable"
                [columns]="cmc().columns"
                additionalInstrSlug="txt_import_instr_students"
                templateUrlSlug="sa_student_template_url"
                altTemplateUrlSlug="sa_osslt_student_template_url"
                [useAltUrl]="currentClassFilter === ClassFilterId.OSSLT"
                [isPJ]="currentClassFilter === ClassFilterId.Primary || currentClassFilter === ClassFilterId.Junior"
                pjTemplateUrlSlug="sa_pj_student_template_url"
                [additionalDownload]="additionalDownload"
                ></tc-table-common-import>
            </div>
            <div *ngSwitchCase="AccountModal.IMPORT_PROGRESS" class="simple-content-bounds student-export">
                <tra-md slug="sa_import_student_progress"></tra-md>
                <tra-md slug="sa_import_progress_warning_msg"></tra-md>
                <br>
                <progress id="file"  max="100" [value] = "getImportProgress()" style = "height: 2em; min-width: 100%"></progress>
                <div style = "font-weight:50; top:0px">{{currentImportNumber}} / {{totalImportNumber}}</div>
            </div>
            <div *ngSwitchCase="AccountModal.IMPORT_VALIDATION" class="simple-content-bounds import-validation">
                <div class="export-summary">        <!-- Total Import Student Number -->
                    <div class="export-summary-description">
                        <tra-md [slug]="getTotalImportStudentNumber()"></tra-md>
                    </div>
                </div>  
                <div class="export-summary">        <!-- Summary of Import Title -->
                    <div class="export-summary-description">
                        <tra-md slug="sa_student_summary_import"></tra-md>
                    </div>
                </div> 
                <!-- Successfully Added Student Number, Successfully Update Student Number, No Change Student Number -->
                <ul>
                    <li class="export-summary">        <!-- Successfully Added Student Number -->
                        <div class="export-summary-description">
                            <tra [slug]="getSuccessCreateStudentNumber()"></tra>
                        </div>
                    </li>  
                    <li class="export-summary">        <!-- Successfully Update Student Number -->
                        <div class="export-summary-description">
                            <tra [slug]="getSuccessUpdateStudentNumber()"></tra>
                        </div>
                    </li>  
                    <li class="export-summary">        <!-- No Change Student Number -->
                        <div class="export-summary-description">
                            <tra [slug]="getNoChangeStudentNumber()"></tra>
                        </div>
                    </li> 
                    <li class="export-summary">        <!-- Failed Student Number -->
                        <div class="export-summary-description">
                            <tra [slug]="getFailedImportStudentNumber()"></tra>
                        </div>
                    </li> 
                </ul>

                <div class="export-summary">        <!-- Summary Export Button Description -->
                    <div class="export-summary-description">
                        <tra-md slug="sa_summay_export" style="text-align: center; margin-top: 1.5em; margin-left: 4rem; margin-right: 4rem;"></tra-md>
                    </div>
                </div> 
                <div class="btn-export">
                    <button class="file-cta btn-export-summary" (click)="exportStudentsImportSummaryModal()">
                        <span class="icon"><i class="fas fa-table"></i></span>
                        <span style="margin-left: 0.3em;"><tra slug="g9_export"></tra></span>
                    </button>
                </div>    
                <div *ngIf="cmc().results.length"><tra-md slug="import_validation_header"></tra-md></div>  <!--Following students have not been imported due to validation errors:-->
                <table class="table is-width-auto" *ngIf="cmc().results.length">
                    <tr>
                        <th *ngFor="let col of cmc().columns">
                            {{col.caption}}
                        </th>
                    </tr>
                    <tr *ngFor="let row of cmc().results; index as i" [class.data]="!isOdd(i)" [class.errors]="isOdd(i)">
                        <ng-container *ngIf="!isOdd(i)">
                            <td *ngFor="let col of cmc().columns">
                                <div>{{ row[col.prop] }}</div>
                            </td>
                        </ng-container>
                        <ng-container *ngIf="isOdd(i)">
                            <td [attr.colspan]="cmc().columns.length">
                                <div *ngFor="let error of row" class="error-message">
                                    {{error.message}}                                    
                                </div>
                            </td>
                        </ng-container>
                    </tr>
                </table>
            </div>
            <div *ngSwitchCase="AccountModal.REMOVE_STUDENT" class="simple-content-bounds student-export">
                <tra-md slug="sa-students-remove-student-conf-message"></tra-md>
            </div>
            <div *ngSwitchCase="AccountModal.REMOVE_STUDENT_VALIDATION" class="simple-content-bounds student-export">
                <sa-modal-remove-student-summary [studentRemovalRecords]="studentRemovalRecords"></sa-modal-remove-student-summary>
            </div>
            <div *ngSwitchCase="AccountModal.UPLOAD_SCAN" class="simple-content-bounds upload-scan">
                <div class="box">
                    <div class="content">
                        <h4 class="title is-4"><tra slug="sa_upload_stu_sca_model_title"></tra></h4>
                        <div class="columns is-multiline">
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="sa_students_col_fname"></tra></label>
                                    <div class="control">{{cmc().student.first_name}}</div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="sa_students_col_lname"></tra></label>
                                    <div class="control">{{cmc().student.last_name}}</div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="sa_students_col_oen"></tra></label>
                                    <div class="control">{{cmc().student.eqao_student_gov_id}}</div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="sa_classrooms_col_class_code"></tra></label>
                                    <div class="control">{{renderClassCodeAndGroupingLabel(cmc().student)}}</div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="pj_title_missing_scans"></tra></label>
                                    <div class="control" [innerHTML]="getMissingScanSesssionSlugProps()?.session_slugs || ''"></div>
                                </div>
                            </div>
                            <div class="column is-half">
                                <div class="field">
                                    <label class="label"><tra slug="sa_students_col_teacher_name"></tra></label>
                                    <div class="control">{{getTeacher(cmc().student)}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="notification">
                    <tra-md [slug]="getMissingScanSessions()"></tra-md>
                </div>

                <div class="field">
                    <div class="file has-name is-fullwidth">
                        <label class="file-label">
                            <input #uploadPDFScanInput class="file-input" type="file" accept=".pdf" (change)="onUploadScanFileSelected($event)">
                            <span class="file-cta">
                                <span class="file-icon">
                                    <i class="fas fa-upload"></i>
                                </span>
                                <span class="file-label">
                                    Choose file...
                                </span>
                            </span>
                            <span class="file-name">
                                {{selectedUploadScanFile?.name || 'No file selected'}}
                            </span>
                        </label>
                    </div>
                </div>

                <div class="field">
                    <label class="label"><tra slug="sa_upload_stu_sca_model_upload_comment_lbl"></tra></label>
                    <div class="control">
                        <textarea class="textarea" [(ngModel)]="uploadScanFileComment"></textarea>
                    </div>
                </div>

                <div class="field">
                    <div class="control">
                        <button class="button is-success" (click)="uploadPDFScan()" [disabled]="!selectedUploadScanFile">
                            <tra slug="sa_upload_stu_sca_model_upload_btn"></tra>
                        </button>
                    </div>
                </div>

                <div class="box" style="margin-top: 2rem;">
                    <h5 class="title is-5"><tra slug="sa_upload_stu_sca_model_prev_upload_title"></tra></h5>
                    <div *ngIf="isLoadingUploadScanInfo">
                        <tra slug="loading_caption"></tra>
                    </div>
                    <div *ngIf="uploadScanInfo && uploadScanInfo.length" class="table-container">
                        <ag-grid-angular
                            class="ag-theme-alpine"
                            style="width: 100%; height: 300px;"
                            [rowData]="uploadScanInfo"
                            [gridOptions]="uploadScanInfoGridOptions"
                            (selectionChanged)="uploadScanInfoRecordSelectionChanged($event)"
                            [enableCellTextSelection]="true"
                            (gridReady)="uploadScanInfoGridReady($event)"
                        ></ag-grid-angular>
                        <div *ngIf="selecteduploadScanInfo" class="buttons" style="margin-top: 1rem;">
                            <button class="button is-info" (click)="downloadUploadScanInfoBtn()">
                                <tra slug="sa_upload_stu_sca_model_uploaded_scan_view"></tra>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- [export]="isExportModal(cModal().type)"  -->
        <modal-footer *ngIf="modalFooterWithConfirmAlert"
            [pageModal]="pageModal" 
            [isConfirmAlert]="isConfirmAlert()" 
            [confirmationMessage]="getConfirmMsg()"
            [isEditDisable] = "((!isCurrentTestWindowActive() && isCreateEdit() && !selectedIsFlagged && isSingleSelect) || !isCurrentTestWindowRegUnlocked()) && cModal().type != AccountModal.EXPORT"
        ></modal-footer>
        <modal-footer *ngIf="cModal().type == AccountModal.ASSIGN_CLASSROOM"
            [pageModal]="pageModal" 
            [isConfirmAlert]="isConfirmAlert()" 
            [isEditDisable]="!(isClassroomSelected)"
        ></modal-footer>
        <modal-footer *ngIf="modalFooterWithConfirmButton" 
            [pageModal]="pageModal" 
            [confirmButton]="true"
        ></modal-footer>
        <modal-footer *ngIf="cModal().type == AccountModal.REMOVE_STUDENT" 
            [pageModal]="pageModal" 
        ></modal-footer>
    </div>
</div>
