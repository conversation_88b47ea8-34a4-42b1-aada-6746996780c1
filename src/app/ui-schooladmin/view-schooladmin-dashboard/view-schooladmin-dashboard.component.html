<div class="specific-sa-dash-view">
  <div class="page-body view-sa-dash">
    <div>
      <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true" class="dont-print"></header>
      <!-- [hasSidebar]="true"  -->
      <div class="page-content">
        <div *ngIf="!isInited && !noRole" class="dont-print">
          <tra [slug]="getLoadingText()" style="padding-right: 0.5em;"></tra><i class="fa fa-spinner fa-spin"></i>
        </div>
        <div *ngIf="noRole" class="dont-print">
          <tra-md slug="You are not a school administrator. <br>Only a school administrator can access this page.">
          </tra-md>
        </div>
        <div *ngIf="isInited">
          <div *ngIf="false" class="notification is-danger dont-print">
            <tra-md slug="sa_dashboard_temp_warn"></tra-md>
          </div>
          <div *ngIf="!selectedView && !isSchoolAccessView" class="view-summary-container dont-print">
            <div>
              <!-- <p> -->
              <!-- <tra-md slug="sa_dashboard_welcome_msg"></tra-md> this is the eqao welcome msg -->
              <!-- Welcome to your school administrator account for the Foundation Skills Assessment. Everything you need to manage and monitor your school's completion of the assessment can be found in the tabs below.
                The first step in the process is to complete the Technical Readiness checklist found below to ensure your school is ready to run the assessment. The data found in the sections below has been loaded through the 1701 data load, where applicable (using the data pulled from the SLD system).
                Prior to accessing the secure assessment, teachers should provide students with the opportunity to try a sample assessment through the Safe Exam Browser (SEB) or Kiosk mode.
                Live webinars for teachers and educators are also available through this link: https://
                For resources and help, see the Session Information page below. If you require additional support, here's the HelpDesk Toll Free number: 1-888-444-4017
              </p> -->
            </div>
            <!-- CLOSER_LOOK_20210807 this block was commented out in the current branch but not in the incoming -->
            <div>
              <p>
                <tra-md *ngIf="!isPrivateSchool" slug="sa_dashboard_welcome_msg"></tra-md>
                <tra-md *ngIf="isPrivateSchool" slug="sa_dashboard_private"></tra-md>
              </p>
            </div>
            <div *ngIf="!isPrivateSchool">
              <filter-toggles
                [state]="mySchool.getClassFilterToggles()"
                (id)="setClassFilter($event)"
              ></filter-toggles>
            </div>
            <div *ngIf="isPrivateSchool">
              <filter-toggles
                [state]="mySchool.getPrivateSchoolClassFilterToggles()"
                (id)="setClassFilter($event)"
              ></filter-toggles>
            </div>
            
            <div class="container ui-container message-container">
              <div class="ui-container-header has-text-centered">
                <span class="icon-text">
                  <span class="icon">
                    <i class="fas fa-2x fa-envelope"></i>
                  </span>
                  <span class="title"><tra slug="lbl_message_centre"></tra></span>
                </span>
                <ntf-new-messages></ntf-new-messages>
              </div>
              <ntf-top-messages></ntf-top-messages>
            </div>
            
            <div class="view-summary">
              <div class="summary-card-info">
                <div class="summary-card-text">
                  <div class="summary-card-account-label">
                    <tra slug="sa_dashboard_school_admin"></tra>
                    <!-- <tra *ngIf="isFSA()" slug="sa_fsa_bcfsa"></tra> -->
                    <!-- <tra *ngIf="!isFSA()" slug="sa_grad_bcgrad"></tra> -->
                  </div>
                  <div class="dashboard-title">
                    {{schoolData.name}}({{ schoolData.foreign_id }})
                  </div>
                  <!-- <select class="list-of-schools">
                    <option *ngFor="let school of schools" [ngValue]="school">
                      {{school.name}}({{ schoolData.foreign_id }})
                    </option>
                  </select> -->
                </div>

                <div class="summary-card-button">
                  <button class="button is-primary" (click)="navigateToManageMySchool()">
                    <tra slug="sa_btn_manage_my_school"></tra>
                  </button>
                </div>
              </div>
  
              <div class="views-selector-container">
                <div *ngFor="let view of views" class="button views-selector">
                  <img [src]="getUrl(view.imgUrl)" [alt]="view.caption">
                  <div class="view-descriptors">
                    <ng-container *ngIf="isEQAO()">
                      <ng-container *ngIf="view.id !== 'tech_readiness'">
                        <a href="#" (click)="switchViews(getViewRoute(view.id)); $event.preventDefault(); " class="view-name">{{view.caption}}</a>
                      </ng-container>
                      <ng-container *ngIf="view.id == 'tech_readiness'">
                        <a href="#" (click)="switchViews(getViewRoute(view.id)); $event.preventDefault(); " class="view-name">{{getTechnicalReadinessCaption(view)}}</a>
                      </ng-container>
                      <div *ngIf="view.id !== 'tech_readiness'" class="view-long-desc">
                        <div *ngIf="isPrimary()">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="isJunior()">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="isG9()">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="isOsslt()">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="isNotPJAndNotG9AndNotOsslt()">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                      </div>
                      <div *ngIf="view.id === 'students' && view.hasIndicator && studentRecordHasError()" class="warning-indicator err-indicator">
                        <img style="width:1.6em; height:1.6em;"
                          src="https://eqao.vretta.com/authoring/user_uploads/515512/authoring/red_exclamation/1632147303557/red_exclamation.png" />
                      </div>
                      <div *ngIf="view.id === 'tech_readiness' && checklistLoaded">
                        <div *ngIf="techReadinessConfirmed" class="view-long-desc">
                          <tra-md slug="txt_tech_readi_done" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="!techReadinessConfirmed && !warnBoardTechReadiness" class="view-long-desc">
                          <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                        </div>
                        <div *ngIf="!techReadinessConfirmed && warnBoardTechReadiness" class="view-long-desc">
                          <tra slug="sa_tech_readiness_error_msg"></tra>
                        </div>
                        <div *ngIf="view.hasIndicator && !techReadinessConfirmed && !warnBoardTechReadiness"
                          class="issue-indicator">
                          <i class="fas fa-exclamation"></i>
                        </div>
                        <div *ngIf="view.hasIndicator && !techReadinessConfirmed && warnBoardTechReadiness"
                          class="warning-indicator">
                          <img style="width:1.6em; height:1.6em;"
                            src="https://bubo.vretta.com/vea/platform/vea-web-client/uploads/29926bc7d0084cf3af89d5085b43c57b/icons8-warning-96.png" />
                        </div>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="isBced()">
                      <div class="name-container">
                        <a *ngIf="!view.disabled" (click)="switchViews(getViewRoute(view.id))" class="view-name">
                          <tra [slug]="view.caption"></tra>
                        </a>
                        <a *ngIf="view.disabled" (click)="pageUnavailable($event)" class="view-name">{{view.caption}}</a>
                        <img *ngIf="view.secondaryImgUrl" [src]="getUrl(view.secondaryImgUrl)">
                      </div>
                      <div class="view-long-desc">
                        <tra-md [slug]="view.description" [props]="getMessageProps(view.id)"></tra-md>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </div>
            </div>
          </div>
  
          <div *ngIf="selectedView" class="view-container">
            <div (click)="navigateToSessions()" *ngIf="getUnpaidClassFilters()" style="background-color: rgb(242, 154, 154); text-align: center; padding-top: 0.8em; padding-bottom: 0.8em; cursor: pointer;">
              <div><tra-md slug="sa_txt_reminder_payment_required"></tra-md></div>
              <div>
                {{ getUnpaidClassFilters() }}
              </div>
            </div>
            <menu-bar class="dont-print"
              [menuTabs]="views"
              [tabIdInit]="selectedView"
              (change)="selectView($event)"
            ></menu-bar>
            <div [ngSwitch]="selectedView">
              <!--
              <div *ngSwitchCase="BCFSASchoolAdminView.BC_FSA_STUDENTS">
                <sa-accounts [schoolData]="schoolData"></sa-accounts>
              </div>
              <div *ngSwitchCase="BCFSASchoolAdminView.BC_FSA_ASSESSMENTS">
                <sa-assessments [breadcrumbProxy]="breadcrumbProxy"></sa-assessments>
              </div>
              <div *ngSwitchCase="BCFSASchoolAdminView.BC_FSA_TECH_READI">
                <sa-technical-guides [isThisGrad]='false' [isThisFSA]='true' [breadcrumbProxy]="breadcrumbProxy">
                </sa-technical-guides>
              </div>
              <div *ngSwitchCase="BCFSASchoolAdminView.BC_FSA_ASSESSMENT_MATERIALS">
                <sa-assessment-materials [breadcrumbProxy]="breadcrumbProxy"></sa-assessment-materials>
              </div>
  
              <div *ngSwitchCase="BCGradSchoolAdminView.BC_GRAD_TECH_READI">
                <sa-technical-guides [isThisGrad]='true' [isThisFSA]='false' [breadcrumbProxy]="breadcrumbProxy">
                </sa-technical-guides>
              </div>
              <div *ngSwitchCase="BCGradSchoolAdminView.BC_GRAD_SESSION_INFO">
                <sa-session-info [breadcrumbProxy]="breadcrumbProxy"></sa-session-info>
              </div>
              <div *ngSwitchCase="BCGradSchoolAdminView.BC_GRAD_STUDENTS">
                <sa-accounts-student></sa-accounts-student>
              </div>
              -->
              <div *ngSwitchCase="SchoolAdminView.TECH_READI">
                <sa-tech-readiness (onSetClassFilter)="setClassFilter($event)" (readinessStatus)="setTechReadiness($event)"></sa-tech-readiness>
              </div>
              <div *ngSwitchCase="SchoolAdminView.CLASSROOMS">
                <sa-classrooms (onSetClassFilter)="setClassFilter($event)"></sa-classrooms>
              </div>
              <div *ngSwitchCase="SchoolAdminView.TEACHERS">
                <sa-teacher-accounts (onSetClassFilter)="setClassFilter($event)"></sa-teacher-accounts>
              </div>
              <div *ngSwitchCase="SchoolAdminView.STUDENTS">
                <sa-students (onSetClassFilter)="setClassFilter($event)" (unPaidStudentCurrentFilter)="addToUnpaidClassFilters($event)"></sa-students>
              </div>
              <div *ngSwitchCase="SchoolAdminView.SESSIONS">
                <sa-assessment-sessions (onSetClassFilter)="setClassFilter($event)" (unPaidStudentCurrentFilter)="addToUnpaidClassFilters($event)" (unPaidStudentCurrentFilterCancelled)="removeFromUnpaidClassFilters($event)"></sa-assessment-sessions>
              </div>
              <div *ngSwitchCase="SchoolAdminView.QUESTIONNAIRE">
                <div *ngIf="!haveQuestionnaireSession">
                  <tra-md slug="sa_questionnaire_header"></tra-md>
                </div>
                <div *ngIf="haveQuestionnaireSession">
                  <tra-md slug="sa_questionnaire_header_2"></tra-md>
                  <span *ngIf="reset_flag" style="margin-top:1em ;">
                    <tra-md slug ="teacher_questionnaire_complete" [props]="{DUE_DATE: getQuestionnaireDueDate()}"></tra-md>
                  </span>
                  <div style="margin-top: 1em;">
                    <button class="button is-small is-info" (click)="onClickQuestionnaire()"><tra slug="lbl_principal_questionnaire"></tra></button>
                  </div>
                </div>  
              </div>
              <div *ngSwitchCase="SchoolAdminView.REPORTS">
                <isr-reports (onSetClassFilter)="setClassFilter($event)"></isr-reports>
              </div>
              <div *ngSwitchCase="SchoolAdminView.REQUESTS">
                <sa-requests (onSetClassFilter)="setClassFilter($event)" (unPaidStudentCurrentFilter)="addToUnpaidClassFilters($event)"></sa-requests>
              </div>
              <div *ngSwitchCase="SchoolAdminView.PURCHASES">
                <sa-purchases *ngIf="isPaymentModuleEnabled" (onSetClassFilter)="setClassFilter($event)"></sa-purchases>
              </div>
            </div>
          </div>
          <div *ngIf="isSchoolAccessView" class="view-container">
            <view-manage-my-school></view-manage-my-school>
          </div>  
        </div>
      </div>
    </div>
    <footer [hasLinks]="true"></footer>
    </div>
      <div class="custom-modal" *ngIf="cModal()">
        <div class="modal-contents">
          <div [ngSwitch]="cModal().type">
            <div *ngSwitchCase="SchoolAdminModal.VIEW_PAYMENT_AGREEMENT" style="width: 65em;">
              <sa-modal-payment-agreement-review
                (agreementConfirmed)="confirmPaymentAgreement($event)"
                [isReview]="false"
              ></sa-modal-payment-agreement-review>
            </div>
        </div>
      </div>
    </div>
</div>