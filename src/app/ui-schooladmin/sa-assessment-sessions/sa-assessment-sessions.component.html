<div class="assessment-sessions-view">
    <div *ngIf="!isPrivateSchool" class="filter-panels">
        <div>
            <filter-toggles 
                [state]="mySchool.getClassFilterToggles()"
                (id)="setClassFilter($event)"
            ></filter-toggles>
        </div>
        <div>
            <sa-test-window-filter
              [currentClassFilter] = "currentClassFilter"
              (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>   
    </div>
    <div *ngIf="isPrivateSchool" class="filter-panels">
        <div>
            <filter-toggles 
                [state]="mySchool.getPrivateSchoolClassFilterToggles()"
                (id)="setClassFilter($event)"
            ></filter-toggles>
        </div>
        <div>
            <sa-test-window-filter
            [currentClassFilter] = "currentClassFilter"
            (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>
    </div>

    <div *ngIf="!currentClassFilter">
        <tra-md slug="txt_msg_sessions_req_filter"></tra-md>
    </div>

    <div *ngIf="currentClassFilter && currentTestWindow">
        <p><tra-md slug="sa_assessments_header"></tra-md></p>
        <p *ngIf="isPaymentModuleEnabled"><tra-md slug="sa_assessments_payment_header"></tra-md></p>
        <p *ngIf="isPaymentModuleEnabled">
            <tra slug="sa_assessments_payment_refund_policy_link"></tra><a (click)="showRefundPolicy()"><tra slug="lbl_here"></tra>.</a>
        </p>
        <br>
        <div class="pre-table-strip">
            <div>
                <button  class="button is-small has-icon is-success"  (click)="newSessionModalStart()" [disabled]="!isCurrentTestWindowActive()">
                    <span class="icon"><i class="fas fa-plus-square"></i></span>
                    <span><tra slug="sa_sessions_new"></tra><!--New Session--></span>
                </button>
                <button class="button is-small has-icon" [disabled]="!isAnySelected || !currentClassFilter || isShowingSubmitted || !isCurrentTestWindowActive()" (click)="editSessionModalStart()">
                    <span class="icon"><i class="fas fa-edit"></i></span>
                    <span><tra slug="sa_sessions_edit"></tra><!--Edit Selected--></span>
                </button>
                <button class="button is-small has-icon" [disabled]="!isAnySelected || !allowCancelSession()" (click)="cancelSelectedSessions()">
                    <span class="icon"><i class="fas fa-trash"></i></span>
                    <span><tra slug="btn_cancell_session"></tra><!--Cancel Selected--></span>
                </button>
                <button class="button is-small has-icon" (click)="showSubmittedSessions()">
                    <span class="icon"><i class="fas fa-archive"></i></span>
                    <tra *ngIf="isShowingSubmitted" slug="btn_progress_pend_sessions"></tra>
                    <tra *ngIf="!isShowingSubmitted" slug="btn_submitted_sessions"></tra>
                </button>
                <button *ngIf="isPaymentModuleEnabled" class="button is-small has-icon" [disabled]="!isPaymentAvailable()" (click)="showSessionPaymentModal()">
                    <span class="icon" style="margin-right: 0.3em"><i class="fas fa-archive"></i></span>
                    <tra slug="btn_sa_pay_sessions"></tra>
                </button>
            </div>
            <div>
                <button class="button is-small has-icon" (click)="loginGuard.disabledPopup()">
                    <span class="icon"><i class="fas fa-table"></i></span>
                    <span><tra slug="sa_sessions_import"></tra></span><!--Import-->
                </button>
                <button class="button is-small has-icon" (click)="loginGuard.disabledPopup()">
                    <span class="icon"><i class="fas fa-table"></i></span>
                    <span><tra slug="sa_sessions_export"></tra></span><!--Export-->
                </button>
            </div>
        </div>
        <paginator *ngIf="isLoaded" [model]="sessionsTable.getPaginatorCtrl()" [page]="sessionsTable.getPage()" [numEntries]="sessionsTable.numEntries()"></paginator>
        <table *ngIf="isLoaded" style="overflow-x: scroll;" class="sessions-table table is-hoverable">
            <tr>
                <th> <table-row-selector [entry]="this" prop="isAllSelected" (toggle)="toggleSelectAll()"></table-row-selector> </th>
                <th *ngIf="!isShowingSubmitted" class="flush"> <table-header id = "status"       [caption] = "columnLabels.status"         [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header><!--Status--> </th>
                <!-- <th *ngIf="!currentClassFilter" class="flush"> <table-header id = "slug" [caption] = "columnLabels.slug"      [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header> </th>--><!--ASSESSMENT-TYPE-->
                <th class="flush" *ngIf="isPaymentModuleEnabled"> <table-header id = "isPaid"  [caption] = "columnLabels.isPaid"   [ctrl] = "sessionsTable" [isSortEnabled]="false"></table-header><!--Paid?--> </th>
                <th class="flush" *ngIf="isPaymentModuleEnabled"> <table-header id = "paymentStatus"  [caption] = "columnLabels.paymentStatus"   [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header><!--Payment Status--> </th>
                <th class="flush"> <table-header id = "invigilator"  [caption] = "columnLabels.invigilators"   [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header><!--Invigilator(s)--> </th>
                <th class="flush"> <table-header id = "classCode" [caption] = "columnLabels.classroom"      [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header><!--Classroom--> </th>
                <th class="flush"> <table-header id = "students"     [caption] = "columnLabels.students"       [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header><!--Students--> </th>
                <th class="flush"> <table-header id = "type" [caption] = "columnLabels.type"      [ctrl] = "sessionsTable"  [isSortEnabled]="true"></table-header>
                <th *ngIf="!isShowingSubmitted" class="flush" style="min-width: 15em;"> <table-header id = "times"        [caption] = "columnLabels.times"          [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header><!--Times--> </th>
                <th *ngIf="isShowingSubmitted" class="flush"> <table-header id = "submittedOn"        [caption] = "columnLabels.submittedOn"          [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header><!--Times--> </th>
                <th class="flush" *ngIf="isG9OrOsslt()"> <table-header id = "submissions"        [caption] = "columnLabels.submissions"          [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header></th>
                <th class="flush" *ngIf="isPrimaryOrJunior()"> <table-header id = "submissions_lang"        [caption] = "columnLabels.submissions_lang"          [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header></th>
                <th class="flush" *ngIf="isPrimaryOrJunior()"> <table-header id = "submissions_math"        [caption] = "columnLabels.submissions_math"          [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header></th>
                <th *ngIf="!isShowingSubmitted" class="flush"> <table-header id = "invigilate" [caption] = "columnLabels.invigilate" [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header></th>
            </tr>
            <tr  *ngFor="let session of sessionsTable.getCurrentPageData();">
                <td>
                    <table-row-selector [entry]="session" prop="__isSelected" (toggle)="checkSelection()"></table-row-selector>
                </td>
                <td *ngIf="!isShowingSubmitted" class="status">
                    <div class="content">
                        <span class="fa fa-circle" [ngClass]="session.status">
                        </span>{{columnStatusLabel[session.status]}}
                    </div>
                </td>
                <!-- <td *ngIf="!currentClassFilter"> {{ getSessionSlug(session.slug) }}</td> -->
                <td *ngIf="isPaymentModuleEnabled">
                    <div class="space-between">
                        <div>{{ session.studentsPaid }}/{{session.students}}</div>
                        <!-- <div>{{session.isPaid}}</div> -->
                    </div>
                </td>
                <td *ngIf="isPaymentModuleEnabled">
                    <div>
                        <span *ngIf="session.paymentStatus == 'lbl_required'" class="fa fa-circle" style="color: red; font-size: 9px; margin-right: 7px;"></span>
                        <span><tra [slug]="session.paymentStatus"></tra></span>
                    </div>
                </td>
                <td>
                    <div class="space-between">
                        <div>{{session.invigilator}}</div>
                        <!-- <div>
                            <a class="button is-small" (click)="openInvigilation(session)"><tra slug="lbl_invigilate_myself"></tra></a>
                        </div> -->
                    </div>
                </td>
                <td> {{session.classCode}} </td>
                <td class="studentId"> 
                    <!-- <a (click)="openStudentCountsModal(session.classroom_id)">{{getTotalStudent(session)}}</a> -->
                    <!-- <a (click)="openStudentCountsModal(session.classroom_id)">{{session.students}}</a> -->
                    {{session.students}}
                </td>
                <td> {{ getAssessmentType(session.slug) }}</td>
                <td *ngIf="!isShowingSubmitted">    
                    <div *ngIf="session.session_b && isG9OrOsslt()"> 
                        <div><span style="margin-right: 4px;">{{ session.session_a.slug }}:</span><span>{{ session.session_a.datetime_start }}</span></div>
                        <div><span style="margin-right: 4px;">{{ session.session_b.slug }}:</span><span>{{ session.session_b.datetime_start }}</span></div>
                    </div>
                    <div *ngIf="session.session_b && isPrimaryOrJunior()"> 
                        <ng-container *ngIf="isLanguageVisibleForFIClass(session.classroom_id) || isJunior()">
                            <div>
                                <span style="margin-right: 4px;">{{ session.session_a.slug }}:</span>
                                <span *ngIf="session.session_a.datetime_start">{{ session.session_a.datetime_start }}</span>
                                <span *ngIf="!session.session_a.datetime_start"><tra slug="lbl_not_scheduled"></tra></span>
                            </div>
                            <div *ngIf="session.session_a.datetime_end">
                                <span style="margin-right: 4px;">{{ session.session_a.slug }}:</span>
                                <span>{{ session.session_a.datetime_end }}</span>
                            </div>
                        </ng-container>
                        <div>
                            <span style="margin-right: 4px;">{{ session.session_b.slug }}:</span>
                            <span *ngIf="session.session_b.datetime_start">{{ session.session_b.datetime_start }}</span>
                            <span *ngIf="!session.session_b.datetime_start"><tra slug="lbl_not_scheduled"></tra></span>
                        </div>
                        <div *ngIf="session.session_b.datetime_end">
                            <span style="margin-right: 4px;">{{ session.session_b.slug }}:</span>
                            <span>{{ session.session_b.datetime_end }}</span>
                        </div>
                    </div>
                    <div *ngIf="!session.session_b">
                        <div>{{ session.session_a.datetime_start }}</div> 
                    </div>
                </td>
                <td *ngIf="isShowingSubmitted">
                    <div>{{ session.closed_on }}</div>
                </td>
                <td *ngIf="isG9OrOsslt()"> 
                    <div>{{ session.submissions }}</div>
                </td>
                <td *ngIf="isPrimaryOrJunior()"> 
                    <div>{{ session.submissions }}</div>
                </td>
                <td *ngIf="isPrimaryOrJunior()"> 
                    <div>{{ session.submissions_1 }}</div>
                </td>
                <td *ngIf="!isShowingSubmitted"> 
                    <button style="margin-top: 0.5em;" class="button is-small is-success" (click)="onClickInvigilate(session)"><tra slug="lbl_school_admin_invigilate"></tra></button>
                </td>
            </tr>
        </table>
    </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
        <div>
            <div *ngSwitchCase="SessionModal.NEW_SESSION">
                <sa-modal-session [savePayload]="cmc()" saveProp="payload" (isDateValid)="setDateValidity($event)"></sa-modal-session>
            </div>
            <div *ngSwitchCase="SessionModal.EDIT_SESSION">
                <sa-modal-session [isEditing]="true" [isActive]="isActive" [isPartialActive]="isPartialActive"  (isDateValid)="setDateValidity($event)" [sessions]="cmc().sessions" [savePayload]="cmc()" saveProp="payload"></sa-modal-session>
            </div>
            <div *ngSwitchCase="SessionModal.STUDENTS_SESSION">
                <sa-modal-student-count [studentList]="studentList"></sa-modal-student-count>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_OVERVIEW">
                <sa-modal-session-payment-overview [pageModal]="pageModal" [sessionList]="getSelectedSessions(true)" [schoolName]="schoolName" [assessmentName]="getAssessmentType(getSelectedSessions(true)[0].slug)" [administrationWindow]="getTestWindowViewText(currentTestWindow)" [currentClassFilter]="currentClassFilter" [currentTestWindow]="currentTestWindow" (onStripePayment)="onStripePayment($event)" (onAlternativePayment)="onAlternativePayment($event)"></sa-modal-session-payment-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_STRIPE_OVERVIEW">
                <sa-modal-session-payment-stripe-overview 
                    [sessionList]="getSelectedSessions(true)" 
                    [schoolName]="schoolName" 
                    [assessmentName]="getAssessmentType(getSelectedSessions(true)[0].slug)" 
                    [administrationWindow]="getTestWindowViewText(currentTestWindow)" 
                    [currentClassFilter]="currentClassFilter" 
                    [totalCost]="cmc().totalCost" 
                    (onBack)="showSessionPaymentModal()">
                </sa-modal-session-payment-stripe-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_ALTERNATIVE_OVERVIEW">
                <sa-modal-session-payment-alternative-overview 
                    [sessionList]="getSelectedSessions(true)" 
                    [schoolName]="schoolName" 
                    [assessmentName]="getAssessmentType(getSelectedSessions(true)[0].slug)" 
                    [administrationWindow]="getTestWindowViewText(currentTestWindow)" 
                    [currentClassFilter]="currentClassFilter"
                    [totalCost]="cmc().totalCost"
                    (onBack)="showSessionPaymentModal()"
                    (onCompletePaymentAlt)="onCompletePaymentAlt($event)">
                </sa-modal-session-payment-alternative-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_ALTERNATIVE_WARNING">
                <sa-modal-session-payment-alternative-warning [alternative_days_before_session]="alternative_days_before_session" (onBack)="showSessionPaymentModal()"></sa-modal-session-payment-alternative-warning>
            </div>
            <div *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY">
                <sa-modal-purchase-refund-overview></sa-modal-purchase-refund-overview>
            </div>
        </div>
        <modal-footer *ngSwitchCase="SessionModal.NEW_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="SessionModal.EDIT_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="SessionModal.STUDENTS_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY" 
            class="modal-refund-policy" 
            [pageModal]="pageModal" 
            [confirmButton]="false" 
            [closeMessage]="'btn_close'">
        </modal-footer>
    </div>
</div>