import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { coinFlip } from '../../ui-testadmin/demo-data.service';
import * as moment from 'moment-timezone';
import { LangService } from "../../core/lang.service";
import {IClassroom, ISession, IStudentAccount, PurchaseModal, SessionModal, SessionStatus} from "../data/types";
import { MemDataPaginated } from "../../ui-partial/paginator/helpers/mem-data-paginated";
import { LoginGuardService } from '../../api/login-guard.service';
import { ListSelectService } from '../../ui-partial/list-select.service';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { G9DemoDataService, G9_SLUG_TO_CAPTION } from '../g9-demo-data.service';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import {FilterSettingMode} from "../../ui-partial/capture-filter-range/capture-filter-range.component";
import { ASSESSMENT } from '../../ui-teacher/data/types';
import { faWindowRestore } from '@fortawesome/free-solid-svg-icons';
import { data } from 'jquery';
import { ActivatedRoute, Router } from '@angular/router';
import { formatDate } from '@angular/common';
import { etz, tzLangAbbrs } from 'src/app/core/util/moment';
export enum SCHEDULER {
  NOW = 'NOW',
  LATER = 'LATER'
}
const STANDARD_TIMEZONE = 'America/Toronto';
@Component({
  selector: 'sa-assessment-sessions',
  templateUrl: './sa-assessment-sessions.component.html',
  styleUrls: ['./sa-assessment-sessions.component.scss']
})
export class SaAssessmentSessionsComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();
  @Output() unPaidStudentCurrentFilter = new EventEmitter<string>();
  @Output() unPaidStudentCurrentFilterCancelled = new EventEmitter<string>();

  isActive: boolean;
  isPartialActive:boolean;
  selectedError: boolean;
  schedulerErrors: string;
  completedSessions: ISession[] = [];
  nonCompletedSessions: ISession[] = [];
  isShowingSubmitted: boolean = false;
  isPaymentModuleEnabled: boolean = false;

  constructor(
    public lang: LangService,
    public loginGuard: LoginGuardService,
    private listSelectService: ListSelectService,
    private pageModalService: PageModalService,
    public mySchool: MySchoolService,
    private auth: AuthService,
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
    private router: Router,
    private route: ActivatedRoute,
  ) { }

  public sessionsTable: MemDataPaginated<ISession>;
  public studentList
  public sessions: ISession[] = [];
  public isAllSelected = false;
  public isAnySelected = false;
  public isLoaded: boolean;
  public isInited: boolean;
  public columnLabels;
  isPrivateSchool:boolean = false;
  public columnStatusLabel;
  private initalSessionStatus = SessionStatus.PENDING;
  currentClassFilter: ClassFilterId;
  pageModal: PageModalController;
  SessionModal = SessionModal;
  PurchaseModal = PurchaseModal;
  SCHEUDLER = SCHEDULER;
  currentTestWindow;
  isLangScheduled: boolean;
  isMathScheduled: boolean;
  schoolName: string = this.g9DemoData.schoolData.name;
  paymentPolicy: any;
  alternative_days_before_session: number;
  unpaidPrimaryClassFilterCount: number = 0;
  unpaidJuniorClassFilterCount: number = 0;
  unpaidG9ClassFilterCount: number = 0;
  unpaidOssltClassFilterCount: number = 0;

  ngOnInit() { 
    const schoolLang = this.g9DemoData.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);
    this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    this.initRouteView();
    // this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow())
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.getPaymentPolicy();
  }

  initRouteView() {
    this.columnLabels = {
      id: this.lang.tra('sa_sessions_col_id'),
      isPaid: this.lang.tra('sa_private_student_paid'), 
      paymentStatus: this.lang.tra('pc_lbl_payment_status'),
      invigilators: this.lang.tra('sa_sessions_col_invig'),
      classroom: this.lang.tra('sa_sessions_col_class'),
      description: this.lang.tra('sa_sessions_col_desc'),
      students: this.lang.tra('sa_sessions_col_stud'),
      times: this.lang.tra('sa_sessions_col_times'),
      submittedOn: this.lang.tra('lbl_date_submission'),
      slug: this.lang.tra('lbl_assessment_type'),
      status: this.lang.tra('sa_title_sessions_status'),
      submissions: this.lang.tra('lbl_submissions'),
      submissions_lang: this.lang.tra('lbl_submissions_lang'),
      submissions_math: this.lang.tra('lbl_submissions_math'),
      type: this.lang.tra('lbl_assessment_type'),
      invigilate: this.lang.tra('lbl_school_admin_invigilate'),
    };
    this.columnStatusLabel = {
      [SessionStatus.PENDING]: this.lang.tra('sa_sessions_status_pend'),
      [SessionStatus.ACTIVE]: this.lang.tra('lbl_active'),
      // [SessionStatus.COMPLETED]: this.lang.tra('sa_sessions_status_comp')
    };


    if (!this.isInited) {
      this.isInited = true;
      this.loadSessions();
    }
  }

  /**
   * Check if current assessment is junior
   * @return boolean
   */
  isJunior() {
    return this.currentClassFilter === ClassFilterId.Junior;
  }

  isG9OrOsslt() { return this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT; }

  isPrimaryOrJunior() { return this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior; }

  getSessionSlug(session) {
    if(session == "G9_OPERATIONAL"){
      return this.lang.tra('lbl_operational');
    } else if(session == "G9_SAMPLE"){
      return this.lang.tra('lbl_sample');
    }
  }

  toggleSelectAll() {
    this.setSelectAll(this.isAllSelected);
  }

  setSelectAll(state: boolean) {
    const sessionSelector = this.visibleSessions()
    this.isAllSelected = state;
    sessionSelector.forEach(session => session.__isSelected = state);
    this.isAnySelected = state;
  }

  checkSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false

    // const sessionSelector = this.sessions
    const sessionSelector = this.visibleSessions();
    const sessions = sessionSelector.filter(session => session.__isSelected);
    if(sessions.length > 0)
      this.isAnySelected = true;
    if(sessions.length == sessionSelector.length)
      this.isAllSelected = true;
  }

  getSessionStatus(session: ISession) {
    const studentMap = this.g9DemoData.getStudentsByClassroomId(String(session.classroom_id));
    if(!studentMap)
      return;

    const students = studentMap.list

    session.studentsPaid = 0;
    session.students = students.length;

    for(let student of students) {
      const studentPaidStatus = student.paidForClass[+session.classroom_id]
      if(studentPaidStatus.isPaid == 1)
        session.studentsPaid++
      if(studentPaidStatus.altPaymentStatus == 1)
        session.altPaymentPending = true;
      if(studentPaidStatus.altPaymentStatus == 2)
        session.altPaymentContactEqao = true;
    }
    session.paymentStatus = "lbl_required"
    if(session.altPaymentContactEqao)
      session.paymentStatus = "pc_btn_contact_eqao" 
    if(session.altPaymentPending) 
      session.paymentStatus = "lbl_pending";
    if(session.studentsPaid == students.length)
      session.paymentStatus = "lbl_approved";
    if(this.isSampleSession(session))
      session.paymentStatus = "lbl_not_required";
    
    const paymentEnabledForSession = this.g9DemoData.schoolData[this.convertPaymentRequiredSlug(session.slug)]
    if(paymentEnabledForSession && session.studentsPaid < students.length && session.paymentStatus == "lbl_required" && !session.isclosed){
      console.log('aSession', session)
      let classFilterSlug:string = null;
      classFilterSlug = this.convertClassFilterSlug(session.slug);
      if(classFilterSlug !== null && classFilterSlug !== undefined){
        if(session.slug == ASSESSMENT.PRIMARY_OPERATIONAL){
          this.unpaidPrimaryClassFilterCount++; 
        }
        if(session.slug == ASSESSMENT.JUNIOR_OPERATIONAL){
          this.unpaidJuniorClassFilterCount++;
        }
        if(session.slug == ASSESSMENT.G9_OPERATIONAL){
          console.log(this.unpaidG9ClassFilterCount)
          this.unpaidG9ClassFilterCount++; 
          console.log(this.unpaidG9ClassFilterCount)
        }
        if(session.slug == ASSESSMENT.OSSLT_OPERATIONAL){
          this.unpaidOssltClassFilterCount++; 
        }
        this.unPaidStudentCurrentFilter.emit(classFilterSlug);
      }
    }
  }

  convertPaymentRequiredSlug(slug: ASSESSMENT | string) {
    let paymentRequiredSlug = "payment_req_";
    if(slug == ASSESSMENT.PRIMARY_OPERATIONAL || slug == ASSESSMENT.JUNIOR_OPERATIONAL){
      paymentRequiredSlug += "pj";
    }
    if(slug == ASSESSMENT.G9_OPERATIONAL) {
      paymentRequiredSlug += "g9";
    }
    if(slug == ASSESSMENT.OSSLT_OPERATIONAL) {
      paymentRequiredSlug += "osslt";
    }
    return paymentRequiredSlug;
  }

  convertClassFilterSlug(slug:string){
    if(slug == ASSESSMENT.PRIMARY_OPERATIONAL){
      return 'lbl_primary'; 
    }
    if(slug == ASSESSMENT.JUNIOR_OPERATIONAL){
      return 'lbl_junior';
    }
    if(slug == ASSESSMENT.G9_OPERATIONAL){
      return 'txt_g9_math';
    }
    if(slug == ASSESSMENT.OSSLT_OPERATIONAL){
      return 'lbl_osslt';
    }
  }

  isSampleSession(session: ISession) {
    return session.slug == ASSESSMENT.PRIMARY_SAMPLE || session.slug == ASSESSMENT.JUNIOR_SAMPLE || session.slug == ASSESSMENT.G9_SAMPLE || session.slug == ASSESSMENT.OSSLT_SAMPLE
  }

  processSession(session: ISession) {
    if(this.isPaymentModuleEnabled){
      this.getSessionStatus(session);
    }
    if(session.isclosed){
      this.completedSessions.push(session)
    }
    else {
      this.nonCompletedSessions.push(session)
    }
  }

  loadSessions() {
   this.sessions = <any[]>this.g9DemoData.assessments.list;
   if(this.isPrivateSchool){
    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9DemoData.schoolData;
    if(payment_req_g9 || payment_req_osslt || payment_req_pj){
      this.isPaymentModuleEnabled = true;
    }
   }
   this.sessions.forEach(session => this.processSession(session));
   this.sessions = this.nonCompletedSessions
   for(let i=0; i < this.sessions.length; i++) {
    if(this.sessions[i].slug === ASSESSMENT.G9_SAMPLE || this.sessions[i].slug === ASSESSMENT.G9_OPERATIONAL || this.sessions[i].slug === ASSESSMENT.OSSLT_SAMPLE || this.sessions[i].slug === ASSESSMENT.OSSLT_OPERATIONAL){
      // G9 && OSSLT Session A
      if(this.sessions[i].session_a && this.sessions[i].session_a.caption === "A") {
        this.sessions[i].session_a.slug = this.lang.tra(this.sessions[i].session_a.slug);
        this.sessions[i].session_a.datetime_start = this.getDate(this.sessions[i].session_a.datetime_start);
      }
      // G9 && OSSLT Session B
      if(this.sessions[i].session_b && this.sessions[i].session_b.caption === "B") {
        this.sessions[i].session_b.slug = this.lang.tra(this.sessions[i].session_b.slug);
        this.sessions[i].session_b.datetime_start = this.getDate(this.sessions[i].session_b.datetime_start);
      }
    }
    if(this.sessions[i].slug === ASSESSMENT.PRIMARY_SAMPLE || this.sessions[i].slug === ASSESSMENT.PRIMARY_OPERATIONAL || this.sessions[i].slug === ASSESSMENT.JUNIOR_SAMPLE|| this.sessions[i].slug === ASSESSMENT.JUNIOR_OPERATIONAL){
      // PJ Session Language
      if(this.sessions[i].session_a && this.sessions[i].session_a.caption === "A") {
        this.sessions[i].session_a.slug = this.lang.tra('pj_lang');
        this.sessions[i].session_a.datetime_start = this.getPJDate(this.sessions[i].session_a.datetime_start);
        this.sessions[i].session_a.datetime_end = this.getPJDate(this.sessions[i].session_a.datetime_end);
      }
      // PJ Session Math
      if(this.sessions[i].session_b && this.sessions[i].session_b.caption === "1") {
        this.sessions[i].session_b.slug = this.lang.tra('pj_math');
        this.sessions[i].session_b.datetime_start = this.getPJDate(this.sessions[i].session_b.datetime_start);
        this.sessions[i].session_b.datetime_end = this.getPJDate(this.sessions[i].session_b.datetime_end);
      }
    }
   }  
    this.sessionsTable = new MemDataPaginated({
      data: this.sessions,
      pageSize: 15,
      filterSettings: {
        slug: (session: ISession, val: string) => {
          if (this.currentClassFilter) {
            if(this.currentClassFilter === ClassFilterId.Primary) {
              return session.slug.indexOf('PRIMARY_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.Junior) {
              return session.slug.indexOf('JUNIOR_') === 0
            }
            else if(this.currentClassFilter === ClassFilterId.G9) {
              return session.slug.indexOf('G9_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.OSSLT) {
              return session.slug.indexOf('OSSLT_') === 0
            }
          } else {
            const slug = this.getSessionSlug(session.slug) || '';
            return slug.indexOf(val) === 0;
          }
        },
        type: (session: ISession, val: string) => session.slug.includes(val) , 
        testWindowFilter:  (session: ISession) => this.filterSessionTestWindow(session), 
      },
      sortSettings: {
        type : (session: ISession) => session.slug.includes("SAMPLE"),
      }
    });
    this.isLoaded = true;
  }

  getSubmissionStatus(res, session) {
    const submissions = []
    const states = res[0].session_states.find(state => { return state.test_session_id === session.test_session_id }).states.studentStates;

    for (const [key, value] of Object.entries(states)) {
      for (const [innerKey, innerValue] of Object.entries(value)) {
        if (innerKey === 'is_submitted' && innerValue === 1) {
          submissions.push(key)
        }
      }
    }
    return submissions;
  }
  archiveSession(sessionId) {
    return this.auth.apiRemove(this.routes.SCHOOL_ADMIN_SESSION, sessionId, this.configureQueryParams())
  }
  showSubmittedSessions(){
    console.log(this.isShowingSubmitted)
    if(this.isShowingSubmitted){
      this.sessionsTable.injestNewData(this.sessions)
      this.isShowingSubmitted = false;
      return;
    }
    if(this.isShowingSubmitted == false){
      this.sessionsTable.injestNewData(this.completedSessions)
      this.isShowingSubmitted = true;
    }
  }

  allowCancelSession() {
    const targets = [].concat(this.sessions).filter(entry => entry.__isSelected);
    const activeSession = targets.find(session => session.status === 'active');
    return activeSession === undefined;
  }

  cancelSelectedSessions() {
    const confirmationMsg = this.lang.tra('sa_remove_session')
    const targets = [].concat(this.sessions).filter(entry => entry.__isSelected);
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: () => {
        targets.map(entry => {
          this.archiveSession(entry.id)
            .then(res => {
              const i = this.sessions.indexOf(entry);
              this.sessions.splice(i, 1);
              const assessmentListIndex = this.g9DemoData.assessments.list.indexOf(entry);
              if(assessmentListIndex != -1){
                this.g9DemoData.assessments.list.splice(assessmentListIndex, 1);
              }
              const nonCompletedIndex = this.nonCompletedSessions.indexOf(entry);
              if(nonCompletedIndex != -1){
                this.nonCompletedSessions.splice(nonCompletedIndex, 1);
              }
              if(entry.status === 'active'){
                this.g9DemoData.activeSessionsCount--;
              }
              else{
                this.g9DemoData.scheduledSessionsCount--;
              }
              if(entry.paymentStatus == "lbl_required"){
                let classFilterSlug:string = null;
                classFilterSlug = this.convertClassFilterSlug(entry.slug);
                if(classFilterSlug !== null && classFilterSlug !== undefined){
                  if(classFilterSlug == 'lbl_primary' && this.unpaidPrimaryClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  if(classFilterSlug == 'lbl_junior' && this.unpaidJuniorClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  console.log(this.unpaidG9ClassFilterCount)
                  if(classFilterSlug == 'txt_g9_math' && this.unpaidG9ClassFilterCount == 1) {
                    console.log(this.unpaidG9ClassFilterCount)
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  if(classFilterSlug == 'lbl_osslt' && this.unpaidOssltClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                }
                this.removeUnpaidClassFilterSlug(classFilterSlug);
              }
              this.sessionsTable.injestNewData(this.sessions);
              alert(this.lang.tra('msg_session_cancelled'));
            })
            .catch(error => {
              if (error.message === 'NO_CANCEL_FOR_ACTIVE_SESSION'){
                alert("Can't cancel ongoing session");
              }
            })
        })
      }
    })
  }

  removeUnpaidClassFilterSlug(classFilterSlug:string){
    if(classFilterSlug == 'lbl_primary' && this.unpaidPrimaryClassFilterCount > 0) {
      this.unpaidPrimaryClassFilterCount--;
    }
    if(classFilterSlug == 'lbl_junior' && this.unpaidJuniorClassFilterCount > 0) {
      this.unpaidJuniorClassFilterCount--;
    }
    if(classFilterSlug == 'txt_g9_math' && this.unpaidG9ClassFilterCount > 0) {
      this.unpaidG9ClassFilterCount--;
    }
    if(classFilterSlug == 'lbl_osslt' && this.unpaidOssltClassFilterCount > 0) {
      this.unpaidOssltClassFilterCount--;
    }
  }

  configureQueryParams() {
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
        }
      }
    }
    return null;
  }
  getDate(dateTime) {
    const mStart = moment.tz(dateTime, STANDARD_TIMEZONE);
    if(mStart.format(this.lang.tra('datefmt_sentence')) == 'Invalid date'){
      return dateTime
    }
    let timezone = mStart.zoneAbbr();
    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    return `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`
  }

  getPJDate(dateTime) {
    if (!dateTime) {
      return null
    }

    const mStart = moment.tz(dateTime, STANDARD_TIMEZONE);
    if(mStart.format(this.lang.tra('pj_datefmt_sentence')) == 'Invalid date'){
      return dateTime
    }
    return `${mStart.utc().format(this.lang.tra('pj_datefmt_sentence'))}`
  }

  returnSessionStatus(dateTime) {
    let currDate = new Date();
    let sessionDate = new Date(dateTime)
    if (currDate.getTime() > sessionDate.getTime()) {
      return true;
    }
    return false;
  }
  getStudentsRoute() {
    // return ['',
    //   this.lang.c(),
    //   AccountType.SCHOOL_ADMIN,
    //   'students'
    // ].join('/')
  }

  openInvigilation(session){
    this.loginGuard.quickPopup(this.lang.tra('msg_invigilate_myself_error'))
  }

  openStudentCountsModal(classroom_id) {
    this.getStudentList(classroom_id);
    const config = {};
    this.pageModal.newModal({
      type: SessionModal.STUDENTS_SESSION,
      config,
      finish: () => {}
    })
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  // New Session
  newSessionModalStart() {
    const config = {
      filter: this.currentClassFilter
    };
    this.pageModal.newModal({
      type: SessionModal.NEW_SESSION,
      config,
      finish: this.newSessionModalFinish
    });
  }
  newSessionModalFinish = (config: { payload }) => {
    if(!config.payload.data.sessionAStartDate || !config.payload.data.sessionAStartTime) {
      this.fillUpSessionADateTime(config);
    }
    if(this.validateAssesment(config)){
      this.finalizeAssessmentCreation(config)
    }  
  }

  fillUpSessionADateTime(config) {
    /* 
      If session A is completed by all students, when a new operational assessment created, 
      fill in Session A Start date and time 1hr earlier than scheduled date and time for Session B
    */
    const startDate = config?.payload?.data?.sessionBStartDate;
    const startTime = config?.payload?.data?.sessionBStartTime;
    if (startDate && startTime) {
      const sessionBDateTime = `${startDate} ${startTime}`;
      const sessionBStart = moment(sessionBDateTime, 'YYYY-MM-DD hh:mma');
      const sessionAStart = moment(sessionBStart).subtract(1, 'hour');
      config.payload.data.sessionAStartDate = sessionAStart.format('YYYY-MM-DD');
      config.payload.data.sessionAStartTime = sessionAStart.format('hh:mma');
    }
  }

  verifyScheduleGracePeriod(sessionAEnd,dateToCompare){
    const gracePeriod = 1;
    const startDate = sessionAEnd + (gracePeriod*60*1000);
    console.log(startDate, dateToCompare)
    if(dateToCompare < startDate){
      return false;
    }
    return true;
  }
  isSessionScheduled(payload){
    // if(payload.slug === "G9_SAMPLE" || payload.slug === "OSSLT_SAMPLE"){
    //   return false;
    // }
    const session = this.sessions.find(session => parseInt(session.classroom_id) === parseInt(payload.school_class_id) && (session.slug === payload.slug));
    if(session){
      if(this.currentClassFilter === ClassFilterId.Primary) {
        switch (payload.slug){
          case ASSESSMENT.PRIMARY_SAMPLE:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_sample_primary'))
            break;
          case ASSESSMENT.PRIMARY_OPERATIONAL:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_primary'))
            break;
        }
      }
      if(this.currentClassFilter === ClassFilterId.Junior) {
        switch (payload.slug){
          case ASSESSMENT.JUNIOR_SAMPLE:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_sample_junior'))
            break;
          case ASSESSMENT.JUNIOR_OPERATIONAL:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_junior'))
            break;
        }
      }
      if(this.currentClassFilter === ClassFilterId.G9) {
        switch (payload.slug){
          case "G9_SAMPLE":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))
            break;
          case "G9_OPERATIONAL":
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))
            break;
        }
      }
      if (this.currentClassFilter === ClassFilterId.OSSLT) {
        switch (payload.slug){
          case "OSSLT_SAMPLE":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'))
            break;
          case "OSSLT_OPERATIONAL":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'))
            break;
        }
        //this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'))
      }
     return true;
    }
    return false;
  }
  verifySchedulingconditions(sessionAStartDay,sessionAStartTime,sessionBStartDay,sessionBStartTime){
    
    if(sessionBStartDay.isBefore(sessionAStartDay)){
      this.schedulerErrors = 'msg_ses_b_before_ses_a';
      alert(this.lang.tra('msg_ses_b_before_ses_a'))
      throw new Error();
    }
    if(!this.verifyScheduleGracePeriod(sessionAStartTime,sessionBStartTime)){
      this.schedulerErrors = 'msg_time_gap';
      alert(this.lang.tra('msg_time_gap'))
      throw new Error();
    } 
  }

  verifyPJSchedulingConditions(sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate){
    const validateDates = (endDate, startDate): void => {
      if ((endDate && !startDate) || (!endDate && startDate)) {
        this.schedulerErrors = 'sa_asses_time_invalid';  
        alert(this.lang.tra('sa_asses_time_invalid'))          
        throw new Error();
      }
      if(endDate && startDate && endDate.isBefore(startDate)){
        this.schedulerErrors = 'msg_pj_end_before_start';  
        alert(this.lang.tra('msg_pj_end_before_start'))          
        throw new Error();
      }
    };
    validateDates(sessionLangEndDate, sessionLangStartDate);
    validateDates(sessionMathEndDate, sessionMathStartDate);
  }

  /**
   * Converts local date time string to UTC timezone date time string
   * @param payload: string[]
   * @returns `utcTimes`: string[]
   * @example ['2020-01-01T00:00', '2020-01-01T00:00', '2020-01-01T00:00', '2020-01-01T00:00']
   */
  processLocalDateTimeArray(payload:any) {
    const utcTimes: string[] = payload.scheduled_time.map((time: string) => {
      if(!time) return null;
      return moment.tz(time, STANDARD_TIMEZONE).utc().format('YYYY-MM-DDTHH:mm');
    });

    return utcTimes;
  }
  
  /**
   * Converts test window utc time string format
   * @param testWindow: ITestWindow 
   * @returns { windowStart: string, windowEnd: string }
   * @example { windowStart: '2020-01-01', windowEnd: '2020-01-01 00:00:00' }
   */
  convertWindowStartEndDateTime(testWindow: any) {
    //Test window date_start and date_end are saved as UTC time, need to store it as utc time
    const windowStart = moment.utc(testWindow.date_start).format('YYYY-MM-DD HH:mm:ss');
    const windowEnd =  moment.utc(testWindow.date_end).format('YYYY-MM-DD HH:mm:ss');
    return { windowStart, windowEnd };
  }

  finalizeAssessmentCreation(config) {
    let sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate, sessionAStartDay, sessionAStartTime, sessionBStartDay, sessionBStartTime
    const payload = this.getPayload(config)
    console.log('new payload', payload)
    let pjSchedulePayload;
    let windowStartEndDate;
    if(payload.scheduled_time){
      if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
        sessionAStartDay = moment(payload.scheduled_time[0],'YYYY-MM-DD');
        sessionAStartTime = new Date(payload.scheduled_time[0]).getTime();
        sessionBStartDay = moment(payload.scheduled_time[1],'YYYY-MM-DD');
        sessionBStartTime = new Date(payload.scheduled_time[1]).getTime();
      }
      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
        if (payload.isLangScheduled) {
          sessionLangStartDate = payload.scheduled_time[0] ? moment(payload.scheduled_time[0],'YYYY-MM-DD') : null;
          sessionLangEndDate = payload.scheduled_time[1] ? moment(payload.scheduled_time[1],'YYYY-MM-DD') : null;
        }
        if (payload.isMathScheduled) {
          sessionMathStartDate = payload.scheduled_time[2] ? moment(payload.scheduled_time[2],'YYYY-MM-DD') : null;
          sessionMathEndDate = payload.scheduled_time[3] ? moment(payload.scheduled_time[3],'YYYY-MM-DD') : null;
        }
      }
      const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == Number(config.payload.data.classId));
      const semester = this.g9DemoData.semesters.list.find(semester => semester.id == Number(classroom.semester));
      const testWindow = this.g9DemoData. testWindows.find(window => window.id == semester.testWindowId)

      // convert user typed time into utc
      const scheduledTime = this.processLocalDateTimeArray(payload);

      // convert test window start/end time to utc format
      windowStartEndDate = this.convertWindowStartEndDateTime(testWindow);

      if (payload.slug === ASSESSMENT.OSSLT_OPERATIONAL && windowStartEndDate){
        const alertSlug = 'msg_osslt_administration_window_warning';
        this.validateTestDatesWindow(scheduledTime[0], scheduledTime[1], windowStartEndDate.windowStart, windowStartEndDate.windowEnd, alertSlug)
      } else if(payload.slug === ASSESSMENT.G9_OPERATIONAL && windowStartEndDate){
        const alertSlug = 'msg_g9_administration_window_warning';
        this.validateTestDatesWindow(scheduledTime[0], scheduledTime[1], windowStartEndDate.windowStart, windowStartEndDate.windowEnd, alertSlug)
      } else if((payload.slug === ASSESSMENT.PRIMARY_OPERATIONAL || payload.slug === ASSESSMENT.JUNIOR_OPERATIONAL) && windowStartEndDate) {
        const alertSlug = 'msg_pj_administration_window_warning';
        this.validatePJTestDatesWindow(scheduledTime[0], scheduledTime[1], scheduledTime[2], scheduledTime[3], windowStartEndDate.windowStart, windowStartEndDate.windowEnd, alertSlug)
      } 
      if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
        if(payload.scheduled_time[0] && payload.scheduled_time[1]){ 
          this.verifySchedulingconditions(sessionAStartDay, sessionAStartTime,sessionBStartDay,sessionBStartTime) 
        }
      }
      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
        const validSchedules: string[] = [];
        if (payload.isLangScheduled) {
          validSchedules.push(payload.scheduled_time[0], payload.scheduled_time[1]);
        }
        if (payload.isMathScheduled) {
          validSchedules.push(payload.scheduled_time[2], payload.scheduled_time[3]);
        }
        pjSchedulePayload = JSON.parse(JSON.stringify(payload));
        pjSchedulePayload.scheduled_time = validSchedules;
        this.verifyPJSchedulingConditions(sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate);
      }
    }
    let isVerifiedPastDate = (this.isG9OrOsslt() || (this.isPrimaryOrJunior() && !payload.scheduled_time)) ? this.verifyPastDate(payload) : this.verifyPastDate(pjSchedulePayload);
    if (!isVerifiedPastDate && !this.isSessionScheduled(payload)){
      const is_fi_class = !this.isLanguageVisibleForFIClass(Number(payload.school_class_id));
      const newPayload = {...payload, is_fi_class};
      if(this.checkForStudents(config)){
        return this.auth
          .apiCreate(this.routes.SCHOOL_ADMIN_SESSION, newPayload, this.configureQueryParams())
          .then((session: any) => {  
            this.logSessionBTime(session.test_session_id, config.payload.data)
            let session_A = null;
            let session_B = null;
            if(session.slug === ASSESSMENT.G9_SAMPLE || session.slug === ASSESSMENT.G9_OPERATIONAL || session.slug === ASSESSMENT.OSSLT_SAMPLE || session.slug === ASSESSMENT.OSSLT_OPERATIONAL) {
              if(session[0] || session[1]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[1] && session[1].caption === 'B') {
                    session_B = session[1];
                  } 
                } else if(session[1] && session[1].caption === 'A') {
                  session_A = session[1];
                  if(session[0] && session[0].caption === 'B') {
                    session_B = session[0];
                  } 
                } 
              }
            }
            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.JUNIOR_SAMPLE) {
              if(session[0] || session[2]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[2] && session[2].caption === '1') {
                    session_B = session[2];
                  } 
                } else if(session[2] && session[2].caption === 'A') {
                  session_A = session[2];
                  if(session[0] && session[0].caption === '1') {
                    session_B = session[0];
                  } 
                } 
              }
            }
            if(session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              if(session[0] || session[4]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[4] && session[4].caption === '1') {
                    session_B = session[4];
                  } 
                } else if(session[4] && session[4].caption === 'A') {
                  session_A = session[4];
                  if(session[0] && session[0].caption === '1') {
                    session_B = session[0];
                  } 
                } 
              }
            }  
            let slug_a;
            let slug_b;
            if(session.slug === ASSESSMENT.G9_SAMPLE || session.slug === ASSESSMENT.G9_OPERATIONAL || session.slug === ASSESSMENT.OSSLT_SAMPLE || session.slug === ASSESSMENT.OSSLT_OPERATIONAL) {
              slug_a = session_A.slug;
              slug_b = session_B ? session_B.slug : null;
            }
            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              slug_a = session_A.slug.includes('lang_') ? 'pj_lang' : 'pj_math';
              slug_b = session_B.slug.includes('math_') ? 'pj_math' : 'pj_lang';
            }
            let updatedStatus = payload.isScheduled ? 'pending' : 'active';
            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              if(session_A && session_A.caption == 'A') {
                if (session_A.datetime_start) {                
                  updatedStatus = this.detectSessionStatus(session_A.datetime_start, false);
                }
                if(session_B && session_B.caption == '1') {
                  if((updatedStatus === 'pending' || updatedStatus === null) && session_B.datetime_start){ 
                    updatedStatus = this.detectSessionStatus(session_B.datetime_start, false);
                  }
                }
              }
            }
            const newGeneratedSession = {
              id: session.test_session_id,
              school_name:null,
              invigilator: `${session.studentRecords[0].first_name} ${session.studentRecords[0].last_name}`,
              classroom_id: session.school_class_id,
              firstName: session.studentRecords[0].first_name,
              lastName: session.studentRecords[0].last_name,
              description: '',
              classCode: session.class_code,
              students: session.studentRecords[0].num_students,
              startTime: this.isG9OrOsslt() ? this.getDate(session.date_time_start) : this.getPJDate(session.date_time_start),
              startTimeUTC: session.date_time_start,
              endTime: '',
              slug:session.slug,
              submissions:0,
              submissions_1:0,
              status: updatedStatus,
              isConfirmed: coinFlip(),
              session_a:{
                slug: this.lang.tra(slug_a),
                datetime_start: this.isG9OrOsslt() ? this.getDate(moment.utc(session_A.datetime_start)) : this.getPJDate(session_A.datetime_start),
                datetime_end: session_A.datetime_end != null ? this.getPJDate(session_A.datetime_end) : null,
                caption: session_A.caption
              },
              session_b: session_B ? {
                slug: this.lang.tra(slug_b),
                datetime_start: this.isG9OrOsslt() ? this.getDate(moment.utc(session_B.datetime_start)) : this.getPJDate(session_B.datetime_start),
                datetime_end: session_B.datetime_end != null ? this.getPJDate(session_B.datetime_end) : null,
                caption: session_B.caption
              } : null
            };
            this.processSession(newGeneratedSession);
            // this.sessions.splice(0,0,newGeneratedSession);      
            this.sessionsTable.injestNewData(this.sessions);
            alert(this.lang.tra('msg_new_session'))
            this.pageModal.closeModal()
            this.updateSessionsCount(payload);
          })
          .catch(error =>{
            if (error.message === 'TECH_READI_PENDING'){
              alert(this.lang.tra('msg_tech_read_incomplete'));
            } else if (error.message === 'MSG_NO_TEACHER') {
              alert(this.lang.tra('msg_no_teacher'));
            } else if (error.message === 'MSG_INVALID_SCHEDULE_TIME'){
              this.loginGuard.disabledPopup(this.lang.tra('msg_invalid_schedule_time'));
            }
            else if (error.message === 'ONGOING_SAMPLE_ASSESSMENT') {
              if (this.currentClassFilter === ClassFilterId.Primary) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_primary'))  
              } else if (this.currentClassFilter === ClassFilterId.Junior) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_junior'))  
              } else if (this.currentClassFilter === ClassFilterId.G9) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))  
              } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'));
              }
            }
            else if (error.message === 'ONGOING_LIVE_ASSESSMENT') {
              if (this.currentClassFilter === ClassFilterId.Primary) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_primary'))  
              } else if (this.currentClassFilter === ClassFilterId.Junior) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_junior'))  
              } else if (this.currentClassFilter === ClassFilterId.G9) {
                this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))  
              } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'));   
              }
            }
            else if (error.message === 'MSG_ADMIN_WINDOW_WARNING') {
              this.handleAdminWindowWarningError(windowStartEndDate.windowStart, windowStartEndDate.windowEnd);
            }
            else if (error.message === 'MSG_ASSES_TIME_INVALID_WARNING') {
              alert(this.lang.tra('sa_asses_time_invalid'));
            }
          })
      }
      else{
        alert(this.lang.tra('msg_no_students'))
      }
    }
  }

  handleAdminWindowWarningError(windowStart:any, windowEnd:any) {
    const twDateStr = this.g9DemoData.parseWindowStartEndDateTime(moment.utc(windowStart), moment.utc(windowEnd), 'datefmt_dashboard_long', 'datefmt_dashboard_long')
    if (this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
      this.loginGuard.disabledPopup(this.lang.tra('msg_pj_administration_window_warning', undefined, {start: twDateStr.windowStart, end: twDateStr.windowEnd}))  
    } else if (this.currentClassFilter === ClassFilterId.G9) {
      this.loginGuard.disabledPopup(this.lang.tra('msg_g9_administration_window_warning', undefined, {start: twDateStr.windowStart, end: twDateStr.windowEnd}))  
    } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
      this.loginGuard.disabledPopup(this.lang.tra('msg_osslt_administration_window_warning', undefined, {start: twDateStr.windowStart, end: twDateStr.windowEnd}));   
    }
  }

  updateSessionsCount(payload){
    if(payload.isScheduled){
      this.g9DemoData.scheduledSessionsCount++;
    }
    else{
      this.g9DemoData.activeSessionsCount++;
    }
  }
  checkForStudents(config){
   return (this.g9DemoData.getStudentsByClassroomId(config.payload.data.classId).list.length > 0)
  }
  getStudentList(classroom_id) {
    this.studentList = this.g9DemoData.getStudentsByClassroomId(classroom_id).list;
  }
  isDateValid:boolean;
  setDateValidity($event){
    this.isDateValid = $event;
  }
  // verifyPastDate(payload) {
  //   if(!payload.isScheduled){
  //     return false
  //   }
    
  //   let currDate = new Date();
  //   let selectedDate = new Date(payload.scheduled_time)
  //   if(!this.isDateValid){
  //     alert(this.lang.tra('Invalid date entered!'))
  //     return true;
  //   }
  //   if (Object.prototype.toString.call(selectedDate) === "[object Date]") {
  //     if (isNaN(selectedDate.getTime())) {  // d.valueOf() could also work
  //       alert(this.lang.tra('Invalid date entered!'))
  //       return true;
  //       console.log('date is not valid')
  //     }
  //   } else {
  //     alert(this.lang.tra('Invalid date entered!'))
  //     return true;
  //     console.log('date is invalid')
  //   }

  //   if (currDate.getTime() > selectedDate.getTime()) {
  //     alert(this.lang.tra('msg_date_time_passed'))
  //     return true;
  //   }
  //   return false;
  // }
  verifyPastDate(payload) {
    if(!payload.isScheduled){
      return false;
    }
    if(!this.isDateValid){
      alert(this.lang.tra('lbl_invalid_date'))
      return true;
    }

    let isInvalid = false;
    let dateTime = payload.scheduled_time
    const currDateEtz = etz(new Date())

    for(let i =0; i<dateTime.length; i++){
      if (dateTime[i]) {      
        let selectedDateEtz = etz(dateTime[i]);

        if (!selectedDateEtz.isValid()) {
          alert(this.lang.tra('lbl_invalid_date'))
          isInvalid = true;
          break;
        }

        if (this.currentClassFilter == ClassFilterId.G9 || this.currentClassFilter == ClassFilterId.OSSLT) {
          if (currDateEtz.isAfter(selectedDateEtz)) {
            alert(this.lang.tra('msg_date_time_passed'))
            isInvalid = true
            break;
          }
        }

        if(this.currentClassFilter == ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior) {
          const curr_date = currDateEtz.utc().format('YYYY-MM-DD');
          const selected_date = selectedDateEtz.utc().format('YYYY-MM-DD');
          if (curr_date > selected_date) {
            alert(this.lang.tra('msg_date_time_passed'))
            isInvalid = true
            break;
          }
        }
      }
    }
    return isInvalid;
  }

  getPayload(config){
    const slug = config.payload.data.slug;
    if(config.payload.data.schedule === SCHEDULER.LATER){
      if(slug === ASSESSMENT.G9_SAMPLE){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`]
        }
      } 
      else if(slug === ASSESSMENT.G9_OPERATIONAL || slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            `${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`,
            `${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`
          ]
        }
      }
      else if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.PRIMARY_OPERATIONAL || slug === ASSESSMENT.JUNIOR_SAMPLE || slug === ASSESSMENT.JUNIOR_OPERATIONAL){
        let sessionLangStartDate =  config.payload.data.sessionLangStartDate ? `${config.payload.data.sessionLangStartDate}` : null;
        let sessionLangEndDate =  config.payload.data.sessionLangEndDate ? `${config.payload.data.sessionLangEndDate}` : null;
        let sessionMathStartDate =  config.payload.data.sessionMathStartDate ? `${config.payload.data.sessionMathStartDate}` : null;
        let sessionMathEndDate =  config.payload.data.sessionMathEndDate ? `${config.payload.data.sessionMathEndDate}` : null;
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            sessionLangStartDate,
            sessionLangEndDate,
            sessionMathStartDate,
            sessionMathEndDate,
          ],
          isLangScheduled: config.payload.data.sessionLangStartDate || config.payload.data.sessionLangEndDate ? true : false,
          isMathScheduled: config.payload.data.sessionMathStartDate || config.payload.data.sessionMathEndDate ? true : false,
        }
      }
    }
    return {
      school_class_id: config.payload.data.classId,
      slug: config.payload.data.slug,
      isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
    }
  }
  timeConvert(tm) {
    let time;
    if (tm.includes('pm')){
      time = tm.replace('pm',' PM')
    }
    else{
      time = tm.replace('am', ' AM')
    }
    var hours = Number(time.match(/^(\d+)/)[1]);
    var minutes = Number(time.match(/:(\d+)/)[1]);
    var AMPM = time.match(/\s(.*)$/)[1];
    if (AMPM == "PM" && hours < 12) hours = hours + 12;
    if (AMPM == "AM" && hours == 12) hours = hours - 12;
    var sHours = hours.toString();
    var sMinutes = minutes.toString();
    if (hours < 10) sHours = "0" + sHours;
    if (minutes < 10) sMinutes = "0" + sMinutes;
    return (sHours + ":" + sMinutes);
  }
  // Edit Session
  editSessionModalStart() {
    this.isActive = false;
    this.isPartialActive = false;
    const sessions = this.getSelectedSessions(true);
    const config = { 
      sessions,
      filter: this.currentClassFilter,
    };
    if (config.sessions.length === 1) {
      const currentSession = config.sessions[0];
      if (currentSession.status === 'active') {
        this.isActive = true;
      }
      const session_a_datetime_start_null = currentSession.session_a && !currentSession.session_a.datetime_start
      const session_b_datetime_start_null = currentSession.session_b && !currentSession.session_b.datetime_start
      if(this.isG9OrOsslt() && (session_a_datetime_start_null || session_b_datetime_start_null)){
        this.isPartialActive = true;
      }
      if (this.isPrimaryOrJunior() && currentSession.session_a && currentSession.session_b) {
        const isFutureDate = (startDate: string): boolean => {
          if (!startDate) {
            return true;
          }
          const processedDate = moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
          const now = moment().format('YYYY-MM-DD');
          if (processedDate <= now) {
            return false;
          }
          return true;
        };
        if ((!currentSession.session_a.datetime_start && !currentSession.session_a.datetime_end) || isFutureDate(currentSession.session_a.datetime_start)) {
          this.isPartialActive = true;
        } else if ((!currentSession.session_b.datetime_start && !currentSession.session_b.datetime_end) || isFutureDate(currentSession.session_b.datetime_start)) {
          this.isPartialActive = true;
        }
      }
    }
    this.pageModal.newModal({
      type: SessionModal.EDIT_SESSION,
      config,
      finish: this.editSessionModalFinish,
    });
  }

  /**
   * Check if the user entered session Date is within Test window time
   * @param a_string user input session A start time (UTC)
   * @param b_string user input session B start time (UTC)
   * @param windowStart test window start time (UTC)
   * @param windowEnd test window end time (UTC)
   * @param alertSlug 
   */
  private validateTestDatesWindow(a_string, b_string, windowStart, windowEnd, alertSlug){
    const a = a_string ? moment(a_string) : undefined
    const b = b_string ? moment(b_string) : undefined

    //calculate test window start/end time(UTC) in Ontario time to used in the error alert prompt
    let startDate = moment.utc(windowStart).tz(STANDARD_TIMEZONE).format(this.lang.tra('datefmt_month_year_time'));
    let endDate = moment.utc(windowEnd).tz(STANDARD_TIMEZONE).format(this.lang.tra('datefmt_month_year_time'));
    if(this.lang.c() === 'fr'){
      startDate = tzLangAbbrs(startDate, 'fr')
      endDate = tzLangAbbrs(endDate, 'fr')
    }
    const alertErrorMsg = () => alert(this.lang.tra(alertSlug, undefined, {start: startDate, end: endDate}))

    //checking
    if(!a && b){
      if (b.isBefore(windowStart)){
        alertErrorMsg()
        throw new Error();
      }
      if (b.isAfter(windowEnd)){
        alertErrorMsg()
        throw new Error();
      }
    }
    else if(!b && a){
      if (a.isBefore(windowStart)){
        alertErrorMsg()
        throw new Error();
      }
      if (a.isAfter(windowEnd)){
        alertErrorMsg()
        throw new Error();
      }
    }
    else if(a && b){
      if (a.isBefore(windowStart) || b.isBefore(windowStart)){
        alertErrorMsg()
        throw new Error();
      }
      else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){
        alertErrorMsg()
        throw new Error();
      }
    }
  }

  showSessionPaymentModal() {
    const config = {};
    const sessions = this.getSelectedSessions(true);
    if(sessions.length > 0){
      const paymentNotRequiredAssessment = sessions.find(session => session.paymentStatus === "lbl_not_required");
      const alternativePaymentPendingAssessment = sessions.find(session => session.altPaymentPending); 
      const approvedPaymentAssessment = sessions.find(session => session.paymentStatus === "lbl_approved");
      if(paymentNotRequiredAssessment || alternativePaymentPendingAssessment || approvedPaymentAssessment) {
        const errorMsg_paymentNotRequired = paymentNotRequiredAssessment ? this.lang.tra('caption_private_sample_payment_not_required') : "";
        const errorMsg_alternativePaymentPending = alternativePaymentPendingAssessment ? this.lang.tra('caption_private_alternative_payment_pending') : "";
        const errorMsg_approvedPaymentAssessment = approvedPaymentAssessment ? this.lang.tra('caption_private_approved_payment') : "";
        const confirmationMsg = this.lang.tra('caption_private_select_session_error') + errorMsg_paymentNotRequired + errorMsg_alternativePaymentPending + errorMsg_approvedPaymentAssessment;
        this.loginGuard.confirmationReqActivate({
          caption: confirmationMsg,
          btnCancelConfig: {
            hide: true
          },
          confirm: () => {}
        })
      } 
      else {
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_OVERVIEW,
          config,
          finish: () => {}
        })
      }
    }
  }

  isPaymentAvailable() {
    this.checkSelection();
    if(this.isAnySelected) {
      const selectedSessions = this.getSelectedSessions(true)
      const onlyOneSessionSelected      = selectedSessions.length === 1;
      const everySessionRequiresPayment = selectedSessions.every(session => session.paymentStatus === "lbl_required");
      const allowedTwId = this.g9DemoData.schoolData.allow_subm_sess_pay_tw;
      const paymentForClosedSessions =  this.g9DemoData.filterSessionTestWindow(selectedSessions[0], allowedTwId) || allowedTwId == -1;
      return onlyOneSessionSelected && everySessionRequiresPayment && (!selectedSessions[0].isclosed || paymentForClosedSessions);
    }
    return false
  }

  async getPaymentPolicy() {
    await this.auth.apiFind('public/school-admin/payment-policy', {
      query: {
        schl_group_id: this.g9DemoData.schoolData.group_id
      }
    })
    .then(paymentPolicy => {
      this.paymentPolicy = paymentPolicy;
    })
    .catch(e => {
      console.error(e);
    });
  }

  onStripePayment(event) {
    console.log('event', event);
    if (event) {
      const config = {
        totalCost: event.totalCost
      };
      this.pageModal.newModal({
        type: SessionModal.SESSION_PAYMENT_STRIPE_OVERVIEW,
        config,
        finish: () => {}
      })
    }
  }

  onAlternativePayment(event) {
    if (event && event.totalCost && event.diffInDays) {
      this.alternative_days_before_session = this.paymentPolicy.find(policy => policy.method_type == 'Alternative').days_before_session;

      if (event.diffInDays > this.alternative_days_before_session) {
        const config = {
          totalCost: event.totalCost
        };
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_ALTERNATIVE_OVERVIEW,
          config,
          finish: () => {}
        })  
      } else {
        const config = {};
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_ALTERNATIVE_WARNING,
          config,
          finish: () => {}
        })          
      }
    }
  }

  onCompletePaymentAlt(event) {
    if(event) {
      this.getSelectedSessions(true).forEach(session => {
        session.paymentStatus = "lbl_pending";
      })
      this.setSelectAll(false);
      this.pageModal.closeModal();
    }
  }

  getTestWindowViewText(tw){
    if(tw){
      const startDate = formatDate(new Date(tw.date_start), 'MMM yyyy', 'en_US')
      const endDate = formatDate(new Date(tw.date_end), 'MMM yyyy', 'en_US')
      const isActive = (new Date(tw.date_end) > new Date ())? "lbl_active":"lbl_inactive"
      return startDate+" "+this.lang.tra("lbl_date_to") +" "+endDate+" ("+this.lang.tra(isActive)+")";
    } 
  }

  private validatePJTestDatesWindow(langStart, langEnd, mathStart, mathEnd, windowStart, windowEnd, alertSlug){
    const langStartDate = langStart ? moment(langStart) : undefined;
    const langEndDate = langEnd ? moment(langEnd) : undefined;
    const mathStartDate = mathStart ? moment(mathStart) : undefined;
    const mathEndDate = mathEnd ? moment(mathEnd) : undefined;
    const twDateStr = this.g9DemoData.parseWindowStartEndDateTime(moment.utc(windowStart), moment.utc(windowEnd), 'datefmt_month_year_time', 'datefmt_month_year_time')
    if(langStartDate && langEndDate){
      if(langStartDate.isBefore(windowStart) || langStartDate.isAfter(windowEnd) || langEndDate.isBefore(windowStart) || langEndDate.isAfter(windowEnd)){
        alert(this.lang.tra(alertSlug, undefined, {start: twDateStr.windowStart,end: twDateStr.windowEnd}))
        throw new Error();
      }
    }
    if(mathStartDate && mathEndDate){
      if(mathStartDate.isBefore(windowStart) || mathStartDate.isAfter(windowEnd) || mathEndDate.isBefore(windowStart) || mathEndDate.isAfter(windowEnd)){
        alert(this.lang.tra(alertSlug, undefined, {start: twDateStr.windowStart,end: twDateStr.windowEnd}))
        throw new Error();
      }
    }
  }

/*   private validateTimes(_a, _b, assessment){
    console.log(_a, _b)
    const a = _a ? moment(_a) : undefined
    const b = _b ? moment(_b) : undefined
    const windowStart = moment('2021-03-23');
    const windowEnd = moment('2021-06-05');
    if (assessment === ASSESSMENT.OSSLT_OPERATIONAL){
      if(!a && b){
        if (b.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        if (b.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
      else if(!b && a){
        if (a.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        if (a.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
      else if(a && b){
        if (a.isBefore(windowStart) || b.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
    }
  } */

  private logSessionBTime(test_session_id, data){
    console.log('logSessionBTime', test_session_id, data)
    this.auth.apiCreate(this.routes.LOG, {
      slug:'SA_SESSION_B_DATE_TEMP',
      data: {
        test_session_id,
        data
      }
    })
  }

  editSessionModalFinish = (config: { payload }) => {
    if(this.isActive && !this.isPartialActive){ //checks if session is fully active and closes modal before preceeding to checking validity of session
      return this.pageModal.closeModal()
    } else{
      if(!config.payload.data.sessionAStartDate || !config.payload.data.sessionAStartTime) {
        // Use the orignal Session A Date and Time when Edit the session
        const originalSessionADateTime = config?.payload?.record?.session_a?.datetime_start;
        const originalSessionADateTimeMoment = moment(originalSessionADateTime, "dddd h:mma MMMM DD, YYYY [EDT]");
        config.payload.data.sessionAStartDate = originalSessionADateTimeMoment.format("YYYY-MM-DD");
        config.payload.data.sessionAStartTime = originalSessionADateTimeMoment.format("hh:mma");
      }
      if(!this.validateAssesment(config, true)){
        return;
      }
      if(config.payload){
        const record = config.payload.record;
        const data = config.payload.data;
        const {slug} = record;
        let scheduled_time:any[] = [];
        if(slug === ASSESSMENT.G9_SAMPLE || slug === ASSESSMENT.G9_OPERATIONAL || slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL) {
          if(data.sessionBStartDate && data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
            scheduled_time.push(`${data.sessionBStartDate}T${this.timeConvert(data.sessionBStartTime)}`);
          } else if(!data.sessionBStartDate && !data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
          }
        }
        if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.PRIMARY_OPERATIONAL || slug === ASSESSMENT.JUNIOR_SAMPLE || slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
          if (record.session_a) {
            if (record.session_a.datetime_start && record.session_a.datetime_end) {
              if (data.sessionLangStartDate === undefined || data.sessionLangStartDate === null) {
                data.sessionLangStartDate = moment(record.session_a.datetime_start, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
              if (data.sessionLangEndDate === undefined || data.sessionLangEndDate === null) {
                data.sessionLangEndDate = moment(record.session_a.datetime_end, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
            }
          }
          if (record.session_b) {
            if (record.session_b.datetime_start && record.session_b.datetime_end) {
              if (data.sessionMathStartDate === undefined || data.sessionMathStartDate === null) {
                data.sessionMathStartDate = moment(record.session_b.datetime_start, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
              if (data.sessionMathEndDate === undefined || data.sessionMathEndDate === null) {
                data.sessionMathEndDate = moment(record.session_b.datetime_end, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
            }
          }
          let sessionLangStartDate = null, sessionLangEndDate = null, sessionMathStartDate = null, sessionMathEndDate = null;
          // if Language has been scheduled (edit modal), w/o Math
          if(data.sessionLangStartDate && data.sessionLangEndDate) {
            sessionLangStartDate = data.sessionLangStartDate;
            sessionLangEndDate = data.sessionLangEndDate;
          }
          // if Math has been scheduled (edit modal), w/o Language
          if(data.sessionMathStartDate && data.sessionMathEndDate) {
            sessionMathStartDate = data.sessionMathStartDate;
            sessionMathEndDate = data.sessionMathEndDate;
          }
          scheduled_time.push(sessionLangStartDate);
          scheduled_time.push(sessionLangEndDate);
          scheduled_time.push(sessionMathStartDate);
          scheduled_time.push(sessionMathEndDate);
        }
        const payload = {
          scheduled_time,
          slug
        }
        const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == Number(config.payload.record.classroom_id));
        const semester = this.g9DemoData.semesters.list.find(semester => semester.id == Number(classroom.semester));
        const testWindow = this.g9DemoData. testWindows.find(window => window.id == semester.testWindowId)

        // convert user typed time into utc
        const scheduledTime = this.processLocalDateTimeArray(payload);

        // convert test window start/end time to utc format
        const windowStartEndDate = this.convertWindowStartEndDateTime(testWindow);

        if (config.payload.record.slug === ASSESSMENT.OSSLT_OPERATIONAL && windowStartEndDate){
          const alertSlug = 'msg_osslt_administration_window_warning';
          this.validateTestDatesWindow(scheduledTime[0], scheduledTime[0], windowStartEndDate.windowStart, windowStartEndDate.windowEnd, alertSlug)
          this.validateTestDatesWindow(scheduledTime[1], scheduledTime[1], windowStartEndDate.windowStart, windowStartEndDate.windowEnd,alertSlug)
        } else if(config.payload.record.slug === ASSESSMENT.G9_OPERATIONAL && windowStartEndDate){
          const alertSlug = 'msg_g9_administration_window_warning';
          this.validateTestDatesWindow(scheduledTime[0], scheduledTime[0], windowStartEndDate.windowStart, windowStartEndDate.windowEnd,alertSlug)
          this.validateTestDatesWindow(scheduledTime[1], scheduledTime[1], windowStartEndDate.windowStart, windowStartEndDate.windowEnd,alertSlug)
        } else if ((config.payload.record.slug === ASSESSMENT.PRIMARY_OPERATIONAL || config.payload.record.slug === ASSESSMENT.JUNIOR_OPERATIONAL) && windowStartEndDate){
          const alertSlug = 'msg_pj_administration_window_warning';
          this.validatePJTestDatesWindow(scheduledTime[0], scheduledTime[1], scheduledTime[2], scheduledTime[3], windowStartEndDate.windowStart, windowStartEndDate.windowEnd, alertSlug)
        } 
        if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
          if(config.payload.data.sessionAStartDate && config.payload.data.sessionBStartDate && config.payload.data.sessionAStartTime && config.payload.data.sessionBStartTime){ 
            this.verifySchedulingconditions(moment(config.payload.data.sessionAStartDate), new Date(`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`).getTime(), moment(config.payload.data.sessionBStartDate),  new Date(`${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`).getTime()) 
          }
        } 
        if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
          if(data.sessionLangStartDate && data.sessionLangEndDate && data.sessionMathStartDate && data.sessionMathEndDate) {
            this.verifyPJSchedulingConditions(moment(data.sessionLangStartDate), moment(data.sessionLangEndDate), moment(data.sessionMathStartDate), moment(data.sessionMathEndDate));
          }
        }

        return this.auth
          .apiPatch(this.routes.SCHOOL_ADMIN_SESSION, record.id, payload, this.configureQueryParams())
          .then((session: any) => {
            record['startTime'] = this.getDate(payload.scheduled_time[0]);
            if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
              record['startTime'] = this.getPJDate(payload.scheduled_time[0]) <= this.getPJDate(payload.scheduled_time[2]) ? this.getPJDate(payload.scheduled_time[0]) : this.getPJDate(payload.scheduled_time[2]);
            }
            if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
              if(record.session_a && record.session_a.caption == 'A') {
                record.session_a.datetime_start = this.getDate(payload.scheduled_time[0]);
                if(record.session_b && record.session_b.caption == 'B') {
                  record.session_b.datetime_start = this.getDate(payload.scheduled_time[1]);
                }
              }
            }
            if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
              if(record.session_a && record.session_a.caption == 'A') {
                record.session_a.datetime_start = this.getPJDate(payload.scheduled_time[0]);
                record.session_a.datetime_end = this.getPJDate(payload.scheduled_time[1]);
                let updatedStatus = record.status;
                if (record.session_a.datetime_start) {
                  updatedStatus = this.detectSessionStatus(record.session_a.datetime_start);
                }
                if(record.session_b && record.session_b.caption == '1') {
                  record.session_b.datetime_start = this.getPJDate(payload.scheduled_time[2]);
                  record.session_b.datetime_end = this.getPJDate(payload.scheduled_time[3]);
                  if((updatedStatus === 'pending' || updatedStatus === null) && record.session_b.datetime_start){
                    updatedStatus = this.detectSessionStatus(record.session_b.datetime_start);
                  }
                }
                if(record.status !== updatedStatus){
                  record.status = updatedStatus;
                }
              }
            }
            this.logSessionBTime(session.test_session_id, data)
            alert(this.lang.tra('msg_session_edit_success'))
            this.pageModal.closeModal()
          })
          .catch(error =>{
            if (error.message === 'MSG_ADMIN_WINDOW_WARNING') {
              this.handleAdminWindowWarningError(windowStartEndDate.windowStart, windowStartEndDate.windowEnd,);
            }
            else if (error.message === 'MSG_ASSES_TIME_INVALID_WARNING') {
              alert(this.lang.tra('sa_asses_time_invalid'));
            }else if (error.message === 'MSG_INVALID_SCHEDULE_TIME'){
              this.loginGuard.disabledPopup(this.lang.tra('msg_invalid_schedule_time'));
            }
          })  
      }
      else{
        alert(this.lang.tra('msg_session_select'));
      }
    }
    this.isActive = false;
  }

  private detectSessionStatus(startDate: string, extractDate: boolean = true): string {
    if (!startDate) {
      return null;
    }
    const processedDate = extractDate ? moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD') : moment(startDate).utc().format('YYYY-MM-DD') ;
    const now = moment().format('YYYY-MM-DD');
    if (processedDate <= now) {
      return 'active';
    }
    return 'pending';
  }

  getSelectedSessions(throwNullSet: boolean = false) {
    const sessionSelector = this.visibleSessions();

    const sessions: ISession[] = sessionSelector.filter((session) => session.__isSelected);
    if (throwNullSet && sessions.length === 0) {
      alert(this.lang.tra('msg_session_select'));
      throw new Error(this.lang.tra('msg_account_select'));
    }
    return sessions;
  }

  visibleSessions() {
    return this.sessionsTable.getCurrentPageData();
  }

  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);

    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9DemoData.schoolData;
    this.currentClassFilter = filterId;
    this.sessionsTable.activeFilters['slug'] = null;
    if (this.currentClassFilter) {
      if(this.currentClassFilter === ClassFilterId.G9) {
        this.sessionsTable.activeFilters['slug'] = { mode: FilterSettingMode.VALUE, config: { value: ClassFilterId.G9 } }
        this.isPrivateSchool && payment_req_g9 ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.OSSLT) {
        this.sessionsTable.activeFilters['slug'] = { mode: FilterSettingMode.VALUE, config: { value: ClassFilterId.OSSLT } }
        this.isPrivateSchool && payment_req_osslt ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.Primary) {
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.Junior) {
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
    }
    this.sessionsTable.refreshFilters();
  }

  setTestWindowFilter(tw){
    this.currentTestWindow = tw;
    this.sessionsTable.activeFilters['testWindowFilter'] = {mode: FilterSettingMode.VALUE, config: { value: this.currentTestWindow } }
    this.sessionsTable.refreshFilters();
  }

  filterSessionTestWindow(session: ISession): boolean {
    return this.g9DemoData.filterSessionTestWindow(session, this.currentTestWindow.id);
  }

  validateAssesment(config, fromEdit = false){
    let sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate, sessionAStartDate, sessionAStartTime, sessionBStartDate, sessionBStartTime; 
    const assessment = config.payload.data;
    const classId = fromEdit ? config.payload.record.classroom_id : assessment.classId;
    let isLanguageVisible = true;
    const g9AndOssltAssessment = [ASSESSMENT.G9_SAMPLE, ASSESSMENT.G9_OPERATIONAL, ASSESSMENT.OSSLT_SAMPLE, ASSESSMENT.OSSLT_OPERATIONAL];
    const pjAssessment = [ASSESSMENT.PRIMARY_SAMPLE, ASSESSMENT.PRIMARY_OPERATIONAL, ASSESSMENT.JUNIOR_SAMPLE, ASSESSMENT.JUNIOR_OPERATIONAL];
    if(fromEdit){
      isLanguageVisible = this.isLanguageVisibleForFIClass(Number(classId));
    }
    const slug = fromEdit ? config.payload.record.slug : assessment.slug;
    const schedule = fromEdit ? "LATER" : assessment.schedule;
    if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
      sessionLangStartDate = assessment.sessionLangStartDate;
      sessionLangEndDate = assessment.sessionLangEndDate;
      sessionMathStartDate = assessment.sessionMathStartDate;
      sessionMathEndDate = assessment.sessionMathEndDate;
    } else {
      sessionAStartDate = assessment.sessionAStartDate;
      sessionAStartTime = assessment.sessionAStartTime;
      sessionBStartDate = assessment.sessionBStartDate;
      sessionBStartTime = assessment.sessionBStartTime;
    }
    //const startTime = moment("12:15 AM", ["h:mm A"]).format("HH:mm");
    if(classId == undefined){
      alert(this.lang.tra('sa_asses_class_invalid'));
      return false;
    }
    if(slug == undefined){
      alert(this.lang.tra('sa_asses_type_invalid'));
      return false;
    }
    if(schedule == undefined || schedule == ''){
      alert(this.lang.tra('sa_asses_time_invalid'));
      return false;
    }
    const isInValid = (input): boolean => {
      if (input === null || input === undefined) {
        return true;
      }
      return false;
    }
    if(schedule == 'LATER') {
      const isG9OrOssltAssessment = g9AndOssltAssessment.includes(slug);
      const isPJAssessment = pjAssessment.includes(slug);
      const isG9Sample = slug.includes(ASSESSMENT.G9_SAMPLE)
      if(isG9Sample && (isInValid(sessionAStartDate) || isInValid(sessionAStartTime))){
        alert(this.lang.tra('sa_asses_time_invalid'));
        return false;
      }
      if(isG9OrOssltAssessment && !isG9Sample && (isInValid(sessionAStartDate) || isInValid(sessionAStartTime) || isInValid(sessionBStartDate) || isInValid(sessionBStartTime))) {
        alert(this.lang.tra('sa_asses_time_invalid'));
        return false;
    }
      if(isPJAssessment && (isInValid(sessionLangStartDate) && isInValid(sessionLangEndDate) && isInValid(sessionMathStartDate) && isInValid(sessionMathEndDate))) {
          if(!isLanguageVisible && this.isActive){
            return false;
          } else {
            alert(this.lang.tra('sa_asses_time_invalid'));
            return false;
          }
      } 
    }
    return true;
  }

  getTotalStudent(session:any){
    const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == session.classroom_id)
    if(classroom != undefined){
      return classroom.students
    }
    return 0;
  }

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) > new Date ()
    } 
  }

  /**
   * Indicates whether current test window is registration unlocked
   * @returns {boolean} true - if current tw is present and not locked
   */
  isCurrentTestWindowRegUnlocked() {
    if(!this.currentTestWindow) return false
    
    return !this.currentTestWindow.reg_locked
  }

  onClickInvigilate(session) {
    const class_id = session.classroom_id;
    const schl_group_id = this.g9DemoData.schoolData.group_id;
    this.auth.apiPatch(this.routes.SCHOOL_ADMIN_INVIGILATE_CLASS, 0, {class_id, schl_group_id}, this.configureQueryParams())
    .then(result =>{
      this.g9DemoData.setIsFromSchoolAdmin(true)
      const queryParams = { ...this.route.snapshot.queryParams};
      const route = `/${this.lang.c()}/educator/assessment/${session.classroom_id}/${session.id}/`;
      this.router.navigate([route], {
        relativeTo: this.route,
        queryParams
      });
    });
  }

  isLanguageVisibleForFIClass(classroomId): boolean {
    const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == classroomId)
    const isFIClass = classroom.is_fi == 1 ? true : false;
    if(this.g9DemoData.schoolDist[0].fi_option === 'C' && isFIClass) {  
      return false;
    }
    return true;
  }

  getAssessmentType(type_slug){
    switch (type_slug){
      case 'PRIMARY_SAMPLE':
        return this.lang.tra(G9_SLUG_TO_CAPTION.PRIMARY_SAMPLE)
      case 'PRIMARY_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.PRIMARY_OPERATIONAL)
      case 'JUNIOR_SAMPLE':
        return this.lang.tra(G9_SLUG_TO_CAPTION.JUNIOR_SAMPLE)
      case 'JUNIOR_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.JUNIOR_OPERATIONAL)
      case 'G9_SAMPLE':
        return this.lang.tra(G9_SLUG_TO_CAPTION.G9_SAMPLE)
      case 'G9_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.G9_OPERATIONAL)
      case 'OSSLT_SAMPLE':
        return this.lang.tra(G9_SLUG_TO_CAPTION.OSSLT_SAMPLE)
      case 'OSSLT_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.OSSLT_OPERATIONAL)
      default:
        return ''  
    }
  }

  showRefundPolicy(){
    const config = {};
    this.pageModal.newModal({
      type: PurchaseModal.PURCHASE_REFUND_POLICY,
      config,
      finish: () => { }
    })
  }
}
