import { Component, EventEmitter, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { RoutesService } from '../../api/routes.service';
import {LangService} from "../../core/lang.service";
import {PageModalController, PageModalService} from '../../ui-partial/page-modal.service';
import {ClassFilterId, MySchoolService} from '../my-school.service';
import {
  boardTechReadinessCheck,
  boardTechReadinessCheckJunior,
  boardTechReadinessCheckOsslt,
  boardTechReadinessCheckPrimary,
  techReadiGoMode,
  techReadiGoModeJunior,
  techReadiGoModeOsslt,
  techReadiGoModePrimary
} from './data/checklist';
import {
  ICheckListItem,
  IEgressIPAddress,
  IITContact,
  ITContactKey,
  SecureLinkSlug,
  TechReadinessModal
} from './data/types';
import {composeChecklist} from './data/util';
import {G9DemoDataService} from "../g9-demo-data.service";
import {LoginGuardService} from "../../api/login-guard.service";
import { ActivatedRoute} from "@angular/router";
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { LDBConfigLevel, LDBModal } from 'src/app/ui-partial/modal-ldb-config/modal-ldb-config.component';

export enum CheckListTableId {
  checklistItems = 'checklist-items',
  checklistItems2 = 'checklist-items-2',
  checklistItems3 = 'checklist-items-3',
}

@Component({
  selector: 'sa-tech-readiness',
  templateUrl: './sa-tech-readiness.component.html',
  styleUrls: ['./sa-tech-readiness.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SaTechReadinessComponent implements OnInit {
  @Output() readinessStatus = new EventEmitter();
  @Output() onSetClassFilter = new EventEmitter();

  disable: boolean = true;
  isLoaded = false;
  constructor(
    public lang: LangService,
    private routes: RoutesService,
    private pageModalService: PageModalService,
    public mySchool: MySchoolService,
    private loginGuard: LoginGuardService,
    private g9DemoData: G9DemoDataService,
    protected route: ActivatedRoute,
  ) { }

  TechReadinessModal = TechReadinessModal
  checklist //= checklist;
  boardTechReadinessConfirmed: boolean = false;
  warnBoardTechReadiness: boolean = false;
  techReadinessConfirmed: boolean = false;
  pageModal: PageModalController;
  currentSecureLink;
  currentClassFilter: ClassFilterId;
  isLoadingChecklist: boolean;

  schoolGroupId;

  isPrivateSchool: boolean;
  schoolType: string;
  isKioskApproved: boolean;
  egressIPList: IEgressIPAddress[] = [];
  ITContacts: Map<ITContactKey, IITContact> = new Map();

  list: ICheckListItem[]; // = SCHOOL_ADMIN_TECH_READ_CHECKLIST (Before Assessment Window)
  list_prior:ICheckListItem[]; // = SCHOOL_ADMIN_TECH_READ_CHECKLIST (Prior to Administering the Assessment)
  list_after:ICheckListItem[]; // = SCHOOL_ADMIN_TECH_READ_CHECKLIST (After to Administering the Assessment)

  CheckListTableId = CheckListTableId;
  checklist_items_collapse = true;
  checklist_items2_collapse = true;
  checklist_items3_collapse = true;
  payment_req_g9 : boolean = false;
  payment_req_osslt : boolean = false;
  payment_req_pj : boolean = false;

  LDBModal = LDBModal;

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    this.schoolType = this.g9DemoData.schoolData["school_type"];
    this.isKioskApproved = this.g9DemoData.schoolData["is_kiosk_approved"];
    this.payment_req_g9 = this.g9DemoData.schoolData["payment_req_g9"];
    this.payment_req_osslt = this.g9DemoData.schoolData["payment_req_osslt"];
    this.payment_req_pj = this.g9DemoData.schoolData["payment_req_pj"];
    this.route.queryParams.subscribe(queryParams => {
      if (queryParams.school) {
        this.schoolGroupId = queryParams.school 
      }
    })
  }

  loadChecklists() {
    this.checklist = null;
    this.isLoadingChecklist = true;
    this.boardTechReadinessConfirmed = false;
    this.warnBoardTechReadiness = false
    // this.mySchool.sub().subscribe(() => { })
    this.mySchool.loadChecklistStates()
      .then(state => {
        this.checklist = state;
        this.verifyChecklistCompletion();
        this.isLoadingChecklist = false;
      })
    this.getIpAddresses();
    this.getITContacts();
  }

  isDisabledForSchool(item) {
    if(!item.disableForSchoolType) return false;
    
    if(item.disableForSchoolType.includes(this.schoolType)) {
      if(!this.checklist[item.id]) {
        this.toggleCheckList(item.id)
        this.checklist[item.id] = true;
      }
      
      return true;
    }
  }

  isBoarchCheckItem(item: ICheckListItem) {
    return (item.id === 'tech_redi_school_board_check_pj_primary'||item.id === 'tech_redi_school_board_check_pj_junior'||item.id === 'tech_redi_school_board_check' || item.id === 'tech_redi_school_board_check_osslt')
  }

  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);

    this.currentClassFilter = filterId;
    if (this.currentClassFilter && !this.isPrivateSchool) {
      switch (this.currentClassFilter) {
        case ClassFilterId.Primary: 
          this.list = composeChecklist(require('./data/by-filter/primary.json'), false); 
          this.list_prior = composeChecklist(require('./data/by-filter/primary_prior.json'), false);
          this.list_after = composeChecklist(require('./data/by-filter/primary_after.json'), false);
        break;
        case ClassFilterId.Junior: 
          this.list = composeChecklist(require('./data/by-filter/junior.json'), false); 
          this.list_prior = composeChecklist(require('./data/by-filter/junior_prior.json'), false);
          this.list_after = composeChecklist(require('./data/by-filter/junior_after.json'), false);
          break;
        case ClassFilterId.G9: this.list = composeChecklist(require('./data/by-filter/g9.json'), false); break;
        case ClassFilterId.OSSLT: this.list = composeChecklist(require('./data/by-filter/osslt.json'), false); break;
      }
      this.loadChecklists();
    }
    const updateListIfKioskApproved = (listJson: any) => {
      //let newList = {...listJson}
      let newList = JSON.parse(JSON.stringify(listJson));
      if (!this.isKioskApproved) {
        let configInd = listJson.slugs.findIndex((config) => config.id === 'tech_redi_device_1');
        newList.slugs[configInd].id = 'tech_redi_device';
      }
      return newList;
    }
    if (this.currentClassFilter && this.isPrivateSchool) {
      let listJson;
      switch (this.currentClassFilter) {
        case ClassFilterId.Primary: 
          this.list = composeChecklist(require('./data/by-filter/primary_private.json'), true);
          if(!this.payment_req_pj){
            this.list = this.list.filter(x => x.id !== 'tech_redi_payments_review_pj_primary' && x.id !== 'tech_redi_payments_pj_primary');
          }
          this.list_prior = composeChecklist(require('./data/by-filter/primary_prior.json'), false);
          this.list_after = composeChecklist(require('./data/by-filter/primary_private_after.json'), false);
        break;
        case ClassFilterId.Junior: 
          this.list = composeChecklist(require('./data/by-filter/junior_private.json'), true);
          if(!this.payment_req_pj){
            this.list = this.list.filter(x => x.id !== 'tech_redi_payments_review_pj_junior' && x.id !== 'tech_redi_payments_pj_junior');
          }
          this.list_prior = composeChecklist(require('./data/by-filter/junior_prior.json'), false);
          this.list_after = composeChecklist(require('./data/by-filter/junior_private_after.json'), false); 
        break;
        case ClassFilterId.G9: 
          listJson = updateListIfKioskApproved(require('./data/by-filter/g9_private.json'));
          this.list = composeChecklist(listJson, true); 
          if(!this.payment_req_g9){
            this.list = this.list.filter(x => x.id !== 'tech_redi_payments_review' && x.id !== 'tech_redi_payments');
          }
        break;
        case ClassFilterId.OSSLT: 
          listJson = updateListIfKioskApproved(require('./data/by-filter/osslt_private.json'));
          this.list = composeChecklist(listJson, true); 
          if(!this.payment_req_osslt){
            this.list = this.list.filter(x => x.id !== 'tech_redi_payments_review_osslt' && x.id !== 'tech_redi_payments_osslt');
          }
        break;
      }
      if(this.isKioskApproved &&  (this.currentClassFilter == ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior) ){
        const tech_redi_device = this.list.find( item => item.id.includes("tech_redi_device"))
        tech_redi_device.links.push(
          {
            linkUrl: 'https://chrome.google.com/webstore/detail/kioåsk/afhcomalholahplbjhnmahkoekoijban',
            linkCaption: 'tech_redi_device_kiosk_ext',
          }, 
          {
            slug: 'KIOSK_POLICY',
            isSecure: true,
            linkCaption: 'tech_redi_device_kiosk_cfg',
            descSlug: 'txt_dl_kiosk_policy',
          }, 
          {
            slug: 'KIOSK_POLICY_CREDS',
            isSecure: true,
            linkCaption: 'tech_redi_device_kiosk_pass',
          }
        )
      }
      this.loadChecklists();
    }

  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }

  parseSecureLinkPayload(slug: SecureLinkSlug, payload: any) {
    switch (slug) {
      case SecureLinkSlug.SEB_CONFIG: return { link: payload.seb.config };
      case SecureLinkSlug.SEB_CONFIG_PASS: return { text: payload.seb.creds };
      case SecureLinkSlug.KIOSK_POLICY: return { link: payload.kiosk.config };
      case SecureLinkSlug.KIOSK_POLICY_CREDS: return { text: payload.kiosk.creds };
    }
  }

  processSecureLink(slug: SecureLinkSlug, caption: string, descSlug: string) {
    this.mySchool
      .fetchSecureLink(this.routes.SCHOOL_ADMIN_LOCK_DOWN_INFO)
      .then(res => {
        const config = {
          ... this.parseSecureLinkPayload(slug, res),
          caption,
          descSlug
        };
        this.pageModal.newModal({
          type: this.TechReadinessModal.NEW,
          config,
          finish: () => this.pageModal.closeModal()
        });
      })
  }

  processInSecureLink(slug: string ) {
    const defaultURL = this.lang.tra(slug)
    if(this.schoolGroupId){
      return defaultURL +'?school='+this.schoolGroupId
    }
    return defaultURL;
  }

  openChecklistModal(type: string) {
    if (type === TechReadinessModal.EGRESS_IP_ADDRESSES) {
      this.openEgressIPAddressesModal()
    } else if (type === TechReadinessModal.IT_CONTACTS) {
      this.openITContactsModal()
    }
    if(type === LDBModal.LDB_CONFIG_MODAL) {
      return this.viewLdbConfigModal()
    }
  }

  private viewLdbConfigModal() {
    this.pageModal.newModal({
      type: LDBModal.LDB_CONFIG_MODAL,  
      config: {
        currentLevel: LDBConfigLevel.SCHOOL,
        query: {
          school_id: this.g9DemoData.schoolData.id,
          school_board_id: (<any>this.g9DemoData.schoolDist[0]).id,
        }
      },
      finish: () => {}
    });
  }

  viewPaymentAgreementModalStart() {
    const config = {}; 
    this.pageModal.newModal({
      type: TechReadinessModal.VIEW_PAYMENT_AGREEMENT,
      config,
      finish: this.viewPaymentAgreementModalFinish
    })
  }

  viewPaymentAgreementModalFinish= () => {
    this.pageModal.closeModal();
  }

  openEgressIPAddressesModal() {
    const config = {};
    this.pageModal.newModal({
      type: this.TechReadinessModal.EGRESS_IP_ADDRESSES,
      config,
      finish: () => this.closeEgressIPAddressesModal()
    });
  }

  closeEgressIPAddressesModal = () => {
    this.pageModal.closeModal()
    if (this.egressIPList.length === 0) {
      const egressIPSlug = 'tech_redi_outgoing_1_osslt';
      if (this.checklist[egressIPSlug] === true) {
        this.toggleCheckList(egressIPSlug)
      }
    }
  }

  getIpAddresses() {
    this.mySchool.getIpAddresses()
      .then(res => {
        this.egressIPList = res;
      })
  }

  getITContacts() {
    this.mySchool.getITContacts()
      .then(res => {
        if (res && res.length > 0) {
          const ITContacts = JSON.parse(res[0].meta)
          this.processItContacts(ITContacts)
        }
      })
  }

  processItContacts(contacts:any) {
    for (const key in ITContactKey) {
      const val = ITContactKey[key]
      if (contacts[val]) {
        this.ITContacts.set(val, contacts[val])
      }
    }
  }

  openITContactsModal() {
    //console.log('openITContactsModal')
    const config = {};
    this.pageModal.newModal({
      type: this.TechReadinessModal.IT_CONTACTS,
      config,
      finish: () => this.closeITContactsModal()
    });
  }

  closeITContactsModal() {
    this.pageModal.closeModal()
    const isBlank = (contact: IITContact): boolean => !contact.name && !contact.email && !contact.phone
    if (isBlank(this.ITContacts.get(ITContactKey.PRIMARY)) && isBlank(this.ITContacts.get(ITContactKey.SECONDARY))) {
      const slug = 'tech_redi_incident_osslt';
      if (this.checklist[slug] === true) {
        this.toggleCheckList(slug)
      }
    }
  }

  verifyChecklistCompletion = () => {
    let isAnyUnchecked = false;
    console.log(this.currentClassFilter)
    if (this.currentClassFilter === ClassFilterId.Primary) {
      isAnyUnchecked = this.processReadinessStatus(techReadiGoModePrimary, boardTechReadinessCheckPrimary)
    }
    else if (this.currentClassFilter === ClassFilterId.Junior) {
      isAnyUnchecked = this.processReadinessStatus(techReadiGoModeJunior, boardTechReadinessCheckJunior)
    }
    else if (this.currentClassFilter === ClassFilterId.OSSLT) {
      if (this.isPrivateSchool) {
        isAnyUnchecked = this.processPrivateReadinessStatus()
      }
      else {
        isAnyUnchecked = this.processReadinessStatus(techReadiGoModeOsslt, boardTechReadinessCheckOsslt)
      }
    }
    else {
      isAnyUnchecked = this.processReadinessStatus(techReadiGoMode, boardTechReadinessCheck)
    }
    this.techReadinessConfirmed = !isAnyUnchecked; // todo, this is still poorly named

    this.readinessStatus.emit(this.techReadinessConfirmed && this.boardTechReadinessConfirmed)
    this.isLoaded = true;
  }
  processPrivateReadinessStatus() {
    let isAnyUnchecked = false;
    this.list.forEach(listEntry => {
      if (!this.checklist[listEntry.id] 
          // && (listEntry.id !== 'tech_redi_payments_osslt')
          // && (listEntry.id !== 'tech_redi_payments_review_osslt')
        ) {
        console.log("this is unchecked", listEntry.id)
        isAnyUnchecked = true;
      }
    })
    if (!isAnyUnchecked) {
      this.boardTechReadinessConfirmed = true;
    }
    return isAnyUnchecked;
  }
  processReadinessStatus(goModeFlag, boardReadiCheck) {
    let isAnyUnchecked = false;
    this.list.forEach(listEntry => {
      //console.log('verifyChecklistCompletion', listEntry.id, this.checklist[goModeFlag])
      if (this.checklist[boardReadiCheck]) {
        this.boardTechReadinessConfirmed = true;
        this.warnBoardTechReadiness = false;
      }
      else if (this.checklist[goModeFlag]) {
        this.warnBoardTechReadiness = true;
        this.boardTechReadinessConfirmed = false;
      }
      if (!this.checklist[listEntry.id] 
           && (listEntry.id !== 'tech_redi_school_board_check_pj_primary') 
           && (listEntry.id !== 'tech_redi_school_board_check_pj_junior') 
           && (listEntry.id !== 'tech_redi_school_board_check') 
           && (listEntry.id !== 'tech_redi_school_board_check_osslt')
          //  && (listEntry.id !== 'tech_redi_payments_pj_primary') 
          //  && (listEntry.id !== 'tech_redi_payments_pj_junior') 
          //  && (listEntry.id !== 'tech_redi_payments')
          //  && (listEntry.id !== 'tech_redi_payments_review_pj_primary')
          //  && (listEntry.id !== 'tech_redi_payments_review_pj_junior')
          //  && (listEntry.id !== 'tech_redi_payments_review')
          ) {
        isAnyUnchecked = true;
        this.warnBoardTechReadiness = false;
        this.checklist[goModeFlag] = false;
      }
    })
    return isAnyUnchecked;
  }
  toggleCheckList(slug: string) {
    if ( slug === 'tech_redi_outgoing_1_primary'|| slug === 'tech_redi_outgoing_1_junior'|| slug === 'tech_redi_outgoing_1' || slug === 'tech_redi_outgoing_1_osslt') {
      if (!this.checklist[slug] && this.egressIPList.length === 0) {
        this.loginGuard.quickPopup(this.lang.tra('msg_no_ip'));
        return;
      }
    }
    if (slug === 'tech_redi_incident_osslt') {
      const addedITContacts = ():boolean => {
        const primary = this.ITContacts.get(ITContactKey.PRIMARY)
        const secondary = this.ITContacts.get(ITContactKey.PRIMARY)
        return ([primary, secondary].filter(c => c && c.name && c.email).length !== 0)
      }
      if (!this.checklist[slug] && !addedITContacts()) {
        this.loginGuard.quickPopup(this.lang.tra('msg_add_it_contact'));
        return;
      }
    }
    const newVal = !this.checklist[slug];
    this.mySchool
      .toggleTRChecklist(slug, newVal)
      .then(() => {
        this.checklist[slug] = newVal;
        this.verifyChecklistCompletion()
      });
  }

  renderChecklistId(item, option) {
    return item.id + '/' + option.slug
  }

  isConfirmAlert(): boolean {
    if (this.cModal()) {
      const config = this.cModal().config;
      if (config && config.payload) {
        return !!config.payload.useConfirmAlert
      }
    }
    return false;
  }

  getConfirmationMessage(): string {
    if (this.isConfirmAlert()) {
      return this.cModal().config.payload.confirmationMessage
    }
  }
  isPJ(){
    return (this.currentClassFilter === ClassFilterId.Primary||this.currentClassFilter === ClassFilterId.Junior)
  }
  isG9(){
    return this.currentClassFilter === ClassFilterId.G9;
  }
  isOsslt(){
    return this.currentClassFilter === ClassFilterId.OSSLT;
  }
  expandCollapseTable(id){
    switch(id){
      case CheckListTableId.checklistItems:
        this.checklist_items_collapse = !this.checklist_items_collapse
        break;
      case CheckListTableId.checklistItems2:
        this.checklist_items2_collapse = !this.checklist_items2_collapse
        break;
      case CheckListTableId.checklistItems3:
        this.checklist_items3_collapse = !this.checklist_items3_collapse
        break;
      default:
        break;      
    }
  }
}
