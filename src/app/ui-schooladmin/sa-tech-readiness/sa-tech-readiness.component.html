<div class="tech_readi_component">
  <div *ngIf="!isPrivateSchool" class="dont-print filter-panels">
    <filter-toggles 
      [state]="mySchool.getClassFilterToggles()"
      (id)="setClassFilter($event)"
    ></filter-toggles>
    <div *ngIf="!currentClassFilter">
      <tra-md slug="txt_guide_select_toggle_for_checklist"></tra-md>
    </div>
  </div>
  <div *ngIf="isPrivateSchool" class="dont-print filter-panels">
    <filter-toggles 
      [state]="mySchool.getPrivateSchoolClassFilterToggles()"
      (id)="setClassFilter($event)"
    ></filter-toggles>
    <div *ngIf="!currentClassFilter">
      <tra-md slug="txt_guide_select_toggle_for_checklist"></tra-md>
    </div>
  </div>
  <div *ngIf="isLoadingChecklist">
    <tra slug="lbl_loading_checklist"></tra>
  </div>
  <div *ngIf="(currentClassFilter || isPrivateSchool) && !isLoadingChecklist">
    <div *ngIf="!isPrivateSchool">
      <div class="tech-readi-status">
        <p *ngIf="!isPJ()"><tra slug="txt_board_network_status_1"></tra></p>&nbsp;
        <p *ngIf="isPJ()"><tra slug="txt_board_network_status_pj"></tra></p>&nbsp;
        <div *ngIf='isLoaded && boardTechReadinessConfirmed && !isPJ()' class='success-msg'><tra slug="lbl_board_network_status_completed"></tra></div>
        <div *ngIf='isLoaded && boardTechReadinessConfirmed && isPJ()' class='success-msg'><tra slug="lbl_board_network_status_completed_pj"></tra></div>
        <div *ngIf='isLoaded && !boardTechReadinessConfirmed && !warnBoardTechReadiness' class='failure-msg'><tra slug="lbl_board_network_status_not_completed"></tra></div>
        <div *ngIf='isLoaded && !boardTechReadinessConfirmed && warnBoardTechReadiness' class='warning-msg'><tra slug="lbl_board_network_status_issue"></tra></div>
      </div>
      <br>
      <p *ngIf="!isPJ()"><tra slug="txt_board_network_status_2"></tra></p>
      <p *ngIf="isPJ()"><tra slug="pj_txt_board_network_status_2"></tra></p>
      <br>
      <p *ngIf="!isPJ()"><tra slug="sa_tech_readiness_header_1"></tra></p>
      <p *ngIf="isPJ()"><tra slug="pj_tech_readiness_header_1"></tra></p>
      <br>
      <p *ngIf="!isPJ()"><tra slug="sa_tech_readiness_header_2"></tra></p>
      <p *ngIf="isPJ()"><tra slug="pj_tech_readiness_header_2"></tra></p>
      <p *ngIf="isPJ()" style="font-size: 1.5em;"><br><b><tra slug="sa_tech_readiness_table_title"></tra></b></p>
    </div>
    
    <div *ngIf="isPrivateSchool">
      <p *ngIf="!isPJ()"><tra slug="private_tech_readiness_header_1"></tra></p>
      <p *ngIf="isPJ()"><tra slug="private_tech_readiness_header_1_pj"></tra></p>
      <br>
      <p *ngIf="!isPJ()"><tra slug="private_tech_readiness_header_2"></tra></p>
      <p *ngIf="isPJ()"><tra slug="private_tech_readiness_header_2_pj"></tra></p>
      <h2 *ngIf="!isPJ()"><tra slug="private_tech_readiness_title"></tra></h2>
    </div>

    <div *ngIf="isPJ()" style="font-size: 1.1em;"><tra-md slug="lbl_pj_expand_checklist"></tra-md></div>

    <table id="checklist-items">
      <tr *ngIf="isPJ()"><td colspan="3" style="font-size: 1.2em;">
        <button *ngIf="checklist_items_collapse" class="fa fa-caret-right collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems)"></button>
        <button *ngIf="!checklist_items_collapse" class="fa fa-caret-down collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems)"></button>
        <b><tra slug="sa_tech_readiness_before_assessment_window"></tra></b>
      </td></tr>
      <div *ngIf="!isPJ() || !checklist_items_collapse">
        <tr *ngFor="let item of list; let listIndex = index;">
          <ng-container *ngIf="!isDisabledForSchool(item)">
          <td style="width:1em;"> 
            <img *ngIf="isBoarchCheckItem(item) && warnBoardTechReadiness" src="https://bubo.vretta.com/vea/platform/vea-web-client/uploads/29926bc7d0084cf3af89d5085b43c57b/icons8-warning-96.png"/>
            <check-toggle *ngIf="isBoarchCheckItem(item) && !warnBoardTechReadiness" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)" [disabled]="true"></check-toggle> 
            <check-toggle *ngIf="!isBoarchCheckItem(item)" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)"></check-toggle> 
          </td>
          <td>
            <div><tra-md [slug]="item.caption"></tra-md></div>
            <div *ngIf="item.selectionCaption" style="margin-top:1em;">
              <div *ngFor="let caption of item.selectionCaption;" [ngStyle]="((caption.isForOsslt && isOsslt()) || (caption.isForG9 && isG9())) ? {} : {'display': 'none'}">
                <tra-md [slug]="caption.heading"></tra-md>
                <div class="select is-multiple is-small"  style="margin-top:0.5em;">
                  <div class="checkbox-option" *ngFor="let option of caption.selectionOptions">
                    <check-toggle  [isChecked]="checklist[renderChecklistId(item, option)]" (toggle)="toggleCheckList(renderChecklistId(item, option))"></check-toggle> 
                    <div class="checklist-label">
                      <tra-md (click)="toggleCheckList(renderChecklistId(item, option))" [slug]="option.caption"></tra-md>
                    </div>
                  </div>
                  <br/>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div *ngIf="item.links">
              <div *ngFor="let link of item.links; let i = index;" [ngStyle]="listIndex==6 && i==0 && !isPrivateSchool ? {'margin-top': '6.5rem'} : {}">
                <button *ngIf="link.modal" class="button is-small is-modal" [disabled]="link.disabled" (click)="openChecklistModal(link.modal)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <a *ngIf="!link.isSecure && !link.modal && !link.hyperLinkModal" [class.disabled]="link.disabled" [href]="processInSecureLink(link.linkUrl)" target="_blank"> 
                  <tra [slug]="link.linkCaption"></tra> 
                </a>         
                <button *ngIf="link.isSecure" class="button is-small" [disabled]="link.disabled" (click)="processSecureLink(link.slug, link.linkCaption, link.descSlug)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <div *ngIf="link.hyperLinkModal">
                  <div (click)="viewPaymentAgreementModalStart()" [class.disabled]="link.disabled"  style="color:#154997;; font-weight:bold; cursor: pointer;">
                    <tra [slug]="link.linkCaption"></tra>
                  </div>
                </div>
              </div>
            </div>
          </td>
          </ng-container>
        </tr>
      </div>
    </table>
    <br/>

    <div class='sa-tech-readiness-confirmation-div'>
      <div>
      </div>
      <div *ngIf="!isPrivateSchool">
        <div *ngIf='techReadinessConfirmed  && boardTechReadinessConfirmed && !warnBoardTechReadiness && !isPJ()'>
          <div class='success-msg'><tra slug="sa_tech_readiness_success_msg"></tra></div>
        </div>
        <div *ngIf='techReadinessConfirmed  && boardTechReadinessConfirmed && !warnBoardTechReadiness && isPJ()'>
          <div class='success-msg'><tra slug="sa_tech_readiness_success_msg_pj"></tra></div>
        </div>
        <div *ngIf='isLoaded && !boardTechReadinessConfirmed && !warnBoardTechReadiness'>
          <div class='failure-msg'><tra slug="msg_board_network_status_footer"></tra></div>
        </div>
        <div *ngIf='isLoaded && !boardTechReadinessConfirmed && warnBoardTechReadiness'>
          <div class='warning-msg'><tra slug="sa_tech_readiness_error_msg"></tra></div>
        </div>
      </div>
      <div *ngIf="isPrivateSchool">
        <div *ngIf="isLoaded && !techReadinessConfirmed">
          <div class='failure-msg'><tra slug="msg_not_technology_ready"></tra></div>
        </div>
        <div *ngIf="isLoaded && techReadinessConfirmed && !isPJ()">
          <div class='success-msg'><tra slug="msg_technology_ready"></tra></div>
        </div>
        <div *ngIf="isLoaded && techReadinessConfirmed && isPJ()">
          <div class='success-msg'><tra slug="sa_tech_readiness_success_msg_pj"></tra></div>
        </div>
      </div>
    </div>
    <br/>
    <table *ngIf="isPJ()" id="checklist-items_2">
      <tr><td colspan="3" style="font-size: 1.2em;">
        <button *ngIf="checklist_items2_collapse" class="fa fa-caret-right collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems2)"></button>
        <button *ngIf="!checklist_items2_collapse" class="fa fa-caret-down collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems2)"></button>
        <b><tra slug="sa_tech_readiness_prior_assessment_window"></tra></b>
      </td></tr>
      <div *ngIf="!isPJ() ||  !checklist_items2_collapse">
        <tr *ngFor="let item of list_prior; let listIndex = index;">
          <td style="width:1em;"> 
            <img *ngIf="isBoarchCheckItem(item) && warnBoardTechReadiness" src="https://bubo.vretta.com/vea/platform/vea-web-client/uploads/29926bc7d0084cf3af89d5085b43c57b/icons8-warning-96.png"/>
            <check-toggle *ngIf="isBoarchCheckItem(item) && !warnBoardTechReadiness" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)" [disabled]="true"></check-toggle> 
            <check-toggle *ngIf="!isBoarchCheckItem(item)" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)"></check-toggle> 
          </td>
          <td>
            <tra-md [slug]="item.caption"></tra-md>
          </td>
          <td>
            <div *ngIf="item.links">
              <div *ngFor="let link of item.links">
                <button *ngIf="link.modal" class="button is-small is-modal" [disabled]="link.disabled" (click)="openChecklistModal(link.modal)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <a *ngIf="!link.isSecure && !link.modal && !link.hyperLinkModal" [class.disabled]="link.disabled" [href]="processInSecureLink(link.linkUrl)" target="_blank"> 
                  <tra [slug]="link.linkCaption"></tra> 
                </a>         
                <button *ngIf="link.isSecure" class="button is-small" [disabled]="link.disabled" (click)="processSecureLink(link.slug, link.linkCaption, link.descSlug)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <div *ngIf="link.hyperLinkModal">
                  <div (click)="viewPaymentAgreementModalStart()" [class.disabled]="link.disabled"  style="color:#154997;; font-weight:bold; cursor: pointer;">
                    <tra [slug]="link.linkCaption"></tra>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </div>  
    </table>
    <br/>

    <table *ngIf="isPJ()" id="checklist-items_3">
      <tr><td colspan="3" style="font-size: 1.2em;">
        <button *ngIf="checklist_items3_collapse" class="fa fa-caret-right collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems3)"></button>
        <button *ngIf="!checklist_items3_collapse" class="fa fa-caret-down collapse" (click)="expandCollapseTable(CheckListTableId.checklistItems3)"></button>
        <b><tra slug="sa_tech_readiness_after_assessment_window"></tra></b>
      </td></tr>
      <div *ngIf="!isPJ() ||  !checklist_items3_collapse">
        <tr *ngFor="let item of list_after; let listIndex = index;">
          <td style="width:1em;"> 
            <img *ngIf="isBoarchCheckItem(item) && warnBoardTechReadiness" src="https://bubo.vretta.com/vea/platform/vea-web-client/uploads/29926bc7d0084cf3af89d5085b43c57b/icons8-warning-96.png"/>
            <check-toggle *ngIf="isBoarchCheckItem(item) && !warnBoardTechReadiness" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)" [disabled]="true"></check-toggle> 
            <check-toggle *ngIf="!isBoarchCheckItem(item)" [isChecked]="checklist[item.id]" (toggle)="toggleCheckList(item.id)"></check-toggle> 
          </td>
          <td>
            <tra-md [slug]="item.caption"></tra-md>
          </td>
          <td>
            <div *ngIf="item.links">
              <div *ngFor="let link of item.links">
                <button *ngIf="link.modal" class="button is-small is-modal" [disabled]="link.disabled" (click)="openChecklistModal(link.modal)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <a *ngIf="!link.isSecure && !link.modal && !link.hyperLinkModal" [class.disabled]="link.disabled" [href]="processInSecureLink(link.linkUrl)" target="_blank"> 
                  <tra [slug]="link.linkCaption"></tra> 
                </a>         
                <button *ngIf="link.isSecure" class="button is-small" [disabled]="link.disabled" (click)="processSecureLink(link.slug, link.linkCaption, link.descSlug)">
                  <tra [slug]="link.linkCaption"></tra>
                </button>
                <div *ngIf="link.hyperLinkModal">
                  <div (click)="viewPaymentAgreementModalStart()" [class.disabled]="link.disabled"  style="color:#154997;; font-weight:bold; cursor: pointer;">
                    <tra [slug]="link.linkCaption"></tra>
                  </div>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </div>  
    </table>

    <div class="custom-modal" *ngIf="cModal()">
      <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="TechReadinessModal.NEW" style="width: 30em; text-align: center;">
            <div *ngIf="cModal().config.text">
              <h3> <tra [slug]="cModal().config.caption"></tra> </h3>
              <div style="white-space: pre;">{{cModal().config.text}}</div>
            </div>
            <div *ngIf="cModal().config.link">
              <div><tra-md [slug]="cModal().config.descSlug"></tra-md></div>
              <a [href]="cModal().config.link" target=_blank><tra [slug]="cModal().config.caption"></tra></a>
            </div>
          </div>
          
          <div *ngSwitchCase="TechReadinessModal.EGRESS_IP_ADDRESSES" style="width: 30em; text-align: center;">
            <sa-tech-readi-egress-ip [savePayload]="cModal().config" saveProp="payload" [IPList]="egressIPList"></sa-tech-readi-egress-ip>
          </div>
          
          <div *ngSwitchCase="TechReadinessModal.IT_CONTACTS" style="width: 30em; text-align: center;">
            <sa-tech-readi-it-contacts [savePayload]="cModal().config" saveProp="payload" [contacts]="ITContacts"></sa-tech-readi-it-contacts>
          </div>

          <div *ngSwitchCase="TechReadinessModal.VIEW_PAYMENT_AGREEMENT" style="width: 65em;">
            <sa-modal-payment-agreement-review [isReview]="true"></sa-modal-payment-agreement-review>
          </div>

          <div *ngSwitchCase="LDBModal.LDB_CONFIG_MODAL" style="width: 80em;">
            <modal-ldb-config [config]="cModal().config" [pageModal]="pageModal"></modal-ldb-config>
          </div>
        <br/>
        <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()" [confirmationMessage]="getConfirmationMessage()" *ngIf="cModal().type !== LDBModal.LDB_CONFIG_MODAL"></modal-footer>
      </div>
    </div>
  </div>
  </div>
</div>