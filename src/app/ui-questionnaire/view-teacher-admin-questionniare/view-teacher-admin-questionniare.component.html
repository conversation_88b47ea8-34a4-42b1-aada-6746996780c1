<test-runner *ngIf="testForm && !isShowingResults" 
  [testFormType]="testForm.currentTestDesign.testFormType"
  [testFormId]="-1" 
  [sectionIndexInit]="testAttemptInfo.section_index"
  [frameWorkTags]="asmtFmrk.tags"
  [questionIndexInit]="testAttemptInfo.question_index" 
  [sectionsAllowed]="testAttemptInfo.sections_allowed"
  [moduleIdInit]="testAttemptInfo.module_id" 
  [testAttemptId]="testAttemptInfo.attemptId"
  [currentTestDesign]="testForm.currentTestDesign" 
  [questionSrcDb]="testForm.questionSrcDb"
  [questionStates]="testForm.questionStates" 
  [testLang]="testForm.testLang" 
  [testTakerName]="testTakerName"
  [isTimeEnabled]="!testForm.currentTestDesign.isTimerDisabled"
  [documentItems]="testForm.currentTestDesign.referenceDocumentPages"
  [helpPageItem]="testForm.currentTestDesign.helpPageId"
  [rubricDownloadLink]="asmtFmrk.rubricDownloadLink"
  [defaultZoomInit]="defaultZoom"
  [asmtFmrk]="asmtFmrk"
  [regularTimeRemaining]="999"
  [testSessionId]="-1"
  [saveQuestion]="saveQuestionResponse"
  [submitTest]="submitTest"
  [postSubmit]="postSubmit"
  [savePosition]="savePosition"
  [forceQuestionsSkippable]="isQuestionnaire"
  (questionTitles)="questionTitleMap = $event"
  [customConfirmTestDialogData]="customConfirmTestDialogData"
  (studentPosition)="setStudentPosition($event)" 
  [isTeacherAdminQuestionnaire] = "isTeacherAdminQuestionnaire"
  (backToMenu)='postSubmit()'
></test-runner>


<div *ngIf="!testForm" style="display:flex; justify-content: center; align-items: center; height:100vh;">
  <div *ngIf="!errorMessage">Loading...</div>
  <div *ngIf="errorMessage">
    <tra slug="admin_teacher_questionnaire_closed"></tra>
    <div style="margin-top:0.5em;">
      <button (click)="goDashboard()" class="button">
        <tra slug="btn_admin_teacher_questionnaire_close"></tra>
      </button>
    </div>
  </div>
</div>