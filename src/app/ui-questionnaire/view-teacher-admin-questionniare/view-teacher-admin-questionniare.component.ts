import { Component, OnInit } from '@angular/core';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { Router, ActivatedRoute } from '@angular/router';
import { EStyleProfile, StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { Subscription } from 'rxjs';
import { ITestDef } from '../../ui-testrunner/sample-questions/data/sections';
import { ITestAttemptRes } from '../../ui-testtaker/view-tt-test-runner/view-tt-test-runner.component';
import { ICustomConfirmTestDialogData } from '../../ui-testrunner/test-runner/test-runner.component';
import { ASSESSMENT } from '../../ui-teacher/data/types';
import { AccountType } from '../../constants/account-types';
import { ElementType, IContentElement, IQuestionConfig, ScoringTypes, getElementWeight } from '../../ui-testrunner/models';
import { StudentDashboardService } from '../../ui-student/student-dashboard.service';
import { StudentAssistiveTechService } from '../../ui-student/student-assistive-tech.service';
import { parseAttemptLoadErrorMessage } from '../../ui-student/view-student-live-assess/util/parse-warning';
import { SchoolTeacherAdminQuestionnaireService } from '../school-teacher-admin-questionnaire.service';
import { G9DemoDataService } from '../../ui-schooladmin/g9-demo-data.service';
import { lzCompressProps } from 'src/app/core/util/lzstring';
import { OnlineOrPaperService } from 'src/app/ui-testrunner/online-or-paper.service';
@Component({
  selector: 'view-teacher-admin-questionniare',
  templateUrl: './view-teacher-admin-questionniare.component.html',
  styleUrls: ['./view-teacher-admin-questionniare.component.scss']
})
export class ViewTeacherAdminQuestionniareComponent implements OnInit {

  constructor(
    public lang: LangService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private routes: RoutesService,
    private auth: AuthService,
    private dash: SchoolTeacherAdminQuestionnaireService,
    private router: Router,
    private route: ActivatedRoute,
    private styleProfile: StyleprofileService,
    private assisTech: StudentAssistiveTechService,
    private whitelabel: WhitelabelService,
    private g9demoService: G9DemoDataService,
    public onlineOrPaper: OnlineOrPaperService
  ) { }

  userInfo;
  routeSub: Subscription;
  queryParamSub: Subscription;
  userSub: Subscription;
  testSessionId: string;
  testSessionSlug: string;
  isShowingResults = false;
  questions: Map<number, any>;
  testTakerName: string;
  questionTitleMap:any;
  testForm: {
    currentTestDesign: ITestDef,
    questionSrcDb: Map<number, any>,
    questionStates: { [key: string]: any },
    testLang: string,
  };
  currentStudentPosition = {
    stageIndex: null,
    questionCaption: null
  };

  testAttemptInfo: ITestAttemptRes;
  public currentWarning = null;
  currentCalculatorState: boolean = false;
  unknownWarningErrorMsg: string;
  currentAttemptId: number;
  isQuestionnaire: boolean = false;
  isTeacherAdminQuestionnaire = true;
  customConfirmTestDialogData: ICustomConfirmTestDialogData;
  interval;
  asmtFmrk;
  defaultZoom:number;

  isWindowFocused: boolean = true;
  
  asmtSlug: ASSESSMENT;
  schl_group_id;
  questionLoaded: boolean = false;
  
  ngOnInit(): void {
    this.routeSub = this.route.params.subscribe(params => {
      this.testForm = null;
      this.testSessionId = params['testSessionId'];

      // this.styleProfile.setStyleProfile("gr9styleprofile.json");

      this.userSub = this.auth.user().subscribe((userInfo: any) => {
        if (userInfo) {
          this.userInfo = userInfo
          this.testTakerName = [userInfo.first_name, userInfo.last_name].join(' ')
          if(this.schl_group_id && !this.questionLoaded){
            this.loadQuestions()
            this.questionLoaded = true;
          }
        }
      })      
    })

    this.queryParamSub = this.route.queryParams.subscribe(queryParams => {
      if (queryParams['defaultZoom']){
        this.defaultZoom = +queryParams['defaultZoom'];
      }
      if (queryParams.school) {
        this.schl_group_id = queryParams.school 
        if(this.userInfo && !this.questionLoaded){
          this.loadQuestions();
          this.questionLoaded = true;
        }
      }
    })

  }


  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    if(this.queryParamSub) {
      this.queryParamSub.unsubscribe();
    }
    if(this.userSub) {
      this.userSub.unsubscribe();
    }
    clearInterval(this.interval)
    console.log('calling destroy')
  }

  private objectToNumMap(obj: { [key: number]: any }) {
    const map = new Map();
    Object.keys(obj).forEach(key => {
      map.set(+key, obj[key]);
    })
    return map;
  }
 
  errorMessage: string;
  async loadQuestions() {
    await this.dash
      .loadAttempt(this.testSessionId, undefined, this.schl_group_id)
      .then(res => {
        const payload = res[0]

        this.asmtSlug = payload.asmt_slug;
        this.currentAttemptId = payload.attemptId;
        this.testAttemptInfo = payload;
        this.isQuestionnaire = payload.attempt_twtdar_order === 0;
        this.customConfirmTestDialogData = this.getCustomConfirmTestDialogData();
        // console.log('loadAttempt', payload)
        const testDesign = payload.testDesign;
        this.asmtFmrk = payload.framework ? JSON.parse(payload.framework) : {};
        this.questions = this.objectToNumMap(testDesign.questionDb),

          this.testForm = {
            currentTestDesign: {
              ...testDesign,
              questionDb: null,
            },
            questionStates: payload.questionStates,
            testLang: testDesign.lang,
            questionSrcDb: this.questions,
          };
        this.initInterval()
        if (payload.assistiveTech) {
          this.assisTech.setStudentAssistiveTechStatus(true);
        }
      })
      .catch(e => {      
        if(e.message === 'ATTEMPT_CLOSED'){
          this.errorMessage = e.message
        } 
        else{
          const { mode, isUnknown } = parseAttemptLoadErrorMessage(e.message);
          if (isUnknown) {
            this.unknownWarningErrorMsg = e.message;
          }
          this.errorMessage = this.lang.tra(mode);
        }
      })
  }

  setStudentPosition($event) {
    this.currentStudentPosition.stageIndex = $event.stageIndex
    this.currentStudentPosition.questionCaption = $event.questionCaption
  }

  /**
   * Redirect user back to previous page respective to their role 
   */
  goDashboard() {
    let returnRouterLink:string;
    switch (this.auth.u().accountType) {
      case AccountType.EDUCATOR:
        returnRouterLink = `/${this.lang.c()}/educator/classrooms`;
        break
      case AccountType.SCHOOL_ADMIN:
        returnRouterLink = `/${this.lang.c()}/school-admin/dashboard`;
        break
      default:
        returnRouterLink = '/'
    }
    this.router.navigate([returnRouterLink], { queryParams: { school: this.schl_group_id }});
  }

  getQuestionById(id: number) {
    let question: IQuestionConfig;
    this.questions.forEach(_question => {
      if (_question.id === id) {
        question = _question;
      }
    });
    return question;
  }

  getQuestionByLabel(label: string) {
    let question: IQuestionConfig;
    this.questions.forEach(_question => {
      if (_question.label === label) {
        question = _question;
      }
    });
    return question;
  }

  initInterval() {
    setInterval(() => {
      this.auth.apiPatch(
        this.routes.SURVEY_SESSION, 
        this.testSessionId, 
        { test_attempt_id: this.testAttemptInfo.attemptId },
        this.configureQueryParams()
      )
    }, 30 * 1000)
  }
  configureQueryParams() {
    if (this.userInfo) {
      return {
        query: {
          schl_group_id: this.schl_group_id
        }
      }
    }
    return null;
  }


  public saveQuestionResponse = (data: any) => {
    // console.log('save question response', data)
    return this.auth.apiCreate(
      this.routes.SURVEY_SESSION_QUESTION,
      {
        ...data,
        test_attempt_id: this.currentAttemptId,
      },
      this.configureQueryParams()
    );
  }

  public savePosition = (data: {section_index, question_index, question_caption, module_id?}) => {
    return this.auth.apiPatch(
      this.routes.STUDENT_ATTEMPT,
      this.currentAttemptId,
      data,
      // this.configureQueryParams()
    )
  }


  public postSubmit = () => {
    if (this.asmtSlug === ASSESSMENT.G9_SAMPLE){
      // this.showAnswers();
    }
    else {
      this.goDashboard()
    }
    return Promise.resolve();
  }
 
  /**
   * Submit principal/teacher questionnaire - handles unseen question response_raw shells
   */
  public submitTest = (skipPost: boolean = false) => {
    const questionStates = this.testForm.questionStates;
    const questionDB = this.testForm.questionSrcDb
    // converts questionTitleMap to an object
    const questionTitleMapObject = {};
    let index = 0;
    for (const [key, value] of this.questionTitleMap) {
      questionTitleMapObject[index] = { key, value };
      index++;
    }
    const isPaper = this.onlineOrPaper.isPaper
    let compressedQuestionStates;
    for (const question in questionStates) {
      let questionState = questionStates[question]
      
      let entryState;
      if (questionState){
        const questionData = questionDB.get(+question)
        for(let questionContent of questionData.content ){
          
          const entryId = questionContent.entryId
          entryState = questionState[entryId]
         
          // Create a response_raw shell for non-existent question entry states
          if (!entryState && (questionContent.elementType !== 'text' || questionContent.advancedList.length)){
            entryState  = {
              type: questionContent.elementType,
              isCorrect: false,
              isStarted: false,
              isFilled: false,
              isResponded: false,
              score: 0,
              weight: getElementWeight(questionContent),
              scoring_type: ScoringTypes.AUTO, 
            }
            if(questionContent.elementType === 'mcq'){
              entryState.selections = []
            }
            if(questionContent.elementType === 'select_table'){
              const newCheck = [];
              const testTakerAnswers = []
              if (questionContent.leftCol) {
                questionContent.leftCol.forEach((row, r)=>{
                  newCheck.push([]);
                  testTakerAnswers.push([]);
                  questionContent.topRow.forEach((col, c)=>{
                    newCheck[r].push({value:false});
                    testTakerAnswers[r].push({value: false});
                  })
                })
              }
              entryState.isCorrect = true
              entryState.score = 1
              entryState.isCustomGrading = false,
              entryState.checkMarks = newCheck
            }
            if(questionContent.elementType === 'input'){
              if(questionContent.format === 'text'){
                delete entryState.isCorrect
                entryState.type = "input-longtext"
                entryState.isCustomGrading = true
                entryState.isPaperFormat = false
                entryState.str = undefined
                entryState.scoring_type = ScoringTypes.MANUAL
              }
              if(questionContent.format === 'number'){
                entryState.type = 'input-number'
                entryState.isCorrect = true
                entryState.score = 1
                entryState.scoring_type = ScoringTypes.REVIEW
              }
            }
            if(questionContent.elementType === 'frame'){
              if(questionContent.content.length > 0){
                questionContent.content.forEach(item => {
                  questionData.content.push(item);
                });
              }
              if(questionContent.isStateEnabled){
                delete entryState.score
                delete entryState.weight
                delete entryState.scoring_type
                entryState.rotation = undefined
                entryState.currHeight = undefined
              } else{
                continue
              }
            }
            if(questionContent.elementType === 'text'){
              questionContent.advancedList.forEach(item => {
                if(item.elementType === 'input')
                questionData.content.push(item)
              })
              continue
            }
            questionState[entryId] = entryState;
          }
          questionState.__meta.isStarted = questionState.__meta.isStarted ? true : false
          questionState.__meta.entryOrder = questionData.entryOrder
        }}

      questionStates[question] = JSON.stringify(questionStates[question])
      // compress all question response_raw
      compressedQuestionStates = lzCompressProps(questionStates, [question])
    }
    return this.auth.apiPatch(
      this.routes.SURVEY_SESSION_QUESTION,
      1,
      { test_attempt_id: <any>this.currentAttemptId, question_states: compressedQuestionStates, isPaper: isPaper, question_title_map: questionTitleMapObject },
      this.configureQueryParams()
    ).then(r => {
      this.logTestSubmit();
      
      if(!skipPost) {
        this.postSubmit();
      }
    });
    // .then( r => {
    //   this.my.gotoDashboard();
    // });
  }


  logTestSubmit() {

    // if(!this.auth.userIsStudent()) {
    //   return;
    // }

    this.auth.apiCreate(this.routes.LOG, {
      slug: 'TA_SURVEY_QUESTIONNAIRE_SUBMIT',
      data: {
        uid: this.auth.getUid(),
        testSessionId: this.testSessionId,
        // asmtLabel: this.asmtLabel,
        lang: this.lang.c(),
        states: this.testForm.questionStates,
        questions: this.testForm.questionSrcDb,
        // withTeacher: this.route.snapshot.queryParams.withTeacher,
      }
    })
  }


  private getCustomConfirmTestDialogData(): ICustomConfirmTestDialogData {
    return {
      text: 'ta_questionnaire_submit',
      confirmMsg: 'lbl_yes',
      cancelMsg: 'lbl_no'
    };
  }

}
