import { Injectable } from '@angular/core';
import { LangService } from '../core/lang.service';
import { Router } from '@angular/router';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { AccountType } from '../constants/account-types';
import { G9DemoDataService } from '../ui-schooladmin/g9-demo-data.service';
import { EStyleProfile, ProfileAttemptLoadComponents, StyleprofileService } from '../core/styleprofile.service';

export enum ITypeSlug{
  TEACHER_QUESTIONNAIRE = "TEACHER_QUESTIONNAIRE",
  PRINCIPAL_QUESTIONNAIRE = "PRINCIPAL_QUESTIONNAIRE"
}

@Injectable({
  providedIn: 'root'
})
export class SchoolTeacherAdminQuestionnaireService {

  constructor(
    private lang: LangService,
    private router: Router,
    private auth: AuthService,
    private routes: RoutesService,
    private g9demoService: G9DemoDataService,
    private styleprofile: StyleprofileService,
  ) { }

  userSub;

  ngOnDestroy() {
    if(this.userSub) {
      this.userSub.unsubscribe();
    }
  }

  findQuestionnaireSession = (account_type: AccountType, slug: ITypeSlug, schl_group_id) => {
    return this.auth.apiFind(this.routes.SESSION_QUESTIONNAIRE, {
      query: {
        account_type,
        slug,
        schl_group_id
      }
    }).then((session) => {
      
      if(!session.length){
        // alert("No Questionnaire session found");
        return undefined;
      }

      return session;
    })
    .catch((e) => console.error(e))
  }

  loadAttempt(test_session_id, route:string = this.routes.SURVEY_SESSION, schl_group_id = this.g9demoService.schoolData.group_id){
    if(!test_session_id) {
      return Promise.resolve();
    }
    const lang = this.lang.c();

    return this.auth
      .apiGet(route, test_session_id, {query:{KIOSK_PASSWORD: this.auth.activeKioskPassword, lang, schl_group_id}}).then( async (res) => {
        console.log(res)
        const styleProfile =  res[0]?.styleProfile??EStyleProfile.GR9;
        await this.styleprofile.setAttemptStyleProfile(styleProfile, ProfileAttemptLoadComponents.TEACHER_ADMIN_QUESTIONNAIRE_SERVICE);
        return res;
      })
  }
}
