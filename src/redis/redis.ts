import _ from 'lodash';
import Redis, { Cluster } from 'ioredis';
import logger from '../logger';
import { Errors } from '../errors/general';
import { Id, NullableId } from '@feathersjs/feathers';
import { Application } from '../declarations';
import { default as Redlock, Lock } from 'redlock';
import Bluebird from 'bluebird';
import { WS_ROLE_TYPE } from '../services/public/educator/websocket/connected-users/connected-users.class';

// Expire Time
export const REDIS_SYS_DATA_EXPIRE = 60 * 60 * 24 * 7 // 7 days
export const REDIS_ASSESSMENT_DATA_EXPIRE =  60 * 60 * 18 * 1 // 18 hours
export const REDIS_ASSESSMENT_DATA_EXPIRE_GRACE_BUFFER =  60 * 10 // 10 minutes

/**
 * @default seconds=REDIS_ASSESSMENT_DATA_EXPIRE
 * @returns UNIX timestamp of when the key should expire
 */
export const redisExpireAt = (seconds: number = REDIS_ASSESSMENT_DATA_EXPIRE) => {
   return Math.floor(Date.now() / 1000) + seconds
}

export type RedisKey = string;
export type RedisDate = Date | string | null;

/**
 * type for RedisKeyPrefixes
 * RedisKeyPrefixes can be used as "full key name", ex: styleProfile
 * @important Make sure to create a Key generation function when add a new RedisKeyPrefixes
 * @important Some keys have {HASH} tags to ensure they stay within the same shard. Refer to redis cluster mode documentation
 */
export const RedisKeyPrefixes = {
    // Unique keys
    ClassStuCredOEN: "classStuCredOEN",
    ClassStuCredSASN: "classStuCredSASN",
    PendingSAPBuffer: "pendingSAPBuffer",
    PendingSubmWrites: "{RESPONSE_BUFFER}.pendingSubmWrites",
    ProcessingSubmWrites: "{RESPONSE_BUFFER}.processingSubmWrites",
    SubmData: "submData",
    AttemptPos: "attemptPos",
    ClassConnections: "class",
    UserConnections: "user",  // This RedisKeyPrefixes is same as User in "DB table keys",  but the keyGenerator is different. its ok here but don't do the same for other.
    WebsocketConnection: "websocketConnection",
    StudentHeartbeatControl: "sTUDENT_HEARTBEAT_CTRL",
    SysConstantsNumeric: "sysConstants:numeric",
    SysConstantsString: "sysConstants:string",
    SessionAttemptsInit: "sessionAttemptsInit",
    ResponseBufferStatus: "responseBufferStatus",
    SessionLoadedInit: "sessionLoadedInit",

    // DB table keys
    StyleProfile: "style-profile",
    TestSession: "testSession",
    TestAttempt: "testAttempt",
    SchoolClass: "schoolClass",
    UserRole: "userRole",
    User: "user", // This RedisKeyPrefixes is same as UserConnections in "Unique keys",  but the keyGenerator is different. its ok here but don't do the same for other.
    UserMeta: "userMeta:uid:key_name_space",
    TestAttemptSubSession: "testAttemptSubSession",
    TestSessionSubSession: "testSessionSubSession",
    TestWindowTDAllocRule: "testWindowTDAllocRule",
    AssessmentComponent: "AssessmentComponent",
    TestAttemptUnsubmission: "TestAttemptUnsubmission",

    // DB table relation keys
    ClassUserRoles: "classUserRoles",
    ClassSchoolUserRoles: "classSchoolUserRoles",  // class user's related school user_roles
    ClassTestSessions: "classTestSessions",
    ClassGuestClasses: "classGuestClasses",
    TestAttemptTASSs: "testAttemptTASSs",
    TestSessionTestAttempts: "testSessionTestAttempts",
    UserUserMetas: "userUserMetas",
    TestSessionTSSSs: "testSessionTSSSs",
    TestWindowTwtdars: "testWindowTwtdars",
    TestWindowAssessmentComponents: "TestWindowAssessmentComponents",
    TestSessionTestAttemptUnsubmissions: "TestSessionTestAttemptUnsubmissions",
    TestSessionTATrasferRequireUids: "TestSessionTATrasferRequireUids",

    // RedisLocks
    TestSessionInit: "testSessionInit",
    TestWindowTestSessions: "TestWindowTestSessions",
    AttemptASRGInit: "AttemptASRGInit",
    TestWindowAutoSubmit: "TestWindowAutoSubmit",
    KTestWindowAutoSubmitMessage: "KTestWindowAutoSubmitMessage",
    ValidateStudentsInfo: "ValidateStudentsInfo",
}

/**
 * Returns an RedisKey with the given affixes appended to the prefix.
 * @example 'pendingSAPBuffer:1234:5678'
 */
type KGenerator = (...affixes: NullableId[]) => RedisKey;
export const keyGenerator = (prefix: RedisKey, ...affixes: NullableId[]): RedisKey => {
  // should not have a trailing colon if no affixes
  return `${prefix}${affixes.length ? ':' : ''}${affixes.join(':')}`;
}
export const addShardingHash = (hash: Id, key: string): RedisKey => {
  return `{${hash}}.${key}`
}

// Redis key generation
// Unquie keys
export const KClassStuCredOEN = (accessCode:string): RedisKey =>  `${RedisKeyPrefixes.ClassStuCredOEN}:${accessCode}`;
export const KClassStuCredSASN = (accessCode:string): RedisKey =>  `${RedisKeyPrefixes.ClassStuCredSASN}:${accessCode}`;
export const KPendingSAPBuffer: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.PendingSAPBuffer);
export const KAttemptPos: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.AttemptPos);
export const KSubmData: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.SubmData);
export const KSessionAttemptsInit: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.SessionAttemptsInit);
export const KSessionLoadedInit: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.SessionLoadedInit);

// DB table keys
export const KStyleProfile = (slugName:string): RedisKey  => `${RedisKeyPrefixes.StyleProfile}:${slugName}`;
export const KTestSession: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSession);
export const KTestAttempt: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestAttempt);
export const KSchoolClass: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.SchoolClass);
export const KUserRole: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.UserRole);
export const KUser: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.User);
export const KUserMeta: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.UserMeta);
export const KTestAttemptSubSession: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestAttemptSubSession);
export const KTestSessionSubSession: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionSubSession);
export const KTestWindowTDAllocRule: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestWindowTDAllocRule);
export const KAssessmentComponent: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.AssessmentComponent);
export const KTestAttemptUnsubmission: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestAttemptUnsubmission);

//DB table relation keys
export const KClassUserRoles: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.ClassUserRoles);
export const KClassSchoolUserRoles: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.ClassSchoolUserRoles);
export const KClassTestSessions: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.ClassTestSessions);
export const KClassGuestClasses: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.ClassGuestClasses);
export const KTestAttemptTASSs: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestAttemptTASSs);
export const KTestSessionTestAttempts: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionTestAttempts);
export const KUserUserMetas: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.UserUserMetas);
export const KTestSessionTSSSs: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionTSSSs);
export const KTestWindowTwtdars: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestWindowTwtdars);
export const KTestWindowAssessmentComponents: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestWindowAssessmentComponents);
export const KTestSessionTestAttemptUnsubmissions: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionTestAttemptUnsubmissions);
export const kTestSessionTATrasferRequireUids: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionTATrasferRequireUids);
export const KTestWindowTestSessions: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestWindowTestSessions);

/**
 * Sharded by classId  
 * '{classId}.user:{uid}:connections'
 */
export const KUserConnections = (uid: Id, class_id: Id) => addShardingHash(class_id, keyGenerator(RedisKeyPrefixes.UserConnections, uid, 'connections'));
/**
 * Sharded by classId  
 * '{classId}.class:{classId}
 */
export const KClassConnections = (class_id: Id, role_type: WS_ROLE_TYPE) => {
  return addShardingHash(class_id, keyGenerator(RedisKeyPrefixes.ClassConnections, class_id, role_type))
} 
export const KWebsocketConnection = (connection_id: Id) => keyGenerator(RedisKeyPrefixes.WebsocketConnection, connection_id)

// RedisLocks
export const KTestSessionInit: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestSessionInit);
export const KAttemptASRGInit: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.AttemptASRGInit);
export const KTestWindowAutoSubmit: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.TestWindowAutoSubmit);
export const KTestWindowAutoSubmitMessage: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.KTestWindowAutoSubmitMessage);
export const KValidateStudentsInfo: KGenerator = keyGenerator.bind(null, RedisKeyPrefixes.ValidateStudentsInfo);

/**
 * Check if redisKey exist in current redis
 * @param app
 * @param redisKey
 * @returns bool
 */
export const checkRedisKey = async (app:Application, redisKey:string): Promise<boolean> =>{
  const redis: Redis = app.get('redis');
  const exists = await redis.exists(redisKey)
  return exists === 1
}

export const DEFAULT_REDLOCK_TIMEOUT = 1000 * 5 // default time out is 5 second for the lock to release itself.
export const TESTSESSION_INIT_REDLOCK_TIMEOUT = 1000 * 60 // time out for test session init lock
export const ATTEMPTASRG_INIT_REDLOCK_TIMEOUT = 1000 * 60 // time out for test attempts ISR request ASRG lock
export const TEST_WINDOW_AUTO_SUBMIT_REDLOCK_TIMEOUT = 1000 * 60 // time out for daily auto sumit process time out
export const VALIDATE_STUDENTS_INFO_REDLOCK_TIMEOUT = 1000 * 60 * 2 // time out for validate students info lock (2 mins)

export const getRedisLock = async (app:Application, resources:string[], duration:number = DEFAULT_REDLOCK_TIMEOUT ) => {
  const redis: Redis = app.get('redis');
  const redlock = new Redlock([redis], {retryCount: 0});
  return await redlock.acquire(resources, duration);
}

/**
 * Get Redis Expire time in second
 * @param app
 * @param lockKey Redis lock key string( recomment use the key manage system to get the key)
 * @returns number -1 : Lock key does not exist
 *                 -2 : Lock key have nore expire time
 *                  n : Lock key time to live is n second
 */
export const getRedisLockExpireAt =  async (app:Application, lockKey:string ) => {
  const redis: Redis = app.get('redis');
  return redis.ttl(lockKey);
}

export const SubmDataFields = 17
/**
 * @note if adding new fields that will be reflected in Redis, make sure to update SubmDataFields
 */
export interface SubmData {
  id?: number|null;
  test_attempt_id: number,
  uid: number,
  test_session_id: number,
  test_question_id: number,
  test_question_version_id: number,
  module_id: number,
  response_raw: string,
  score: number,
  weight: number,
  response: string,
  section_id: number,
  timestamp: number,
  updated_by_uid: number,
  tass_id?: number,
  tass_started_on?: number,
  question_index: number,
  question_caption: string,
  sub_session_id: number,
  taqr_id?: number,
  is_paper_format: number,
  is_real_time_audit_processed: number,
  is_nr: number,
  is_not_seen?: number
}

/**
 * Pings the Redis connection to check if it is alive.
 * Defines a custom timeout of 5 seconds.
 * @exit 1 if timeout is reached
 * @param redis - Redis connection instance
 * @param activeConfig - Redis config, purely for logging purposes
 */
const redisHealthCheck = async (redis: Redis | Cluster, activeConfig: any) => {
  const pingPromise = Bluebird.resolve(redis.ping());

  pingPromise.timeout(5000)  // 5 seconds
    .then(() => logger.info('connected to redis'))
    .catch(Bluebird.TimeoutError, () => {
      logger.error('Redis health check timed out', { activeConfig });
      process.exit(1);
    })
}

export default function veaRedisConn (config: any, isDevMode: boolean = false): Redis|Cluster|null {
  if (!config || config.isDisabled) {
    logger.warn('no redis config provided - disabling redis')
    return null;
  }

  if(config.tls) { // add a server identity bypass for local development to allow tunneling to Elasticache
    config.tls = {
      ...config.tls,
      checkServerIdentity: function (host: any, cert: any) {
        if (host === 'localhost') {
          return undefined;
        }
      }
    }
  }

  const isElasticacheCluster = config.forceCluster || /clustercfg/.test(config.host);
  let activeConfig:any;
  if (isElasticacheCluster) {
    activeConfig = new Redis.Cluster(
      [
        {
          host: config.host,
          port: config.port,
        },
      ],
      {
        dnsLookup: (address, callback) => callback(null, address),
        redisOptions: {
          tls: config.tls,
        },
      }
    );
  } else {
    activeConfig = config;
  }
  logger.info('connecting to redis', { activeConfig })
  const redisConnect = isElasticacheCluster ? activeConfig : new Redis(activeConfig);
  redisConnect.on('error', (err:any) => {
    logger.error('error connecting to redis', err, { activeConfig })
    throw new Errors.GeneralError('ERR_REDIS_CONNECTION', err);
  })

  redisHealthCheck(redisConnect, activeConfig)

  return redisConnect;
}
