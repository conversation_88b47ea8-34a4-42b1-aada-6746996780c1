export enum DBD_U_GROUP_TYPES {
        mpt_sys             = 'mpt_sys'              // This group contains all users who have system-level access to the MPT test. There is no automatic control which removes access of these accounts other than TTL (expires_on) and direct revocations by the appropriate user.
      , cl_sys              = 'cl_sys'
      , mpt_institution     = 'mpt_institution'      // Each institution (which acts as the Test Administrator) is assigned a group so that the individual accounts can be assigned roles.
      , mpt_test_window     = 'mpt_test_window'      // Each test window is assigned a group so that MPT system level permissions can be controlled on a per-test-window basis.
      , mpt_test_session    = 'mpt_test_session'     // 
      , mpt_cert_body       = 'mpt_cert_body'        // 
      , mpt_test_controller = 'mpt_test_controller' 
      , school_district     = 'school_district'      // introduced with g9
      , school              = 'school'               // introduced with g9
      , school_class        = 'school_class'         // introduced with g9
      , project             = 'project'              // Actions connected to a specific project or initiative.
      , mpt_applicants      = 'mpt_applicants'       // all registered teacher applicants (validated OCT ID)
      , questionbank        = 'questionbank'         // to manage access to questions
      , single_questionbank = 'single_questionbank'  // manage access to specific question bank
      , alt_version_req_ctrl = 'alt_version_req_ctrl'  // alternate version request controller
      , alt_version_req_shipping_vendor = 'alt_version_req_shipping_vendor'  // alternate version request controller
      , alt_version_template_controller = 'alt_version_template_controller' // Alternative Version File Template Controller
}

export enum DBD_U_ITEM_TYPES {
  item = 'item',
  sequence = 'sequence',
  page = 'page',
  text = 'text'
}

export enum DBD_U_ROLE_TYPES {
  bc_auth_acc_spec = 'bc_auth_acc_spec',
  bc_auth_base = 'bc_auth_base',
  bc_auth_chair ='bc_auth_chair',
  bc_auth_coord = 'bc_auth_coord',
  bc_auth_ext_rev = 'bc_auth_ext_rev',
  bc_auth_ling_rev = 'bc_auth_ling_rev',
  bc_auth_ministry_rev = 'bc_auth_ministry_rev',
  bc_auth_psycho = 'bc_auth_psycho',
  bc_auth_soc_spec = 'bc_auth_soc_spec',
  bc_auth_standard_spec = 'bc_auth_standard_spec',
  bc_auth_trans = 'bc_auth_trans',
  bc_auth_trans_coord = 'bc_auth_trans_coord',
  bc_auth_trial_writer = 'bc_auth_trial_writer',
  bc_fsa_admin = 'bc_fsa_admin',
  cert_body_data_retr = 'cert_body_data_retr',  // 		Download list of successful attempts along with organizational ID.
  cl_examinee = 'cl_examinee',
  cl_sys = 'cl_sys',
  cron = 'cron',
  debug = 'debug', //support
  eqao_auth_read_only = 'eqao_auth_read_only',
  mpt_accomm_applicant = 'mpt_accomm_applicant',  //, 		
  mpt_applicant = 'mpt_applicant',  //, 		
  mpt_booked_applicant = 'mpt_booked_applicant',  //, 		
  mpt_pending_booking_applicant = 'mpt_pending_booking_applicant',  //, 		
  mpt_sys_admin = 'mpt_sys_admin',  // 	MPT System Administrator	
  mpt_test_admin_accomm_coord = 'mpt_test_admin_accomm_coord',  // 	Accommodations Coordinator	The person responsible for intaking test applicants who have indicated a need for special accommodations.
  mpt_test_admin_inst_mngr = 'mpt_test_admin_inst_mngr',  // 	Institutional Manager	The supervisor at the institution which serves as the institutional manager for the test.
  mpt_test_admin_invig = 'mpt_test_admin_invig',  // 	Invigilator	A person who is responsible for administering a single test session
  mpt_waiting_list_applicant = 'mpt_waiting_list_applicant',  //, 		
  mrkg_mrkr = 'mrkg_mrkr', // scorer
  mrkg_rafi = 'mrkg_rafi', // range finder
  mrkg_ctrl = 'mrkg_ctrl', // scoring leader
  mrkg_sensi = 'mrkg_sensi', // sensitive student responses
  mrkg_supr = 'mrkg_supr', // scoring supervisor
  schl_admin = 'schl_admin',  // 	school administrator
  schl_dist_admin = 'schl_dist_admin',  // 	school district administrator
  school_district_curri = 'school_district_curr', // board lead K12
  schl_disct_curr_ele = 'schl_disct_curr_ele', // board lead elementary
  schl_disct_curr_sec = 'schl_disct_curr_sec', // board lead secondary
  schl_dist_it_admin = 'schl_dist_it_admin', // school board
  schl_student = 'schl_student',  // 	student at a school
  schl_teacher = 'schl_teacher',  // 	teacher at a school
  schl_teacher_invig = 'schl_teacher_invig',  // 	invigilator at a school
  test_ctrl_data_retr = 'test_ctrl_data_retr',  // 		Download anonymized response data (to review scoring)
  test_ctrl_data_exporter = 'test_ctrl_data_exporter',
  test_ctrl_issue_tracker = 'test_ctrl_issue_tracker',  // 		Review reported issues by invigilators and test applicants.
  test_ctrl_issue_revw = 'test_ctrl_issue_revw',  // 		Review reported issues by invigilators and test applicants.
  test_ctrl_lias_cert_body = 'test_ctrl_lias_cert_body',  // 	Certification Body Liaison	Create and Revoke Accounts for OCT members.
  test_ctrl_lias_internal = 'test_ctrl_lias_internal',  //  Create and Revoke Accounts for other EQAO members.
  test_ctrl_lias_test_admin = 'test_ctrl_lias_test_admin',  // 	Test Administrator Liaison	Create and Revoke Accounts for Test Administrators (institutional managers from the Faculties only, not accommodation coordinators or invigilators).
  test_ctrl_meta_reg = 'test_ctrl_meta_reg',  // 	Test Controller (Meta Regulator)	Define the Test Window. Set the dates, select test design, manually open or close it.
  test_ctrl_score_valid = 'test_ctrl_score_valid',  // 		Sign-off on the release of scores to applicants.
  test_ctrl_window_monitor = 'test_ctrl_window_monitor',  // 		View usage meta statistics (de-aggregated by Faculty) including number of registrations, number of test sessions, etc.
  test_ctrl_ntf_ctrl = 'test_ctrl_ntf_ctrl',
  test_ctrl_mfa_ctrl = 'test_ctrl_mfa_ctrl',
  test_ctrl_auto_submit_ctrl = 'test_ctrl_auto_submit_ctrl', // Auto submit controller for test sessions
  test_item_author = 'test_item_author',  // 	Item Author	Create, Modify, and Archive test questions.
  test_item_author_rev = 'test_item_author_rev',
  test_item_author_super = 'test_item_author_super',  // 	Supervisor of Item Authors	Archive and validate test questions. Assign users to groups.
  test_item_template_author = 'test_item_template_author', // Create, Modify, and Archive template.
  translator = 'translator',  // 	Translator	A person who has read and write access to the translation system.
  translator_super = 'translator_super',  // 	Supervisor of Translators	
  test_ctrl_issue_revw_exempt_ovr = 'test_ctrl_issue_revw_exempt_ovr', // Creates and removes student exemption override in issue reviewer
  payment_ctrl = 'payment_ctrl',
  ctrl_system_monitor = 'ctrl_sys_monit', // these role recieve daily report for EQAO
  mfa_exempt = 'mfa_exempt'
}