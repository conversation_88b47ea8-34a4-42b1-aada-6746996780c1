@import '/src/styles/pseudo-objects//or-horz.scss';

%page-form-content {
    
    &.is-fullpage {
        background-color: #F5F5F5;
        min-height: 100vh;
    }
    .or-horz {
        @extend %or-horz;
        color: #595959;
        hr {
            background-color: #595959;
        }
    }
    .form-content {
        max-width: 380px;
        margin:auto;
        .form-instruction-major {
            font-size: 1em;
            margin-bottom:0.5em;
        }
        .form-instruction-minor {
            font-size: 0.8em;
            margin-bottom:0.5em;
        }
    }
    .read-detail {
        margin-top: 1em;
        max-height: 10em;
        overflow: auto;
        padding: 1em;
        background-color: #e0e0e0;
        margin-bottom:0.5em;
    }
    hr {
        background-color: #b9b9b94f;
        border: none;
        display: block;
        height: 1px;
        margin: 1em 0;
    }
    h2.strong { 
        padding-bottom: 0em;
        margin-bottom: 0.2em;
        font-weight: 700 !important;
    }
    h2.minor { 
        margin-top:0em;
        padding-top:0em;
        font-weight: 300;
    }
    label .optional-tag {
        font-weight: 300;
    }
    .invalid-submission {
        text-align: center;
        padding: 0.5em;
        font-size: 1.4em;
        margin-bottom: 1em;
        color: red;
    }
    .form-popout-info {
        border-radius: 0.5em;
        padding: 1.5em;
        margin-top: 1em;
        margin-bottom: 2em;
        background-color: #fff;
        box-shadow: 0px 4px 8px rgba(0,0,0,0.2);
        text-align:center;
    }
    label.checkbox input {
        margin-right:1em;
    }
}
