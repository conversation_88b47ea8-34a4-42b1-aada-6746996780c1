/* You can add global styles to this file, and also import other style files */
@import 'bulma/css/bulma.css';
@import '~@ctrl/ngx-emoji-mart/picker';
@import '~@fullcalendar/common/main.css';
@import '~@fullcalendar/daygrid/main.css';
@import 'styles/partials/media.scss';
// @import url('https://vea-cdn.vretta.com/assets/fontawesome-free-5.11.2-web/css/all.min.css'); // store locally
@import '~@angular/material/prebuilt-themes/deeppurple-amber.css';
@import "~jsoneditor/dist/jsoneditor.min.css";
@import "~ag-grid-community/dist/styles/ag-grid.css";
@import "~ag-grid-community/dist/styles/ag-theme-alpine.css";

// code mirror
@import "~codemirror/lib/codemirror";
// @import "~codemirror/theme/material";

// @import url('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.58.3/codemirror.min.css'); // todo:BUCKET need to put in our own bucket, also can we connect to item set editor lazy load?
@import "./assets/lib/diagramatics/diagramatics.scss";

// Fonts
/* Roboto: file and css copied from: https://fonts.googleapis.com/css?family=Roboto:300,400,500&display=swap */
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-Ext-300.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-300.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-Ext-400.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-400.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-Ext-500.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    src: url('./assets/fonts/Roboto-Latin-500.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
/* Source Sans Pro: file and css copied from https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,600,700&display=swap*/
  /* latin-ext */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Ext-Light.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Light.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Ext-Regular.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Regular.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Ext-SemiBold.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-SemiBold.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
  /* latin-ext */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Ext-Bold.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Source Sans Pro';
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    src: url('./assets/fonts/SourceSansPro-Latin-Bold.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
/* Palanquin css and font files from: https://fonts.googleapis.com/css?family=Palanquin */
  /* latin-ext */
  @font-face {
    font-family: 'Palanquin';
    font-style: normal;
    font-weight: 400;
    src: url('./assets/fonts/Palanquin-Latin-Ext-Regular.woff2') format('woff2');
    unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
  }
  /* latin */
  @font-face {
    font-family: 'Palanquin';
    font-style: normal;
    font-weight: 400;
    src: url('./assets/fonts/Palanquin-Latin-Regular.woff2') format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  }
/* Material Icons: font and css from: https://fonts.googleapis.com/icon?family=Material+Icons */
  /* fallback */
  @font-face {
      font-family: 'Material Icons';
      font-style: normal;
      font-weight: 400;
      src: url('./assets/fonts/material-icons-v142-latin-regular.woff2') format('woff2');
  }
  
  .material-icons {
      font-family: 'Material Icons';
      font-weight: normal;
      font-style: normal;
      font-size: 24px;
      line-height: 1;
      letter-spacing: normal;
      text-transform: none;
      display: inline-block;
      white-space: nowrap;
      word-wrap: normal;
      direction: ltr;
      -webkit-font-feature-settings: 'liga';
      -webkit-font-smoothing: antialiased;
  }

//specific
@import "./styles/specific-component-styles/view-school-admin-dashboard.scss";

// Colors
$white: #fff !default;
$black: #000 !default;
$red: #d9534f !default;
$orange: #f0ad4e !default;
$yellow: #ffd500 !default;
$green: #5cb85c !default;
$blue: #0275d8 !default;
$teal: #5bc0de !default;
$pink: #ff5b77 !default;
$purple: #613d7c !default;
$light-blue: #3e72c0 !default;
// Grayscale
$gray-dark: #222;
$gray: #212121;
$gray-light: #666;
$gray-lighter: #bbb;
$gray-lightest: #eee;

///// Content
$body-font: 'Source Sans Pro',
sans-serif;
$heading-font: 'Palanquin',
sans-serif;
html {
    overflow-y: auto;
}

body, .mat-typography {
    font-family: $body-font;
    h1,h2,h3,h4 { font-family: $body-font; }
}

html,
body,
app-root {
    height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: $heading-font;
    font-weight: 400 !important;
}

a {
    color: $light-blue;
    font-weight: bold;
    &:hover {
        color: darken($light-blue, 10%)
    }
}

#wrapper {
    display: flex;
    min-height: 100vh;
    flex-direction: column;
}

texthelp-highlight-span {
    color: black;
    texthelp-highlight-span {
        color: white
    }
}

.content {
    // min-height: 70vh;
    // padding: 2vw;
    // flex: 1
    .markdown-inline {
        &.is-mark-inline {
            display: inline;
            p {
                display: inline;
            }
        }
    }
    .markdown {
        strong {
            color: unset;
        }
        ul {
            list-style-type: disc;
            padding-inline-start: 1em;
            // li { }
        }
        p, ul, ol, blockquote {
            margin-bottom: 1em !important;
            a { word-break: break-word; }
        }
        &.is-condensed {
            p, ul, ol, blockquote {
                margin-bottom: 0em !important;
            }
        }
        blockquote {
            padding-bottom: 0.2em;
        }
        a.button {
            white-space: normal;
            height: auto;
        }
    }
}

.logo {
    font-size: 1.2em;
    font-weight: bold;
    font-family: $heading-font;
    color: #1D262F;
    img {
        margin-right: 5px;
    }
}

.footer {
    border-top: 2px solid $gray-lightest;
    min-height: 100px;
    text-align: center;
    font-size: 0.9em;
    background: #f9f9f9;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.box {
    border-radius: 0px;
    margin-top: 5px;
}

///// Social Buttons
.input {
    margin-bottom: 5px;
}

.btn-social {
    color: #fff;
    i {
        margin-right: 5px;
    }
    &:hover {
        color: #fff;
        box-shadow: 0 14px 26px -12px rgba(0, 0, 0, 0.2), 0 4px 23px 0px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.1);
    }
    .btn-block {
        margin-bottom: 10px;
    }
}

.btn-twitter {
    background-color: #55ACEE;
}

.btn-facebook {
    background-color: #3B5998;
}

.btn-github {
    background-color: #333333;
}

.btn-google {
    background-color: #DD4B39;
}

.btn-social.button[disabled] {
    color: #333333;
    background-color: #fff;
    border-color: #dbdbdb;
    box-shadow: none;
    opacity: .6;
}

.form-label {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-weight: bold;
}

.small-number {
    width: 4em;
}

.loading {
    opacity: 0;
    position: fixed;
    height: 100%;
    width: 100%;
    background: #272c33;
    padding-top: 25vh;
    text-align: center;
    z-index: -1;
    transition: opacity .8s ease-out;
}


.disable-cdk-animation {
    &.cdk-drag {
        transition: none !important;
    }
    &.cdk-drag-animating {
        transition: none !important;
    }
}

app-root:empty+.loading {
    opacity: 1;
    z-index: 100;
    h1 {
        font-family: "courier", monospace;
        color: #EEE;
        margin-bottom: 50px;
    }
}

.fc {
    .fc-widget-content {
        background-color: #fff;
    }
}

.content table td {
    border-width: 1px;
}

.button {
    margin-right: 5px;
}

.button-main-row {
    margin-top: 2em;
    margin-bottom: 3em;
}

.button.is-main {
    color: #2196F3;
    font-weight: 700;
    // font-weight: 600;
    text-transform: uppercase;
    background-color: #E3F2FD;
    border: none;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.2);
    padding: 0.7em 4em;
    @include viewport-md {
        padding: 0.7em;
    }
    height: auto;
    letter-spacing: 0.1em;
    white-space: inherit;
    &.is-neg {
        font-weight: 400;
        color: #FF1F00;
        background-color: #FFE7E7;
        letter-spacing: 0.15em;
    }
    &.is-warn {
        color: #F39F21;
        background-color: #FDF6E3;
    }
    &.is-plain {
        font-weight: 400;
        color: #333;
        background-color: #fff;
        letter-spacing: 0.15em;
    }
    &.is-green {
        font-weight: 600;
        color: #7BB658;
        background-color: #E7FBE0;
        letter-spacing: 0.15em;
    }
    &.is-save {
        font-weight: 400;
        color: white;
        letter-spacing: 0.15em;
        background-color: #2196F3;
    }
}

.coming-soon {
    opacity: 0.2;
    display: none;
}

.card.quickpad {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    border-radius: 0.2rem;
}


/* Animate items as they're being sorted. */

.cdk-drop-list-dragging .cdk-drag {
    transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}


/* Animate an item that has been dropped. */

.cdk-drag-animating {
    transition: transform 0ms cubic-bezier(0, 0, 0.2, 1);
}

// quick override, to investigate
.content p:not(:last-child),
.content dl:not(:last-child),
.content ol:not(:last-child),
.content ul:not(:last-child),
.content blockquote:not(:last-child),
.content pre:not(:last-child),
.content table:not(:last-child) {
    margin-bottom: 0em;
}

.placehold-attention {
    padding: 5rem;
    text-align: center;
}

$purple: #774a77;
$blue: #007e99;
.or-block {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #ddd;
    // font-size:90%;
    font-weight: 600;
    margin: 1em 0em;
    hr {
        flex-grow: 2;
        margin: 1em;
        border-color: #ddd;
    }
}

.button-social {
    img {
        width: 27px;
    }
    span {
        flex-grow: 2;
    }
}

.fc-view,
.fc-view>table {
    z-index: 0;
}

.paginator-input {
    font-size: 0.9em;
    width: 3em;
}

.checkbox-label {
    input {
        margin-right: 1em;
    }
}

.progress-spinner {
    display: inline-block;
    width: 1em;
    height: 1em;
    border: 1px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    animation: rotate 800ms linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@media print {
    .dont-print {
        display: none !important;
    }
}

.printed-page {
    @media screen {
        border: 1px solid #ccc;
        box-shadow: 0 0 5em rgba(0, 0, 0, 0.5);
        margin:6em auto;
        width: 60em;
        padding:1.5em;
    }
    @media print {
    }
}

.printed-test-form {
    img {
        // max-width: 360px;
        // max-height: 200px;
        // object-fit: contain;
    }
    .content-element {
        display: flex;
        .option-container {
            margin-top: 1em;
            flex-wrap: wrap;
            button.option {
                // padding: 2em;
                // padding-bottom: 4em;
            }
            img {
                max-width: 260px;
                max-height: 340px;
                object-fit: contain;
            }
            .option-indicator {
                .radio-container {
                    opacity: 0;
                }
            }
        }
    }
    .inline-block.is-math {
        top: 0em;
    }
    .mfrac {
        font-size: 1.4em;
    }
}

.tag.is-french {
    color: #fff;
    background-color: rgb(30, 138, 8);
    font-weight: 600;
}

table.table.is-width-auto {
    width:auto;
}

// =========================
// Animation Definitions
// =========================
.katex .mfrac .frac-line {
    background-color: #4a4a4a;
}

.katex .move-degree-left-make-bigger {
    font-family: math;
}

.markdown-inline {
    &.is-overlay {
        white-space: pre;
    }
    .no-wrap-on-whitespace {
        white-space: nowrap;
    }
    .keep-together {
        display: inline-block;
    }
    .katex {
        // position: relative;
        // top: 0.05em;
    }
    em .katex {
        font-style: italic;
    }
    strong {
        color: inherit;
    }
}

.button.is-danger {
    background-color: #fddcdc;
    color: #ce0000;
    &[disabled] {
        color: #333;
        border-color: #fddcdc;
        background-color: #fff;
    }
    &:hover {
        background-color: #CA4949;
    }
}

.button.is-success {
    background-color: #aeeac3;
    color: #067000;
    font-weight: 600;
    &.is-outlined {
        color: #30854e;
    }
    &:hover, &.is-outlined:hover,
    &:focus, &.is-outlined:focus {
        background-color: #30854e;
        border-color: #30854e;
    }
    &[disabled] {
        color: #333;
        border-color: #aeeac3;
        background-color: #fff;
    }
}

.button {
    &.is-info {
        background-color: #297cb3;
        border-color: #297cb3;
        &:hover, &:focus {
            background-color: #297cb3;
            border-color: #297cb3;
        }
        &.is-outlined, &.is-outlined[disabled] {
            color: #297cb3;
            &:hover, &:focus {
                background-color: #297cb3;
                border-color: #297cb3;
            }
        }
    }
}

.menu ul {
    list-style: none;
    margin-left: 0em;
    a {
        font-weight: 400;
    }
    ul {
        list-style-type: none;
    }
}

.tabs ul {
    margin-left: 0em;
    margin-top: 0em;
}

.mcq-max-selected-msg {
    margin-top:1em;
    color:#12479c;
    background-color: #c9deff;
    border-radius: 0.2em;
    padding: 1em;
    line-height: 1.4em;
    white-space: pre-wrap;
    text-align: center;
    .click-dismiss-msg {
        padding-top: 1em;
    }
}

span.mord.text {
    color: black;
}

.is-hi-contrast {
    .banner {
        .banner-title, .banner-subtitle {
            filter: invert(1) contrast(2)
        }
    }
    .runner-container {
        .content-element, .dont-print {
            .ck .ck-content {
                color: #000;
                strong {color: rgb(66, 38, 38);}
            }
            &.is-not-high-contrast-exception {
                
                .ignore-inversion {
                    strong {
                        color: initial;
                    }
                }
                
                color: #fff !important;
                i{
                    &.fas, &.fa {
                        color: initial;
                    }
                }
                .drag-el{
                    color: black;
                }
                strong{
                    color: #fff;
                }
                tr td.header-col{
                    background-color: #333 !important;
                    border-right-color: #DDD !important;
                }
                tr td.header-row{
                    background-color: #333 !important;
                    border-bottom-color: #DDD !important;
                }
                img, span.mord.text{
                    filter: invert(1)
                }
                svg{
                    filter: invert(0);
                }
                table tr.header-row{
                    td,th{
                        background-color: #4d4d4d !important;
                    }
                }
            }
            
        }
    }
    
    .option-container button.option {
        background-color: #fff;
        svg, img {
            filter: unset;
        }
        strong {
            color: #373737!important;
        }
        &.is-active {
            .option-indicator {
                background-color: #00f !important;
                .radio-label {
                    color: #fff !important;
                }
                .radio-container {
                    .radio-outer {
                        border-color: #fff !important;
                    }
                    .radio-inner {
                        background-color: #fff !important;
                    }
                }
            }
        }
        table tr.header-row  {
            td,th{
                background-color: #fff !important;
            }
        }
        tr td.header-col {
            border-right-color: #4d4d4d !important;
            background-color: #fff !important;
            
        }
    }
}

.content table th.flush {
    padding: 0.5em 0.1em;
    &.is-plain {
        font-size: 0.85em;
        padding: 1em 1em;
    }
    white-space: nowrap;
}

element-config-grouping,
element-config-matching {
    .prop-input-grid {
        .prop-input-cell.coord-cell {
            display: none !important;
        }
    }
}

.checkbox input {
    margin-right: 0.5em;
    vertical-align: middle;
}

strong .katex {
    font-weight: 600;
}

.katex .textsf {
    font-family: $body-font !important;
    font-size: 0.826em;
    
}

.hide-ckeditor-toolbar {
    .ck-editor__top {
        // display:none;
        opacity: 0;
    }
}

.solution-block:hover {
    .ck-editor__top {
        opacity: 1;
    }
}

.ck-editor__editable {
    strong {
        color:inherit;
    }
}

@media print {
    .print-question-table {

        &.is-grayscale {
            filter: grayscale(1);
        }

        .fade-in {
            opacity: 1 !important;
            animation: none !important;
            animation-fill-mode:unset !important;
            animation-duration:unset !important;
        }
        .question-row {
            display: block !important;
            flex-direction: unset !important;

            &.is-one-per-page {
                page-break-inside: avoid;
                page-break-after: always;
            }
            .option-container {
                // display:block !important;
                button.option {
                    display:block !important;

                    &.is-polaroid-style {
                        box-shadow: none;
                    }
                }

                &.is-horizontal, 
                &.is-grid {
                    .option-button-container {
                        display: inline-block  !important;
                    }
                }
            }
        }
        .question-panels,
        .question-container,
        .runner-container
        {
            display:block !important;
            white-space: pre-wrap;
        }
        
    }
}

.align-center {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.space-between {
    @extend .align-center;
    justify-content: space-between;
    &.align-top {
        align-items: flex-start;
    }
}

.simple-form, .simple-form-container label {
    @extend .align-center;
    
    font-weight:600;
    input {
        margin: 0.3em;
        &[type="text"], &[type="number"] {
            width: 5em;
        }
    }
}
.simple-form-header {
    font-weight: 600;
    margin-top: 1em;
    font-size: 1.2em;
    text-transform: uppercase;
    color: #c3c3c3;
    border-bottom: 1px solid #ccc;
    margin-bottom: 0.2em;
}
.simple-form-container {
    display:flex;
    flex-wrap: wrap;
    justify-content: space-between;
    label {
        color: #777;
        width:9em;
        margin-right: 1em;
        justify-content: space-between;  
    }
}

.select {
  &.icon-invert {
    &:not(.is-multiple):not(.is-loading) {
        &::after {
            border-color:white;
            border-width: 4px
        }
    }
  }
}

// overwrites to pink-bluegrey material stylesheet
textarea.cdk-textarea-autosize {
    resize: vertical;
}
.mat-radio-button, .mat-checkbox  {
    font-family: $body-font;
}

.space-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

table.is-hoverable tr:hover td {
    background-color: rgba(0,0,0, 0.05);
}
.hide-toolbar-ckeditor {
    .ck.ck-editor__top {
        // opacity: 0;
        display:none;
    }
}

.ck-editor__editable_inline { 
    max-height: 80vh; 
}    
.fixed-height-ckeditor {
    .ck-editor__editable_inline { height: 5em; }    
}
.ck-ck-editor__editable, .ck-content { 
    line-height: 1.3em !important;
    
}

.high-contrast {
    .ck-ck-editor__editable, .ck-content {
        //This color should be inverted in the block
        border-color: black !important;
        border-width: 2px !important;
        color: black !important;
    }  

    .ck, .ck-editor__top {
        // opacity: 0;
        border-color: black !important;
        border-width: 2px !important;
    }
}

.likert-buttons {
    .field, .control, .button {
        font-size: 1em !important;
    }
}

// Drag and Drop


$border-radius: 1em;
.element-render-moveable-dnd {
    position:relative;
    
    .background-el {
        pointer-events:none; 
        overflow: visible; 
        z-index: 15;
        position:absolute;
    }
    .target-el {
        position: absolute;
        display: flex;
        flex-grow: 1;
        align-items: center;
        justify-content: center;
        z-index:20;
        border-radius: $border-radius;
    }
    
    .drag-el {
        cursor: pointer;
        position:relative;
        transition: box-shadow 300ms;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: $border-radius;
        z-index: 50;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
        &.is-deactivateDropShadow{
            box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
        }
        &.is-transparent {
            background-color:rgba(0, 0, 0, 0);
        }
        &:hover {
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
        }
        .img-drag-shield {
            position:absolute; 
            top:0px;
            left:0px;
            right:0px;
            bottom:0px;
        }
    }
}


.element-render-grouping,
.element-render-moveable-dnd {
    .target-el, .drag-drop-list {
        .hover-indicator {
            opacity:0;
            position: absolute;
            top:0px;
            bottom:0px;
            left:0px;
            right:0px;
            border-radius: $border-radius;
            transition: 300ms;
            background-color: $light-blue;
            pointer-events: none;
            &.is-bluewash {
                background-color: #000;
            }
        }
    }
    .drag-el {
        &:hover {
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
        }
    }
    &.is-dragging {
        cursor: grabbing;
        .hover-indicator {
            pointer-events: initial;
        }
        .target-el:hover {
            .drag-el {
                opacity:0.5;
            }
        }
        .target-el, .drag-drop-list {
            &:hover {
                .hover-indicator {
                    cursor: grab;
                    opacity:0.4;
                }
            }
        }
        .drag-el {
            pointer-events: none;
        }
    }
}
.element-render-grouping {
    &.is-dragging {
        .hover-indicator {
            top:0.5em;
            bottom:0.5em;
            left:0.5em;
            right:0.5em;
        }
        .drag-el {
            z-index:100;
        }
    }
}



// 

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

html.is-screenshotting, body.is-screenshotting {
    &, body, app-root,  app-root>div,  app-root>div>div,  >div>div>div>div {
        height: auto !important;
        overflow: visible !important;
        .panels-container { position: relative !important; }
        .panel-questions { display: none !important;  }
        .panel-preview { width:auto !important; position: relative  !important; left: unset !important;right:unset  !important; bottom:unset  !important;right:unset  !important; }
        .panel-editor { display: none  !important; }
        .question-config { display: none !important;}
    }
}

.double-font-size {
    font-size: 2em;
}

$infoButtonBackColor: rgb(0, 145, 255);
.info-button {
    background-color: $infoButtonBackColor;
    height: 3.5em;
    display: flex;
    justify-content: center;
    align-items: center;
}

.item-set-editor {
    
    @import './app/ui-item-maker/styles/drag-drop.scss';
    @import './styles/partials/_modal.scss';

    .custom-modal {
        @extend %custom-modal;
    }

    $selectedColor: #209cee;
    $darkGrey: #555;
    $lightGrey: #d0d0d0;

    $menuSelectionColor: #1c90f3;
    $topDivHeight: 5em;
    $leftPanelWidth: 4em;
    $leftPanelWidthExpanded: 16em;


    // top: 0.5em;
    //     left: 0.5em;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     background-color: #4dc187f2;
    //     font-size: 1em;
    //     padding: 0.3em 1em;
    //     font-weight: 600;
    //     color: rgb(255, 255, 255);
    // position:fixed;
    // top: 0px; left: 0px;
    //  right: 0px;

    .save-indicator {
        background-color: #f1f1f1;
        color: #ccc;
        display:flex;
        justify-content: center;
        align-items: center;
        font-size: 1em;
        padding:0.3em 0.5em;
        font-weight: 600;
        margin-bottom: 1em;
        &.is-green {
            background-color: rgba(62, 207, 135, 0.37);
            color: rgba(20, 143, 81, 0.5);
        }

        &.is-suggestion {
            background-color: #ffb34040;
            color: #fc9d0f;
        }

        &.is-suggestion-cancel {
            background-color: rgba(255,0,0, 0.25);
            color: rgba(255,0,0,0.75);
        }

        &.is-cancel {
            background-color: rgba(207, 135, 65, 0.37);
            color: rgba(143, 81, 20, 0.5);
        }
    }

    // .is-hidden {
    //     display:none;
    // }

    // $questionPanelWidth: 14em;
    $questionPanelWidth: 18em;
    $questionPanelWidthSmall: 4em;
    $rightPanelWidth: 34em;
    $rightPanelExpandedWidth: 54em;
    $roundCornerSmall: 0.2em;


    .is-hidden {
        display:none;
    }
    .panels-container {
        position:fixed; 
        top:0px;
        bottom:0px;
        left:0px;
        right:0px;
        transition: left 400ms;
        background-color: #fff;

        .panel-editor {
            .special-character-keyboard {
                background-color: $gray-dark;
            }
        }


        &.is-focus-view {
            .panel-preview {
                left: 0;
                right: 0;
            }
            .panel-editor {
                width: auto;
                bottom: auto;
                border: none;
                padding-right: 0;
            }
            .runner-container {
                .question-container {
                    align-items: center;
                }
            }
            .navigation-panel-container {
                position: fixed;
                bottom: 0em;
                right: 0em;
                display: flex;
                flex-direction: row;
                align-items: flex-end;
                z-index: 100;
                .navigation-panel {
                    width: 10em;
                    height: 5em;
                    background-color:  #2A304B;
                    border-top-left-radius: 0.7em;
                    box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
                    display: flex;
                    flex-direction: row;
                    justify-content: space-evenly;
                    align-items: center;
                    &.is-wider {
                        width: 12em;
                    }
                    .navigation-icon {
                        width: 45%;
                        height: 86%;
                        background-color: #1e2235;
                        color: white;
                        font-size: 1.2em;
                        padding: 0em;
                        border: none;
                
                        &.left-nav-button {
                            border-top-left-radius: 0.5em;
                        }
                
                        .nav-button-div {
                            display: flex;
                            width: 100%;
                            height: 100%;
                            flex-direction: column;
                            align-content: center;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }
        }

        &.is-left-panel-visible {
            left: $leftPanelWidth;
            &.is-left-panel-expanded {
                left: $leftPanelWidthExpanded;
            }    
        }
    }
    .panel-questions, .panel-preview, .panel-editor { 
        position:absolute; 
        top:0px;
        bottom:0px;
    }
    .panel-questions { 
        left:0px;
        width:$questionPanelWidth;
        padding: 1em;
    }
    .panel-preview { 
        left:$questionPanelWidth;
        right:$rightPanelWidth;
        &.is-condensed {
            right:$rightPanelExpandedWidth;
        }
        // background-color: #f1f1f1;
        border-left: 2px solid #ccc;
        overflow: auto;
        
        .panel-preview-container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 100vh;
        }
        .question-config {
            margin-top:2em;
            padding: 2em;
            background-color: #f1f1f1;
            border-top: 1px solid #ccc;
        }
    }
    .panel-editor { 
        width:$rightPanelWidth;
        right:0px;
        padding: 3em;
        padding-top: 1em;
        border-left: 1px solid #ccc;
        background-color: #fff;
        &.is-expanded {
            width:$rightPanelExpandedWidth;
            border-left: 2px solid #ccc;
        }
        overflow: auto;
    }

    .section-header {
        margin-top:2em;
        margin-bottom:0.5em;
        font-size: 120%; 
        font-weight: 900;
        color: $darkGrey;
    }

    .stack-edit-container {
        border-radius: 2*$roundCornerSmall;
        box-shadow: 8px 8px 8px rgba(0,0,0, 0.2);
        background-color: #f1f1f1;
        // margin: 2em;
        padding:0.5em;
    }

    .stack-edit-element { 
        margin: 0.5em;
        border-radius: $roundCornerSmall;
        background-color: #fff;
        // overflow: hidden;
        overflow: auto;
        .stack-edit-element-header {
            padding: 0.2em;
            display: flex;
            justify-content: space-between;
            color: $lightGrey;
            margin-bottom: 0.5em;
            transition: 200ms;
            cursor: move;
            &:hover {
                background-color: lighten($selectedColor, 0.2);
                color: #fff;
            }
        }
        .stack-edit-element-config {
            padding: 0.4em 1.2em;
        }
    }


    .panel-questions { 
        display:flex;
        flex-direction: column;
        justify-content: space-between;
    }
    // .question-list-container{
    //     display:flex;
    //     flex-direction: column;
    //     align-items: center;
    //     user-select:none;
    //     overflow:auto;
        
    // }
    // .question-block {
    //     width:5em;
    //     height:2em;
    //     min-height:1.5em;
    //     font-size:2em;
    //     display:flex;
    //     align-items: center;
    //     justify-content: center;
    //     border: 1px solid #ccc;
    //     margin-bottom: 0.3em;
    //     cursor: pointer;
    //     .question-block-indicator { 
    //         position: absolute;  
    //         top: 0px; 
    //         right: 3px; 
    //         font-size: 12px;
    //         .is-confirmed { color: #83e8bb; }
    //         .is-unconfirmed { color: #f1f1f1; }
    //     }
    //     .question-block-label {
    //         font-size:0.5em;
    //     }
    //     &.is-info-slide {
    //         height:1em;
    //         justify-content: flex-start;
    //         padding: 0.2em;
    //         background-color: #ececec;
    //         .question-block-label {
    //             font-size:0.4em;
    //             font-weight: 600;
    //             text-align:left;
    //         }
    //     }
    //     &:hover {
    //         border: 1px solid $selectedColor;
    //     }
    //     &.is-active {
    //         border: 1px solid $darkGrey;
    //         color: #fff;
    //         background-color: $selectedColor;
    //     }
    
    // }


    .element-selector-container {
        display:flex;
        flex-wrap: wrap;
        user-select: none;
        
    }
    .element-selector {
        width: 8em;
        height: 6em;
        margin: $roundCornerSmall;
        font-size:1em;
        font-weight:900;
        display:flex;
        flex-direction: column;
        border: 1px solid #ccc;
        color: $darkGrey;
        align-items: center;
        justify-content: center;
        border-radius: $roundCornerSmall;
        cursor: pointer;
        .icon {
            font-size: 1.6em;
            margin-bottom:0.2em;
        }
        &:hover {
            border: 1px solid $selectedColor;
        }
        &.is-disabled {
            color: #ccc;
            &:hover {
                // border: 1px solid rgb(131, 100, 100);
                border: 1px solid #ccc;
            }   
            cursor: default;
        }
    }

    .lang-btn-container{
        display: flex;
        margin-bottom: 1em;
        justify-content: center;
    }

    .mode-toggle {
        margin-bottom:2em; 
        display:flex; 
        flex-direction: row; 
        justify-content: flex-end;
        font-size: 0.93em;
        .mode-toggle-option {
            margin-left:1em;
            padding: 0.3em;

            &.is-active {
                font-weight: 600;
                border-bottom: 2px solid #333;
            }
        }
    }

    .zoom-settings {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-bottom:1em;
        border-bottom: 1px solid #ccc;
        margin-bottom:1em;
    }

    .submission-state-preview {
        padding-top:1em;
        padding-bottom:2em;
        border-bottom: 1px solid #ccc;
    }

    .advanced-setting-option {
        margin-bottom: 0.2em;
    }

    .simple-field {
        display:flex; 
        flex-direction:row; 
        justify-content: flex-start;
        margin-bottom: 0.7em;
        
        &.read-sels {
            flex-direction:column;
            justify-content: flex-start;
        }

        .simple-field-label {
            width: 6.5em;
            font-weight:bold;
        }
        code {
            height: fit-content;
        }
    }
    .pagination {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        >span {
            padding: 0.3em
        }
    }
    .item-detail-review {
        display: flex;
        flex-direction: row;
        .item-table-container {
            flex-grow:3;
            margin-top:2em;  
            position:relative; 
            max-height: 90vh;
            &.is-shared-w { max-width: 70vw; } 
            overflow: auto;
            .item-table {
                td {
                    height: 3.5em;
                    vertical-align: middle;
                }
            }
        }
        .question-review {
            flex-grow:0;
            width: 32em;
            padding: 0.5em;
            padding-top: 2em;
            max-height: 90vh; 
            overflow:auto;
        }
    }
    .framework-container {
        margin-top: 1em;
        margin-bottom: 1em;
        background-color: rgba(0,0,0,0.1);
        padding: 1em;
    }
    .param-card-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        .param-card {
            margin: 1em;
            background-color: #fff;
            box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
            padding: 0.5em;
        
            // :nth-child(3n+1) { order: 1; }
            // :nth-child(3n+2) { order: 2; }
            // :nth-child(3n)   { order: 3; }
        }
    }

    a.close {
        color: #ccc;
    }

    tr.is-reviewed td {
        background-color: #dbfad7;
    }

    .stat-header-indic {
        margin-bottom: -0.6em;
        font-size: 0.875em;
        font-weight: 300;
    }

    .checkbox-icon {
        font-size:1.25em;
        .fa-check-square {
            color: #3b80ff;
        }
    }

    .audit-table {
        .audit-table-col-num {width:3em; border-width: 1px 0px 1px 1px;}
        .audit-table-col-name {width:16em; border-width: 1px 0px;}
        .audit-table-col-btn {width: 16em; border-width: 1px 0px;}
        .audit-table-col-result {}
    }

    .print-question-table {
        .question-row {
            display: flex;
            flex-direction:row;
            page-break-inside : avoid;
            border-bottom: 1px solid #ccc;
            padding-bottom: 1em;
            margin-bottom: 3em;

            .question-row-info {
                width: 18em;
                flex-grow: 0;
            }
            .question-row-content {
                page-break-inside : avoid;
                flex-grow: 0;;

                .question-row-label {
                    display: flex;
                    margin-bottom: 20px;
            
                    >div:first-child {
                        margin-right: 10px;
                    }
            
                    .question-row-label-grow {
                        flex-grow: 1;
                    }
            
                    >div:last-child {
                        max-width: 50%;
                    }
                }
            }
            
        }
    }

    .pre-table-strip {
        margin-bottom:1em; 
        display: flex; 
        flex-direction:row; 
        justify-content: space-between;
    }

    .buttons .button {
        margin: 5px;
    }

    .section-settings-container {
        border-top: 1px solid #f1f1f1;
        border-bottom: 1px solid #f1f1f1;
        padding: 1em 0.5em;
        margin: 1em 0em;
        font-size: 0.8em;
        .section-setting {
            display: flex;
            flex-direction: row;
            align-items: center;
            .first-el { margin-right: 0.5em}
            margin-bottom:0.5em;
        }
        .section-setting-no-row {
            .first-el { margin-right: 0.5em}
            margin-bottom: 0.5em;
        }
        .section-setting-sub {
            border-left:1px dotted rgb(73, 168, 78);
            // margin-top:0.3em;
            padding-left:1em;
            margin-left:0.6em;
            margin-bottom:1em;
        }
    }

    .section-question-row {
        display: flex;
        flex-direction: row;
        margin-bottom:0.5em;
        &:hover {
            background-color: #f1f1f1;
        }
        &.is-question-nav-open {
            background: #eef6fc;
        }
        button {
            flex-grow:0;
        }
        .question-label {
            flex-grow:1;
            padding:0.3em;
        }
    }

    .section-question-review {
        flex-grow: 0;
        width: 30em;
        padding: 1em;
        border-top: 1px solid #ccc;
        max-height: 90vh;
        overflow: auto;
        
    }

    .slide-top {
    display:none;
    &.is-active {
        position:absolute;
        top:0px;
        left:0px;
        display:block;
        padding:0.3em 0.5em;
        width:10em;
        background-color:#f1f1f1;
        border: 1px solid #ccc;
        box-shadow: 5px 5px 20px rgba(0,0,0,0.2);
        animation: slide-top 1000ms cubic-bezier(0.455, 0.030, 0.515, 0.955) both;
    }
    }

    @keyframes slide-top {
    0% {
        opacity: 0;
        transform: translateY(0);
    }
    20% {
        opacity: 1;
        transform: translateY(-10px) rotate(0deg);
    }
    80% {
        opacity: 1;
        transform: translateY(-15px) rotate(2deg) translateX(0px);
    }
    100% {
        opacity: 0;
        transform: translateY(-200px) rotate(0deg) translateX(40px);
    }
    }

    .is-not-claimed {
        color: #999;
    }

    .no-pointer-events {
        pointer-events: none;
    }

    a.is-disabled {
        pointer-events: none;
        color: lightgray;
        cursor: default;
    }

    a.is-disabled-black {
        pointer-events: none;
        color: black;
    }

    .select.is-disabled {
        pointer-events: none;
        &::after {
            border-color: #7a7a7a;
        }
    }

    .multiline {
        white-space: pre-wrap;
    }
}

.tooltip {
    position: relative;
    display: inline-block;
    .tooltip-text {   
        display: block;         
        position : absolute;
        content : attr(tooltip);
        width: max-content;
        background-color: #e65454;
        box-shadow: 3px 2px 5px rgba(0, 0, 0, .25);
        color: #ffffff;
        padding: 0.5em;
        font-weight: normal;
        bottom: 25px;
        white-space: pre;
        opacity: 0;
        transition: opacity .25s ease;
        pointer-events: none;
        cursor: text;
        left: 50%;
        transform: translateX(-50%);
        &[align="left"] {
            left: 0;
            right: auto;
            transform: translateX(0);
        }
        &[align="right"] {
            left: auto;
            right: 0;
            transform: translateX(0);
        }
    }
    &:hover .tooltip-text {        
        opacity : 1;
    }
}
.split-view-right {
    .bookmark {
        &.splitview {
            background-color: rgba(255, 255, 0, 0.4);
        }
    }
}
.bookmark {
    display: inline;
    &.is-active {
        background-color: rgba(255, 255, 0, 0.4);
    }
    &.always {
        background-color: rgba(255, 255, 0, 0.4);
    }
}

.header-info {
    margin-top: 1em;
    background-color: #e7f3fe;
    border-left: 0.3em solid #2196F3;
}    
.scroll-table-container {
    margin-bottom:2em; 
    max-height:70vh; 
    overflow: auto;
}
table.data-table {
    &.is-small {
        font-size: 0.8em;
    }
    white-space: nowrap;
    line-height: 1.0em;
    width:auto;
    th {
        vertical-align: bottom;
        white-space: pre;
    }
    a {
        font-weight: 500;
        text-decoration: underline;
    }
}

.response-set-element {
    display:flex; 
    flex-direction: row; 
    align-items: center; 
    padding-top:0.2em; 
    padding-bottom:0.2em; 
    padding-right:0.2em; 
    border-bottom: 1px solid #ccc;
    background-color: rgba(255,255,255,0.9);
    a {
        color: #333;
        text-decoration: underline;
    }
    &.is-selected {
        background-color: #2650c4;
        color: #fff;
        a {
            color: #fff;
            font-weight: 600;
        }
    }
}

.element-type-grouping {
    .pos-el {
      position:absolute;
    }
    .drag-el {
      // position:absolute;
      // position: inherit;
      cursor: pointer;
      margin: 0.2em;
      transition: box-shadow 300ms;
      display:flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #999;
      user-select: none;
      // &.is-centered {
      // }
      &:hover {
        box-shadow: 0px 0px 10px rgba(6, 6, 6, 0.5);
      }
    }
  
    .pos-container{
      position:relative;
      // border:1px solid #000;
      .pos-container-sub {
        display:flex;
        flex-direction: column;
        // align-items: center;
        &.is-targets-above {
          flex-direction: column-reverse;
          .group-container {
            margin-bottom:1.5em;
          }
        }
      }
    }
  
    .drag-drop-source {
      display: flex;
      flex-direction: row;
      flex: 1 1 0%;
      align-items: flex-start;
      flex-wrap: wrap;
      border: unset;
      // justify-content: center;
      min-height:4em;
      transition:200ms;
      // height: 100%;
      &.is-empty {
        background-color: #f1f1f1;
        margin-bottom: 0.5em;
        min-height:6em;
      }
    }
    .drag-drop-container {
      //width: 15em;
      max-width: 100%;
      //margin: 0 25px 25px 0;
      display: flex;
      flex-flow: column;
      // display: inline-block;
      // vertical-align: top;
      border: solid 1px black;
      .group-title {
        padding: 10px;
        border-bottom: 1px solid #ccc;
        display: flex;
        justify-content: center;
        position: relative;
      }
      .drag-drop-list {
        position: relative;
        border-radius: 0.4em;
        overflow: hidden;
        display: block;
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-grow: 1;
        padding-top:0.5em;
        padding-bottom:2em;
      }
    }
  
  
  
  
    .drag-drop-box {
      padding: 20px 10px;
      border-bottom: solid 1px #ccc;
      color: rgba(0, 0, 0, 0.87);
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      cursor: move;
      background: white;
      font-size: 14px;
    }
  
    .cdk-drag-preview {
      box-sizing: border-box;
      border-radius: 4px;
      box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
    }
  
    .cdk-drag-placeholder {
      opacity: 0;
    }
  
    .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  
    .drag-drop-box:last-child {
      border: none;
    }
  
    .drag-drop-list.cdk-drop-list-dragging .drag-drop-box:not(.cdk-drag-placeholder) {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  
    .question-block-label {
      font-size: 18px;
    }
  
    .group-container {
      display: flex;
      flex-wrap: wrap;
  
      &.is-side-by-side {
        flex-wrap: nowrap;
      }
  
      &.is-vertical {
        flex-direction: column;
        .drag-drop-container {
          display:flex;
          flex-direction: row;
          width: 20em;
          .group-title {
            width: auto;
            flex-grow: 0;
            justify-content: start;
            border-bottom: none;
          }
          .drag-drop-list {
            min-height: 3em;
            min-width: 3em;
            flex-grow:1;
          }
        }
      }
    }
  
    .mb-3 {
      margin-bottom: 1.5rem;
    }  
  }
  
  .is-hi-contrast .runner-container .content-element.is-not-high-contrast-exception {
    .element-type-grouping {
      .inline-text-block {
        strong {
          color: #363636 !important;
        }
      }
    }
  }

  .element-type-insertion {
    .pos-el {
      position:absolute;
    }
    .drag-el {
      position:absolute;
      cursor: pointer;
      transition: box-shadow 300ms;
      margin-bottom: 0.5em;
      user-select: none;
      &:hover {
        box-shadow: 0px 0px 10px rgba(6, 6, 6, 0.5);
      }
      &.is-placed {
        padding: 0em 0.2em;
        border: none;
        height: auto;
        position: relative;
        top: -0.2em;
      }
    }
  
    .is-space-after {
      margin-right: 0.2em;
    }
  
    .block-el {
      display:inline;
      vertical-align: baseline;
    }
  
    .pos-container{
      position:relative;
      // border:1px solid #000;
    }
  
    .drag-drop-source {
      min-height:3em;
      &.is-empty {
        background-color: #f1f1f1;
        margin-bottom: 0.5em;
      }
    }
    .block-drop {
      display:inline-block;
      border: solid 2px #ababab;
      margin: 0em 0.3em;
      border-radius: 0.2em;
      position: relative;
      top: -0.2em;
      padding: 0.2em;
      .drag-drop-list {
        min-width: 2.5em;
        min-height: 1.2em;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }
    .inline-drop {
      position:relative;
      min-width: 0.3em;
      min-height: 0.5em;
      display:inline-block;
    
      .drag-drop-list {
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      &.has-no-content {
        .drag-drop-list {
          position:absolute;
          top:-0.5em;
          left:-0.2em;
          right:-0.8em;
          bottom:-0.50em;
          //border: 1px solid red;
        }
      }
    }
  
  
  
    .drag-drop-box {
      padding: 20px 10px;
      border-bottom: solid 1px #ccc;
      color: rgba(0, 0, 0, 0.87);
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      cursor: move;
      background: white;
      font-size: 14px;
    }
  
    .cdk-drag-preview {
      box-sizing: border-box;
      border-radius: 4px;
      box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
      0 8px 10px 1px rgba(0, 0, 0, 0.14),
      0 3px 14px 2px rgba(0, 0, 0, 0.12);
    }
  
    .cdk-drag-placeholder {
      opacity: 0;
    }
  
    .cdk-drag-animating {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  
    .drag-drop-box:last-child {
      border: none;
    }
  
    .drag-drop-list.cdk-drop-list-dragging .drag-drop-box:not(.cdk-drag-placeholder) {
      transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
    }
  
    .question-block-label {
      font-size: 18px;
    }
  
    .group-container {
      display: flex;
      flex-wrap: wrap;
  
  
      &.is-vertical {
        flex-direction: column;
        .drag-drop-container {
          display:flex;
          flex-direction: row;
          width: 20em;
          .group-title {
            width: auto;
            flex-grow: 0;
            justify-content: start;
            border-bottom: none;
          }
          .drag-drop-list {
            min-height: 3em;
            min-width: 3em;
            flex-grow:1;
          }
        }
      }
    }
  
    .mb-3 {
      margin-bottom: 1.5rem;
    }
  
    .drag-el {
      position: inherit;
    }
  }
  
  .is-hi-contrast .runner-container .content-element.is-not-high-contrast-exception {
    .element-type-insertion {
      .drag-drop-source {
        strong {
          color: #363636 !important;
        }
      }
      .block-el {
        strong {
          color: #fff !important;
        }
        .cdk-drop-list {
          strong {
            color: #363636 !important;
          }
        }
      }    
    }
  }

  $selectedColor: #209cee;
$borderColor: #ccc;
.element-type-ordering {
    .option-container {
        display:flex;
        justify-content: center;
        align-items: center;
        flex-grow: 1;
        background-color: #ffffff;
        &.is-vertical {
            flex-direction: column;
            align-items: center;
        }
        &.is-horizontal {
            flex-direction: row;
            align-items: center;
            flex-wrap: nowrap;
            &.is-wrap-allowed {
                flex-wrap: wrap;
            }
            &.answers {
                justify-content: flex-start;
            }
        }
        &.is-target-mode-source {
            min-width: 16em;
            min-height: 6em;
            background-color: #f1f1f1;
            // [style.min-width.em]="isTargetMode() ? '2em' : 'none'"
        }
    }
    .inner-option-container {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-grow: 1;
        &.is-vertical {
            flex-direction: column;
            .option {
                flex-grow: 1;
            }
        }
        &.is-horizontal {
            flex-direction: row;
            padding-bottom: 1em;
            .option {
                flex-grow: 1 ;
            }
        }
    }
    .option {
        border: 3px solid $borderColor;
        //background-color: #f1f1f1;
        // border-radius: 0.5em;
        padding: 0.5em;
        &.not-in-target {
            margin: 0.2em 0.2em;
        }

        &.is-fixed {
            background-color: $borderColor;
            &:hover {
                border-color: $borderColor;
            }
        }

        user-select: none;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        &:hover {
            border: 3px solid #999;
        }

    }
    .drag-el {
        user-select: none;
    }
}

.is-hi-contrast .runner-container .content-element.is-not-high-contrast-exception {
    .element-type-ordering {
        strong {
            color: #363636 !important;
        }
    }
}



.element-type-table {
    table td {
        border: 1px solid #666;
        border-bottom: 1px solid #666;
    }
    table tr:last-child td {
        border-bottom-width: 1px;
    }

    table tr.header-row td {
        font-weight:900;
        border-bottom: 4px solid #333;
        background-color: #f1f1f1;
    }
    table tr td.header-col {
        font-weight:900;
        border-right: 4px solid #333;
        background-color: #f1f1f1;
    }

    .element-text {
        white-space: pre-wrap;
    }
    .inline-block {
        display: inline-block;
    }
}

.ag-grid-fullpage {
    width: auto; 
    // height: 600px; 
    height: 70vh; 
    max-width:100%;
}
.ag-grid-halfpage {
    height: 70vh; 
    width: 30em;
    max-width:100%;
}
.ag-grid-halfpage-vertical {
    height: 35vh; 
    width: auto;
    max-width:100%;
}

.specific-sa-dash-view {
    @extend %view-school-admin-dashboard;
}

.panel-editor {
    .code-mirror-sizer .CodeMirror{
        height: auto;
        width: 23em;
    }
    &.is-expanded {
        .code-mirror-sizer .CodeMirror{
            width: 43em;
        }   
    }
}

.diagram-editor {
    .code-mirror-sizer-modal .CodeMirror{
        height: 30vh;
    }
}

.passage {
    white-space: pre;
    line-height: 1.5em;
    &.is-framed {
        padding: 1em;
        border: 1px solid #ccc; /* style class option */
    }
    .img-container {
        display:inline-block;
        margin: 0.5em;
        clear: both;
        &.align-right  { float: right; }
        &.align-left   { float: left;}
        &.align-center { 
            display:block;
            text-align:center;
        }
        img {
            // width: 12em;  /* config */ 
            // float: right; /* config */ 
        }
    }
        /* Tooltip content */
    .tooltip-content {
        position: absolute;
        background-color: #000000;
        color: #fff;
        padding-left: 8px;
        padding-right: 8px;
        border-radius: 6px;
        z-index: 1;
        visibility: hidden;
        opacity: 1;
        transition: visibility 0s, opacity 0.2s;
        white-space: nowrap;
        font-size: 0.7em;
    }

    /* Show the tooltip on hover */
    .tooltip-container:hover .tooltip-content {
        visibility: visible;
        opacity: 1;
    }
    span.small-caps {
        font-variant: small-caps;
    }
    .passage-numbering-placer {
        position: absolute; 
        width: 2em;
        &.align-left {
            left: -2em; 
        }
        &.align-right {
            right: -3em;
        }
        &.is-lines {
            bottom: 0em; 
        }
        &.is-paragraph {
            top: 0em; 
        }
        .passage-numbering {
            font-size: 0.75em
        }
    }
}
.is-hi-contrast {
    .passage {
    }
}

.test-runner-live {
    .is-input-read-only{
        opacity: 0.5;
        &:focus, &:hover{
            border-color:rgb(210, 210, 210);
            box-shadow: none;
        }
    }
}
