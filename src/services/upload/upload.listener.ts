import { Application } from '../../declarations';
import authentication from '../../authentication';
import * as XLSX from 'xlsx';
import { generateExcel } from '../download-data-frame/download.listener';
import { Readable } from 'stream';
import logger from '../../logger';
import { RequestHandler } from 'express';
import { S3, <PERSON> } from 'aws-sdk';
import { Errors } from '../../errors/general';
import { getSignedUrl } from "@aws-sdk/cloudfront-signer";

const fileUpload = require('express-fileupload');
const short = require('short-uuid');
const translator = short();
const fs = require('fs');

export const AWS_CONFIG = {
  secretAccessKey: 'Czd414/T9Agn0+d4P2xo1Z6ejQxNdaURBdf6WmAF',
  accessKeyId: '********************',
  region: 'ca-central-1'
}
export const STORAGE_BUCKET = 'storage.mathproficiencytest.ca';

// configure file uploads
const s3 = new S3({
  ... AWS_CONFIG
});

const polly = new Polly({
  ... AWS_CONFIG
});

export enum CLOUD_FRONT_ACCESS_TYPE {
  SIGNED = 'SIGNED',
  PUBLIC = 'PUBLIC'
}

export type CloundFrontConfig = {
  bucket_name: string;
  cloudfront_domain: string;
  type: CLOUD_FRONT_ACCESS_TYPE;
  signed_config?: {
    key_path:string,
    key_id:string
  }
}

//config bucket cloudfronts

//1. "authoring.mathproficiencytest.ca" bucket cloudfront "https://d3azfb2wuqle4e.cloudfront.net"
// comments out for now since its public it can be directly access so no API end point to generate signedURL is require now.
// if uncomments remeber to add it into bucketCloudFrontList
// export const authoringBucketCloudFront:CloundFrontConfig = {
//   bucket_name: "authoring.mathproficiencytest.ca",
//   cloudfront_domain: "https://d3azfb2wuqle4e.cloudfront.net",
//   type: CLOUD_FRONT_ACCESS_TYPE.PUBLIC
// }

//2. "storage.mathproficiencytest.ca" bucket cloudfront "https://d3c1mbjtikq6ue.cloudfront.net"
export const storageBucketCloudFront:CloundFrontConfig = {
  bucket_name: "storage.mathproficiencytest.ca",
  cloudfront_domain: "https://eqao.vretta.com/storage",
  type: CLOUD_FRONT_ACCESS_TYPE.SIGNED,
  signed_config :{
    key_path: 'src/services/upload/keys/cloudfront-vea-storage.pem',
    key_id: 'K4ZW9U7FNM12N'
  }
}

export const bucketCloudFrontList = [storageBucketCloudFront]

export const synthesizeSpeech = async (Text: string, lang:string, uid:number=0) : Promise<string> => {
  return new Promise((resolve, reject) => {
    var params:any = {
      OutputFormat: "mp3",
      // SampleRate: "22050",
      Text,
      TextType: "text",
    };
    if (lang === 'en'){
      params.VoiceId ="Joanna";
    }
    else if (lang === 'fr'){
      params.LanguageCode = "fr-CA";
      params.VoiceId = "Chantal";
    }

    polly.synthesizeSpeech(params, function(err:any, data:any) {
      if (err) console.log(err, err.stack); // an error occurred
      else     console.log(data);           // successful response
    try{
      if (data.AudioStream instanceof Buffer) {
        const Bucket = 'authoring.mathproficiencytest.ca';
        const uuid = translator.new();
        const filePath = [
          'voice_uploads',
          uid,
          Date.now(),
          `voice-${uuid}.mp3`,
        ].join('/');
        const params:any = {
          Bucket,
          Key: filePath,
          Body: data.AudioStream
        };
        s3.upload(params, (err:any, data:any) => {
          if (err) {
            const message = JSON.stringify({err, message: 'ERROR_UPLOADING_DATA'});
            console.log(message);
            reject({msg: 'Error Uploading Data: ' + JSON.stringify(err) + '\n' + JSON.stringify(err.stack)});
          }
          else {
            const url = 'https://vea-authoring.vretta.com/' + filePath;
            resolve(url)
          }
        })
      }
    }
    catch(err){
      console.log("the error is occuring because Maximum text length has been exceeded ")

    }
    })
    // fs.writeFile("../speech.mp3", data.AudioStream)
   });
}

const pollyTest = async () => {
  const tests = [
    // ['24 m^3', 'en'],
    // ['24 m3', 'en'],
    // ['24 m cubed', 'en'],
    // ['((1/3)^3)^2', 'en'],
    // ['Table. Row 1, Column 1: Time, t (days). Column 2: Heights, h (cm). Row 2, Column 1: 0. Column 2: 1.5. Row 3, Column 1: 1. Column 2: 4.5. Row 3, Column 1: 2. Column 2: 7.5', 'en'],
    // ['C = 11 + 2.25n ', 'en'],
    // ['W = 1 over 8 times n + 2', 'en'],
    ['W = 1 sur 8 times n + 2', 'en'],
    // ['W = \\frac{1}{8}n + 2', 'en'],
    // ['open bracket, open bracket, 1 over 3, close bracket, exponent 3, close bracket, exponent 2', 'en'],
    // ['ouvre parenthèse, ouvre parenthèse, 1 sur 3, ferme parenthèse, exposant 3, ferme parenthèse, exposant 2', 'fr'],
  ]
  for (let i=0; i<tests.length; i++){
    const text = tests[i][0];
    const url_en = await synthesizeSpeech(text, 'en');
    const url_fr = await synthesizeSpeech(text, 'fr');
    console.log('en voice', text, url_en)
    console.log('fr voice',text, url_fr)
  }
}

export const storeInS3 = (filePath:string, data:Buffer | Blob | string | Readable): Promise<any> => {
  return new Promise((resolve, reject) => {
    const params:any = {
      Bucket: STORAGE_BUCKET,
      Key: filePath,
      Body: data
    };
    s3.upload(params, (err:any, data:any) => {
      if (err){
        console.log(JSON.stringify({err, message: 'S3_UPLOAD_FAIL'}));
        reject('S3_UPLOAD_FAIL');
      }
      else{
        resolve(filePath)
      }
    })
  })
}

export const pullFromS3 = (filePath:string) => {
  return new Promise((resolve, reject) => {
    const params:any = {
      Bucket: STORAGE_BUCKET,
      Key: filePath,
    };
    s3.getObject(params, (err, data) => {
      if (err){
        console.log(JSON.stringify({err, message: 'S3_GETOBJECT_FAIL'}));
        reject('S3_GETOBJECT_FAIL');
      }
      else{
        resolve(<Buffer> data.Body)
      }
    })
  })
}

/** Get a stream of an S3 objects contents
 */
export const streamFromS3 = (bucket:string, objectKey:string) => {
  const params = {
    Bucket: bucket,
    Key: objectKey,
  };

  try {
    const stream = s3.getObject(params).createReadStream();
    return stream;
  } catch (err) {
    console.error('Error fetching object from S3', err);
    throw err;
  }
}

export const copyToNewBucketS3 = (desFilePath: string, srcPath:string) => {
  return new Promise((resolve, reject) => {
    const params:any = {
      Bucket: STORAGE_BUCKET,
      Key: desFilePath,
      CopySource: `${STORAGE_BUCKET}/${srcPath}`
    };
    s3.copyObject(params, (err:any, data:any) => {
      if (err){
        console.log(JSON.stringify({err, message: 'S3_COPY_FAIL'}));
        reject('S3_COPY_FAIL');
      }
      else{
        resolve(desFilePath)
      }
    })
  })
}

export const storeJpegBase64InS3 = (filePath: string, base64: string) => {
  return new Promise((resolve, reject) => {
    const bin = Buffer.from(base64.replace(/^data:image\/\w+;base64,/, ""), 'base64');
    const params:any = {
      Bucket: STORAGE_BUCKET,
      Key: filePath,
      Body: bin,
      ContentEncoding: 'base64',
      ContentType: 'image/jpeg',
    };
    s3.putObject(params, (err:any, data:any) => {
      if (err){
        console.log(JSON.stringify({err, message: 'S3_UPLOAD_FAIL'}));
        reject('S3_UPLOAD_FAIL:' + JSON.stringify(err) + '\n' + JSON.stringify(err.stack));
      }
      else{
        resolve(filePath)
      }
    })
  })
}

// storeInS3('test_forms/pregen/2/hit.json', '{"content": 2}');

export const setupUploadListener = (app:Application)=>{

  // pollyTest();

  app.use(fileUpload({
    defParamCharset: 'utf8'
  }));
  // configure file uploads


  const validateUser_legacy = (jwt:string, uid:number, isSkipped:boolean, res:any) => {
    if (!isSkipped ){
      return new Promise((resolve, reject) => {
        app.defaultAuthentication()
          .verifyAccessToken(jwt)
          .then(pass => {
            if ( (''+pass.uid) != (''+uid)){
              res.status(400).send({msg: 'Could not validate account.' });
              return reject();
            }
            return resolve(pass);
         })
      })
    }
    return Promise.resolve({});
  }

  const validateUser = async (jwt:string, uid:number, isSkipped:boolean, res:any) => {
    if (isSkipped) {
      return {};
    }
    const pass = await app.defaultAuthentication().verifyAccessToken(jwt)
    if ( (''+pass.uid) != (''+uid)){
      res.status(401).send({msg: 'Could not authenticate account.' });
      throw new Errors.NotAuthenticated();
    }
    return pass;
  }

  const FORM_PROP_FILE = 'form_upload';

  const uploadPrepMiddleware:RequestHandler = async (req, res, next) => {
    const uid = req.body.uid;
    const jwt = req.body.jwt;
    const files = (<any>req).files;
    if (!files || Object.keys(files).length === 0 || !files[FORM_PROP_FILE]) {
      return res.status(400).send({msg: 'No data was uploaded.'});
    }
    const fileName = req.body.fileName;
    const fileUploaded = files[FORM_PROP_FILE];
    const amznTraceId = req.headers['x-amzn-trace-id'];
    const logInfo = { uid, fileName, amznTraceId }
    res.locals.uploadLabel = req.path.replace(/^\//, '');
    logger.info(`upload:${res.locals.uploadLabel}:starting`, {
      fileSize: fileUploaded.size,
      fileHash: fileUploaded.md5,
      fileMimetype: fileUploaded.mimetype,
      fileTruncated: fileUploaded.truncated,
      fileNameFromFile: fileUploaded.name,
      ...logInfo
    });

    try {
      await validateUser(jwt, uid, false, res);
    } catch (error) {
      logger.error(`upload:${res.locals.uploadLabel}:not-authenticated`, {
        jwt,
        error,
        ...logInfo
      });
      res.status(401).send({msg: 'Could not authenticate account.' });
      return;
    }
    res.locals.preppedUpload = {
      fileUploaded,
      logInfo,
      uid,
      fileName
    }
    next();
  };

  app.post('/convert-xlsx-to-json', async (req:any, res:any) => {
    const uid = req.body.uid;
    const jwt = req.body.jwt;

    const files = (<any>req).files;

    if (!files || Object.keys(files).length === 0 || !files[FORM_PROP_FILE]) {
      return res.status(400).send({msg: 'No files were uploaded.'});
    }
    const file = files[FORM_PROP_FILE];
    validateUser_legacy(jwt, uid, false, res)
    .then(async ()=>{
      const buffer = file.data;
      const wb : XLSX.WorkBook = XLSX.read(buffer, {type: 'array'});
      const firstSheet = Object.values(wb.Sheets)[0];
      const json =
      res.send({json: XLSX.utils.sheet_to_json(firstSheet, {raw: false, defval: null})});
    })
    .catch(e =>{
      res.status(400).send({msg: 'Error Processing Excel'});
    })
  })

  app.post('/upload-json-as-xlsx', uploadPrepMiddleware, async (req:any, res:any) => {
    const { fileUploaded, logInfo, uid, fileName } = res.locals.preppedUpload;

    const records = JSON.parse(fileUploaded.data.toString());
    const excelFile = generateExcel(records);
    logger.info(`upload:${res.locals.uploadLabel}:excel-generated`, logInfo);
    const filePath = [
      'user_uploads',
      uid,
      'xlsx-export',
      fileName,
      Date.now(),
      fileName+'.'+'xlsx',
    ].join('/');

    const Bucket = STORAGE_BUCKET;
    const params:any = {
      Bucket: Bucket,
      Key: filePath,
      Body: excelFile
    };

    try {
      await s3.upload(params).promise();
      const url = generateS3DownloadUrl(filePath);
      logger.info(`upload:${res.locals.uploadLabel}:s3-upload-success`, logInfo);
      res.send({success: true, pass:null, url});
    } catch (err:any) {
      logger.error(`upload:${res.locals.uploadLabel}:s3-upload-fail`, {
        filePath,
        error: err,
        ...logInfo
      });
      const respMsg:{ msg: string, error?: string} = {
        msg: 'Error Uploading Data',
        error: app.get('isDevMode')
          ? JSON.stringify(err) + '\n' + JSON.stringify(err?.stack)
          : undefined
      }
      res.status(500).send(respMsg);
    }
  })

  app.post('/upload', (req:any, res:any) => {
    const files = (<any>req).files;
    if (!files || Object.keys(files).length === 0 || !files[FORM_PROP_FILE]) {
      return res.status(400).send({msg: 'No files were uploaded.'});
    }
    const uid = req.body.uid;
    const purpose = req.body.purpose;
    const jwt = req.body.jwt;
    const isPermaLink = req.body.isPermaLink;
    const file = files[FORM_PROP_FILE];
    const fileNameSplit = file.name.split('.');
    const fileExt = fileNameSplit.pop();
    const fileName = fileNameSplit.join('.');
    let Bucket = STORAGE_BUCKET;

    if (purpose === 'cts-tech-readi') {
      if (fileExt !== 'docx' && fileExt !== 'pdf') {
        return res.status(400).send({msg: 'Invalid document type. Must be pdf or docx' });
      }
    }

    if (purpose === 'authoring'){
      Bucket = 'authoring.mathproficiencytest.ca';
    }
    if (purpose === 'vea'){
      Bucket = 'uploadtovretta';
      // Bucket = 'storage.eassessment.ca';
    }
    let filePath = [
      'user_uploads',
      uid,
      purpose,
      fileName,
      Date.now(),
      fileName+'.'+fileExt,
    ].join('/');

    if(purpose === 'admin-upload-scan'){
      const folderName = 'admin_upload_scans/'
      filePath = folderName+fileName+'.'+fileExt
    } else if (purpose === 'altversion-access-files') {
      const folderName = 'altversion_access_files/'
      filePath = [
        folderName,
        fileName,
        Date.now(),
        fileName+'.'+fileExt,
      ].join('/');
    }
    const params:any = {
      Bucket,
      Key: filePath,
      Body: files[FORM_PROP_FILE].data
    };
    if(fileExt === 'svg'){
      params.ContentType = 'image/svg+xml';
    }


    // if (isPermaLink === '1'){ // causes the write to fail
    //   params.ACL = 'public-read'
    // }
    // validate
    validateUser_legacy(jwt, uid, (purpose === 'vea'), res).then( pass => {
      s3.upload(params, (err:any, data:any) => {
        if (err) {
          const message = 'ERROR_UPLOADING_DATA: ' + JSON.stringify(err) + '\n' + JSON.stringify(err.stack);
          console.log(message);
          res.status(400).send({message});
          return
        }
        if (purpose === 'authoring'){
          const url  = 'https://vea-authoring.vretta.com/' + filePath;
          const urls = [url];
          if(fileExt == "mp4") {
            urls.push('https://vea-authoring.vretta.com/' + '/video/' + filePath + '.webm')
          }
          res.send({success: true, pass:null, url, urls});
          return
        }
        res.send({success: true, pass,  filePath, url:generateS3DownloadUrl(filePath, 604800)});
      })
    })
  })

}

export const uploadStrFileToPath = async (filePath:string, fileData:string): Promise<{url:string, filePath:string}> => {
  await storeInS3(filePath, fileData).catch((err:any) => {
    throw new Error('ERROR_UPLOADING_DATA: ' + JSON.stringify(err) + '\n' + JSON.stringify(err.stack));
  })

  return {filePath, url: generateS3DownloadUrl(filePath, 60 * 1000)};
}

export const generateSebDownloadUrl = (filename:string)=>{
    return generateS3DownloadUrl(filename, 300);
}

export const signS3DownloadUrl = (url:string, bucketPrefix:string='s3://storage.mathproficiencytest.ca/') => {
  return generateS3DownloadUrl(url.replace(bucketPrefix,''), 60, bucketPrefix.replace('s3://','').replace('/',''))
}

/**
 * foundthe bucket have a cloundfront setting
 * @param bucket
 * @returns CloundFrontConfig|undefined
 */
export const getBucketCloudFront = (bucket:string) :CloundFrontConfig | undefined => {
  const bucketCloudFront = bucketCloudFrontList.find(cf => ''+cf.bucket_name === ''+ bucket)
  return bucketCloudFront
}

/**
 * Get the s3 download url.
 * If the bucket has a configed cloundfront domain, use the cloudfront domain instead.
 * @param filename
 * @param expireSeconds
 * @param bucket
 * @returns s3 file url string
 */
export const generateS3DownloadUrl = (filename:string, expireSeconds:number=60, bucket:string = 'storage.mathproficiencytest.ca') => {
    //get bucket cloundfront setting
    const bucketCloudFront = getBucketCloudFront(bucket)

    if(!bucketCloudFront){ // if no cloundfront setting, return the s3 link directly.
      const config:any = {
        Bucket: bucket,
        Key: filename,
      }
      if (expireSeconds){
          config.Expires = expireSeconds
      }
      const url = s3.getSignedUrl('getObject', config)
      return url
    }else{
      // if there is cloundfront setting., return the cloundfront link instead.

      // Encode URL manually so that any special characters in filename (e.g. accents) are correctly handled
      const encodedFilename = filename.split("/").map(part => encodeURIComponent(part)).join("/")

      const fileS3UrlEncoded = `${bucketCloudFront.cloudfront_domain}/${encodedFilename}`
      switch(bucketCloudFront.type){

        // cloundfront link need to be signed
        case CLOUD_FRONT_ACCESS_TYPE.SIGNED:
          const expireTimeInMilliseconds = expireSeconds * 1000
          const privateKey = fs.readFileSync(bucketCloudFront.signed_config?.key_path || '', 'utf8')+'';
          const config = {
              url: fileS3UrlEncoded,
              keyPairId: bucketCloudFront.signed_config?.key_id || '',
              dateLessThan: new Date(Date.now() + expireTimeInMilliseconds).toISOString(),
              privateKey,
          }
          const signedUrl = getSignedUrl(config);
          return signedUrl;

        //clound front link is public
        case CLOUD_FRONT_ACCESS_TYPE.PUBLIC:
        default:
          return fileS3UrlEncoded
      }
    }
}

export const generateS3UploadUrl = (filename:string, expireSeconds:number=60, bucket:string = 'storage.mathproficiencytest.ca')=>{
    const config:any = {
        Bucket: bucket,
        Key: filename,
    }
    if (expireSeconds){
        config.Expires = expireSeconds
    }
    const url = s3.getSignedUrl('putObject', config)
    return url;
}

export const listObjects = (folderPath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    const params:any = {
      Bucket: STORAGE_BUCKET,
      Prefix: folderPath,
    };
    s3.listObjectsV2(params, function(err:any, data:any) {
      if (err){
        console.log(JSON.stringify({err, message: 'S3_LIST_OBJECT_FAIL'}));
        reject('S3_LIST_OBJECT_FAIL:' + JSON.stringify(err) + '\n' + JSON.stringify(err.stack));
      }
      else{
        resolve(data)
      }
    });
  })
}


/** Lists all object keys in an S3 bucket (AWS SDK v2), using pagination.
 *
 * WARNING: there is no limit to the number of object keys that can be returned.
 */
export async function listObjectsV2(bucket: string, prefix: string) {
    const objects: string[] = [];
    let isTruncated = true;
    let continuationToken: string | undefined = undefined;
    try {
      while (isTruncated) {
        const params: AWS.S3.ListObjectsV2Request = {
          Bucket: bucket,
          Prefix: prefix,
          ContinuationToken: continuationToken,
        };

        const response = await s3.listObjectsV2(params).promise();

        if (response.Contents) {
          objects.push(...response.Contents.map( (o: any) => o.Key ));
        }

        isTruncated = response.IsTruncated || false;
        continuationToken = response.NextContinuationToken;
      }
    } catch (err: any) {
      console.error(`Error listing objects for ${bucket}: ${err.name}: ${err.message}`);
    }

    return objects;
}

