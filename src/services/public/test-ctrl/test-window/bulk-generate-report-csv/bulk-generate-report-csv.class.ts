import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle, dbRawReadSingleReporting, dbRawWrite } from '../../../../../util/db-raw';
import { TestWindowType } from '../../../alt-version-ctrl/alt-version-requests/types';
import { DBD_U_ROLE_TYPES } from '../../../../../constants/db-extracts';
import { storeInS3 } from '../../../../upload/upload.listener';
import _ from 'lodash';
import logger from '../../../../../logger';

interface Data {}

interface ServiceOptions {}

export class BulkGenerateReportCsv implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  
  ResultS3folderName = 'bulk_generate_csv_report_results'
  ResultS3fileName = {
    OVERALL_REPORT: 'overall_school_dist_group_ids.json',
    FAILED_REPORT: 'fail_csv_school_dist_group_ids.json'
  }

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }
    const bulk_report_csv_generations = await this.fetchbulkCSVReportGenerationRecords()
    return bulk_report_csv_generations
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (test_window_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query || !test_window_id){
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }

    const bulkCSVReportGenerationRecords = await this.fetchbulkCSVReportGenerationRecords(test_window_id)
    return bulkCSVReportGenerationRecords
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
    if (!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }

    if (!data || !data.test_window_id){
      throw new Errors.BadRequest('MISSING_DATA_REQ');
    }

    //Get request process uid
    const created_by_uid = await currentUid(this.app, params);

    const { test_window_id, missing_report_sd_only } = data

    //get test window type slug
    const test_window = await dbRawReadSingleReporting(this.app, {test_window_id}, `
      -- 109 ms to run on mirror db
      select id, type_slug from test_windows where id = :test_window_id
    ;`)

    if(!test_window || !test_window.type_slug) {
      throw new Errors.BadRequest('MISSING_TW_SLUG_FOR_REPORT')
    }

    //Check if there's on going bulk CSV generation process
    await this.checkOngoingBulkCSVGenerationProcess(test_window_id);

    //bulk csv generation
    const new_bulk_csv_generations_process:any = await dbRawWrite(this.app, [created_by_uid, test_window_id, missing_report_sd_only], `
      insert into bulk_csv_report_generations (created_by_uid, test_window_id, missing_report_sd_only) values (?,?,?)
    ;`)

    const test_window_slug:TestWindowType = test_window.type_slug 

    //Load schools group ids that need isr pdf generation
    const csv_require_schl_dist_grp_ids = await this.getCsvReportRequireSchlDistGrpIds(test_window_id, test_window_slug, missing_report_sd_only);

    //start process async
    this.runBulkISRPDFGenerationProcess(test_window, +new_bulk_csv_generations_process.insertId, csv_require_schl_dist_grp_ids, created_by_uid);

    const bulkCsvReportGenerationRecords = await this.fetchbulkCSVReportGenerationRecords(test_window_id)
    return { csv_require_schl_dist_grp_ids, bulkCsvReportGenerationRecords }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (test_window_id: Id, params?: Params): Promise<Data> {
    if (!params || !params.query){
      throw new Errors.BadRequest('MISSING_PARAMS_REQ');
    }
    const created_by_uid = await currentUid(this.app, params);
    const running_bulk_csv_generation_process = await dbRawReadSingle(this.app, {test_window_id}, `
      -- 0.05s
      select bcrg.id
        from bulk_csv_report_generations bcrg
        where bcrg.test_window_id = :test_window_id
          and bcrg.is_revoked = 0
          and bcrg.process_completed_on is null
    ;`)
    if(!running_bulk_csv_generation_process){
      throw new Errors.BadRequest('NO_RUNNING_PROCESS_FOUND');
    }
    
    //Revoke the process
    await dbRawWrite(this.app, [created_by_uid, running_bulk_csv_generation_process.id], `
      update bulk_csv_report_generations 
         set process_completed_on = now(),
             is_revoked = 1,
             revoked_by_uid = ?,
             revoked_on = now()
       where id = ?
    ;`)
    
    const bulk_csv_report_generations_record = await this.fetchbulkCSVReportGenerationRecords()
    return bulk_csv_report_generations_record  
  }

  /**
   * Check if there's ongoing bulk isr pdf generation  process
   */
  async checkOngoingBulkCSVGenerationProcess(test_window_id:number){
    const ongoingBulkCSVGenerationProcess = await dbRawReadSingle(this.app, {test_window_id}, `
        -- 0.04s
        select bcrg.id
              , bcrg.is_revoked
              , bcrg.process_completed_on
           from bulk_csv_report_generations bcrg
          where bcrg.test_window_id = :test_window_id
            and bcrg.is_revoked = 0
            and bcrg.process_completed_on is null
      ;`)
    if(ongoingBulkCSVGenerationProcess){
      throw new Errors.BadRequest(`ONGOING_BULK_CSV_GENERATION_PROCESS ${ongoingBulkCSVGenerationProcess.id}`);
    }  
  }

  /**
   * get last 100 bulk_csv_report_generations records order desc
   */
  async fetchbulkCSVReportGenerationRecords(test_window_id?:Id){  

    const bulk_isr_pdf_generations = await dbRawRead(this.app, {test_window_id}, `
      -- 0.08s
       select bcrg.id
            , bcrg.test_window_id
            , bcrg.missing_report_sd_only
            , bcrg.created_on
            , bcrg.created_by_uid
            , bcrg.total_sd_count
            , bcrg.overall_sd_group_ids_file
            , count(distinct bsdrg.schl_dist_group_id) as started_generation_schl_dist_count
            , group_concat(distinct bsdrg.schl_dist_group_id) as started_generation_schl_dist_group_ids
            , count(
                distinct 
                  case when bsdrg.report_generate_on is not null then bsdrg.schl_dist_group_id 
                  else null 
                end
              ) AS successed_pdf_generation_school_count
            , group_concat(
                distinct 
                  case when bsdrg.report_generate_on is not null then bsdrg.schl_dist_group_id 
                  else null 
                end
              ) as successed_csv_generation_school_dist_group_ids
            , bcrg.fail_generation_sd_count
            , bcrg.fail_sd_group_ids_file
            , bcrg.process_completed_on
            , bcrg.is_revoked
            , bcrg.revoked_by_uid
            , bcrg.revoked_on
        from bulk_csv_report_generations bcrg
    left join bulk_schl_dist_report_generations bsdrg on bsdrg.bulk_csv_report_generation_id = bcrg.id
    ${ test_window_id ? 'where bcrg.test_window_id = :test_window_id' : '' }
    group by bcrg.id
    order by bcrg.id desc
        limit 100
    ;`)
    return bulk_isr_pdf_generations
  }

  async runBulkISRPDFGenerationProcess(test_window:{id:number, type_slug:string}, new_bulk_csv_report_generation_id:number, csv_require_schl_dist_grp_ids:number[], created_by_uid:number){
    const test_window_slug = test_window.type_slug
    const test_window_id = test_window.id
    const current_time = Date.now()

    const bulkCSVReportGenerationRecords: {
      fail_csv_school_dist_group_ids: any[],
      individual_school_dist_result: {
        schl_dist_group_id: number,
        overall_csv_school_group_ids: any[],
        fail_csv_school_group_ids: { schl_group_id: number, error: any }[]
      }[]
    } = {
      fail_csv_school_dist_group_ids: [],
      individual_school_dist_result: []
    }

    if (csv_require_schl_dist_grp_ids.length) {
      
      const csv_require_schl_dist_grp_ids_length = csv_require_schl_dist_grp_ids.length
      await dbRawWrite(this.app, [csv_require_schl_dist_grp_ids_length, new_bulk_csv_report_generation_id], `
        -- 84ms to run on mirror db
        update bulk_csv_report_generations 
          set total_sd_count = ?
        where id = ?
      ;`)
        
      const asyncProcessNumber = 2
      for(let csv_require_schl_dist_grp_ids_chunk of _.chunk(csv_require_schl_dist_grp_ids, asyncProcessNumber)){
        //check if the process is revoked
        const generationProcessIsRevoke = await this.checkGenerationProcessIsRevoked(new_bulk_csv_report_generation_id)
        if(generationProcessIsRevoke){
          break
        }

        const startTime = performance.now();
        await Promise.all(csv_require_schl_dist_grp_ids_chunk.map(async csv_require_sd_grp_id => {
          try {
            if(test_window_slug === TestWindowType.EQAO_G9M){
              const csvRequireSchools = await this.app.service("public/dist-admin/g9-report").getSchoolsByTwSchlDist(test_window_id, csv_require_sd_grp_id, null)
              const prevBSDRGRecords = await this.app.service("public/dist-admin/g9-report").getBulkSchlDistReportsGenerationRecords([csv_require_sd_grp_id], test_window_id)

              // Generate CSV report
              const isSecreteUser = false
              const reportGenerationResult = await this.app.service("public/dist-admin/g9-report").getG9ReportDataAndGenerateCSV(csvRequireSchools, created_by_uid, test_window_id, csv_require_sd_grp_id, isSecreteUser, new_bulk_csv_report_generation_id)

              bulkCSVReportGenerationRecords.individual_school_dist_result.push({
                schl_dist_group_id: csv_require_sd_grp_id,
                ...reportGenerationResult
              })

              // Revoke previous bulk schl dist report generation record
              if (prevBSDRGRecords.length) {
                const bsdrg_ids = prevBSDRGRecords.map(bsdrg => bsdrg.id)
                await this.app.service("public/dist-admin/g9-report").patchBulkSchlDistReports(created_by_uid, bsdrg_ids, true)
              }
            }
          }
          catch(e:any){
            bulkCSVReportGenerationRecords.fail_csv_school_dist_group_ids.push({
              schl_dist_group_id: csv_require_sd_grp_id,
              error: e.stack
            })
          }
        }))
        const endTime = performance.now();
        const durationMs = endTime - startTime; 

        logger.info({
          created_by_uid: created_by_uid,
          slug: 'CSV_REPORT_GENERATION_LOG',
          data: {chunk: csv_require_schl_dist_grp_ids_chunk, durationMs: durationMs}
        })
      }
    }

    // Store result to s3 
    const overall_csv_school_dist_group_ids_file_path = this.storeGenerationResultToS3(bulkCSVReportGenerationRecords.individual_school_dist_result, this.ResultS3fileName.OVERALL_REPORT, created_by_uid, current_time)
    
    const fail_csv_school_dist_group_ids_file_path = this.storeGenerationResultToS3(bulkCSVReportGenerationRecords.fail_csv_school_dist_group_ids, this.ResultS3fileName.FAILED_REPORT, created_by_uid, current_time)

    // Update bulk_csv_report_generations
    const overall_csv_school_dist_group_ids_length = bulkCSVReportGenerationRecords.individual_school_dist_result.length
    const fail_csv_school_dist_group_ids_length = bulkCSVReportGenerationRecords.fail_csv_school_dist_group_ids.length
    await dbRawWrite(this.app, [overall_csv_school_dist_group_ids_length, overall_csv_school_dist_group_ids_file_path, fail_csv_school_dist_group_ids_length, fail_csv_school_dist_group_ids_file_path, new_bulk_csv_report_generation_id], `
      -- 101ms to run on mirror db
      update bulk_csv_report_generations 
         set process_completed_on = now(),
             total_sd_count = ?, 
             overall_sd_group_ids_file = ?,
             fail_generation_sd_count = ?,
             fail_sd_group_ids_file = ?
       where id = ?
    ;`)

  }

  /**
   * Get school dist group id list that need to generate CSVs
   * @returns array of school dist group ids
   */
  async getCsvReportRequireSchlDistGrpIds(test_window_id:number, test_window_slug:TestWindowType, missing_report_sd_only:number){
    if(test_window_slug === TestWindowType.EQAO_G9M){
      return this.getG9RequireCsvSchlDistGrpIds(test_window_id, missing_report_sd_only)
    }
    return []
  }

  /**
   * Get school dist group id list that need to generate CSV report
   * @returns array of school dist group ids
   */
  async getG9RequireCsvSchlDistGrpIds(test_window_id:number, missing_report_sd_only:number){
    const student_role_type = DBD_U_ROLE_TYPES.schl_student 
    const report_require_sd_grp_ids_list = await dbRawReadReporting(this.app, {student_role_type, test_window_id}, `
      -- Benchmark: 0.1 second to run on mirror db on tw 147
      select /*+ MAX_EXECUTION_TIME(1440000000)*/
        a.* 
      from (
      select distinct schl.schl_dist_group_id as sd_group_id
        from test_windows tw 
        join school_semesters ss on ss.test_window_id = tw.id
        join school_classes sc on sc.semester_id = ss.id
        join user_roles ur on ur.group_id  = sc.group_id 
          and ur.role_type = (:student_role_type)
        join schools schl on schl.group_id = sc.schl_group_id
      where tw.id = :test_window_id
      ) a
     ${ missing_report_sd_only ? 
      `left join bulk_schl_dist_report_generations bsdrg on bsdrg.schl_dist_group_id = a.sd_group_id 
        and bsdrg.test_window_id = :test_window_id 
        and bsdrg.is_revoked = 0 
        and bsdrg.s3_report_link is not null 
        where bsdrg.id is null`
      : '' }
    ;`)
    const report_require_sd_grp_ids = report_require_sd_grp_ids_list.map( record => record.sd_group_id )
    return report_require_sd_grp_ids
  }

  /**
   * Check if Generation Process is Revoked
   * @param new_bulk_csv_report_generation_id 
   */
  async checkGenerationProcessIsRevoked(new_bulk_csv_report_generation_id:number){
    const csv_generation_processes = await dbRawReadSingle(this.app, {new_bulk_csv_report_generation_id}, `
      -- 94ms on mirror db
      select bcrg.id
           , bcrg.is_revoked
        from bulk_csv_report_generations bcrg
        where bcrg.id = :new_bulk_csv_report_generation_id
    ;`)

    return !!csv_generation_processes.is_revoked
  }

  storeGenerationResultToS3(resultData:any, fileName:string, created_by_uid:number, timestamp:number, ) {
    const fileContent = JSON.stringify(resultData, null, 2);
    const filePath = [this.ResultS3folderName, created_by_uid, timestamp, fileName].join('/')
    storeInS3(filePath, fileContent)
    return filePath
  }
}
