const _ = require('lodash');
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { dbDateNow } from '../../../../util/db-dates';
import axios from 'axios';
import { dbRawRead } from '../../../../util/db-raw';
import { ITestAttempt } from '../../../db/schemas/test_attempts.schema';
import { arrToMap, arrToStringMap } from '../../../../util/param-sanitization';
import logger from '../../../../logger';
import { ISchoolClass } from '../../dist-admin/student/student.class';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import moment from 'moment';
import { TW_EXCEPTION_STUDENT_STATUS } from '../g9-reports/g9-reports.class';

export interface ITestAttemptQuestionResponses {
  id:number,
  test_question_id:number,
  response_raw:string,
  score: number,
  weight: number,
  is_not_seen: number,
  is_invalid: number,
  is_nr: number,
}

interface Data {}

interface ServiceOptions {}

export interface IStudentReportPayload {
  studentInfo: IStudentInfo,
  schoolAndDistInfo: ISchoolAndDistInfo,
  reportInfo?: IReportInfo,
}

export interface IStudentsReportPayload {
  testForm: string,
  studentReports: IStudentReportPayload[]
}
export interface IStudentRecord { // ths is not the one that is passed back, this is the one that is used for aggregation
  uid: number,
  first_name: string,
  last_name: string,
  is_revoked: number,
  stuEdNum: string,
  isDuplicateStuEdNum:boolean,
  attemptInfo?: IStudentAttemptComboRecord,
  reportStatus?:ReportStatus,
  report_requested_on: string
}
interface IStudentAttemptComboRecord {
  test_session_id:number,
  ta_id:number,
  is_closed:number,
  is_submitted:number,
  test_form_id:number,
  started_on:string,
  first_touched_on:string,
  last_touched_on:string,
  uid:number,
  taqr_tally:number,
  sr_id: number,
  sr_created_on: string,
  overall_raw_proportional: number,
  sr_data: string,
  sr_data_parsed: IRawReport,
  sessionProgress: any[],
}

export interface IStudentInfo {
  firstName: string,
  lastName: string,
  oen: number,
  isDuplicate:boolean,
  isRevoked:boolean,
  sessionProgress?: any[],
}

export interface ISchoolAndDistInfo {
  schoolName: string,
  schoolMident: number,
  distName: string,
  testWindowId: number
}

export interface ITestFormQuesConfig {
}

interface IRawReport {
  overallRawScore: number,
  overallRawWeight: number,
  sectionsPerf: ISectionPerf[],
  routings:string[]
}

interface ISectionPerf {
  label: string,
  score: number,
  weight: number,
}

export interface IReportInfo extends IRawReport{
  reportGeneratedOn: string,
  writtenOn: string,
}

interface IReportGenConfig {
  assessmentReportTitle: string,
  sectionsInScope: number[],
  sectionsToSessionsMapping: {
    [sectionIndex:number]: string
  }
}

enum ReportStatus {
  GENERATIONREADY = 'generationReady',
  GENERATING = 'generating',
  VALIDATING = 'validating',
  HAVESINGLEREPORT = 'haveSingleReport',
  HAS_REPORTED_ISSUE = 'hasReportedIssue',
  PENDED = 'pended',
  WITHHELD = 'withheld'
}

export class ReportResults implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async validateG9FeatureFlags(schClassId:number){
    const isDisabled = await getSysConstNumeric(this.app, 'DISABLE_G9_REPORTS');
    if (isDisabled){
      const schoolSampleDistrictRecords = await dbRawRead(this.app, [schClassId], `
        select sd.id
        from school_classes sc
        join schools s on s.group_id = sc.schl_group_id
        join school_districts sd on sd.group_id = s.schl_dist_group_id and sd.is_sample=1
        where sc.id = ?
      ;`)
      if (schoolSampleDistrictRecords.length === 0){
        throw new Errors.Forbidden('G9_REPORTS_DISABLED');
      }
    }
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // This service is not really well named (anymore?).
    // This end point is being used exclusively to ensure that each student has a raw report.
    // it is also a format where scores are reported by section
    if (params) {
      const requester_uid = await currentUid(this.app, params);
      const {
        classroomId,
        outputMode,
        selectedStudentUids
      } = (<any>params).query;
      return this.processG9Report(requester_uid, classroomId, outputMode, false, false, selectedStudentUids);
    }
    throw new Error()
  }

  async processG9Report(requester_uid:number, classroomId:number, outputMode:string, isSaveDisabled:boolean, isBypassFeatureFlag:boolean=false, selectedStudentUids:number[]): Promise<IStudentReportPayload[]>{
    const assessmentCode = 'G9_OPERATIONAL'; // this should be passed in from the client
    if (!isBypassFeatureFlag){
      await this.validateG9FeatureFlags(classroomId);
    }
    const reportData = await this.ensureStudentReports(classroomId, assessmentCode, requester_uid, isSaveDisabled, selectedStudentUids);
    if (outputMode =='flat'){
      return reportData.map(r => {
        let oen = ''+r.studentInfo.oen;
        let writtenOnSanitized = '';
        let writtenOn = r.reportInfo?.writtenOn;
        if (writtenOn){
          writtenOnSanitized = moment(writtenOn).format('YYYY-MM-DD')
        }
        let row:any = {
          'Date Written': writtenOnSanitized,
          'OEN': oen.substr(0, 2)+'*****'+oen.substr(oen.length-2, 2),
          'First Name': r.studentInfo.firstName,
          'Last Name': r.studentInfo.lastName,
          'Submitted?': r.reportInfo ? 'Yes' : 'No',
        }
        // rush -- this is not good enough, the first row must have all props, even if they are blank/null otherwise many CSV parsers will fail to read them
        if (r.reportInfo){
          r.reportInfo.sectionsPerf.forEach(section => {
            row[`Session ${section.label}: Score`] = section.score;
            row[`Session ${section.label}: Out Of`] = section.weight;
          })
          row['Total Score'] = r.reportInfo.overallRawScore;
          row['Out Of Total'] = r.reportInfo.overallRawWeight;
        }
        return row;
      })
    }
    else{
      return reportData;
    }
  }

  async ensureStudentReports(classroomId:number, assessmentCode:string, requester_uid:number, isSaveDisabled:boolean=false, selectedStudentUids:number[]): Promise<IStudentReportPayload[]> {
    const isReportGenAvail = await this.app.service('public/school-admin/student-asmt-info-signoff').isReportGenAvailForClassId(classroomId);
    // pull class and assessment information
    const studentClass = await this.getSchlClass(classroomId);
    const schoolAndDistInfo: ISchoolAndDistInfo= await this.getIsrClassSchDistInfo(studentClass.schl_group_id, studentClass.semester_id);
    const isrConfig = await this.getISReportConfigByAsmt(assessmentCode, classroomId);
    // get students for the class (with their associated information)
    const students = await this.getStudentsInfoForClass(studentClass.group_id)
    // look up all student attempts (and identify which have an associated report that can be used for the current purpose, get as many as needed then compress it down)
    // AND identify if any/all of the associated attempts that have an associated student report (needs overall_raw_proportional otherwise it is not valid)
    await this.getStudentAttemptsForClass( classroomId, students, {isPropRepReq:true});
    // should now have the list of attempts for which we need to generate reports
    const studentsWhoNeedReports = students.filter(s => s.attemptInfo && !s.attemptInfo.sr_id);
    // for the list of attempts that are getting reports generated, revoke the old ones before starting to process in bulk
    //await this.revokeOldStudentReports(studentsWhoNeedReports, requester_uid)
    
    // iterate through the relevant attempts and generate reports(cooments out this part of code to prevent student_report being generated by the old way)
    // if (isReportGenAvail && selectedStudentUids){
    //   selectedStudentUids = selectedStudentUids.map(uid => +uid)
    //   const selectedStudents:IStudentRecord[] = [];
    //   studentsWhoNeedReports.forEach(student => {
    //     if (selectedStudentUids.indexOf(+student.uid) !== -1){
    //       selectedStudents.push(student);
    //     }
    //   })
    //   await this.processNewReports(selectedStudents, isrConfig, requester_uid, isSaveDisabled)
    // }
    const test_windows = await dbRawRead(this.app, [studentClass.group_id], `
        select tw.* 
          from school_classes sc
          join school_semesters ss on ss.id = sc.semester_id
          join test_windows tw on tw.id = ss.test_window_id
         where sc.group_id = ? 
    ;`)
    const test_window_report_require_validate = test_windows[0].report_require_validate
    const noReportAttempts = await this.app.service('public/educator/g9-reports').getAttemptsReadyForReports(studentClass.group_id)
    const generatingStudentReports = await this.app.service('public/educator/g9-reports').generatingStudentReport(studentClass.group_id)
    const validatingReports = await this.app.service('public/educator/g9-reports').validatingReport(studentClass.group_id)
    const haveSingleReports = await this.app.service('public/educator/g9-reports').classStudentsHaveIndividualReport(studentClass.group_id, test_window_report_require_validate)
    const nfpHaveSingleReports = await this.app.service('public/educator/g9-reports').notFullyParticipateStudentsHaveSingleReport(studentClass.group_id, test_window_report_require_validate)
    const hasReportedIssueAttempts = await this.app.service('public/educator/g9-reports').getReportedIssueStudents(studentClass.group_id)
    const withheldPendedAttempts = await this.app.service('public/educator/g9-reports').getWithheldPendedStudents(studentClass.group_id)
    // sanitize the response to precisely what is needed for the reports
    return <any> students.map(student => {
      let reportInfo: IReportInfo | null = null;
      let sessionProgress:any[] = [];
      student.reportStatus = undefined;
      let attemptId = undefined
      const nfpHaveSingleReport = nfpHaveSingleReports.find( ansrg => +ansrg.uid === +student.uid )
      if (student.attemptInfo){
        sessionProgress = student.attemptInfo.sessionProgress;
        attemptId = student.attemptInfo.ta_id
        if (student.attemptInfo.sr_data_parsed){
          reportInfo = {
            ... student.attemptInfo.sr_data_parsed,
            reportGeneratedOn: student.attemptInfo.sr_created_on,
            writtenOn: student.attemptInfo.last_touched_on,
          }
        }
        const ta_id = student.attemptInfo.ta_id
        const withheldPendedAttempt = withheldPendedAttempts.find( ta => +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if(withheldPendedAttempt && student.reportStatus === undefined){
          const exception_status_map = {
            [TW_EXCEPTION_STUDENT_STATUS.WITHHOLD as string] : ReportStatus.WITHHELD,
            [TW_EXCEPTION_STUDENT_STATUS.PENDED as string] :  ReportStatus.PENDED,
          }
          student.reportStatus = exception_status_map[withheldPendedAttempt.twes_category] || ReportStatus.PENDED
        }

        const noReportAttempt = noReportAttempts.find( ta => +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if(noReportAttempt && student.reportStatus === undefined){
          student.reportStatus = ReportStatus.GENERATIONREADY
        }
        const generatingStudentReport = generatingStudentReports.find( ta =>  +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if(generatingStudentReport && student.reportStatus === undefined){
          student.reportStatus = ReportStatus.GENERATING
          student.report_requested_on = generatingStudentReport.report_requested_on
        }
        const validatingReport = validatingReports.find( ta => +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if( validatingReport && student.reportStatus === undefined  && test_window_report_require_validate){
          student.reportStatus = ReportStatus.VALIDATING
          student.report_requested_on = validatingReport.report_requested_on
        }
        const haveSingleReport = haveSingleReports.find( ta => +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if(( haveSingleReport || nfpHaveSingleReport ) && (student.reportStatus === undefined || student.reportStatus === ReportStatus.WITHHELD)){ // Allow downloading withheld report once become available at the end of window
          student.reportStatus = ReportStatus.HAVESINGLEREPORT
          student.report_requested_on = haveSingleReport?.report_requested_on || nfpHaveSingleReport?.report_requested_on
        }
        const hasReportedIssue = hasReportedIssueAttempts.find( ta => +ta.uid === +student.uid && +ta.ta_id === +ta_id )
        if(hasReportedIssue && student.reportStatus === undefined){
          student.reportStatus = ReportStatus.HAS_REPORTED_ISSUE
        }
      } else if (nfpHaveSingleReport) { // If student who doesn't have attempt but have report generated as NFP student
        student.reportStatus = ReportStatus.HAVESINGLEREPORT
        student.report_requested_on = nfpHaveSingleReport.report_requested_on
      }

      return {
        isReportGenAvail, // to do, encapsulate and dont repeat this
        reportStatus: student.reportStatus,
        test_attempt_id: attemptId,
        uid: student.uid,
        studentInfo:{
          firstName: student.first_name,
          lastName: student.last_name,
          oen: student.stuEdNum,
          isDuplicate: student.isDuplicateStuEdNum,
          isRevoked: (student.is_revoked == 1),
          reportRequestedOn: student.report_requested_on,
          sessionProgress,
        },
        schoolAndDistInfo: {
          schoolName: schoolAndDistInfo.schoolName,
          schoolMident: schoolAndDistInfo.schoolMident,
          distName: schoolAndDistInfo.distName,
          testWindowId: schoolAndDistInfo.testWindowId,
        },
        reportInfo,
      }
    })
  }

  async getISReportConfigByAsmt(assessmentCode:string, classroomId:number) : Promise<IReportGenConfig>{
    if (assessmentCode !== 'G9_OPERATIONAL'){ throw new Errors.NotImplemented('METHOD_IMPL_FOR_G9_ONLY')}
    // this is G9-specific now, move to a data model later when more assessments start using this pattern

    return {
      assessmentReportTitle: '',
      sectionsInScope: [
        0,
        2
      ],
      sectionsToSessionsMapping: {
        0: 'A',
        1: 'A',
        2: 'B',
        3: 'B',
      },
    }
  }

  private async getSchlClass(classroomId:number){
    const studentClass:ISchoolClass = await this.app
      .service('db/read/school-classes')
      .get(classroomId);
    return studentClass;
  }

  async getIsrClassSchDistInfo(schl_group_id:number, semester_id?:number): Promise<ISchoolAndDistInfo>{
    const schools = await this.app
      .service('db/read/schools')
      .db()
      .where('group_id', schl_group_id);
    const school = schools[0]
    const schl_dist_group_id = school.schl_dist_group_id;
    const schoolDists = await this.app
      .service('db/read/school-districts')
      .db()
      .where('group_id', schl_dist_group_id);
    const schoolDist = schoolDists[0];
    if (!semester_id){ throw new Errors.GeneralError('SEMESTER_NOT_ASSIGN_TO_SC'); }
    const semester = await this.app.service('db/read/school-semesters').get(semester_id);
    const testWindowId = semester.test_window_id

    return {
      schoolName: school.name,
      schoolMident: school.foreign_id,
      distName: schoolDist.name,
      testWindowId,
    }
  }

  async getStudentsInfoForClass(sc_group_id:number){
    const students:IStudentRecord[] = await dbRawRead(this.app, [sc_group_id], `
      select ur.uid
           , u.first_name
           , u.last_name
           , min(ur.is_revoked) is_revoked -- not filtering them out, so that we can choose to show them to teachers who *had* these students in their class
      from user_roles ur
      join users u
        on u.id = ur.uid
      join school_classes sc on sc.group_id = ur.group_id
      join school_semesters ss on ss.id = sc.semester_id
      join test_windows tw on tw.id = ss.test_window_id
      where ur.group_id = ?
        and ur.role_type = 'schl_student'
        and (ur.is_revoked = 0  or ur.revoked_on > tw.reg_lock_on)  -- only show current class student or student revoked after tw.reg_lock_on due to sdc load
      group by ur.uid
      order by u.last_name, u.first_name, ur.uid
    ;`);
    // load student numbers
    const studentUids = students.map(s => s.uid);
    const studentRef = arrToMap(students, 'uid');
    const studentEducationNumbers:{uid:number, value:string}[] = await this.app
      .service('db/read/user-metas')
      .db()
      .select('uid', 'value')
      .where('key', 'StudentOEN')
      .where('key_namespace', 'eqao_sdc')
      .whereIn('uid', studentUids)
    studentEducationNumbers.forEach(studentEdNumRec => {
      const student = studentRef.get(studentEdNumRec.uid);
      if (student){
        student.stuEdNum = studentEdNumRec.value; // I'd rather "stuEdNum" than OEN here
      }
    });
    // check for duplicate accounts in the list (by matching PEN)
    const stuEdNumToStu = arrToStringMap(students, 'stuEdNum');
    students.forEach(student => {
      const possiblePair = stuEdNumToStu.get(student.stuEdNum);
      if (possiblePair && possiblePair !== student){
        possiblePair.isDuplicateStuEdNum = true;
        student.isDuplicateStuEdNum = true;
      }
    })
    return students;
  }

  /**
   * Get each student attempt info and link to students, each student should only have 1 test attempt
   * @param classroomId 
   * @param students 
   * @param options 
   */
  async getStudentAttemptsForClass(classroomId:number, students:IStudentRecord[], options:{isPropRepReq:boolean}){
    const studentUids = students.map(s =>s.uid);
    const studentRef = arrToMap(students, 'uid');

    const studentAttempts = await this.getStudentAttempts(studentUids, classroomId, options);

    // for now, I'm going to assume only one valid attempt per student attempt, using the one with the most attempts always
    for (let i=0; i<studentAttempts.length; i++){
      const attempt = studentAttempts[i];
      const studentRecord = studentRef.get(attempt.uid);
      if (studentRecord){
        studentRecord.attemptInfo = attempt;
        if (studentRecord.attemptInfo){
          studentRecord.attemptInfo.sessionProgress = [];
          if (attempt.sr_data) {
            studentRecord.attemptInfo.sr_data_parsed = JSON.parse(attempt.sr_data);
          }
        }
      }
    }
    // get sub session info
    const taIds = studentAttempts.map( (ta:any) => ta.ta_id);
    let subSessionIRecords = [];
    if (taIds.length){
      subSessionIRecords = await dbRawRead(this.app, [taIds], `
        select tass.test_attempt_id ta_id
        , tass.uid -- a little risky
        , tsss.order
        , tsss.slug
        , tsss.caption
        , tass.is_submitted
        , tass.last_touch_on
        from test_attempt_sub_sessions tass
        join test_session_sub_sessions tsss
        on tsss.id = tass.sub_session_id
        where tass.test_attempt_id in (?)
        order by tsss.order
      `);
    }
    subSessionIRecords.forEach(tass => {
      const studentRecord = studentRef.get(tass.uid);
      if (studentRecord && studentRecord.attemptInfo && studentRecord.attemptInfo.sessionProgress){
        studentRecord.attemptInfo.sessionProgress.push({
          order: tass.order,
          slug: tass.slug,
          caption: tass.caption,
          is_submitted: tass.is_submitted,
          last_touch_on: tass.last_touch_on,
        })
      }
    })
  }

  /**
   * Get student G9 operational test attempt.
   * Regardless if student wrote the assessment in current class, as guest student or in previous classes, as long as the school class and test attempt fall within the same test window.
   */
  async getStudentAttempts(studentUids:number[], classroomId:number, options:{isPropRepReq:boolean}){
    let studentAttempts:any = [];
    if(studentUids.length >0){
      //incase educator request ISR right after student finished test, pulling data from main db so it has the latest and most accurate data.
      studentAttempts = await dbRawRead(this.app, {studentUids, classroomId}, `
        -- took 15.4 second to run for class with 223 students (The biggest class G9 2023-2024 school year, class id 403529)
        select * from (
         select ta.test_session_id
              , ta.id ta_id
              , ta.is_closed
              , ta.is_submitted
              , ta.started_on
              , ta.test_form_id
              , ta.uid
              , min(taqr.created_on) first_touched_on
              , max(taqr.updated_on) last_touched_on
              , count(taqr.id) taqr_tally
              , sr.id sr_id
              , sr.created_on sr_created_on
              ${ options.isPropRepReq ? `, sr.overall_raw_proportional` : ''}
              ${ options.isPropRepReq ? `, sr.data sr_data` : ''}
          FROM school_classes sc
          JOIN school_semesters ss 
            ON ss.id = sc.semester_id
          JOIN test_window_td_alloc_rules twtdar
            ON twtdar.test_window_id = ss.test_window_id
           AND twtdar.type_slug = 'G9_OPERATIONAL'
           AND twtdar.generate_report = 1
          JOIN test_attempts ta 
            ON ta.twtdar_id = twtdar.id
           and ta.is_invalid = 0
           and ta.uid IN (:studentUids)
           and ta.started_on IS NOT NULL
          join test_attempt_question_responses taqr
            on taqr.test_attempt_id = ta.id
          left join student_reports sr
            on sr.attempt_id = ta.id
            and sr.uid = ta.uid
            and sr.is_revoked = 0
            and sr.is_isr = 1
            ${ options.isPropRepReq ? `and sr.overall_raw_proportional is not null` : ''}
          where sc.id = :classroomId
          group by ta.id
          having taqr_tally > 0
        ) t
        order by uid, taqr_tally
      `);
    }
    return studentAttempts
  }

  async revokeOldStudentReports(studentsWhoNeedReports:IStudentRecord[], requester_uid:number){
    const testAttemptIds:number[] = studentsWhoNeedReports
      .map(s => s.attemptInfo ? s.attemptInfo.ta_id : -1)
      .filter(a => a!==-1)
    await this.app.service('db/write/student-reports')
      .db()
      .whereIn('attempt_id', testAttemptIds)
      .update({
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid: requester_uid,
      })
  }

  async processNewReports(studentsWhoNeedReports:IStudentRecord[], isrConfig:IReportGenConfig, requester_uid:number, isSaveDisabled=false){
    for (let i=0; i<studentsWhoNeedReports.length; i++){
      const student = studentsWhoNeedReports[i];
      logger.silly('Generating report %d of %d | Test Attempt: %s', i+1, studentsWhoNeedReports.length, student.attemptInfo?.ta_id);
      if (student.attemptInfo){
        const ta_id = student.attemptInfo.ta_id
        const studentReport: IRawReport = await this.createStudentRawReport(ta_id, requester_uid, student.uid, false, isSaveDisabled);
        student.attemptInfo.sr_data_parsed = studentReport;
      }
    }
  }

  async buildTestSubmissionConfigForAttempt (testForm: any, test_attempt_id: number): Promise<ITestFormQuesConfig> {
    // only care about in module 1 and 4
    // 2 and 3 - stage 2
    // module 4 - stage 3
    const firstSetQuesAnsweredByApplicant = await this.getQuestionsResponsesForModule(test_attempt_id, 1);
    const secondSetQuesAnsweredByApplicant = await this.getQuestionsResponsesForModule(test_attempt_id, 4);

    const firstSetQuesOnTestForm = await this.getQuesIdsForModuleOnTestForm(testForm, 1);
    const secondSetQuesOnTestForm = await this.getQuesIdsForModuleOnTestForm(testForm, 4);

    const quesOnFormAndAnswered1: number[] = [];
    const quesOnFormAndAnswered2: number[] = [];

    const quesOnFormAndUnanswered1: number[] = [];
    const quesOnFormAndUnanswered2: number[] = [];

    const findAnsweredAndUnansweredOnTestForm = (quesAnsweredByApplicant: number[], quesOnTestForm: number[], answeredQues: number[], unansweredQues: number[]) => {
      return quesOnTestForm.map((quesId: number) => {
        const questionId = quesAnsweredByApplicant.find((qId) => qId === quesId);
        if (!questionId) unansweredQues.push(quesId);
        else answeredQues.push(quesId);
      });
    }

    findAnsweredAndUnansweredOnTestForm(firstSetQuesAnsweredByApplicant, firstSetQuesOnTestForm, quesOnFormAndAnswered1, quesOnFormAndUnanswered1);
    findAnsweredAndUnansweredOnTestForm(secondSetQuesAnsweredByApplicant, secondSetQuesOnTestForm, quesOnFormAndAnswered2, quesOnFormAndUnanswered2);

    return {};
  }

  async getQuestionsResponsesForModule (test_attempt_id: number, moduleId: number) {
    const quesResponses = await this.app
      .service('db/read/test-attempt-question-responses')
      .db()
      .where('test_attempt_id', test_attempt_id)
      .where('module_id', moduleId)
      .select('test_question_id');

    return quesResponses;
  }

  async getQuesIdsForModuleOnTestForm (testForm: any, moduleId: number) {
    let listQuesForModule = testForm.panelModules.findIndex((module: any) => {module.moduleId === moduleId}).questions;

    return listQuesForModule;
  }

  async getTestForm(test_form_id: number) {
    const testFormRecord = await this.app
      .service('db/read/test-forms')
      .db()
      .where('id', test_form_id);

    let fileUrl = testFormRecord[0].file_path;
    fileUrl = generateS3DownloadUrl(fileUrl, 60);
    if (fileUrl){
      const payload = await axios.get(fileUrl, {})
        .catch((e) => {
          throw new Errors.BadRequest(e);
        });
      return payload.data;
    }
    throw new Errors.BadRequest('INVALID TEST FORM');
  }

  mapQuestionIdsToStages(testForm:any){
    const {
      panelModules,
      panelRouting,
      // sections,
    } = testForm;
    const questionIdToModuleId = new Map();
    const questionIdToStageNumber = new Map();
    type Panel = {moduleId:number, questions:number[]};
    const modules = <Panel[]> panelModules;
    modules.forEach((m:any) => m.moduleId = +m.moduleId)
    const panelRef:Map<number | string, Panel> = arrToMap(modules, 'moduleId');
    if (panelModules && panelRouting){
      const traceStages = (moduleId:number, stageNumPrev=0) => {
        if (stageNumPrev > 100){
          logger.warn('EXCEEDED_MAX_STAGE_DEPTH_TF');
          return;
        }
        const stageNum = stageNumPrev+1;
        const panel:Panel | undefined = panelRef.get(+moduleId) || panelRef.get(<any>''+moduleId);
        if (panel){
          panel.questions.forEach(questionId => {
            // assumption: question only appears in one stage
            questionIdToModuleId.set(questionId, stageNum);
            questionIdToStageNumber.set(questionId, stageNum);
          })
          const nextRouting:{module:string}[] = panelRouting[+moduleId] || panelRouting[''+moduleId];
          if (nextRouting){
            nextRouting.forEach(routing => {
              traceStages(+routing.module, stageNum);
            })
          }
        }
      }
      traceStages(1);
    }
    return {
      questionIdToModuleId,
      questionIdToStageNumber,
      panelRef,
      modules,
    }
  }

  async computePanelPerf(testForm:any, attemptQuestions:any[]) : Promise<IRawReport> {
    const sectionsPerf:ISectionPerf[] = [];
    const {
      questionIdToModuleId,
      questionIdToStageNumber,
      panelRef,
      modules,
    } = this.mapQuestionIdsToStages(testForm)
    // identify modules touched
    const scoreMap = new Map();
    const weightMap = new Map();
    attemptQuestions.forEach(taqr => {
      const qId = taqr.test_question_id;
      const stageNum = questionIdToStageNumber.get(qId);
      const moduleId = questionIdToModuleId.get(qId);
      scoreMap.set(qId, +(taqr.score || 0));
      // weightMap.set(qId, +(taqr.score || 0));
    });
    const modulesToProcess:number[] = [1, 4]; // rushing, this should be copied from further up
    let overallRawScore = 0;
    let overallRawWeight = 0;
    modulesToProcess.forEach(moduleId => {
      const module = panelRef.get(moduleId);
      if (module){
        let score = 0;
        let weight = 0;
        module.questions.forEach(qId => {
          score += (scoreMap.get(qId) || 0);
          weight += 1; // need to grab it from the form... or eventually the register
        })
        overallRawScore += score;
        overallRawWeight += weight;
        sectionsPerf.push({
          label: (moduleId==1) ? 'A' : 'B', // rushing
          score,
          weight,
        })
      }
    });


    return {
      overallRawScore,
      overallRawWeight,
      sectionsPerf,
      routings: []
    };
  }

  async computeLinearPerf(testForm:any, attemptQuestions:any[]): Promise<IRawReport>{
    const sectionsPerf:ISectionPerf[] = [];
    const {
      sections,
    } = testForm;

    const anchorItemMap = new Map(); // shoul come from test question register
    const anchorItemMapCheck = new Map(); // shoul come from test question register
    // the following list was extracted based on AB=1 filter at https://eqao.vretta.com/#/en/test-auth/framework/1/341
    // const snapShotPossibleAnchors = [8155, 13744, 12863, 13757, 13161, 13748, 13865, 12842, 13194, 13747, 13751, 12847, 13759, 12850, 13758, 12866, 13756, 12901, 13689, 3986, 1775, 13774, 1830, 13780, 4029, 13783, 2164, 13767, 1751, 13771, 2097, 13763, 3973, 13768, 2850, 13770, 2288, 13781, 2355, 13785, 2553, 13765, 2703 ]
    // the above was not the true anchor, the true anchor are the questions that are actually used in stages 1 and 3 for the other form... the Anchor flags should actually be updated to reflect this...
    // I could pull the new list from https://eqao.vretta.com/#/en/test-auth/framework/1/355
    const snapShotPossibleAnchors:number[] = [13744, 13768, 13770, 13769, 13748, 13773, 13763, 13780, 13751, 13782, 13758, 13689, 13752, 13771, 13774, 13794, 13765, 13753, 13783, 13757, 13767, 13747, 13760, 13759, 13781, 13756];
    snapShotPossibleAnchors.forEach(qId => anchorItemMap.set(qId, true) )

    // temporary: pull the live questions
    // const itemIds:number[] = [];
    // sections.forEach((section:any) => {
    //   section.questions.forEach((qId:number) => itemIds.push(qId));
    // })
    // const questionsWithMetaTemp = <any[]> await this.app
    //   .service('db/read/test-questions')
    //   .find({
    //     query: {
    //       id: { $in: itemIds }
    //     },
    //     paginate: false
    //   })
    // questionsWithMetaTemp.forEach((questionRecord:any) => {
    //   const config = JSON.parse(questionRecord)
    // })
    //
    let overallRawScore = 0;
    let overallRawWeight = 0;
    const scoreMap = new Map();
    attemptQuestions.forEach(taqr => {
      const qId = taqr.test_question_id;
      scoreMap.set(qId, +(taqr.score || 0));
    });
    sections.forEach((section:any, sectionIndex:number) => {
      let score = 0;
      let weight = 0;
      section.questions.forEach((qId:number) => {
        if (anchorItemMap.get(qId)){
          anchorItemMapCheck.set(qId, true)
          score += (scoreMap.get(qId) || 0);
          weight += 1; // need to grab it from the form... or eventually the register
        }
      })
      overallRawScore += score;
      overallRawWeight += weight;
      sectionsPerf.push({
        label: (sectionIndex==0) ? 'A' : 'B', // rushing
        score,
        weight,
      })
    });
    snapShotPossibleAnchors.forEach(qId => {
      if (!anchorItemMapCheck.get(qId)){
        logger.silly('ITEM NOT INCLUDED %d', qId)
      }
    })
    return {
      overallRawScore,
      overallRawWeight,
      sectionsPerf,
      routings: []
    };
  }

  async generateStudentRawReport(test_attempt_id:number, created_by_uid:number) : Promise<IRawReport> {
    const testAttempt:ITestAttempt = await this.app
      .service('db/read/test-attempts')
      .get(test_attempt_id)

    // const testSession = await this.app
    //   .service('db/read/test-sessions')
    //   .get(testAttempt.test_session_id)
    // const writtenOn = testSession.date_time_start;
    // const numTotalQues = attemptQuestions.length;

    const attemptQuestions:ITestAttemptQuestionResponses[] = await this.app
      .service('db/read/test-attempt-question-responses')
      .db()
      .where('test_attempt_id', test_attempt_id)
      .orderBy('created_on');

    const testForm = await this.getTestForm(testAttempt.test_form_id);

    if (testForm.panelModules){
      return await this.computePanelPerf(testForm, attemptQuestions);
    }
    else if (testForm.sections){
      return await this.computeLinearPerf(testForm, attemptQuestions);
    }
    else {
      logger.error('CANNOT_COMPUTE_FORM_TYPE')
      throw new Errors.GeneralError('CANNOT_COMPUTE_FORM_TYPE')
    }
  }

  async userMetaSnapshot(){

  }

  async createStudentRawReport(test_attempt_id:number, created_by_uid:number, studentUid: number, isAutoGen:boolean=false, isSaveDisabled=false){
    // to do: check if the school has validated all students for the current window
    const studentSubmission = await this.generateStudentRawReport(test_attempt_id, created_by_uid);
    const overall_raw_proportional = studentSubmission.overallRawWeight ? (studentSubmission.overallRawScore / studentSubmission.overallRawWeight) : 0;
    const submissionPayload = {
      uid: studentUid,
      attempt_id: test_attempt_id,
      created_on: dbDateNow(this.app),
      created_by_uid,
      is_data_insufficient: 0,
      overall_raw_proportional,
      data: JSON.stringify(studentSubmission)
    }
    if (submissionPayload && !isSaveDisabled){
      // take usermeta snapshot
      const user_meta_snapshot = await dbRawRead(this.app, [studentUid], `
        select um.id, um.key, um.key_namespace, um.value, um.updated_on, um.updated_by_uid
        from user_metas um
        where uid = ?
      `);

      // write
      const report = await this.app
        .service('db/write/student-reports')
        .create({
          ... submissionPayload,
          is_auto: isAutoGen ? 1 : 0,
          is_isr: 1,
        })
        .catch((e) => {
          logger.error('Caught exception while creating a student-report', e, { submissionPayload } );
        });

      await this.app
        .service('db/write/student-user-metas-snapshot')
        .create({
          student_report_id: report.id,
          uid: studentUid,
          created_on: dbDateNow(this.app),
          created_by_uid,
          data: JSON.stringify(user_meta_snapshot),
        });

    }
    return studentSubmission;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if(!data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }
    if (data) {
      const requester_uid = await currentUid(this.app, params);
      const {
        classroomId,
        outputMode,
        selectedStudentUids
      } = (<any>data);
      return this.processG9Report(requester_uid, classroomId, outputMode, false, false, selectedStudentUids);
    }
    throw new Error();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async patch (id: NullableId, data: {test_attempt_id: number}, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

}
