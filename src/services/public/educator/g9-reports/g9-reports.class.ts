import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadReporting, dbRawReadSingleReporting, dbRawWrite } from '../../../../util/db-raw';
import { dbDateNow } from '../../../../util/db-dates';
import { AxiosInstance } from 'axios';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';
import moment from 'moment';
import { Knex } from 'knex';
import logger from '../../../../logger';
import { ATTEMPTASRG_INIT_REDLOCK_TIMEOUT, KAttemptASRGInit, getRedisLock } from '../../../../redis/redis';


interface Data {}

interface ServiceOptions {}

export enum TW_EXCEPTION_STUDENT_STATUS {
  PENDED = "PENDED",
  WITHHOLD = "WITHHOLD",
  EXCLUDE_FROM_CSV = "EXCLUDE_FROM_CSV"
}

enum G9_OVERALL_TEXT {
  BL1 = 'BL1',
  NULL = 'NULL',
  NULL_fr = 'NUL',
  WITH = 'WITH'
}

export class G9Reports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;
  eqaoScanningService: AxiosInstance;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
    this.eqaoScanningService = app.get('eqaoScanningService');
  }

  maxReportGenerateMinutes = 30;  // set to 1 minutes to testing purpose, need to change to soemthing bigger like 10, 20 or 30.

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data> {
    // this function is used to download single student report
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    return await this.getSingleReport(params)
  }

  async getSingleReport(params: any){
    const { schl_class_group_id, clientDomain, attemptId, uid } = params.query;
    const requester_uid = await currentUid(this.app, params);

    const test_window = await dbRawReadSingleReporting(this.app, [schl_class_group_id], `
      -- benchmark: 0.031 sec
    select tw.id as tw_id
         , tw.report_require_validate
         , sc.schl_group_id
         , tw.isr_version
      from school_classes sc
      join school_semesters ss on ss.id = sc.semester_id
      join test_windows tw on tw.id = ss.test_window_id
     where sc.group_id = ?
    ;`)

    // throw REPORT_IS_PENDING if the report is pending
    const tw_id = test_window.tw_id
    const tw_exceptions_students = await dbRawRead(this.app, {uid, tw_id}, `
      -- Benchmark: 0.031 sec
     SELECT distinct
            twes.*
       FROM tw_exceptions_students twes 
      WHERE twes.uid = (:uid )
        AND twes.test_window_id = (:tw_id) 
        AND twes.is_revoked = 0 
        AND twes.is_pended = 1
    ;`)

    if(tw_exceptions_students.length ){
      throw new Errors.BadRequest("REPORT_IS_PENDING");
    }

    const test_window_report_require_validate = test_window.report_require_validate
    let auto_student_report_generations = []
    let auto_nfp_student_report_generations = []

    if (attemptId) {
      auto_student_report_generations = await dbRawRead(this.app, [attemptId], `
        select *
        from auto_student_report_generations
        where test_attempt_id = ?
        and student_pdf_generated_on is not null
        and is_revoked = 0
        ${
          test_window_report_require_validate ?
          `and eqao_validated_on is not null`:``
        }
      ;`)
    }
    // If no existing individual report found, look for not fully participate student report
    if (!auto_student_report_generations.length) {
      auto_nfp_student_report_generations = await dbRawRead(this.app, [uid], `
      select id
        from auto_nfp_student_report_generations
       where student_uid = ?
         and student_pdf_generated_on is not null
         and is_revoked = 0
        ${
          test_window_report_require_validate ?
          `and eqao_validated_on is not null`:``
        }
      ;`)
    }

    if (!auto_student_report_generations.length && !auto_nfp_student_report_generations.length) {
      if (test_window_report_require_validate) {
        throw new Errors.BadRequest('REPORT_NOT_VALIDATED')
      }
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }

    //Log Educator Access G9 Report Record when there's report*************************// 
    const schl_group_id = test_window.schl_group_id
    await this.app.service("public/school-admin/reports").logUserAccessRecord(tw_id, requester_uid, schl_group_id, params)
    //********************************************************************************/

    const tw_isr_version = test_window.isr_version
    let g9_individaul_report_folder_name = 'g9_single_isrs/'
    if (tw_isr_version === 1){
      g9_individaul_report_folder_name += `tw_${tw_id}/`
    }
    const pdfFileName = auto_student_report_generations.length ? `ta_${attemptId}` : `ansrg_${auto_nfp_student_report_generations[0].id}`
    return { message: 'REPORT_GENERATED', reportURL:generateS3DownloadUrl( g9_individaul_report_folder_name + pdfFileName + ".pdf")}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    // this function is used to download class's students report
    return this.getBulkReport(params)
  }
  
  /**
   * Get G9 class bulk pdf report ( used by both educator and issue-reviwer )
   * @param params class_group_id 
   * @param fromIssueReviewer true, if from issue reviewer
   * @returns message and reportURL for downloading report
   */
  async getBulkReport(params:any, fromIssueReviewer:boolean = false){
    if (params && params.query){
      const {
        schl_class_group_id,
        clientDomain,
      } = params.query;

      const created_by_uid = await currentUid(this.app, params);
      const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1
      const fromPyschPipline = false

      //throw NO_DATA_FOR_REPORT error with test_window_allow_report is 0 and isBypassDomain is false
      const test_windows = await dbRawWrite(this.app, [schl_class_group_id], `
          select tw.*
            from school_classes sc
            join school_semesters ss on ss.id = sc.semester_id
            join test_windows tw on tw.id = ss.test_window_id
          where sc.group_id = ?
      ;`)

      if(test_windows.length === 0){
        throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
      }

      const test_window_allow_report = test_windows[0].is_allow_results_tt
      if( test_window_allow_report === 0 && !isBypassDomain && !fromIssueReviewer ){
        throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
      }

      //throw REPORT_IS_PENDING if the report is pending*******************
      const tw_exceptions_students = await dbRawRead(this.app, {schl_class_group_id}, `
          select distinct twes.*
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student' and ur.is_revoked = 0
            join school_semesters ss on ss.id = sc.semester_id
            join tw_exceptions_students twes on twes.uid = ur.uid and twes.test_window_id = ss.test_window_id and twes.is_revoked = 0 and twes.is_pended = 1
          where sc.group_id = :schl_class_group_id
      ;`)

      if(tw_exceptions_students.length && !fromIssueReviewer){
        throw new Errors.BadRequest("REPORT_IS_PENDING");
      }
      //************************************************************** */

      //throw SCHL_ADMIN_SIGN_OFF_STUDENT_DATA_REQUIRED error with no valid school admin sign off and test_window_allow_report is 1 and isBypassDomain is false
      // const school_student_asmt_info_signoffs = await dbRawWrite(this.app, [schl_class_group_id], `
      //     select ssais.*
      //       from school_classes sc
      //       join school_semesters ss on ss.id = sc.semester_id
      //       join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = sc.schl_group_id and ssais.is_revoked = 0 and ssais.test_window_id = ss.test_window_id
      //     where sc.group_id = ?
      // ;`)
      // if(school_student_asmt_info_signoffs.length === 0 && test_window_allow_report === 1 && !isBypassDomain){
      //   throw new Errors.BadRequest("SCHL_ADMIN_SIGN_OFF_STUDENT_DATA_REQUIRED");
      // }

      //throw REPORT_GENERATING error if reports are generating in pipline
      // const generatingStudentReport = await this.generatingStudentReport(schl_class_group_id)
      // if(generatingStudentReport.length){
      //   throw new Errors.BadRequest("REPORT_GENERATING");
      // }

      //throw error when report is validating
      const test_window_report_require_validate = test_windows[0].report_require_validate
      if(test_window_report_require_validate && !fromIssueReviewer){
        const validatingReports = await this.validatingReport(schl_class_group_id)
        if(validatingReports.length){
          throw new Errors.BadRequest("REPORT_VALIDATING");
        }
      }

      const { bulk_student_results_g9_reports, generating_reports, long_generate_reports } = await this.updateReportGenerateStatus(created_by_uid, schl_class_group_id)

      //regenerate report if pdf is generating for too long
      if(long_generate_reports.length > 0){
        const is_regenerate = long_generate_reports.length > 0 
        return this.generateReportPDF(created_by_uid, schl_class_group_id, is_regenerate, clientDomain, fromPyschPipline, fromIssueReviewer)
      }

      if(bulk_student_results_g9_reports.length == 0){
        throw new Errors.BadRequest("N0_AVALIBLE_REPORTS");
      }

      //get bulk pdf link
      if(bulk_student_results_g9_reports.length>0 && bulk_student_results_g9_reports[0].report_generate_on != null){
        return {message: 'REPORT_GENERATED', reportURL:generateS3DownloadUrl( bulk_student_results_g9_reports[0].s3_report_link)}
      }
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }  
    throw new Errors.BadRequest('ERR_MISSING_PARAMS'); 
  } 

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    // this function is for teacher to initialize the report generations
    if (!params || !params.query) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }
    const {
      schl_class_group_id,
      clientDomain
    } = params.query;

    const fromPyschPipline = false
    const created_by_uid = await currentUid(this.app, params);
    const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1

    const test_windows = await dbRawWrite(this.app, [schl_class_group_id], `
        select tw.*
          from school_classes sc
          join school_semesters ss on ss.id = sc.semester_id
          join test_windows tw on tw.id = ss.test_window_id
         where sc.group_id = ?
    ;`)

    if(test_windows.length === 0){
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }

    //throw NO_DATA_FOR_REPORT error with test_window_allow_report is 0 and isBypassDomain is false
    const test_window_allow_report = test_windows[0].is_allow_results_tt
    if( test_window_allow_report === 0 && !isBypassDomain){
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }

    //throw NO_DATA_FOR_REPORT error with no valid school admin sign off and test_window_allow_report is 1 and isBypassDomain is false
    const school_student_asmt_info_signoffs = await dbRawWrite(this.app, [schl_class_group_id], `
        select ssais.*
          from school_classes sc
          join school_semesters ss on ss.id = sc.semester_id
          join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = sc.schl_group_id and ssais.is_revoked = 0 and ssais.test_window_id = ss.test_window_id
         where sc.group_id = ?
    ;`)
    if(school_student_asmt_info_signoffs.length === 0 && test_window_allow_report === 1 && !isBypassDomain){
      throw new Errors.BadRequest("SCHL_ADMIN_SIGN_OFF_STUDENT_DATA_REQUIRED");
    }

    //check if the test attempts in this class is requesting ASRG, throw error if it is.
    let lock: any
    try{
      lock = await getRedisLock(this.app, [KAttemptASRGInit(schl_class_group_id)], ATTEMPTASRG_INIT_REDLOCK_TIMEOUT);
    } catch (error){
      if(lock){
        await lock.release();
      }
      throw new Errors.Conflict("REPORT_ALREADY_REQUESTING"); // Conflict (http error 409)
    }
    
    /*
    Step 1: fetch the students' uids and attempts id in the class
    */
    let noReportAttempts;
    let generatingStudentReport;
    let validatingReports; 
    let test_window_report_require_validate; 
    try{
      noReportAttempts = await this.getAttemptsReadyForReports(schl_class_group_id)
      generatingStudentReport = await this.generatingStudentReport(schl_class_group_id)
      validatingReports = await this.validatingReport(schl_class_group_id)
      test_window_report_require_validate = test_windows[0].report_require_validate

      //let all ready test attempt go into the auto_student_report_generations table not matter what.
      await Promise.all( noReportAttempts.map(async noReportAttempt => {
        await this.app
          .service('db/write/auto-student-report-generations')
          .create({
            student_uid: noReportAttempt.uid,
            test_attempt_id: noReportAttempt.ta_id,
            created_by_uid: created_by_uid,
          })
      }))
    }catch (error){
      if(lock){
        await lock.release();
      }
      throw error;
    }
    await lock.release();
    /*
    Step 2: 1) if have student report currently waiting for pipline to generate it, just return "REPORT_GENERATING"
            2) else if reports are validating
            3) else if need to regenerate pdf (is_regenerate == true) or no new attempt need report than regeneratepdf report
            4) else if have student report currently waiting for pipline to generate it, just return "REPORT_GENERATING"
    */
    if (generatingStudentReport.length){
      return {message: 'REPORT_GENERATING'}
    }else if( validatingReports.length && test_window_report_require_validate){
      return {message: 'REPORT_VALIDATING'}
    }else if(noReportAttempts.length === 0){
      return {message: 'REPORT_GENERATED'}
    }
    else{
      const { bulk_student_results_g9_reports, generating_reports, long_generate_reports } = await this.updateReportGenerateStatus(created_by_uid, schl_class_group_id)
      const is_regenerate = long_generate_reports.length > 0
      if(is_regenerate){
        return this.generateReportPDF(created_by_uid, schl_class_group_id, is_regenerate, clientDomain, fromPyschPipline)
      }else{
        return {message: 'REPORT_GENERATING'}
      }
    }
  }

  /**
   * fetch the students' uids and attempts id in the class that has
    1. no valid auto_student_report_generations records
    2. test attempt is_invalid = 0
    3. test attempt is_submitted = 1
    4. no associated unresolved reported_issue
    5. at least have 1 answer in each session
   * @param schl_class_group_id
   * @param attempt_id
   * @returns `{ta_id, uid}[]`
   */
  async getAttemptsReadyForReports(schl_class_group_id: any, attempt_id?:any){

    const noReportAttemptsQueryParams = [schl_class_group_id];
    if(attempt_id){
      noReportAttemptsQueryParams.push(attempt_id)
    }

    const noReportAttempts = await dbRawWrite(this.app, noReportAttemptsQueryParams, `
    -- Benchmark: Took 0.23 sec for class gr id 762832 that has 127 students on TW 147
    select distinct
           subquery.uid
         , subquery.ta_id
      from
      (
          select distinct
                  ta.uid
                , ta.id as ta_id
                , tsss.id as tsss_id
                , CASE 
                    WHEN COUNT(DISTINCT CASE 
                                        WHEN tqr.is_field_trial IS NULL OR tqr.is_field_trial = 0 
                                        THEN taqr.id 
                                      END) > 0 
                    THEN 1 
                    ELSE 0 
                  END AS session_have_answer
             from school_classes sc
             join user_roles ur on ur.group_id = sc.group_id and ur.role_type ='schl_student' and ur.is_revoked = 0
             join school_semesters ss on ss.id = sc.semester_id
             join test_attempts ta on ta.uid = ur.uid and ta.is_invalid = 0 and ta.is_submitted = 1
             join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.test_window_id = ss.test_window_id and twtdar.generate_report = 1
             join test_attempt_sub_sessions tass on tass.test_attempt_id = ta.id
             join test_session_sub_sessions tsss on tsss.id = tass.sub_session_id
        left join auto_student_report_generations asrg on asrg.student_uid = ta.uid and asrg.test_attempt_id = ta.id and asrg.is_revoked = 0
        left join reported_issues ri on ri.testtaker_uid = ta.uid and ri.test_session_id = ta.test_session_id and ri.is_revoked = 0
        left join reported_issues_common ric on ric.id = ri.reported_issues_common_id and ric.is_resolved = 0
        left join test_attempt_question_responses taqr 
          on taqr.test_attempt_id = ta.id 
         and taqr.is_nr != 1 
         and (
             JSON_CONTAINS(tsss.sections_allowed, CAST(taqr.section_id AS JSON), '$')
          or (taqr.section_id = 0 and JSON_CONTAINS(tsss.sections_allowed, '0', '$'))
          or JSON_CONTAINS(tass.sections_allowed, CAST(taqr.section_id AS JSON), '$')
          or (taqr.section_id = 0 and JSON_CONTAINS(tass.sections_allowed, '0', '$'))
         )
        left join test_question_register tqr 
          on tqr.question_id = taqr.test_question_id
         and tqr.test_design_id = ifnull(twtdar.tqr_ovrd_td_id, twtdar.test_design_id)
            where sc.group_id = ?
              and asrg.id is null
              and ric.id is null
              ${attempt_id? 'and ta.id = ?':''}
         group by ta.uid, ta.id, tsss.id
      ) as subquery
  group by subquery.uid, subquery.ta_id
    having count(distinct subquery.tsss_id) = sum(session_have_answer)
       ;`)

    return noReportAttempts
  }

  async generatingStudentReport(schl_class_group_id:any){
    // fetch the ta ids that are currently waiting for pipline to generate report
    const generatingStudentReport = await dbRawWrite(this.app, [schl_class_group_id], `
         select distinct
                ta.uid
              , ta.id as ta_id
              , asrg.created_on as report_requested_on
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type ='schl_student'
            join school_semesters ss on ss.id = sc.semester_id
            join test_attempts ta on ta.uid = ur.uid and ta.is_invalid = 0 and ta.is_submitted = 1
            join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.test_window_id = ss.test_window_id and twtdar.generate_report = 1
            join auto_student_report_generations asrg on asrg.student_uid = ta.uid and asrg.test_attempt_id = ta.id and asrg.is_revoked = 0 and (asrg.student_report_generated_on is null or asrg.student_pdf_generated_on is null)
           where sc.group_id = ?
      ;`)

    return generatingStudentReport
  }

  async  validatingReport(schl_class_group_id:any){
    // fetch the ta ids that are currently waiting for validating the report
    const validatingStudentReport = await dbRawWrite(this.app, [schl_class_group_id], `
          select distinct
                 ta.uid
               , ta.id as ta_id
               , asrg.created_on as report_requested_on
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type ='schl_student'
            join school_semesters ss on ss.id = sc.semester_id
            join test_attempts ta on ta.uid = ur.uid and ta.is_invalid = 0 and ta.is_submitted = 1
            join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.test_window_id = ss.test_window_id and twtdar.generate_report = 1
            join auto_student_report_generations asrg on asrg.student_uid = ta.uid and asrg.test_attempt_id = ta.id and asrg.is_revoked = 0 and asrg.student_pdf_generated_on is not null and asrg.eqao_validated_on is null
           where sc.group_id = ?
      ;`)

    return validatingStudentReport
  }

  async  classStudentsHaveIndividualReport(schl_class_group_id:any, test_window_report_require_validate:boolean){
    // fetch the ta ids that have report are ready for download
    const classStudentsHaveIndividualReports = await dbRawWrite(this.app, [schl_class_group_id], `
          select distinct
                 ta.uid
               , ta.id as ta_id
               , asrg.created_on as report_requested_on
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type ='schl_student'
            join school_semesters ss on ss.id = sc.semester_id
            join test_attempts ta on ta.uid = ur.uid and ta.is_invalid = 0 and ta.is_submitted = 1
            join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.test_window_id = ss.test_window_id and twtdar.generate_report = 1
            join auto_student_report_generations asrg on asrg.student_uid = ta.uid and asrg.test_attempt_id = ta.id and asrg.is_revoked = 0 and asrg.student_pdf_generated_on is not null ${test_window_report_require_validate?`and asrg.eqao_validated_on is not null`:``}
           where sc.group_id = ?
      ;`)

    return classStudentsHaveIndividualReports
  }

  /**
   * Get all not fully participating student report records (ansrg) for students in this class and in the current test window
   * @param schl_class_group_id 
   * @param test_window_report_require_validate 
   * @returns 
   */
  async notFullyParticipateStudentsHaveSingleReport(schl_class_group_id:any, test_window_report_require_validate:boolean){
    const nfpStudentReports = await dbRawReadReporting(this.app, {schl_class_group_id}, `
      -- Benchmark: 0.14 sec, cost 40.87
     SELECT DISTINCT
            ansrg.student_uid AS uid
          , ansrg.id AS ansrg_id
          , ansrg.created_on AS report_requested_on
       FROM user_roles ur
       JOIN school_classes sc ON sc.group_id = ur.group_id
       JOIN school_semesters ss ON ss.id = sc.semester_id
       JOIN auto_nfp_student_report_generations ansrg 
         ON ansrg.student_uid = ur.uid 
        AND ansrg.test_window_id = ss.test_window_id
        AND ansrg.is_revoked != 1 
      WHERE ur.group_id = (:schl_class_group_id)
      ${ test_window_report_require_validate ? "AND ansrg.eqao_validated_on is not null" : "" }
    ;`)

    return nfpStudentReports
  }

  /**
   * fetch the ta ids that are currently  withheld or pended (have a withheld or pended record in tw_student_exception table)
   * @param schl_class_group_id
   * @returns [] list of attempts that are currently witheld or pended
   */
  async getWithheldPendedStudents(schl_class_group_id:any){
    const filtered_tw_exception_student_status = [TW_EXCEPTION_STUDENT_STATUS.PENDED, TW_EXCEPTION_STUDENT_STATUS.WITHHOLD]
    const withheldPendedReports = await dbRawReadReporting(this.app, {schl_class_group_id, filtered_tw_exception_student_status}, `
      -- Benchmark: 0.031 sec
         select distinct
                ta.uid
              , ta.id as ta_id
              , asrg.created_on as report_requested_on
              , twes.id as twes_id
              , twes.category as twes_category
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type ='schl_student'
            join school_semesters ss on ss.id = sc.semester_id
            join test_attempts ta on ta.uid = ur.uid and ta.is_invalid = 0 and ta.is_submitted = 1
            join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id and twtdar.test_window_id = ss.test_window_id and twtdar.generate_report = 1
            join auto_student_report_generations asrg on asrg.student_uid = ta.uid and asrg.test_attempt_id = ta.id and asrg.is_revoked = 0
            -- and (asrg.student_report_generated_on is null or asrg.student_pdf_generated_on is null) -- Need to show withheld and pended status even if report/pdf exist
            join tw_exceptions_students twes on twes.test_window_id = ss.test_window_id and twes.uid = ta.uid and twes.is_revoked = 0 and twes.category in (:filtered_tw_exception_student_status)
           where sc.group_id = :schl_class_group_id
      ;`)

    return withheldPendedReports
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  async updateReportGenerateStatus(created_by_uid:any, schl_class_group_id:any){
    const maxReportGenerateMS = this.maxReportGenerateMinutes * 60000;

    const bulk_student_results_g9_reports = (await dbRawWrite(this.app, [schl_class_group_id], `
      select * from bulk_student_results_g9_reports where class_group_id = ? and is_revoked != 1 order by report_generate_on DESC
    ;`))
    const generating_reports = bulk_student_results_g9_reports.filter( bsrrg => bsrrg.report_generate_on == null && (new Date()).getTime() - (new Date(bsrrg.created_on)).getTime() < maxReportGenerateMS)
    const long_generate_reports = bulk_student_results_g9_reports.filter( bsrrg => bsrrg.report_generate_on == null && (new Date()).getTime() - (new Date(bsrrg.created_on)).getTime() > maxReportGenerateMS)

    //revoke long_gereate_reports

    const knex = this.app.service('db/write/bulk-student-results-g9-reports').knex
    const trx = await knex.transaction();

    for(let lgr of long_generate_reports) {
      await trx('bulk_student_results_g9_reports')
        .where('id', lgr.id)
        .update({
          is_revoked: 1,
          revoked_by_uid: created_by_uid,
          revoked_on: dbDateNow(this.app)
        })
    }
    await trx.commit();

    //throw error if reports are generating
    if(bulk_student_results_g9_reports.length > 0 && long_generate_reports.length == 0 && generating_reports.length > 0){
      throw new Errors.BadRequest("REPORT_GENERATING");
    }

    return { bulk_student_results_g9_reports, generating_reports, long_generate_reports }
  }

  async generateReportPDF(created_by_uid:any, schl_class_group_id:any, is_regenerate:any, clientDomain:any, fromPyschPipline:any, fromIssueReviewer:boolean = false, isEndOfWindow:boolean = false){
    // get student's data
    let studentData = await this.generateStudentData(schl_class_group_id, clientDomain, fromPyschPipline, fromIssueReviewer, isEndOfWindow);
    const asrgIds:any[] = []
    const ansrgIds:any[] = []
    
    studentData.forEach( (theStudentData:any) => {
      if (theStudentData.is_fully_participate) {
        asrgIds.push(theStudentData.asrg_id)
      } else {
        ansrgIds.push(theStudentData.ansrg_id)
      }
    });
    const s3_report_link = `g9_isrs/tw_${studentData[0].tw_id}/class_group_id_${schl_class_group_id}.pdf`

    //save bulkpdflink to db table
    if(studentData.length > 0){
      const bulk_student_results_g9_reports = await this.app
        .service('db/write/bulk-student-results-g9-reports')
        .create({
          class_group_id: schl_class_group_id,
          created_on: dbDateNow(this.app),
          created_by_uid: created_by_uid,
        })
      await this.generatePdf(studentData, ''+bulk_student_results_g9_reports.id, clientDomain)
      .then(async res => {
        const id = bulk_student_results_g9_reports.id
        await this.app
          .service('db/write/bulk-student-results-g9-reports')
          .patch(id, {
            s3_report_link,
            report_generate_on: dbDateNow(this.app)
          })

          // update database to indicate single repport pdf is ready
          for (const asrgId of asrgIds) {
            await this.app.service('db/write/auto-student-report-generations')
              .patch(asrgId, {
                student_pdf_generated_on: dbDateNow(this.app)
              });
          }

          if (isEndOfWindow) {
            // Not fully participate student pdf report records
            for (const ansrgId of ansrgIds) {
              await this.app.service('db/write/auto-nfp-student-report-generations')
                .patch(ansrgId, {
                  student_pdf_generated_on: dbDateNow(this.app)
                });
            }
          }
      });
    }else{
      throw new Errors.BadRequest("NO_DATA_FOR_REPORT");
    }

    if(fromIssueReviewer){
      return {message: 'REPORT_GENERATED', reportURL: generateS3DownloadUrl(s3_report_link)}
    }else{
      return is_regenerate? {message: 'REPORT_REGENERATING'}: {message: 'REPORT_GENERATING'}
    }
  }

  async generatePdf(studentData:any, bsrr_id:any, clientDomain = '') {
    const isStressTest = this.app.get('isStressTest')  
    const use_testing_s3_folder = isStressTest
    const res = await this.eqaoScanningService.post(`/generate_g9_isr`, {studentData, bsrr_id, use_testing_s3_folder})
      .catch(err => {
        logger.info('UNABLE_TO_GENERATE_PDF', {err})
        throw new Errors.BadRequest('UNABLE_TO_GENERATE_PDF')
      })
    return res
  }
  
  /**
   * Generate student data for PDF report generation
   * @param schl_class_group_id 
   * @param clientDomain 
   * @param fromPyschPipline 
   * @param fromIssueReviewer 
   * @param isEndOfWindow Include withheld and not fully participated students
   * @returns studentData
   */
  async generateStudentData(schl_class_group_id:any, clientDomain:any, fromPyschPipline:any, fromIssueReviewer:boolean = false, isEndOfWindow:boolean = false){
    // fetch reports
    const isBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain().indexOf(clientDomain) > -1

    let schoolReports = await dbRawRead(this.app, [TW_EXCEPTION_STUDENT_STATUS.PENDED, TW_EXCEPTION_STUDENT_STATUS.WITHHOLD, schl_class_group_id], `
          -- Benchmark: 0.187 sec / cost: 34710.5
          select sr.school_year as sch_year
                , u.first_name as first_name
                , u.last_name as last_name
                , sr.student_oen as stu_oen
                , schl.name as school
                , schl.lang as lang
                , schl.is_private
                , sr.school_mident as sch_midant
                , sd.name as sch_board
                , sc.group_id as schl_class_group_id
                , ssais.id as ssais_id
                , tw.is_allow_results_tt as tw_is_allow_results_tt
                , tw.id AS tw_id
                , sr.overall
                , sr.dot_score
                -- , sr.data_raw
                , IF (twes_withheld.id, 1, 0) AS is_withheld
                , sr.attempt_id as test_attempt_id
                , asrg.id as asrg_id
                , IF (schl.school_type = 'section_23', 1, 0) AS is_section23
            from school_classes sc
            join user_roles ur on ur.group_id = sc.group_id and ur.role_type = 'schl_student'
            join users u on u.id = ur.uid
            join school_semesters ss on ss.id = sc.semester_id
            join student_reports sr on sr.uid = ur.uid and sr.is_revoked = 0 and sr.test_window_id = ss.test_window_id and sr.is_pending != 1 
            ${ !isEndOfWindow ? "and sr.is_withheld != 1 and sr.is_data_insufficient != 1 and sr.is_absent != 1" : "" }
            join test_windows tw on tw.id = sr.test_window_id
            join schools schl on schl.foreign_id = sr.school_mident
            join school_districts sd on sd.foreign_id = sr.board_mident
            join auto_student_report_generations asrg on asrg.test_attempt_id = sr.attempt_id and asrg.is_revoked = 0
       left join school_student_asmt_info_signoffs ssais on ssais.schl_group_id = sc.schl_group_id and ssais.tw_type_slug = tw.type_slug and ssais.is_revoked != 1 and ssais.test_window_id = tw.id
       left join bulk_student_results_g9_reports bsrr on  bsrr.class_group_id = sc.group_id and bsrr.is_revoked != 1
       left join tw_exceptions_students twes_pended on twes_pended.test_window_id = tw.id and twes_pended.uid = ur.uid and twes_pended.is_revoked = 0 and twes_pended.category = ?
       left join tw_exceptions_students twes_withheld on twes_withheld.test_window_id = tw.id and twes_withheld.uid = ur.uid and twes_withheld.is_revoked = 0 and twes_withheld.category = ?
           where sc.group_id = ?
           and twes_pended.id is null -- we don't want pended student for report
           ${ !isEndOfWindow ? "and twes_withheld.id is null" : "" }
        group by ur.uid
    ;`)
    
    if (isEndOfWindow) {
      const nfpStudentReports = await dbRawRead(this.app, [TW_EXCEPTION_STUDENT_STATUS.PENDED, TW_EXCEPTION_STUDENT_STATUS.WITHHOLD, schl_class_group_id], `
        -- Benchmark: 0.046 sec, cost: 3.40
        SELECT ss.year as sch_year
               , u.first_name as first_name
               , u.last_name as last_name
               , um.value as stu_oen
               , schl.name as school
               , schl.lang as lang
               , schl.is_private
               , schl.foreign_id as sch_midant
               , sd.name as sch_board
               , sc.group_id as schl_class_group_id
               , ssais.id as ssais_id
               , tw.is_allow_results_tt as tw_is_allow_results_tt
               , tw.id AS tw_id
               , null AS overall
               , null AS dot_score
               , IF (twes_withheld.id, 1, 0) AS is_withheld
               , ansrg.id as ansrg_id 
               , IF (schl.school_type = 'section_23', 1, 0) AS is_section23
            FROM school_classes sc
            JOIN user_roles ur ON ur.group_id = sc.group_id AND ur.role_type = 'schl_student'
            JOIN users u ON u.id = ur.uid
            JOIN user_metas um ON um.uid = ur.uid AND um.key = 'studentOEN' AND um.key_namespace = 'eqao_sdc' 
            JOIN school_semesters ss ON ss.id = sc.semester_id
            JOIN test_windows tw ON tw.id = ss.test_window_id
            JOIN schools schl ON schl.group_id = sc.schl_group_id
            JOIN school_districts sd ON sd.group_id = schl.schl_dist_group_id
            JOIN auto_nfp_student_report_generations ansrg 
              ON ansrg.student_uid = u.id 
             AND ansrg.test_window_id = tw.id 
             AND ansrg.is_revoked = 0
       LEFT JOIN school_student_asmt_info_signoffs ssais on ssais.schl_group_id = sc.schl_group_id AND ssais.tw_type_slug = tw.type_slug AND ssais.is_revoked != 1 AND ssais.test_window_id = tw.id
       LEFT JOIN tw_exceptions_students twes_pended ON twes_pended.test_window_id = tw.id AND twes_pended.uid = ur.uid AND twes_pended.is_revoked = 0 AND twes_pended.category = ?
       LEFT JOIN tw_exceptions_students twes_withheld ON twes_withheld.test_window_id = tw.id AND twes_withheld.uid = ur.uid AND twes_withheld.is_revoked = 0 AND twes_withheld.category = ?
           WHERE sc.group_id = ?
             AND twes_pended.id is null -- we don't want pended student for report
        GROUP BY ur.uid
      ;`)

      schoolReports = schoolReports.concat(nfpStudentReports)
    }

    const bypassAdminSignOff = isBypassDomain || fromPyschPipline || fromIssueReviewer; //Bypass school admin sign off if it from bypass domain or from pysch pipline call

    const filterSchoolReports = schoolReports.filter( sr => (sr.ssais_id !== null || bypassAdminSignOff) && (sr.tw_is_allow_results_tt == 1|| isBypassDomain || fromPyschPipline))
    const studentData:any = [];
    await Promise.all(filterSchoolReports.map( async fsr =>{
      let theStudentData:any = {}
      const notApplicableBoard = await this.app.service('public/translation').getOneBySlug('lbl_report_board_not_applicable', fsr.lang);
      const genericSchoolName = await this.app.service('public/translation').getOneBySlug('lbl_report_secondary_school', fsr.lang);
      const dateFormat = await this.app.service('public/translation').getOneBySlug('datefmt_g9_isr_gen_date', fsr.lang);
      const generateDate = moment().format(dateFormat)
      theStudentData.tw_id = fsr.tw_id
      theStudentData.sch_year = fsr.sch_year;
      theStudentData.stu_name = fsr.first_name.trim().toUpperCase()+ " "+fsr.last_name.trim().toUpperCase();
      theStudentData.sch_midant = ''+ fsr.sch_midant
      theStudentData.stu_oen = await this.maskOenForG9(fsr.stu_oen, fsr.lang);
      theStudentData.school = fsr.is_section23 ? genericSchoolName : `${fsr.school} (${fsr.sch_midant})`;
      theStudentData.sch_board = fsr.is_private === 1?notApplicableBoard:fsr.sch_board;
      theStudentData.lang = fsr.lang;
      theStudentData.schl_class_group_id = fsr.schl_class_group_id
      theStudentData.generate_date = generateDate;  // FR and EN date format is different
      //fsr.dot_score = ""+Math.floor(Math.random() * 5)+"."+Math.floor(Math.random() * 10) // this line need to be removed after testing
      theStudentData.overall_level = +fsr.overall;
      theStudentData.minor_score = this.getMoniorScore(fsr.dot_score);

      const isFullyParticipate = fsr.asrg_id ? true : false
      theStudentData.is_fully_participate = isFullyParticipate
      theStudentData.is_withheld = fsr.is_withheld

      // No box graphic for withheld and not fully participate students
      theStudentData.show_box = !fsr.is_withheld && isFullyParticipate
      
      const {overall_level_text, dot_score_text} = await this.generateISRTexts(fsr.overall, fsr.dot_score, fsr.is_withheld, fsr.lang)
      theStudentData.overall_level_text = overall_level_text
      theStudentData.dot_score_text = dot_score_text
      if (isFullyParticipate) {
        theStudentData.asrg_id = fsr.asrg_id;
        theStudentData.test_attempt_id = fsr.test_attempt_id;
      } else {
        theStudentData.ansrg_id = fsr.ansrg_id
      }

      studentData.push(theStudentData);
    }))
    return studentData;
  }

  /**
   * Generate result text strings to be displayed on ISR
   * @param overall 
   * @param dot_score 
   * @param is_withheld 
   * @param lang 
   * @returns 
   */
  async generateISRTexts(overall:any, dot_score:number, is_withheld: number, lang: string):Promise<{ overall_level_text: string; dot_score_text: string; }> {
    const translation = this.app.service('public/translation');
    let overall_level_text, dot_score_text
    if (is_withheld == 1) {
      overall_level_text = await translation.getOneBySlug('lbl_g9_reports_overall_withheld', lang)
      dot_score_text = await translation.getOneBySlug('lbl_g9_reports_withheld', lang)
    } else if (Number.isNaN(parseInt(overall))) { // if overall === null (for NFP student)
      overall_level_text = await translation.getOneBySlug('lbl_g9_reports_lvl_null', lang)
      dot_score_text = await translation.getOneBySlug('lbl_g9_reports_no_data', lang)
    } else if (+overall === 0) {
      overall_level_text = await translation.getOneBySlug('lbl_g9_reports_lvl_0', lang)
      dot_score_text = this.GenDotScoreTxtByLang(dot_score, lang)
    } else {
      overall_level_text = String(overall)
      dot_score_text = this.GenDotScoreTxtByLang(dot_score, lang)
    }
    return { overall_level_text, dot_score_text }
  }

  /**
   * Generate dot score text by language
   * @param dot_score 
   * @param lang 
   * @returns {string}
   */
  GenDotScoreTxtByLang(dot_score:number, lang:string):string {
    return lang === "fr" ? String(dot_score).replace('.', ',') : String(dot_score)
  }

  getMoniorScore(dotScore:string){
    if(!dotScore || !(+dotScore)){
      return 0
    }
    return +dotScore.split(".")[1]
  }

  async maskOenForG9(oen:any, lang:any){
    const translation = this.app.service('public/translation');
    if(oen == '#' || oen == '000000000'|| oen == null || oen == undefined){
      return await translation.getOneBySlug('g9_report_oen_not_provided', lang);
    } else if(oen == '-1'){
      return await translation.getOneBySlug('g9_report_oen_not_valid', lang);
    } else {
      let oenArr = oen.split('');
      for(let i =1; i < oenArr.length-3; i++){
        oenArr[i] = "*";
      }
      return oenArr.join('');
    }
  }

  /**
   * Get students with active reported issues for a given class group,
   * excludes students with generated reports
   * @param schl_class_group_id
   * @returns `Promise<{uid: Id, ta_id: Id}[]>`
   */
  async getReportedIssueStudents(schl_class_group_id: Id) {
    const knex: Knex = this.app.get('knexClientRead');
    return knex
      .select('ta.uid', 'ta.id as ta_id')
      .from('school_classes as sc')
      .join('user_roles as ur', {
        'ur.group_id': 'sc.group_id',
        'ur.role_type': knex.raw('"schl_student"'),
        'ur.is_revoked': 0
      })
      .join('school_semesters as ss', 'ss.id', 'sc.semester_id')
      .join('test_attempts as ta', {
        'ta.uid': 'ur.uid',
        'ta.is_invalid': 0,
        'ta.is_submitted': 1
      })
      .join('test_window_td_alloc_rules as twtdar', {
        'twtdar.id': 'ta.twtdar_id',
        'twtdar.test_window_id': 'ss.test_window_id',
        'twtdar.generate_report': 1
      })
      .leftJoin('auto_student_report_generations as asrg', {
        'asrg.student_uid': 'ta.uid',
        'asrg.test_attempt_id': 'ta.id',
        'asrg.is_revoked': 0
      })
      .join('reported_issues as ri', {
        'ri.testtaker_uid': 'ta.uid',
        'ri.test_session_id': 'ta.test_session_id',
        'ri.is_revoked': 0
      })
      .join('reported_issues_common as ric', {
        'ric.id': 'ri.reported_issues_common_id',
        'ric.is_resolved': 0
      })
      .where({
        'sc.group_id': schl_class_group_id,
        'asrg.id': null
      })
  }

  //this is for the ticket https://bubo.vretta.com/vea/project-management/eqao-shared-access/eqao-data-discrepancies/-/issues/1908  can remove this later when the ticket pass

  // async create (data: Data, params?: Params): Promise<Data> {
  //   return this.generateReportPDF(515512, 369173, false, "localhost", false)
  // }


  // async generateStudentData(schl_class_group_id:any, bypassAdminSignOff:any, clientDomain:any){
  //   // fetch reports
  //   const schoolReports = await dbRawRead(this.app, [], `
  //       select case when schl.lang = 'en' then "2022–2023"
  //                   else "2022-2023"
  //              end as sch_year
  //            , stu_100.first_name as first_name
  //            , stu_100.last_name as last_name
  //            , concat(stu_100.first_name, " ", stu_100.first_name) as stu_name
  //            , um.value as stu_oen
  //            , schl.name as school
  //            , schl.lang as lang
  //            , schl.foreign_id as sch_midant
  //            , sd.name as sch_board
  //            , '369173' as schl_class_group_id
  //            , sr.dot_score
  //            , sr.uid
  //            , sr.attempt_id as test_attempt_id
  //         from student_reports sr
  //         join test_attempts ta on ta.id = sr.attempt_id
  //         join test_window_td_alloc_rules twtdar on twtdar.id = ta.twtdar_id
  //         join test_windows tw on tw.id = twtdar.test_window_id and tw.type_slug = 'EQAO_G9M'
  //       --  join users u on u.id = sr.uid
  //         join scratch.G9_ISR_Validation_randomeSample100_name_csv stu_100 on stu_100.uid = sr.uid
  //         join user_metas um on um.key_namespace = 'eqao_sdc' and um.key = 'StudentOEN' and um.uid = sr.uid
  //         join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
  //         join school_classes sc on sc.id = scts.school_class_id
  //         join schools schl on schl.group_id = sc.schl_group_id
  //         join school_districts sd on sd.group_id = sc.schl_dist_group_id
  //        where sr.uid in (872607,1890076,1886161,1895735,851204,1865792,1869349,732364,823834,1207458,854712,1902341,1867592,1885937,1154380,1156708,1890197,1886587,1858644,1866263,859289,1212561,1896081,1864476,1902092,1873693,1869208,1877214,1871483,871721,1883634,1172730,1873980,1889963,1901537,1204657,728327,1882751,2326127,1876169,1866705,874196,1881349,1888649,1209464,854694,1207812,2325978,1148122,1894870,1901423,1865622,1866752,879565,1172419,1882740,1207548,1215718,859963,1879458,1890149,1901842,1899295,1864928,729878,880517,1857345,1391088,1857047,1925764,877591,854271,773419,1217715,1219534,1208866,1213879,857916,774102,1175420,877661,1210978,878230,1214324,1219915,1214148,1168644,1168672,1150394,1219960,1156969,854201,1210817,878154,1876150,1168578,1875793,1860700,1825349,1825181)
  //       -- and sr.is_revoked = 0
  //          and sr.is_reporting = 1
  //          and sr.is_isr = 0
  //     group by sr.attempt_id
  //   ;`)

  //   let theStudentData:any = {}
  //   const filterSchoolReports = schoolReports
  //   const studentData:any = [];
  //   await Promise.all(filterSchoolReports.map( async fsr =>{
  //     let theStudentData:any = {}
  //     theStudentData.sch_year = fsr.sch_year;
  //     theStudentData.stu_name = fsr.first_name.trim().toUpperCase()+ " "+fsr.last_name.trim().toUpperCase();
  //     theStudentData.sch_midant = ''+ fsr.sch_midant
  //     theStudentData.stu_oen = await this.maskOenForG9(fsr.stu_oen, fsr.lang);
  //     theStudentData.school = fsr.school;
  //     theStudentData.sch_board = fsr.sch_board;
  //     theStudentData.lang = fsr.lang;
  //     theStudentData.schl_class_group_id = fsr.schl_class_group_id
  //     theStudentData.generate_date = moment().format("YYYY-MM-DD");
  //     theStudentData.overall_level = this.getLevel(fsr.dot_score);
  //     theStudentData.overall_level_text = await this.getLevelText(fsr.dot_score, theStudentData.lang);
  //     theStudentData.minor_score = this.getMoniorScore(fsr.dot_score);
  //     theStudentData.test_attempt_id = fsr.test_attempt_id;
  //     studentData.push(theStudentData);
  //   }))
  //   return studentData;
  // }

}
