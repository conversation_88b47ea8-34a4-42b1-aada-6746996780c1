import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';

const MIN_NUMBER_OF_PRESENT_SCORES = 10;
const MAX_COMBINATION_PER_REQUEST = 32;
interface Data {
  combinations: any[],
  questionID: string,
  lang: string,
  initial: number
}


interface ServiceOptions {}

export class ScoreMatrixValidation implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: number, data: Data, params?: Params): Promise<any[]> {
    const {combinations, lang, initial} = data;
    if(id && initial){
      await dbRawWrite(this.app, [lang, id],
        `
        UPDATE test_question_expected_responses
        SET is_score_matrix_validated = 0
        WHERE lang = ?
          AND item_id = ?;
        `
      )
      return []
    }

    if(combinations.length > MAX_COMBINATION_PER_REQUEST){
      throw new Error("MORE_COMBINATIONS_THAN_ALLOWED");
    }

    try{
      const answer = [];
      const scoreMap: {[key:number]: string[]} = {};
      let weight: number | undefined;
      for(let combination of combinations){
        const response = {
          response_raw: JSON.stringify(combination),
          item_id: +id,
        };
        const subRecord: any = await this.app
        .service("public/test-ctrl/schools/student-attempts")
        .processResponseRaw({
          response,
          responseTypes: <{ [key: string]: boolean }>{},
        });
        if(!weight){ // we just have to do this once since item should always have a single weight
          weight = combination[1]?.weight || combination.weight;
        }
        subRecord.weight = weight
        answer.push(subRecord);
        if(scoreMap[subRecord[0].score]){scoreMap[subRecord[0].score].push(subRecord[0].response)}
        else{
          scoreMap[subRecord[0].score] = [subRecord[0].response];
        }
      }
      const answers = answer.map(ans => ans[0].response)
      const submittedResponses = await dbRawRead(this.app, [lang, id, answers],
        `
        SELECT id, formatted_response, score, weight, is_score_matrix_validated
        FROM test_question_expected_responses 
        WHERE lang = ? 
        AND item_id = ? 
        AND is_score_matrix_validated = 0
        AND formatted_response in (?)
         `
      )
      const validatedElements: string[] = [];
      for(let response of submittedResponses){
        const matrix_response_index = answer.findIndex(ans => {
          return ans[0].response === response.formatted_response;
        });
        
        if(matrix_response_index !== -1){
          const score = answer[matrix_response_index][0].score;
          const weight = answer[matrix_response_index].weight;
          if(response.score.toFixed(3) === score.toFixed(3) && response.weight === weight){
            validatedElements.push(response.id);
          }
        }
      }
      if(validatedElements.length){
        await dbRawWrite(
          this.app,
          [validatedElements],
          `
          UPDATE test_question_expected_responses
          SET is_score_matrix_validated = 1
          WHERE id in (?)
          `
  
        );
      }

      // Getting all possible scores
      const possibleScores = (await dbRawRead(this.app, [lang, id],
        `
        SELECT DISTINCT score
        FROM test_question_expected_responses 
        WHERE lang = ? 
        AND item_id = ? 
        `
      ))?.map(row => row.score.toFixed(3));

      // If there are not enough scores we'll need to add more
      if( !possibleScores || (possibleScores?.length < MIN_NUMBER_OF_PRESENT_SCORES)){
        for (let [score, responses] of Object.entries(scoreMap)) {

          if (!possibleScores?.includes((+score).toFixed(3))) {
            // Select random response so user can see a variety of correct responses
            responses = responses.filter(r => !!r);
            const response = responses[Math.floor(Math.random() * responses.length)];
            if(response){
              await dbRawWrite(this.app, [id, lang, score, response, weight], `
                INSERT INTO test_question_expected_responses
                (item_id, lang, score, formatted_response, weight, is_score_matrix_validated)
                VALUES(?, ?, ?, ?, ?, 1) 
                `);
            }
          }
        }
      }
      return answer;
    }
    catch(err){
      throw err;
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }


  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
    
  }
}
