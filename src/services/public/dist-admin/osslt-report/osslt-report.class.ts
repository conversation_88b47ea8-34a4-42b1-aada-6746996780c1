import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadReporting } from '../../../../util/db-raw';
import { TW_EXCEPTION_STUDENT_STATUS } from '../../educator/g9-reports/g9-reports.class'
import { getSysConstString } from '../../../../util/sys-const-string';
import { STU_EX_OVERRIDES } from '../../test-ctrl/schools/student-exceptions/student-exceptions.class';
interface Data {}

interface ServiceOptions {}
export enum ENTRY_UR {
  ISSUE_REVIEWER = 'issueReviewer',
  SCHL_BOARD = 'schoolBoard',
  SCHL_ADMIN = 'schoolAdmin'
}

interface IDataProps {
  [group: string]: {
    [field: string]: {
      key: string;
      getValue?: boolean;
    };
  };
}

export class OssltReport implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  additionalUserMetasPropsOSSLT = {
    NonParticipationStatus: { key: "NonParticipationStatus", getValue: true },
    EligibilityStatus: { key: "EligibilityStatus", getValue: true },
    isLinear: { key: "Linear", getValue: true },
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const { schl_dist_group_id, testWindowId, clientDomain, isSecreteUser, isVersion2 } = (<any>params).query;
    const allowBypassDomain = this.app.service('public/school-admin/reports').allowBypassDomain();
    const isBypassDomain = allowBypassDomain.indexOf(clientDomain) > -1
    const secreteUserRecord = await getSysConstString(this.app, 'OSSLT_ISR_BYPASS')
    const isValidSecreteUser = isSecreteUser == secreteUserRecord
    const bypassAdminSignOff = false
    const singleStudentUid = undefined
    const isGetIsr =  false
    const csv_ver2 = isVersion2 ? JSON.parse(isVersion2) : false
    return await this.gerReport(ENTRY_UR.SCHL_BOARD, schl_dist_group_id, testWindowId, isBypassDomain, bypassAdminSignOff, isValidSecreteUser, singleStudentUid, isGetIsr, csv_ver2);
  }

  /**
   * This function is used by dist admin, school admin and issue reviewer to get export OSSLT result CSV
   * The newer OSSLT ISR from Fall 2024 and onward uses this function to get student report data 
   * @param {string} entry "schoolBoard", "schoolAdmin", "issueReviewer" - this is to determine type of group id
   * @param group_id 
   * @param testWindowId 
   * @param isBypassDomain 
   * @param bypassAdminSignOff To bypass student data signoff
   * @param isValidSecreteUser To bypass student data signoff, is_allow_results_tt and show_report_to_Board
   * @param singleStudentUid 
   * @param is_get_isr Get ISR related data
   * @param csv_ver2 To apply new override hierarchy rules for CSV from Fall 2024 and onward
   * @returns {array of reports} reports
   */
  async gerReport(entry:ENTRY_UR, group_id:string, testWindowId:string|number, isBypassDomain = false, bypassAdminSignOff = false, isValidSecreteUser = false, singleStudentUid:string | undefined = undefined, is_get_isr = false, csv_ver2 = false){
    const includeded_tw_exception_student_status = [TW_EXCEPTION_STUDENT_STATUS.PENDED]
    const twe_exclude_from_csv = STU_EX_OVERRIDES.EXCLUDE_FROM_CSV
    
    const reportQuery = this.getReportDBQuery(entry, isBypassDomain, isValidSecreteUser, bypassAdminSignOff, false, singleStudentUid)
    const result = await dbRawReadReporting(this.app, {testWindowId, includeded_tw_exception_student_status, group_id, singleStudentUid, twe_exclude_from_csv}, 
      reportQuery
    );

    const guestStudentReports = await this.getGuestStudentReports(entry, isBypassDomain, bypassAdminSignOff, testWindowId, group_id, isValidSecreteUser, singleStudentUid);
    let fullReports = result.concat(guestStudentReports);
    
    // Filter out outcome = 1 pending status for ISR; Reporting Spec 2024-2025
    if(is_get_isr) {
      fullReports = fullReports.filter(report => report.twes_pending !== 1)
    } else { // CSV
      fullReports = fullReports.filter(report => report.twes_ex_csv !== 1)
    }

    //round 1: put those has report and not revoked student in the returnResult
    let returnResult :any[]= [];
    fullReports.forEach( (r:any) => { 
      if(r.HasReport !== null && +r.ur_is_revoked != 1 && +r.sc_is_active != 0 && +r.ts_is_cancelled != 1){
        returnResult.push(r)
      }
    })

    //round 2: put those has report and revoked student in the returnResult if they are not in there
    fullReports.forEach( (r:any) => { 
      if(r.HasReport !== null && (+r.ur_is_revoked == 1 || +r.sc_is_active == 0 || +r.ts_is_cancelled == 1)){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN && rr.SchMident == r.SchMident)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    //round 3: put those has no report and not revoked student and has attempt started
    fullReports.forEach( (r:any) => { 
      if(r.HasReport == null && (+r.ur_is_revoked != 1 && +r.sc_is_active != 0 && +r.ts_is_cancelled != 1) && r.HasSubmitted == 1){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN && rr.SchMident == r.SchMident)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    //round 4: put those has no report and not revoked student in the returnResult if they are not in there
    fullReports.forEach( (r:any) => { 
      if(r.HasReport == null && (+r.ur_is_revoked != 1)){
        const rr = returnResult.find( rr => rr.StudentOEN == r.StudentOEN && rr.SchMident == r.SchMident)
        if(!rr){
          returnResult.push(r)
        }  
      }
    })

    // Retrieve student data from snapshots
    if (csv_ver2 || is_get_isr) {
      this.retrieveReportDataFromSnapshots(returnResult, {
        user_metas: this.additionalUserMetasPropsOSSLT
      })
    }

    returnResult.forEach (report => {
      let resultOrder: any[] = []
      if(csv_ver2 || is_get_isr){ // New override hierarchy rules apply to OSSLT ISR and CSV from Fall 2024 and onward
        resultOrder = [
          {target:'twes_pending',           targetValue:'1', returnValue:'0'},
          {target:'is_withheld',            targetValue:'1', returnValue:'10'},
          {target:'is_data_insufficient',   targetValue:'1', returnValue:'11'},
          {target:'overall_result',         targetValue:'1', returnValue:'1'}, // Successful
          {target:'overall_result',         targetValue:'0', returnValue:'2'}, // Not yet Successful
          {target:'NonParticipationStatus', targetValue:'3', returnValue:'4'}, // OSSLC
          {target:'NonParticipationStatus', targetValue:'4', returnValue:'4'}, // OSSLC Spring
          {target:'NonParticipationStatus', targetValue:'2', returnValue:'5'}, // Deferred
          {target:'NonParticipationStatus', targetValue:'1', returnValue:'6'}, // Exempted
          {target:'is_absent',              targetValue:'1', returnValue:'3'}
        ];
      } else { // This is the old override hierarchy rule, can be removed once CSV files generation are no longer needed for TW 93 and earlier 
        resultOrder[0] =  {target:'twes_pending',           targetValue:'1', returnValue:'0'}
        resultOrder[1] =  {target:'is_withheld',            targetValue:'1', returnValue:'10'}
        resultOrder[2] =  {target:'is_pending',             targetValue:'1', returnValue:'0'}
        resultOrder[3] =  {target:'is_data_insufficient',   targetValue:'1', returnValue:'11'}
        resultOrder[4] =  {target:'overall_result',         targetValue:'1', returnValue:'1'}
        resultOrder[5] =  {target:'is_absent',              targetValue:'1', returnValue:'3'}
        resultOrder[6] =  {target:'overall_result',         targetValue:'0', returnValue:'2'}
        resultOrder[7] =  {target:'NonParticipationStatus', targetValue:'3', returnValue:'4'}
        resultOrder[8] =  {target:'NonParticipationStatus', targetValue:'2', returnValue:'5'}
        resultOrder[9] =  {target:'NonParticipationStatus', targetValue:'1', returnValue:'6'}
        resultOrder[10]=  {target:'HasSubmitted',           targetValue:'1', returnValue:'0'} // has submit but no report
      }

      for (let index =0; index<resultOrder.length;index++){
        const target = resultOrder[index].target 
        const targetValue = resultOrder[index].targetValue
        const stateValue =  resultOrder[index].returnValue
        if(report[target] && +report[target] === +targetValue){
          report.Result = stateValue;
          return
        }
      } 
      report.Result = '3' // hardcoded default value set to absent. Basically we shoult run to this line
    })

    //sort by StudentOEN
    returnResult.sort((a:any,b:any) => +a.StudentOEN - +b.StudentOEN);
    return returnResult;
  }

  async getGuestStudentReports(entry:any, isBypassDomain:any, bypassAdminSignOff:any, testWindowId:any, group_id:any, isValidSecreteUser = false, singleStudentUid: string | undefined = undefined){
    const reportQueryGuestStudent = this.getReportDBQuery(entry, isBypassDomain, isValidSecreteUser, bypassAdminSignOff, true, singleStudentUid)
    const includeded_tw_exception_student_status = [TW_EXCEPTION_STUDENT_STATUS.PENDED]
    const twe_exclude_from_csv = STU_EX_OVERRIDES.EXCLUDE_FROM_CSV

    const guestStudentReports = await dbRawReadReporting(this.app, {testWindowId, includeded_tw_exception_student_status, group_id, singleStudentUid, twe_exclude_from_csv}, 
      reportQueryGuestStudent
  );

    return guestStudentReports
  }

  /**
   * Generate report db query for onsite students and for students who took exams at another school as guest students
   * @param {ENTRY_UR} entry schoolBoard or schoolAdmin
   * @param isBypassDomain 
   * @param isValidSecreteUser 
   * @param bypassAdminSignOff 
   * @param isGuestStudent 
   * @returns {string} db query
   */
  getReportDBQuery (entry:ENTRY_UR, isBypassDomain:boolean, isValidSecreteUser:boolean, bypassAdminSignOff:boolean, isGuestStudent:boolean = false, singleStudentUid: string | undefined = undefined):string {

    const bypassSignoff = bypassAdminSignOff || isValidSecreteUser || entry === ENTRY_UR.ISSUE_REVIEWER
    const showReportToAll = isBypassDomain || isValidSecreteUser || entry === ENTRY_UR.ISSUE_REVIEWER
    // Benchmark: Duration 0.25 sec on QC6 for mident 893978 in tw 90 with 810 rows return
    return `
        select distinct
               schl.foreign_id as SchMident
             , schl.name as SchName
             , schl.lang as school_lang
             , schl.is_private as is_private_schl
             , sd.name as school_board_name
             , sc.name as \`Grouping\` 
             , um1.value as StudentOEN
             , um2.value as SASN
             , us.first_name as FirstName
             , us.last_name as LastName
             , ta.started_on as HasStarted
             , ta.is_submitted as HasSubmitted
             , ta.closed_on as ta_closed_on 
             , ta.started_on as StartedOn
             , sr.id as HasReport
             , sr.overall as overall_result
             , sr.scale_score as OSSLTScaleScore
             , sr.questions_answered
             , sr.max_num_qs as total_questions
             , sr.is_data_insufficient
             , um3.value as NonParticipationStatus
             , um4.value as EligibilityStatus
             , um5.value as isLinear
             , sr.is_absent
             , sr.is_pending
             , sr.is_withheld
             , case when twes.category = "PENDED" then 1
                    else 0
                end as twes_pending
             , case when twes_ex_csv.id is not null then 1
                    else 0
                end as twes_ex_csv
             , ur.is_revoked as ur_is_revoked
             , sc.is_active as sc_is_active
             , ts.is_cancelled as ts_is_cancelled
             , null as Note
             , tw.show_isr_alt_score
             , tw.show_isr_num_q
             , tw.date_end as tw_date_end
             , ur.uid
             , IF (schl.school_type = 'section_23', 1, 0) AS is_section23
             , snapshots.student_info_json AS snapshot_data
          from schools schl
          join school_districts sd on sd.group_id = schl.schl_dist_group_id
          join school_classes sc on sc.schl_group_id = schl.group_id and sc.group_type = 'EQAO_G10'
        ${!isGuestStudent ? `` : `
          join school_classes_guest scg on scg.guest_sc_group_id = sc.group_id
          join school_classes sc2 on sc2.group_id = scg.invig_sc_group_id
        `}
          join school_semesters ss on ss.id = ${!isGuestStudent ? `sc.semester_id` : `sc2.semester_id`}
          join test_windows tw on tw.id = ss.test_window_id 
          ${showReportToAll ? `` : 
            entry === ENTRY_UR.SCHL_BOARD ? `AND tw.show_report_to_Board = 1` :
            ``}
          ${showReportToAll ? `` : 
            entry === ENTRY_UR.SCHL_ADMIN ? `AND tw.is_allow_results_tt = 1` :
            ``}
          and tw.id = :testWindowId
          join user_roles ur on ur.group_id = sc.group_id
          join user_metas um1 on um1.uid = ur.uid and um1.key_namespace = 'eqao_sdc' and um1.key = 'StudentOEN'
     left join user_metas um2 on um2.uid = ur.uid and um2.key_namespace = 'eqao_sdc' and um2.key = 'SASN'
     left join user_metas um3 on um3.uid = ur.uid and um3.key_namespace = 'eqao_sdc_g10' and um3.key = 'NonParticipationStatus'
     left join user_metas um4 on um4.uid = ur.uid and um4.key_namespace = 'eqao_sdc_g10' and um4.key = 'EligibilityStatus'
     left join user_metas um5 on um5.uid = ur.uid and um5.key_namespace = 'eqao_sdc_g10' and um5.key = 'Linear'
          join users us on us.id = ur.uid
     left join school_class_test_sessions scts on scts.school_class_id = ${!isGuestStudent ? `sc.id` : `sc2.id`} and scts.slug = 'OSSLT_OPERATIONAL'
     left join test_sessions ts on ts.id = scts.test_session_id and ts.test_window_id = :testWindowId
     left join test_attempts ta on ta.test_session_id = ts.id and ta.uid = ur.uid and ta.twtdar_order = 0
     left join student_reports sr 
      on sr.uid = ur.uid 
      and sr.attempt_id = ta.id 
      and sr.is_revoked = 0
      and sr.is_isr = 1
      and sr.is_reporting = 1
     left join tw_exceptions_students twes on twes.uid = ur.uid and twes.test_window_id = tw.id and twes.category in (:includeded_tw_exception_student_status) and twes.is_revoked = 0
     left join tw_exceptions_students twes_ex_csv on twes_ex_csv.uid = ur.uid 
      and twes_ex_csv.test_window_id = tw.id 
      and twes_ex_csv.category = (:twe_exclude_from_csv)
      and twes_ex_csv.is_revoked = 0
     left join school_student_asmt_info_signoffs ssais 
       on ssais.schl_group_id = schl.group_id 
      and ssais.tw_type_slug = 'EQAO_G10L' 
      and ssais.is_revoked = 0 
      and ssais.test_window_id = tw.id
     left join school_student_asmt_info_snapshot snapshots 
       on snapshots.student_uid = ur.uid 
      and snapshots.ssais_id = ssais.id 
      and snapshots.is_revoked = 0
          ${entry === ENTRY_UR.SCHL_BOARD ? ` where schl.schl_dist_group_id = :group_id` : ``}
          ${entry === ENTRY_UR.SCHL_ADMIN || entry === ENTRY_UR.ISSUE_REVIEWER ? ` where schl.group_id = :group_id` : ``}
          ${bypassSignoff ? `` : `and ssais.id is not null`}
          ${singleStudentUid ? `and ur.uid = :singleStudentUid`: ``}
    ;`
  }

  /**
   * Replace student report values with data in snapshots.student_info_json
   * If no snapshot data found, keep db data
   * @param student_reports 
   * @param customProps additional props
   * @returns 
   */
  retrieveReportDataFromSnapshots(student_reports: any[], customProps: Partial<IDataProps> = {}) {
    const dataProps: IDataProps = {
      users: {
        FirstName: { key: "first_name" },
        LastName: { key: "last_name" },
      },
      user_metas: {
        StudentOEN: { key: "StudentOEN", getValue: true },
        SASN: { key: "SASN", getValue: true },
      },
    };

    if (customProps.user_metas) {
      dataProps.user_metas = {
        ...dataProps.user_metas,
        ...customProps.user_metas,
      };
    }

    student_reports.forEach((report) => {
      if (report.snapshot_data) {
        const snapshotData = JSON.parse(report.snapshot_data);

        for (const dataGroup in dataProps) {
          for (const [prop, { key, getValue }] of Object.entries(dataProps[dataGroup])) {
            if (getValue) {
              // Find the object in the snapshot array where 'key' matches
              const snapshot_um = snapshotData[dataGroup]?.find((um: { key: string }) => um.key === key);
              if(snapshot_um) {
                report[prop] = snapshot_um.value;
              }
            } else {
              report[prop] = snapshotData[dataGroup]?.[key] ?? report[prop];
            }
          }
        }

        delete report.snapshot_data; // Snapshot no longer needed, deleted to decrease payload
      }
    });
  }
  
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.BadRequest();
  }
}
