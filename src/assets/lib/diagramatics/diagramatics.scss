.diagramatics-dnd-draggable:hover{
    cursor: pointer;
    opacity: 0.5;
}
.diagramatics-dnd-container:hover{
    filter: invert(1) brightness(0.6) invert(1);
}

.diagramatics-dnd-draggable:focus{
    outline: none;
    fill-opacity: 0.5;
}
.diagramatics-dnd-container:focus{
    outline: none;
    filter: invert(1) brightness(0.6) invert(1);
}

.diagramatics-dnd-container.hovered{
    filter: invert(1) brightness(0.6) invert(1);
}
.diagramatics-dnd-draggable.hovered{
    opacity: 0.5;
}
.diagramatics-dnd-draggable.picked{
    opacity: 0.5;
}

.diagramatics-focusable-no-outline {
    outline: none;
}
.diagramatics-focusrect {
    opacity: 1;
    visibility: hidden;
}
:focus > .diagramatics-focusrect {
    visibility: visible;
}